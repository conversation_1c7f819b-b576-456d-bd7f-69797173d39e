{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/Logo.tsx"], "sourcesContent": ["import React from 'react';\n\nconst Logo = ({ className = \"\" }: { className?: string }) => {\n  return (\n    <div className={`inline-flex items-center justify-center w-10 h-10 bg-gray-900 rounded-lg ${className}`}>\n      <span className=\"text-white font-bold text-xl\">M</span>\n    </div>\n  );\n};\n\nexport default Logo;\n"], "names": [], "mappings": ";;;;;AAEA,MAAM,OAAO,CAAC,EAAE,YAAY,EAAE,EAA0B;IACtD,qBACE,8OAAC;QAAI,WAAW,CAAC,yEAAyE,EAAE,WAAW;kBACrG,cAAA,8OAAC;YAAK,WAAU;sBAA+B;;;;;;;;;;;AAGrD;uCAEe", "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/SimpleHeaderChat.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { MessageCircle } from 'lucide-react';\n\nconst SimpleHeaderChat: React.FC = () => {\n  const handleChatOpen = () => {\n    // Try to open Crisp chat if available\n    if (typeof window !== 'undefined' && (window as any).$crisp) {\n      (window as any).$crisp.push(['do', 'chat:open']);\n    } else {\n      // Fallback to mailto if Crisp is not available\n      window.location.href = 'mailto:<EMAIL>?subject=Chat%20Request';\n    }\n\n    // Track chat interaction for analytics\n    if (typeof window !== 'undefined' && (window as any).gtag) {\n      (window as any).gtag('event', 'chat_opened', {\n        event_category: 'engagement',\n        event_label: 'header_chat'\n      });\n    }\n  };\n\n  return (\n    <button\n      onClick={handleChatOpen}\n      className=\"inline-flex items-center gap-2 text-gray-600 dark:text-gray-300 hover:text-dark-charcoal dark:hover:text-white transition-colors duration-200\"\n      aria-label=\"Open chat\"\n    >\n      <MessageCircle className=\"w-5 h-5\" />\n      <span className=\"hidden sm:inline\">Chat</span>\n    </button>\n  );\n};\n\nexport default SimpleHeaderChat;\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKA,MAAM,mBAA6B;IACjC,MAAM,iBAAiB;QACrB,sCAAsC;QACtC,uCAA6D;;QAE7D,OAAO;YACL,+CAA+C;YAC/C,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;QAEA,uCAAuC;QACvC,uCAA2D;;QAK3D;IACF;IAEA,qBACE,8OAAC;QACC,SAAS;QACT,WAAU;QACV,cAAW;;0BAEX,8OAAC,wNAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;0BACzB,8OAAC;gBAAK,WAAU;0BAAmB;;;;;;;;;;;;AAGzC;uCAEe", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/SimpleDarkModeToggle.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Sun, Moon } from 'lucide-react';\nimport { useTheme } from '../contexts/ThemeContext';\n\ninterface SimpleDarkModeToggleProps {\n  className?: string;\n  size?: 'sm' | 'md' | 'lg';\n}\n\nconst SimpleDarkModeToggle: React.FC<SimpleDarkModeToggleProps> = ({\n  className = '',\n  size = 'md'\n}) => {\n  const { theme, toggleTheme } = useTheme();\n\n  const getSizeClasses = () => {\n    switch (size) {\n      case 'sm':\n        return 'w-8 h-8 text-sm';\n      case 'lg':\n        return 'w-12 h-12 text-lg';\n      default:\n        return 'w-10 h-10 text-base';\n    }\n  };\n\n  return (\n    <button\n      onClick={toggleTheme}\n      className={`${getSizeClasses()} flex items-center justify-center rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-200 ${className}`}\n      aria-label={`Switch to ${theme === 'dark' ? 'light' : 'dark'} mode`}\n    >\n      {theme === 'dark' ? (\n        <Sun className=\"w-5 h-5\" />\n      ) : (\n        <Moon className=\"w-5 h-5\" />\n      )}\n    </button>\n  );\n};\n\nexport default SimpleDarkModeToggle;\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AAJA;;;;AAWA,MAAM,uBAA4D,CAAC,EACjE,YAAY,EAAE,EACd,OAAO,IAAI,EACZ;IACC,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IAEtC,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QACC,SAAS;QACT,WAAW,GAAG,iBAAiB,gLAAgL,EAAE,WAAW;QAC5N,cAAY,CAAC,UAAU,EAAE,UAAU,SAAS,UAAU,OAAO,KAAK,CAAC;kBAElE,UAAU,uBACT,8OAAC,gMAAA,CAAA,MAAG;YAAC,WAAU;;;;;iCAEf,8OAAC,kMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;;;;;;AAIxB;uCAEe", "debugId": null}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/config/site.ts"], "sourcesContent": ["/**\n * Site Configuration\n * \n * Centralized configuration for static, developer-controlled values.\n * This file contains non-sensitive data that doesn't belong in the CMS.\n */\n\n// Site Identity\nexport const SITE_CONFIG = {\n  name: 'Mobilify',\n  tagline: 'Turn Your Website or Idea Into a Custom Mobile App',\n  description: 'Mobilify converts your existing website or new idea into a high-quality, native mobile app for iOS and Android. Get a beautiful, custom-designed app without the complexity.',\n  url: process.env.NEXT_PUBLIC_SITE_URL || 'https://mobilify.app',\n  author: 'Mobilify Team',\n} as const;\n\n// Contact Information\nexport const CONTACT_INFO = {\n  email: '<EMAIL>',\n  phone: '+****************', // Update with actual phone\n  address: {\n    street: '123 Tech Street',\n    city: 'San Francisco',\n    state: 'CA',\n    zip: '94105',\n    country: 'USA'\n  }\n} as const;\n\n// Social Media Links\nexport const SOCIAL_LINKS = {\n  twitter: 'https://twitter.com/mobilify',\n  linkedin: 'https://linkedin.com/company/mobilify',\n  github: 'https://github.com/mobilify',\n  facebook: 'https://facebook.com/mobilify',\n  instagram: 'https://instagram.com/mobilify'\n} as const;\n\n// Navigation Structure\nexport const NAVIGATION = {\n  main: [\n    { label: 'Services', href: '#services-overview', id: 'services-overview' },\n    { label: 'How It Works', href: '#process', id: 'process' },\n    { label: 'About Us', href: '#about', id: 'about' },\n  ],\n  footer: {\n    company: [\n      { label: 'About', href: '/about' },\n      { label: 'Services', href: '/services' },\n      { label: 'Blog', href: '/blog' },\n      { label: 'FAQ', href: '/faq' },\n    ],\n    legal: [\n      { label: 'Privacy Policy', href: '/privacy' },\n      { label: 'Terms of Service', href: '/terms' },\n      { label: 'Cookie Policy', href: '/cookies' },\n    ],\n    support: [\n      { label: 'Contact Us', href: '#contact' },\n      { label: 'Help Center', href: '/help' },\n      { label: 'Documentation', href: '/docs' },\n    ]\n  }\n} as const;\n\n// Default/Fallback Content (used when CMS is unavailable)\nexport const FALLBACK_CONTENT = {\n  hero: {\n    headline: 'Your Idea. Your App. Realized.',\n    subtext: 'Mobilify transforms your concepts and existing websites into stunning, high-performance mobile apps. We are the bridge from vision to launch.',\n    buttonText: 'See How It Works'\n  },\n  contact: {\n    headline: 'Ready to Build Your Mobile Future?',\n    subtext: \"Let's discuss your project. We're happy to provide a free, no-obligation consultation and quote.\",\n    buttonText: 'Send Message',\n    successMessage: 'Thank you! Your message has been sent successfully. We\\'ll get back to you within 24 hours.',\n    errorMessage: 'Sorry, there was an error sending your message. Please try again or contact us directly.'\n  },\n  services: {\n    headline: 'Our Services',\n    subtext: 'Choose the perfect solution for your mobile app needs'\n  },\n  process: {\n    headline: 'How It Works',\n    subtext: 'Our proven process to turn your idea into a successful mobile app'\n  },\n  about: {\n    headline: 'About Mobilify',\n    subtext: 'We are passionate about helping businesses and entrepreneurs bring their ideas to life through mobile technology.'\n  }\n} as const;\n\n// Service Offerings\nexport const SERVICES = {\n  starter: {\n    name: 'Starter App',\n    description: 'Perfect for converting existing websites into mobile apps.',\n    price: 'Starting at $5,000',\n    features: ['Website Conversion', 'iOS & Android', 'Basic Features'],\n    popular: false\n  },\n  custom: {\n    name: 'Custom App',\n    description: 'Turn your new ideas into reality with custom development.',\n    price: 'Starting at $15,000',\n    features: ['Idea to App', 'Custom UI/UX', 'Advanced Features'],\n    popular: true\n  },\n  enterprise: {\n    name: 'Enterprise Solution',\n    description: 'Comprehensive solutions for large-scale applications.',\n    price: 'Custom Pricing',\n    features: ['Full Development', 'Scalable Architecture', 'Ongoing Support'],\n    popular: false\n  }\n} as const;\n\n// External Service URLs\nexport const EXTERNAL_SERVICES = {\n  analytics: {\n    googleAnalytics: process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID,\n  },\n  forms: {\n    web3forms: process.env.NEXT_PUBLIC_WEB3FORMS_ACCESS_KEY,\n  },\n  chat: {\n    crisp: process.env.NEXT_PUBLIC_CRISP_WEBSITE_ID,\n    tawkTo: process.env.NEXT_PUBLIC_TAWK_TO_PROPERTY_ID,\n  },\n  newsletter: {\n    mailchimp: process.env.NEXT_PUBLIC_MAILCHIMP_API_KEY,\n    convertkit: process.env.CONVERTKIT_API_KEY,\n  }\n} as const;\n\n// SEO Configuration\nexport const SEO_CONFIG = {\n  defaultTitle: SITE_CONFIG.name + ' | ' + SITE_CONFIG.tagline,\n  titleTemplate: '%s | ' + SITE_CONFIG.name + ' - Custom Mobile App Development',\n  description: SITE_CONFIG.description,\n  keywords: [\n    'mobile app development',\n    'website to app conversion',\n    'custom mobile apps',\n    'iOS app development',\n    'Android app development',\n    'mobile app builder',\n    'app development services',\n    'native mobile apps',\n    'mobile app design',\n    'app development company'\n  ],\n  openGraph: {\n    type: 'website',\n    locale: 'en_US',\n    url: SITE_CONFIG.url,\n    siteName: SITE_CONFIG.name,\n    images: [\n      {\n        url: '/og-image.png',\n        width: 1200,\n        height: 630,\n        alt: SITE_CONFIG.name + ' - ' + SITE_CONFIG.tagline,\n      },\n    ],\n  },\n  twitter: {\n    handle: '@mobilify',\n    site: '@mobilify',\n    cardType: 'summary_large_image',\n  },\n} as const;\n\n// Animation Configuration\nexport const ANIMATION_CONFIG = {\n  duration: {\n    fast: 0.2,\n    normal: 0.4,\n    slow: 0.6,\n    verySlow: 1.0\n  },\n  easing: {\n    easeInOut: [0.4, 0, 0.2, 1],\n    easeOut: [0, 0, 0.2, 1],\n    easeIn: [0.4, 0, 1, 1]\n  }\n} as const;\n\n// Development Configuration\nexport const DEV_CONFIG = {\n  isDevelopment: process.env.NODE_ENV === 'development',\n  isProduction: process.env.NODE_ENV === 'production',\n  enableDebugLogs: process.env.NODE_ENV === 'development',\n  enableAnalytics: process.env.NODE_ENV === 'production',\n} as const;\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GAED,gBAAgB;;;;;;;;;;;;;AACT,MAAM,cAAc;IACzB,MAAM;IACN,SAAS;IACT,aAAa;IACb,KAAK,QAAQ,GAAG,CAAC,oBAAoB,IAAI;IACzC,QAAQ;AACV;AAGO,MAAM,eAAe;IAC1B,OAAO;IACP,OAAO;IACP,SAAS;QACP,QAAQ;QACR,MAAM;QACN,OAAO;QACP,KAAK;QACL,SAAS;IACX;AACF;AAGO,MAAM,eAAe;IAC1B,SAAS;IACT,UAAU;IACV,QAAQ;IACR,UAAU;IACV,WAAW;AACb;AAGO,MAAM,aAAa;IACxB,MAAM;QACJ;YAAE,OAAO;YAAY,MAAM;YAAsB,IAAI;QAAoB;QACzE;YAAE,OAAO;YAAgB,MAAM;YAAY,IAAI;QAAU;QACzD;YAAE,OAAO;YAAY,MAAM;YAAU,IAAI;QAAQ;KAClD;IACD,QAAQ;QACN,SAAS;YACP;gBAAE,OAAO;gBAAS,MAAM;YAAS;YACjC;gBAAE,OAAO;gBAAY,MAAM;YAAY;YACvC;gBAAE,OAAO;gBAAQ,MAAM;YAAQ;YAC/B;gBAAE,OAAO;gBAAO,MAAM;YAAO;SAC9B;QACD,OAAO;YACL;gBAAE,OAAO;gBAAkB,MAAM;YAAW;YAC5C;gBAAE,OAAO;gBAAoB,MAAM;YAAS;YAC5C;gBAAE,OAAO;gBAAiB,MAAM;YAAW;SAC5C;QACD,SAAS;YACP;gBAAE,OAAO;gBAAc,MAAM;YAAW;YACxC;gBAAE,OAAO;gBAAe,MAAM;YAAQ;YACtC;gBAAE,OAAO;gBAAiB,MAAM;YAAQ;SACzC;IACH;AACF;AAGO,MAAM,mBAAmB;IAC9B,MAAM;QACJ,UAAU;QACV,SAAS;QACT,YAAY;IACd;IACA,SAAS;QACP,UAAU;QACV,SAAS;QACT,YAAY;QACZ,gBAAgB;QAChB,cAAc;IAChB;IACA,UAAU;QACR,UAAU;QACV,SAAS;IACX;IACA,SAAS;QACP,UAAU;QACV,SAAS;IACX;IACA,OAAO;QACL,UAAU;QACV,SAAS;IACX;AACF;AAGO,MAAM,WAAW;IACtB,SAAS;QACP,MAAM;QACN,aAAa;QACb,OAAO;QACP,UAAU;YAAC;YAAsB;YAAiB;SAAiB;QACnE,SAAS;IACX;IACA,QAAQ;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,UAAU;YAAC;YAAe;YAAgB;SAAoB;QAC9D,SAAS;IACX;IACA,YAAY;QACV,MAAM;QACN,aAAa;QACb,OAAO;QACP,UAAU;YAAC;YAAoB;YAAyB;SAAkB;QAC1E,SAAS;IACX;AACF;AAGO,MAAM,oBAAoB;IAC/B,WAAW;QACT,eAAe;IACjB;IACA,OAAO;QACL,SAAS;IACX;IACA,MAAM;QACJ,OAAO,QAAQ,GAAG,CAAC,4BAA4B;QAC/C,QAAQ,QAAQ,GAAG,CAAC,+BAA+B;IACrD;IACA,YAAY;QACV,WAAW,QAAQ,GAAG,CAAC,6BAA6B;QACpD,YAAY,QAAQ,GAAG,CAAC,kBAAkB;IAC5C;AACF;AAGO,MAAM,aAAa;IACxB,cAAc,YAAY,IAAI,GAAG,QAAQ,YAAY,OAAO;IAC5D,eAAe,UAAU,YAAY,IAAI,GAAG;IAC5C,aAAa,YAAY,WAAW;IACpC,UAAU;QACR;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,WAAW;QACT,MAAM;QACN,QAAQ;QACR,KAAK,YAAY,GAAG;QACpB,UAAU,YAAY,IAAI;QAC1B,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK,YAAY,IAAI,GAAG,QAAQ,YAAY,OAAO;YACrD;SACD;IACH;IACA,SAAS;QACP,QAAQ;QACR,MAAM;QACN,UAAU;IACZ;AACF;AAGO,MAAM,mBAAmB;IAC9B,UAAU;QACR,MAAM;QACN,QAAQ;QACR,MAAM;QACN,UAAU;IACZ;IACA,QAAQ;QACN,WAAW;YAAC;YAAK;YAAG;YAAK;SAAE;QAC3B,SAAS;YAAC;YAAG;YAAG;YAAK;SAAE;QACvB,QAAQ;YAAC;YAAK;YAAG;YAAG;SAAE;IACxB;AACF;AAGO,MAAM,aAAa;IACxB,eAAe,oDAAyB;IACxC,cAAc,oDAAyB;IACvC,iBAAiB,oDAAyB;IAC1C,iBAAiB,oDAAyB;AAC5C", "debugId": null}}, {"offset": {"line": 408, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/hooks/useAnalytics.ts"], "sourcesContent": ["/**\n * Analytics Hook\n * \n * Unified interface for all analytics tracking across the site.\n * Abstracts away the implementation details of Google Analytics.\n */\n\nimport { useCallback } from 'react';\nimport { DEV_CONFIG } from '@/config/site';\n\n// Analytics event types\nexport interface AnalyticsEvent {\n  name: string;\n  category?: string;\n  label?: string;\n  value?: number;\n  custom_parameters?: Record<string, any>;\n}\n\n// Predefined event types for type safety\nexport const ANALYTICS_EVENTS = {\n  // Navigation & CTA Events\n  HERO_CTA_CLICK: 'hero_cta_click',\n  CONTACT_CTA_CLICK: 'contact_cta_click',\n  SERVICES_CTA_CLICK: 'services_cta_click',\n  \n  // Demo Interaction Events\n  DEMO_INTERACTION: 'demo_interaction',\n  DEMO_TAB_SWITCH: 'demo_tab_switch',\n  DEMO_ANIMATION_COMPLETE: 'demo_animation_complete',\n  DEMO_PREVIEW_CLICK: 'demo_preview_click',\n  \n  // Form Events\n  FORM_SUBMIT: 'form_submit',\n  FORM_SUCCESS: 'form_success',\n  FORM_ERROR: 'form_error',\n  FORM_FIELD_FOCUS: 'form_field_focus',\n  \n  // Content Interaction Events\n  BLOG_POST_CLICK: 'blog_post_click',\n  FAQ_ITEM_EXPAND: 'faq_item_expand',\n  EXTERNAL_LINK_CLICK: 'external_link_click',\n  \n  // Chat & Support Events\n  CHAT_TRIGGER_CLICK: 'chat_trigger_click',\n  PHONE_CLICK: 'phone_click',\n  EMAIL_CLICK: 'email_click',\n  \n  // Page Events\n  PAGE_VIEW: 'page_view',\n  SCROLL_DEPTH: 'scroll_depth',\n  TIME_ON_PAGE: 'time_on_page',\n} as const;\n\n// Hook implementation\nexport function useAnalytics() {\n  const trackEvent = useCallback((\n    eventName: string,\n    eventData?: Partial<AnalyticsEvent>\n  ) => {\n    // Only track in production or when explicitly enabled\n    if (!DEV_CONFIG.enableAnalytics && !DEV_CONFIG.isDevelopment) {\n      return;\n    }\n\n    // Log in development for debugging\n    if (DEV_CONFIG.isDevelopment && DEV_CONFIG.enableDebugLogs) {\n      console.log('📊 Analytics Event:', {\n        event: eventName,\n        ...eventData\n      });\n    }\n\n    // Send to Google Analytics if available\n    if (typeof window !== 'undefined' && (window as any).gtag) {\n      try {\n        (window as any).gtag('event', eventName, {\n          event_category: eventData?.category || 'engagement',\n          event_label: eventData?.label,\n          value: eventData?.value,\n          ...eventData?.custom_parameters\n        });\n      } catch (error) {\n        console.warn('Failed to send analytics event:', error);\n      }\n    }\n  }, []);\n\n  // Convenience methods for common events\n  const trackNavigation = useCallback((destination: string, source?: string) => {\n    trackEvent(ANALYTICS_EVENTS.HERO_CTA_CLICK, {\n      category: 'navigation',\n      label: destination,\n      custom_parameters: { source }\n    });\n  }, [trackEvent]);\n\n  const trackFormInteraction = useCallback((\n    action: 'submit' | 'success' | 'error' | 'field_focus',\n    formName: string,\n    fieldName?: string\n  ) => {\n    const eventMap = {\n      submit: ANALYTICS_EVENTS.FORM_SUBMIT,\n      success: ANALYTICS_EVENTS.FORM_SUCCESS,\n      error: ANALYTICS_EVENTS.FORM_ERROR,\n      field_focus: ANALYTICS_EVENTS.FORM_FIELD_FOCUS\n    };\n\n    trackEvent(eventMap[action], {\n      category: 'form_interaction',\n      label: formName,\n      custom_parameters: { field_name: fieldName }\n    });\n  }, [trackEvent]);\n\n  const trackDemoInteraction = useCallback((\n    action: 'tab_switch' | 'preview_click' | 'animation_complete',\n    details?: string\n  ) => {\n    const eventMap = {\n      tab_switch: ANALYTICS_EVENTS.DEMO_TAB_SWITCH,\n      preview_click: ANALYTICS_EVENTS.DEMO_PREVIEW_CLICK,\n      animation_complete: ANALYTICS_EVENTS.DEMO_ANIMATION_COMPLETE\n    };\n\n    trackEvent(eventMap[action], {\n      category: 'demo_interaction',\n      label: details\n    });\n  }, [trackEvent]);\n\n  const trackContentInteraction = useCallback((\n    contentType: 'blog_post' | 'faq_item' | 'external_link',\n    contentId: string,\n    action?: string\n  ) => {\n    const eventMap = {\n      blog_post: ANALYTICS_EVENTS.BLOG_POST_CLICK,\n      faq_item: ANALYTICS_EVENTS.FAQ_ITEM_EXPAND,\n      external_link: ANALYTICS_EVENTS.EXTERNAL_LINK_CLICK\n    };\n\n    trackEvent(eventMap[contentType], {\n      category: 'content_interaction',\n      label: contentId,\n      custom_parameters: { action }\n    });\n  }, [trackEvent]);\n\n  const trackSupportInteraction = useCallback((\n    method: 'chat' | 'phone' | 'email',\n    source?: string\n  ) => {\n    const eventMap = {\n      chat: ANALYTICS_EVENTS.CHAT_TRIGGER_CLICK,\n      phone: ANALYTICS_EVENTS.PHONE_CLICK,\n      email: ANALYTICS_EVENTS.EMAIL_CLICK\n    };\n\n    trackEvent(eventMap[method], {\n      category: 'support_interaction',\n      label: method,\n      custom_parameters: { source }\n    });\n  }, [trackEvent]);\n\n  const trackPageView = useCallback((pagePath: string, pageTitle?: string) => {\n    trackEvent(ANALYTICS_EVENTS.PAGE_VIEW, {\n      category: 'page_interaction',\n      label: pagePath,\n      custom_parameters: {\n        page_title: pageTitle,\n        page_path: pagePath\n      }\n    });\n  }, [trackEvent]);\n\n  const trackFormSubmission = useCallback((formName: string, success: boolean, metadata?: Record<string, unknown>) => {\n    trackEvent(ANALYTICS_EVENTS.FORM_SUBMIT, {\n      category: 'form',\n      label: formName,\n      custom_parameters: {\n        success,\n        ...metadata\n      }\n    });\n  }, [trackEvent]);\n\n  return {\n    // Core tracking function\n    trackEvent,\n\n    // Convenience methods\n    trackNavigation,\n    trackFormInteraction,\n    trackFormSubmission,\n    trackDemoInteraction,\n    trackContentInteraction,\n    trackSupportInteraction,\n    trackPageView,\n\n    // Event constants for external use\n    EVENTS: ANALYTICS_EVENTS\n  };\n}\n\n\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;AAED;AACA;;;AAYO,MAAM,mBAAmB;IAC9B,0BAA0B;IAC1B,gBAAgB;IAChB,mBAAmB;IACnB,oBAAoB;IAEpB,0BAA0B;IAC1B,kBAAkB;IAClB,iBAAiB;IACjB,yBAAyB;IACzB,oBAAoB;IAEpB,cAAc;IACd,aAAa;IACb,cAAc;IACd,YAAY;IACZ,kBAAkB;IAElB,6BAA6B;IAC7B,iBAAiB;IACjB,iBAAiB;IACjB,qBAAqB;IAErB,wBAAwB;IACxB,oBAAoB;IACpB,aAAa;IACb,aAAa;IAEb,cAAc;IACd,WAAW;IACX,cAAc;IACd,cAAc;AAChB;AAGO,SAAS;IACd,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAC7B,WACA;QAEA,sDAAsD;QACtD,IAAI,CAAC,qHAAA,CAAA,aAAU,CAAC,eAAe,IAAI,CAAC,qHAAA,CAAA,aAAU,CAAC,aAAa,EAAE;YAC5D;QACF;QAEA,mCAAmC;QACnC,IAAI,qHAAA,CAAA,aAAU,CAAC,aAAa,IAAI,qHAAA,CAAA,aAAU,CAAC,eAAe,EAAE;YAC1D,QAAQ,GAAG,CAAC,uBAAuB;gBACjC,OAAO;gBACP,GAAG,SAAS;YACd;QACF;QAEA,wCAAwC;QACxC,uCAA2D;;QAW3D;IACF,GAAG,EAAE;IAEL,wCAAwC;IACxC,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,aAAqB;QACxD,WAAW,iBAAiB,cAAc,EAAE;YAC1C,UAAU;YACV,OAAO;YACP,mBAAmB;gBAAE;YAAO;QAC9B;IACF,GAAG;QAAC;KAAW;IAEf,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CACvC,QACA,UACA;QAEA,MAAM,WAAW;YACf,QAAQ,iBAAiB,WAAW;YACpC,SAAS,iBAAiB,YAAY;YACtC,OAAO,iBAAiB,UAAU;YAClC,aAAa,iBAAiB,gBAAgB;QAChD;QAEA,WAAW,QAAQ,CAAC,OAAO,EAAE;YAC3B,UAAU;YACV,OAAO;YACP,mBAAmB;gBAAE,YAAY;YAAU;QAC7C;IACF,GAAG;QAAC;KAAW;IAEf,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CACvC,QACA;QAEA,MAAM,WAAW;YACf,YAAY,iBAAiB,eAAe;YAC5C,eAAe,iBAAiB,kBAAkB;YAClD,oBAAoB,iBAAiB,uBAAuB;QAC9D;QAEA,WAAW,QAAQ,CAAC,OAAO,EAAE;YAC3B,UAAU;YACV,OAAO;QACT;IACF,GAAG;QAAC;KAAW;IAEf,MAAM,0BAA0B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAC1C,aACA,WACA;QAEA,MAAM,WAAW;YACf,WAAW,iBAAiB,eAAe;YAC3C,UAAU,iBAAiB,eAAe;YAC1C,eAAe,iBAAiB,mBAAmB;QACrD;QAEA,WAAW,QAAQ,CAAC,YAAY,EAAE;YAChC,UAAU;YACV,OAAO;YACP,mBAAmB;gBAAE;YAAO;QAC9B;IACF,GAAG;QAAC;KAAW;IAEf,MAAM,0BAA0B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAC1C,QACA;QAEA,MAAM,WAAW;YACf,MAAM,iBAAiB,kBAAkB;YACzC,OAAO,iBAAiB,WAAW;YACnC,OAAO,iBAAiB,WAAW;QACrC;QAEA,WAAW,QAAQ,CAAC,OAAO,EAAE;YAC3B,UAAU;YACV,OAAO;YACP,mBAAmB;gBAAE;YAAO;QAC9B;IACF,GAAG;QAAC;KAAW;IAEf,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,UAAkB;QACnD,WAAW,iBAAiB,SAAS,EAAE;YACrC,UAAU;YACV,OAAO;YACP,mBAAmB;gBACjB,YAAY;gBACZ,WAAW;YACb;QACF;IACF,GAAG;QAAC;KAAW;IAEf,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,UAAkB,SAAkB;QAC3E,WAAW,iBAAiB,WAAW,EAAE;YACvC,UAAU;YACV,OAAO;YACP,mBAAmB;gBACjB;gBACA,GAAG,QAAQ;YACb;QACF;IACF,GAAG;QAAC;KAAW;IAEf,OAAO;QACL,yBAAyB;QACzB;QAEA,sBAAsB;QACtB;QACA;QACA;QACA;QACA;QACA;QACA;QAEA,mCAAmC;QACnC,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 586, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/hooks/useContactForm.ts"], "sourcesContent": ["/**\n * Contact Form Hook\n * \n * Handles form state management, validation, and submission logic\n * for the contact form component.\n */\n\nimport { useState, useCallback } from 'react';\nimport { EXTERNAL_SERVICES } from '@/config/site';\nimport { useAnalytics } from './useAnalytics';\n\n// Form data interface\nexport interface ContactFormData {\n  name: string;\n  email: string;\n  company: string;\n  projectType: string;\n  message: string;\n}\n\n// Form validation errors\nexport interface FormErrors {\n  name?: string;\n  email?: string;\n  company?: string;\n  projectType?: string;\n  message?: string;\n  general?: string;\n}\n\n// Form state\nexport interface FormState {\n  data: ContactFormData;\n  errors: FormErrors;\n  isSubmitting: boolean;\n  isSubmitted: boolean;\n  submitSuccess: boolean;\n}\n\n// Initial form data\nconst initialFormData: ContactFormData = {\n  name: '',\n  email: '',\n  company: '',\n  projectType: '',\n  message: ''\n};\n\n// Initial form state\nconst initialFormState: FormState = {\n  data: initialFormData,\n  errors: {},\n  isSubmitting: false,\n  isSubmitted: false,\n  submitSuccess: false\n};\n\n// Validation rules\nconst validateField = (name: keyof ContactFormData, value: string): string | undefined => {\n  switch (name) {\n    case 'name':\n      if (!value.trim()) return 'Name is required';\n      if (value.trim().length < 2) return 'Name must be at least 2 characters';\n      return undefined;\n      \n    case 'email':\n      if (!value.trim()) return 'Email is required';\n      const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n      if (!emailRegex.test(value)) return 'Please enter a valid email address';\n      return undefined;\n      \n    case 'company':\n      // Company is optional, but if provided should be reasonable length\n      if (value.trim() && value.trim().length < 2) return 'Company name must be at least 2 characters';\n      return undefined;\n      \n    case 'projectType':\n      if (!value.trim()) return 'Please select a project type';\n      return undefined;\n      \n    case 'message':\n      if (!value.trim()) return 'Message is required';\n      if (value.trim().length < 10) return 'Message must be at least 10 characters';\n      if (value.trim().length > 1000) return 'Message must be less than 1000 characters';\n      return undefined;\n      \n    default:\n      return undefined;\n  }\n};\n\n// Validate entire form\nconst validateForm = (data: ContactFormData): FormErrors => {\n  const errors: FormErrors = {};\n  \n  (Object.keys(data) as Array<keyof ContactFormData>).forEach(field => {\n    const error = validateField(field, data[field]);\n    if (error) {\n      errors[field] = error;\n    }\n  });\n  \n  return errors;\n};\n\n// Hook implementation\nexport function useContactForm() {\n  const [formState, setFormState] = useState<FormState>(initialFormState);\n  const { trackFormInteraction } = useAnalytics();\n\n  // Update form data\n  const updateField = useCallback((field: keyof ContactFormData, value: string) => {\n    setFormState(prev => ({\n      ...prev,\n      data: {\n        ...prev.data,\n        [field]: value\n      },\n      // Clear field error when user starts typing\n      errors: {\n        ...prev.errors,\n        [field]: undefined,\n        general: undefined\n      }\n    }));\n  }, []);\n\n  // Handle field focus for analytics\n  const handleFieldFocus = useCallback((field: keyof ContactFormData) => {\n    trackFormInteraction('field_focus', 'contact_form', field);\n  }, [trackFormInteraction]);\n\n  // Validate single field\n  const validateSingleField = useCallback((field: keyof ContactFormData) => {\n    const error = validateField(field, formState.data[field]);\n    setFormState(prev => ({\n      ...prev,\n      errors: {\n        ...prev.errors,\n        [field]: error\n      }\n    }));\n    return !error;\n  }, [formState.data]);\n\n  // Reset form\n  const resetForm = useCallback(() => {\n    setFormState(initialFormState);\n  }, []);\n\n  // Submit form\n  const submitForm = useCallback(async () => {\n    // Validate form\n    const errors = validateForm(formState.data);\n    \n    if (Object.keys(errors).length > 0) {\n      setFormState(prev => ({\n        ...prev,\n        errors\n      }));\n      return false;\n    }\n\n    // Check if Web3Forms is configured\n    if (!EXTERNAL_SERVICES.forms.web3forms) {\n      setFormState(prev => ({\n        ...prev,\n        errors: {\n          general: 'Form service is not configured. Please contact us directly.'\n        }\n      }));\n      trackFormInteraction('error', 'contact_form');\n      return false;\n    }\n\n    setFormState(prev => ({\n      ...prev,\n      isSubmitting: true,\n      errors: {}\n    }));\n\n    trackFormInteraction('submit', 'contact_form');\n\n    try {\n      const formData = new FormData();\n      formData.append('access_key', EXTERNAL_SERVICES.forms.web3forms);\n      formData.append('name', formState.data.name);\n      formData.append('email', formState.data.email);\n      formData.append('company', formState.data.company);\n      formData.append('project_type', formState.data.projectType);\n      formData.append('message', formState.data.message);\n      formData.append('from_name', 'Mobilify Contact Form');\n      formData.append('subject', `New Contact Form Submission from ${formState.data.name}`);\n\n      const response = await fetch('https://api.web3forms.com/submit', {\n        method: 'POST',\n        body: formData\n      });\n\n      const result = await response.json();\n\n      if (result.success) {\n        setFormState(prev => ({\n          ...prev,\n          isSubmitting: false,\n          isSubmitted: true,\n          submitSuccess: true\n        }));\n        trackFormInteraction('success', 'contact_form');\n        return true;\n      } else {\n        throw new Error(result.message || 'Form submission failed');\n      }\n    } catch (error) {\n      console.error('Form submission error:', error);\n      setFormState(prev => ({\n        ...prev,\n        isSubmitting: false,\n        isSubmitted: true,\n        submitSuccess: false,\n        errors: {\n          general: 'There was an error sending your message. Please try again or contact us directly.'\n        }\n      }));\n      trackFormInteraction('error', 'contact_form');\n      return false;\n    }\n  }, [formState.data, trackFormInteraction]);\n\n  return {\n    // Form state\n    formData: formState.data,\n    errors: formState.errors,\n    isSubmitting: formState.isSubmitting,\n    isSubmitted: formState.isSubmitted,\n    submitSuccess: formState.submitSuccess,\n    \n    // Form actions\n    updateField,\n    handleFieldFocus,\n    validateSingleField,\n    submitForm,\n    resetForm,\n    \n    // Validation helpers\n    isValid: Object.keys(formState.errors).length === 0,\n    hasErrors: Object.keys(formState.errors).length > 0\n  };\n}\n\n\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;AAED;AACA;AACA;;;;AA8BA,oBAAoB;AACpB,MAAM,kBAAmC;IACvC,MAAM;IACN,OAAO;IACP,SAAS;IACT,aAAa;IACb,SAAS;AACX;AAEA,qBAAqB;AACrB,MAAM,mBAA8B;IAClC,MAAM;IACN,QAAQ,CAAC;IACT,cAAc;IACd,aAAa;IACb,eAAe;AACjB;AAEA,mBAAmB;AACnB,MAAM,gBAAgB,CAAC,MAA6B;IAClD,OAAQ;QACN,KAAK;YACH,IAAI,CAAC,MAAM,IAAI,IAAI,OAAO;YAC1B,IAAI,MAAM,IAAI,GAAG,MAAM,GAAG,GAAG,OAAO;YACpC,OAAO;QAET,KAAK;YACH,IAAI,CAAC,MAAM,IAAI,IAAI,OAAO;YAC1B,MAAM,aAAa;YACnB,IAAI,CAAC,WAAW,IAAI,CAAC,QAAQ,OAAO;YACpC,OAAO;QAET,KAAK;YACH,mEAAmE;YACnE,IAAI,MAAM,IAAI,MAAM,MAAM,IAAI,GAAG,MAAM,GAAG,GAAG,OAAO;YACpD,OAAO;QAET,KAAK;YACH,IAAI,CAAC,MAAM,IAAI,IAAI,OAAO;YAC1B,OAAO;QAET,KAAK;YACH,IAAI,CAAC,MAAM,IAAI,IAAI,OAAO;YAC1B,IAAI,MAAM,IAAI,GAAG,MAAM,GAAG,IAAI,OAAO;YACrC,IAAI,MAAM,IAAI,GAAG,MAAM,GAAG,MAAM,OAAO;YACvC,OAAO;QAET;YACE,OAAO;IACX;AACF;AAEA,uBAAuB;AACvB,MAAM,eAAe,CAAC;IACpB,MAAM,SAAqB,CAAC;IAE3B,OAAO,IAAI,CAAC,MAAuC,OAAO,CAAC,CAAA;QAC1D,MAAM,QAAQ,cAAc,OAAO,IAAI,CAAC,MAAM;QAC9C,IAAI,OAAO;YACT,MAAM,CAAC,MAAM,GAAG;QAClB;IACF;IAEA,OAAO;AACT;AAGO,SAAS;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;IACtD,MAAM,EAAE,oBAAoB,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAE5C,mBAAmB;IACnB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAA8B;QAC7D,aAAa,CAAA,OAAQ,CAAC;gBACpB,GAAG,IAAI;gBACP,MAAM;oBACJ,GAAG,KAAK,IAAI;oBACZ,CAAC,MAAM,EAAE;gBACX;gBACA,4CAA4C;gBAC5C,QAAQ;oBACN,GAAG,KAAK,MAAM;oBACd,CAAC,MAAM,EAAE;oBACT,SAAS;gBACX;YACF,CAAC;IACH,GAAG,EAAE;IAEL,mCAAmC;IACnC,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,qBAAqB,eAAe,gBAAgB;IACtD,GAAG;QAAC;KAAqB;IAEzB,wBAAwB;IACxB,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACvC,MAAM,QAAQ,cAAc,OAAO,UAAU,IAAI,CAAC,MAAM;QACxD,aAAa,CAAA,OAAQ,CAAC;gBACpB,GAAG,IAAI;gBACP,QAAQ;oBACN,GAAG,KAAK,MAAM;oBACd,CAAC,MAAM,EAAE;gBACX;YACF,CAAC;QACD,OAAO,CAAC;IACV,GAAG;QAAC,UAAU,IAAI;KAAC;IAEnB,aAAa;IACb,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC5B,aAAa;IACf,GAAG,EAAE;IAEL,cAAc;IACd,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,gBAAgB;QAChB,MAAM,SAAS,aAAa,UAAU,IAAI;QAE1C,IAAI,OAAO,IAAI,CAAC,QAAQ,MAAM,GAAG,GAAG;YAClC,aAAa,CAAA,OAAQ,CAAC;oBACpB,GAAG,IAAI;oBACP;gBACF,CAAC;YACD,OAAO;QACT;QAEA,mCAAmC;QACnC,IAAI,CAAC,qHAAA,CAAA,oBAAiB,CAAC,KAAK,CAAC,SAAS,EAAE;YACtC,aAAa,CAAA,OAAQ,CAAC;oBACpB,GAAG,IAAI;oBACP,QAAQ;wBACN,SAAS;oBACX;gBACF,CAAC;YACD,qBAAqB,SAAS;YAC9B,OAAO;QACT;QAEA,aAAa,CAAA,OAAQ,CAAC;gBACpB,GAAG,IAAI;gBACP,cAAc;gBACd,QAAQ,CAAC;YACX,CAAC;QAED,qBAAqB,UAAU;QAE/B,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,cAAc,qHAAA,CAAA,oBAAiB,CAAC,KAAK,CAAC,SAAS;YAC/D,SAAS,MAAM,CAAC,QAAQ,UAAU,IAAI,CAAC,IAAI;YAC3C,SAAS,MAAM,CAAC,SAAS,UAAU,IAAI,CAAC,KAAK;YAC7C,SAAS,MAAM,CAAC,WAAW,UAAU,IAAI,CAAC,OAAO;YACjD,SAAS,MAAM,CAAC,gBAAgB,UAAU,IAAI,CAAC,WAAW;YAC1D,SAAS,MAAM,CAAC,WAAW,UAAU,IAAI,CAAC,OAAO;YACjD,SAAS,MAAM,CAAC,aAAa;YAC7B,SAAS,MAAM,CAAC,WAAW,CAAC,iCAAiC,EAAE,UAAU,IAAI,CAAC,IAAI,EAAE;YAEpF,MAAM,WAAW,MAAM,MAAM,oCAAoC;gBAC/D,QAAQ;gBACR,MAAM;YACR;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,aAAa,CAAA,OAAQ,CAAC;wBACpB,GAAG,IAAI;wBACP,cAAc;wBACd,aAAa;wBACb,eAAe;oBACjB,CAAC;gBACD,qBAAqB,WAAW;gBAChC,OAAO;YACT,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,aAAa,CAAA,OAAQ,CAAC;oBACpB,GAAG,IAAI;oBACP,cAAc;oBACd,aAAa;oBACb,eAAe;oBACf,QAAQ;wBACN,SAAS;oBACX;gBACF,CAAC;YACD,qBAAqB,SAAS;YAC9B,OAAO;QACT;IACF,GAAG;QAAC,UAAU,IAAI;QAAE;KAAqB;IAEzC,OAAO;QACL,aAAa;QACb,UAAU,UAAU,IAAI;QACxB,QAAQ,UAAU,MAAM;QACxB,cAAc,UAAU,YAAY;QACpC,aAAa,UAAU,WAAW;QAClC,eAAe,UAAU,aAAa;QAEtC,eAAe;QACf;QACA;QACA;QACA;QACA;QAEA,qBAAqB;QACrB,SAAS,OAAO,IAAI,CAAC,UAAU,MAAM,EAAE,MAAM,KAAK;QAClD,WAAW,OAAO,IAAI,CAAC,UAAU,MAAM,EAAE,MAAM,GAAG;IACpD;AACF", "debugId": null}}, {"offset": {"line": 915, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/lib/sanity.ts"], "sourcesContent": ["import { createClient } from '@sanity/client';\nimport imageUrlBuilder from '@sanity/image-url';\n\n// Check if Sanity is configured\nconst isConfigured = !!(\n  process.env.NEXT_PUBLIC_SANITY_PROJECT_ID &&\n  process.env.NEXT_PUBLIC_SANITY_DATASET\n);\n\n// Sanity client configuration (only if configured)\nexport const client = isConfigured ? createClient({\n  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID!,\n  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',\n  apiVersion: '2024-01-01',\n  useCdn: process.env.NODE_ENV === 'production',\n  token: process.env.SANITY_API_TOKEN,\n}) : null;\n\n// Image URL builder (only if configured)\nconst builder = isConfigured && client ? imageUrlBuilder(client) : null;\n\nexport function urlFor(source: any) {\n  if (!builder) {\n    console.warn('Sanity not configured, returning placeholder image URL');\n    return { url: () => '/placeholder-image.jpg' };\n  }\n  return builder.image(source);\n}\n\n// Type definitions for Sanity documents\nexport interface SanityImage {\n  _type: 'image';\n  asset: {\n    _ref: string;\n    _type: 'reference';\n  };\n  alt?: string;\n}\n\nexport interface Category {\n  _id: string;\n  _type: 'category';\n  title: string;\n  slug: {\n    current: string;\n  };\n  description?: string;\n}\n\nexport interface BlogPost {\n  _id: string;\n  _type: 'post';\n  title: string;\n  slug: {\n    current: string;\n  };\n  author: string;\n  mainImage?: SanityImage;\n  categories: Category[];\n  publishedAt: string;\n  excerpt?: string;\n  body: any[]; // Portable Text\n  _createdAt: string;\n  _updatedAt: string;\n}\n\nexport interface FAQTopic {\n  _id: string;\n  _type: 'faqTopic';\n  title: string;\n  slug: {\n    current: string;\n  };\n  description?: string;\n}\n\nexport interface SiteSettings {\n  _id: string;\n  _type: 'siteSettings';\n  // Hero Section\n  heroHeadline?: string;\n  heroSubtext?: string;\n  heroButtonText?: string;\n  // Contact Section\n  contactHeadline?: string;\n  contactSubtext?: string;\n  contactButtonText?: string;\n  // Form Messages\n  formSuccessMessage?: string;\n  formErrorMessage?: string;\n  // Services Section\n  servicesHeadline?: string;\n  servicesSubtext?: string;\n  // Process Section\n  processHeadline?: string;\n  processSubtext?: string;\n  // About Section\n  aboutHeadline?: string;\n  aboutSubtext?: string;\n  // Footer\n  footerTagline?: string;\n  footerCopyright?: string;\n}\n\nexport interface FAQItem {\n  _id: string;\n  _type: 'faqItem';\n  question: string;\n  answer: any[]; // Portable Text\n  topic: FAQTopic;\n  relatedPosts?: BlogPost[];\n  _createdAt: string;\n  _updatedAt: string;\n}\n\n// GROQ queries\nexport const POSTS_QUERY = `*[_type == \"post\" && defined(slug.current)] | order(publishedAt desc) {\n  _id,\n  title,\n  slug,\n  author,\n  mainImage,\n  categories[]-> {\n    _id,\n    title,\n    slug\n  },\n  publishedAt,\n  excerpt,\n  _createdAt\n}`;\n\nexport const POST_QUERY = `*[_type == \"post\" && slug.current == $slug][0] {\n  _id,\n  title,\n  slug,\n  author,\n  mainImage,\n  categories[]-> {\n    _id,\n    title,\n    slug,\n    description\n  },\n  publishedAt,\n  excerpt,\n  body,\n  _createdAt,\n  _updatedAt\n}`;\n\nexport const CATEGORIES_QUERY = `*[_type == \"category\"] | order(title asc) {\n  _id,\n  title,\n  slug,\n  description\n}`;\n\nexport const POSTS_BY_CATEGORY_QUERY = `*[_type == \"post\" && $categoryId in categories[]._ref] | order(publishedAt desc) {\n  _id,\n  title,\n  slug,\n  author,\n  mainImage,\n  categories[]-> {\n    _id,\n    title,\n    slug\n  },\n  publishedAt,\n  excerpt,\n  _createdAt\n}`;\n\nexport const FAQ_ITEMS_QUERY = `*[_type == \"faqItem\"] | order(topic->title asc, _createdAt asc) {\n  _id,\n  question,\n  answer,\n  topic-> {\n    _id,\n    title,\n    slug,\n    description\n  },\n  relatedPosts[]-> {\n    _id,\n    title,\n    slug\n  },\n  _createdAt\n}`;\n\nexport const FAQ_TOPICS_QUERY = `*[_type == \"faqTopic\"] | order(title asc) {\n  _id,\n  title,\n  slug,\n  description\n}`;\n\nexport const RELATED_POSTS_QUERY = `*[_type == \"post\" && _id != $postId && count(categories[@._ref in $categoryIds]) > 0] | order(publishedAt desc)[0...3] {\n  _id,\n  title,\n  slug,\n  mainImage,\n  publishedAt,\n  excerpt\n}`;\n\n// Site Settings Query (Singleton)\nexport const SITE_SETTINGS_QUERY = `*[_type == \"siteSettings\"][0] {\n  _id,\n  heroHeadline,\n  heroSubtext,\n  heroButtonText,\n  contactHeadline,\n  contactSubtext,\n  contactButtonText,\n  formSuccessMessage,\n  formErrorMessage,\n  servicesHeadline,\n  servicesSubtext,\n  processHeadline,\n  processSubtext,\n  aboutHeadline,\n  aboutSubtext,\n  footerTagline,\n  footerCopyright\n}`;\n\n// Utility functions\nexport async function getAllPosts(): Promise<BlogPost[]> {\n  if (!client) {\n    console.warn('Sanity not configured, returning empty posts array');\n    return [];\n  }\n  return await client.fetch(POSTS_QUERY);\n}\n\nexport async function getPostBySlug(slug: string): Promise<BlogPost | null> {\n  if (!client) {\n    console.warn('Sanity not configured, returning null for post');\n    return null;\n  }\n  return await client.fetch(POST_QUERY, { slug });\n}\n\nexport async function getAllCategories(): Promise<Category[]> {\n  if (!client) {\n    console.warn('Sanity not configured, returning empty categories array');\n    return [];\n  }\n  return await client.fetch(CATEGORIES_QUERY);\n}\n\nexport async function getPostsByCategory(categoryId: string): Promise<BlogPost[]> {\n  if (!client) {\n    console.warn('Sanity not configured, returning empty posts array');\n    return [];\n  }\n  return await client.fetch(POSTS_BY_CATEGORY_QUERY, { categoryId });\n}\n\nexport async function getAllFAQItems(): Promise<FAQItem[]> {\n  if (!client) {\n    console.warn('Sanity not configured, returning empty FAQ items array');\n    return [];\n  }\n  return await client.fetch(FAQ_ITEMS_QUERY);\n}\n\nexport async function getAllFAQTopics(): Promise<FAQTopic[]> {\n  if (!client) {\n    console.warn('Sanity not configured, returning empty FAQ topics array');\n    return [];\n  }\n  return await client.fetch(FAQ_TOPICS_QUERY);\n}\n\nexport async function getRelatedPosts(postId: string, categoryIds: string[]): Promise<BlogPost[]> {\n  if (!client) {\n    console.warn('Sanity not configured, returning empty related posts array');\n    return [];\n  }\n  return await client.fetch(RELATED_POSTS_QUERY, { postId, categoryIds });\n}\n\nexport async function getSiteSettings(): Promise<SiteSettings | null> {\n  if (!client) {\n    console.warn('Sanity not configured, returning null for site settings');\n    return null;\n  }\n  try {\n    return await client.fetch(SITE_SETTINGS_QUERY);\n  } catch (error) {\n    console.warn('Failed to fetch site settings from Sanity:', error);\n    return null;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAEA,gCAAgC;AAChC,MAAM,eAAe,CAAC,CAAC,CACrB,QAAQ,GAAG,CAAC,6BAA6B,IACzC,QAAQ,GAAG,CAAC,0BAA0B,AACxC;AAGO,MAAM,SAAS,eAAe,CAAA,GAAA,mKAAA,CAAA,eAAY,AAAD,EAAE;IAChD,WAAW,QAAQ,GAAG,CAAC,6BAA6B;IACpD,SAAS,QAAQ,GAAG,CAAC,0BAA0B,IAAI;IACnD,YAAY;IACZ,QAAQ,oDAAyB;IACjC,OAAO,QAAQ,GAAG,CAAC,gBAAgB;AACrC,KAAK;AAEL,yCAAyC;AACzC,MAAM,UAAU,gBAAgB,SAAS,CAAA,GAAA,gKAAA,CAAA,UAAe,AAAD,EAAE,UAAU;AAE5D,SAAS,OAAO,MAAW;IAChC,IAAI,CAAC,SAAS;QACZ,QAAQ,IAAI,CAAC;QACb,OAAO;YAAE,KAAK,IAAM;QAAyB;IAC/C;IACA,OAAO,QAAQ,KAAK,CAAC;AACvB;AAyFO,MAAM,cAAc,CAAC;;;;;;;;;;;;;;CAc3B,CAAC;AAEK,MAAM,aAAa,CAAC;;;;;;;;;;;;;;;;;CAiB1B,CAAC;AAEK,MAAM,mBAAmB,CAAC;;;;;CAKhC,CAAC;AAEK,MAAM,0BAA0B,CAAC;;;;;;;;;;;;;;CAcvC,CAAC;AAEK,MAAM,kBAAkB,CAAC;;;;;;;;;;;;;;;;CAgB/B,CAAC;AAEK,MAAM,mBAAmB,CAAC;;;;;CAKhC,CAAC;AAEK,MAAM,sBAAsB,CAAC;;;;;;;CAOnC,CAAC;AAGK,MAAM,sBAAsB,CAAC;;;;;;;;;;;;;;;;;;CAkBnC,CAAC;AAGK,eAAe;IACpB,IAAI,CAAC,QAAQ;QACX,QAAQ,IAAI,CAAC;QACb,OAAO,EAAE;IACX;IACA,OAAO,MAAM,OAAO,KAAK,CAAC;AAC5B;AAEO,eAAe,cAAc,IAAY;IAC9C,IAAI,CAAC,QAAQ;QACX,QAAQ,IAAI,CAAC;QACb,OAAO;IACT;IACA,OAAO,MAAM,OAAO,KAAK,CAAC,YAAY;QAAE;IAAK;AAC/C;AAEO,eAAe;IACpB,IAAI,CAAC,QAAQ;QACX,QAAQ,IAAI,CAAC;QACb,OAAO,EAAE;IACX;IACA,OAAO,MAAM,OAAO,KAAK,CAAC;AAC5B;AAEO,eAAe,mBAAmB,UAAkB;IACzD,IAAI,CAAC,QAAQ;QACX,QAAQ,IAAI,CAAC;QACb,OAAO,EAAE;IACX;IACA,OAAO,MAAM,OAAO,KAAK,CAAC,yBAAyB;QAAE;IAAW;AAClE;AAEO,eAAe;IACpB,IAAI,CAAC,QAAQ;QACX,QAAQ,IAAI,CAAC;QACb,OAAO,EAAE;IACX;IACA,OAAO,MAAM,OAAO,KAAK,CAAC;AAC5B;AAEO,eAAe;IACpB,IAAI,CAAC,QAAQ;QACX,QAAQ,IAAI,CAAC;QACb,OAAO,EAAE;IACX;IACA,OAAO,MAAM,OAAO,KAAK,CAAC;AAC5B;AAEO,eAAe,gBAAgB,MAAc,EAAE,WAAqB;IACzE,IAAI,CAAC,QAAQ;QACX,QAAQ,IAAI,CAAC;QACb,OAAO,EAAE;IACX;IACA,OAAO,MAAM,OAAO,KAAK,CAAC,qBAAqB;QAAE;QAAQ;IAAY;AACvE;AAEO,eAAe;IACpB,IAAI,CAAC,QAAQ;QACX,QAAQ,IAAI,CAAC;QACb,OAAO;IACT;IACA,IAAI;QACF,OAAO,MAAM,OAAO,KAAK,CAAC;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,8CAA8C;QAC3D,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 1137, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/hooks/useSiteSettings.ts"], "sourcesContent": ["/**\n * Site Settings Hook\n * \n * Fetches site settings from Sanity CMS with fallback to static configuration.\n * Provides a unified interface for accessing dynamic content across the site.\n */\n\nimport { useState, useEffect } from 'react';\nimport { getSiteSettings, type SiteSettings } from '@/lib/sanity';\nimport { FALLBACK_CONTENT } from '@/config/site';\n\n// Combined settings interface with fallbacks\nexport interface SiteSettingsWithFallbacks {\n  // Hero Section\n  heroHeadline: string;\n  heroSubtext: string;\n  heroButtonText: string;\n  \n  // Contact Section\n  contactHeadline: string;\n  contactSubtext: string;\n  contactButtonText: string;\n  \n  // Form Messages\n  formSuccessMessage: string;\n  formErrorMessage: string;\n  \n  // Services Section\n  servicesHeadline: string;\n  servicesSubtext: string;\n  \n  // Process Section\n  processHeadline: string;\n  processSubtext: string;\n  \n  // About Section\n  aboutHeadline: string;\n  aboutSubtext: string;\n  \n  // Footer\n  footerTagline: string;\n  footerCopyright: string;\n  \n  // Meta information\n  isLoading: boolean;\n  isFromCMS: boolean;\n  error: string | null;\n}\n\n// Create settings with fallbacks\nconst createSettingsWithFallbacks = (\n  cmsSettings: SiteSettings | null,\n  isLoading: boolean = false,\n  error: string | null = null\n): SiteSettingsWithFallbacks => {\n  const currentYear = new Date().getFullYear();\n  \n  return {\n    // Hero Section\n    heroHeadline: cmsSettings?.heroHeadline || FALLBACK_CONTENT.hero.headline,\n    heroSubtext: cmsSettings?.heroSubtext || FALLBACK_CONTENT.hero.subtext,\n    heroButtonText: cmsSettings?.heroButtonText || FALLBACK_CONTENT.hero.buttonText,\n    \n    // Contact Section\n    contactHeadline: cmsSettings?.contactHeadline || FALLBACK_CONTENT.contact.headline,\n    contactSubtext: cmsSettings?.contactSubtext || FALLBACK_CONTENT.contact.subtext,\n    contactButtonText: cmsSettings?.contactButtonText || FALLBACK_CONTENT.contact.buttonText,\n    \n    // Form Messages\n    formSuccessMessage: cmsSettings?.formSuccessMessage || FALLBACK_CONTENT.contact.successMessage,\n    formErrorMessage: cmsSettings?.formErrorMessage || FALLBACK_CONTENT.contact.errorMessage,\n    \n    // Services Section\n    servicesHeadline: cmsSettings?.servicesHeadline || FALLBACK_CONTENT.services.headline,\n    servicesSubtext: cmsSettings?.servicesSubtext || FALLBACK_CONTENT.services.subtext,\n    \n    // Process Section\n    processHeadline: cmsSettings?.processHeadline || FALLBACK_CONTENT.process.headline,\n    processSubtext: cmsSettings?.processSubtext || FALLBACK_CONTENT.process.subtext,\n    \n    // About Section\n    aboutHeadline: cmsSettings?.aboutHeadline || FALLBACK_CONTENT.about.headline,\n    aboutSubtext: cmsSettings?.aboutSubtext || FALLBACK_CONTENT.about.subtext,\n    \n    // Footer\n    footerTagline: cmsSettings?.footerTagline || 'Building the future of mobile apps',\n    footerCopyright: cmsSettings?.footerCopyright || `© ${currentYear} Mobilify. All rights reserved.`,\n    \n    // Meta information\n    isLoading,\n    isFromCMS: !!cmsSettings,\n    error\n  };\n};\n\n// Hook implementation\nexport function useSiteSettings(): SiteSettingsWithFallbacks {\n  const [cmsSettings, setCmsSettings] = useState<SiteSettings | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    let isMounted = true;\n\n    const fetchSettings = async () => {\n      try {\n        setIsLoading(true);\n        setError(null);\n        \n        const settings = await getSiteSettings();\n        \n        if (isMounted) {\n          setCmsSettings(settings);\n          setIsLoading(false);\n        }\n      } catch (err) {\n        if (isMounted) {\n          const errorMessage = err instanceof Error ? err.message : 'Failed to fetch site settings';\n          setError(errorMessage);\n          setIsLoading(false);\n          console.warn('Failed to fetch site settings, using fallbacks:', errorMessage);\n        }\n      }\n    };\n\n    fetchSettings();\n\n    return () => {\n      isMounted = false;\n    };\n  }, []);\n\n  return createSettingsWithFallbacks(cmsSettings, isLoading, error);\n}\n\n// Static version for server-side rendering or when you need immediate access\nexport function getStaticSiteSettings(): SiteSettingsWithFallbacks {\n  return createSettingsWithFallbacks(null, false, null);\n}\n\n// Utility function to get specific section settings\nexport function useSectionSettings(section: 'hero' | 'contact' | 'services' | 'process' | 'about') {\n  const settings = useSiteSettings();\n  \n  switch (section) {\n    case 'hero':\n      return {\n        headline: settings.heroHeadline,\n        subtext: settings.heroSubtext,\n        buttonText: settings.heroButtonText,\n        isLoading: settings.isLoading,\n        isFromCMS: settings.isFromCMS\n      };\n      \n    case 'contact':\n      return {\n        headline: settings.contactHeadline,\n        subtext: settings.contactSubtext,\n        buttonText: settings.contactButtonText,\n        successMessage: settings.formSuccessMessage,\n        errorMessage: settings.formErrorMessage,\n        isLoading: settings.isLoading,\n        isFromCMS: settings.isFromCMS\n      };\n      \n    case 'services':\n      return {\n        headline: settings.servicesHeadline,\n        subtext: settings.servicesSubtext,\n        isLoading: settings.isLoading,\n        isFromCMS: settings.isFromCMS\n      };\n      \n    case 'process':\n      return {\n        headline: settings.processHeadline,\n        subtext: settings.processSubtext,\n        isLoading: settings.isLoading,\n        isFromCMS: settings.isFromCMS\n      };\n      \n    case 'about':\n      return {\n        headline: settings.aboutHeadline,\n        subtext: settings.aboutSubtext,\n        isLoading: settings.isLoading,\n        isFromCMS: settings.isFromCMS\n      };\n      \n    default:\n      return {\n        isLoading: settings.isLoading,\n        isFromCMS: settings.isFromCMS\n      };\n  }\n}\n\n\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;AAED;AACA;AACA;;;;AAwCA,iCAAiC;AACjC,MAAM,8BAA8B,CAClC,aACA,YAAqB,KAAK,EAC1B,QAAuB,IAAI;IAE3B,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,OAAO;QACL,eAAe;QACf,cAAc,aAAa,gBAAgB,qHAAA,CAAA,mBAAgB,CAAC,IAAI,CAAC,QAAQ;QACzE,aAAa,aAAa,eAAe,qHAAA,CAAA,mBAAgB,CAAC,IAAI,CAAC,OAAO;QACtE,gBAAgB,aAAa,kBAAkB,qHAAA,CAAA,mBAAgB,CAAC,IAAI,CAAC,UAAU;QAE/E,kBAAkB;QAClB,iBAAiB,aAAa,mBAAmB,qHAAA,CAAA,mBAAgB,CAAC,OAAO,CAAC,QAAQ;QAClF,gBAAgB,aAAa,kBAAkB,qHAAA,CAAA,mBAAgB,CAAC,OAAO,CAAC,OAAO;QAC/E,mBAAmB,aAAa,qBAAqB,qHAAA,CAAA,mBAAgB,CAAC,OAAO,CAAC,UAAU;QAExF,gBAAgB;QAChB,oBAAoB,aAAa,sBAAsB,qHAAA,CAAA,mBAAgB,CAAC,OAAO,CAAC,cAAc;QAC9F,kBAAkB,aAAa,oBAAoB,qHAAA,CAAA,mBAAgB,CAAC,OAAO,CAAC,YAAY;QAExF,mBAAmB;QACnB,kBAAkB,aAAa,oBAAoB,qHAAA,CAAA,mBAAgB,CAAC,QAAQ,CAAC,QAAQ;QACrF,iBAAiB,aAAa,mBAAmB,qHAAA,CAAA,mBAAgB,CAAC,QAAQ,CAAC,OAAO;QAElF,kBAAkB;QAClB,iBAAiB,aAAa,mBAAmB,qHAAA,CAAA,mBAAgB,CAAC,OAAO,CAAC,QAAQ;QAClF,gBAAgB,aAAa,kBAAkB,qHAAA,CAAA,mBAAgB,CAAC,OAAO,CAAC,OAAO;QAE/E,gBAAgB;QAChB,eAAe,aAAa,iBAAiB,qHAAA,CAAA,mBAAgB,CAAC,KAAK,CAAC,QAAQ;QAC5E,cAAc,aAAa,gBAAgB,qHAAA,CAAA,mBAAgB,CAAC,KAAK,CAAC,OAAO;QAEzE,SAAS;QACT,eAAe,aAAa,iBAAiB;QAC7C,iBAAiB,aAAa,mBAAmB,CAAC,EAAE,EAAE,YAAY,+BAA+B,CAAC;QAElG,mBAAmB;QACnB;QACA,WAAW,CAAC,CAAC;QACb;IACF;AACF;AAGO,SAAS;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IACpE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY;QAEhB,MAAM,gBAAgB;YACpB,IAAI;gBACF,aAAa;gBACb,SAAS;gBAET,MAAM,WAAW,MAAM,CAAA,GAAA,oHAAA,CAAA,kBAAe,AAAD;gBAErC,IAAI,WAAW;oBACb,eAAe;oBACf,aAAa;gBACf;YACF,EAAE,OAAO,KAAK;gBACZ,IAAI,WAAW;oBACb,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;oBAC1D,SAAS;oBACT,aAAa;oBACb,QAAQ,IAAI,CAAC,mDAAmD;gBAClE;YACF;QACF;QAEA;QAEA,OAAO;YACL,YAAY;QACd;IACF,GAAG,EAAE;IAEL,OAAO,4BAA4B,aAAa,WAAW;AAC7D;AAGO,SAAS;IACd,OAAO,4BAA4B,MAAM,OAAO;AAClD;AAGO,SAAS,mBAAmB,OAA8D;IAC/F,MAAM,WAAW;IAEjB,OAAQ;QACN,KAAK;YACH,OAAO;gBACL,UAAU,SAAS,YAAY;gBAC/B,SAAS,SAAS,WAAW;gBAC7B,YAAY,SAAS,cAAc;gBACnC,WAAW,SAAS,SAAS;gBAC7B,WAAW,SAAS,SAAS;YAC/B;QAEF,KAAK;YACH,OAAO;gBACL,UAAU,SAAS,eAAe;gBAClC,SAAS,SAAS,cAAc;gBAChC,YAAY,SAAS,iBAAiB;gBACtC,gBAAgB,SAAS,kBAAkB;gBAC3C,cAAc,SAAS,gBAAgB;gBACvC,WAAW,SAAS,SAAS;gBAC7B,WAAW,SAAS,SAAS;YAC/B;QAEF,KAAK;YACH,OAAO;gBACL,UAAU,SAAS,gBAAgB;gBACnC,SAAS,SAAS,eAAe;gBACjC,WAAW,SAAS,SAAS;gBAC7B,WAAW,SAAS,SAAS;YAC/B;QAEF,KAAK;YACH,OAAO;gBACL,UAAU,SAAS,eAAe;gBAClC,SAAS,SAAS,cAAc;gBAChC,WAAW,SAAS,SAAS;gBAC7B,WAAW,SAAS,SAAS;YAC/B;QAEF,KAAK;YACH,OAAO;gBACL,UAAU,SAAS,aAAa;gBAChC,SAAS,SAAS,YAAY;gBAC9B,WAAW,SAAS,SAAS;gBAC7B,WAAW,SAAS,SAAS;YAC/B;QAEF;YACE,OAAO;gBACL,WAAW,SAAS,SAAS;gBAC7B,WAAW,SAAS,SAAS;YAC/B;IACJ;AACF", "debugId": null}}, {"offset": {"line": 1275, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/hooks/index.ts"], "sourcesContent": ["/**\n * Hooks Index\n * \n * Central export point for all custom hooks.\n */\n\n// Analytics hook\nexport { useAnalytics, ANALYTICS_EVENTS } from './useAnalytics';\nexport type { AnalyticsEvent } from './useAnalytics';\n\n// Contact form hook\nexport { useContactForm } from './useContactForm';\nexport type { ContactFormData, FormErrors, FormState } from './useContactForm';\n\n// Site settings hook\nexport { useSiteSettings, useSectionSettings, getStaticSiteSettings } from './useSiteSettings';\nexport type { SiteSettingsWithFallbacks } from './useSiteSettings';\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED,iBAAiB;;AACjB;AAGA,oBAAoB;AACpB;AAGA,qBAAqB;AACrB", "debugId": null}}, {"offset": {"line": 1306, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/layout/Navigation.tsx"], "sourcesContent": ["/**\n * Navigation Component\n *\n * Handles the main navigation menu for both desktop and mobile views.\n * Supports smooth scrolling to sections and analytics tracking.\n *\n * @component\n * @param {Object} props - Component props\n * @param {string} [props.className] - Additional CSS classes to apply\n *\n * @example\n * ```tsx\n * <Navigation className=\"hidden md:flex space-x-8\" />\n * ```\n *\n * Features:\n * - Smooth scrolling to page sections\n * - Analytics tracking for navigation events\n * - Configurable navigation items from site config\n * - Responsive design support\n */\n\n'use client';\n\nimport React from 'react';\nimport { NAVIGATION } from '@/config/site';\nimport { useAnalytics } from '@/hooks';\n\ninterface NavigationProps {\n  isMobile?: boolean;\n  onItemClick?: () => void;\n  className?: string;\n}\n\nconst Navigation: React.FC<NavigationProps> = ({ \n  isMobile = false, \n  onItemClick,\n  className = ''\n}) => {\n  const { trackNavigation } = useAnalytics();\n\n  const scrollToSection = (sectionId: string, label: string) => {\n    const element = document.getElementById(sectionId);\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' });\n      trackNavigation(label, isMobile ? 'mobile_nav' : 'desktop_nav');\n    }\n    onItemClick?.();\n  };\n\n  const navItems = NAVIGATION.main;\n\n  const baseClasses = isMobile \n    ? \"flex flex-col space-y-4\"\n    : \"hidden md:flex md:items-center md:space-x-8\";\n\n  return (\n    <nav className={`${baseClasses} ${className}`}>\n      {navItems.map((item) => (\n        <button\n          key={item.href}\n          onClick={() => scrollToSection(item.href.replace('#', ''), item.label)}\n          className={`\n            text-gray-700 dark:text-gray-300 hover:text-electric-blue dark:hover:text-electric-blue \n            transition-colors duration-200 font-medium\n            ${isMobile \n              ? 'text-lg py-2 text-left w-full hover:bg-gray-50 dark:hover:bg-gray-800 px-4 rounded-lg' \n              : 'text-sm'\n            }\n          `}\n          aria-label={`Navigate to ${item.label} section`}\n        >\n          {item.label}\n        </button>\n      ))}\n    </nav>\n  );\n};\n\nexport default Navigation;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;CAoBC;;;;AAKD;AACA;AAAA;AAJA;;;;AAYA,MAAM,aAAwC,CAAC,EAC7C,WAAW,KAAK,EAChB,WAAW,EACX,YAAY,EAAE,EACf;IACC,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAEvC,MAAM,kBAAkB,CAAC,WAAmB;QAC1C,MAAM,UAAU,SAAS,cAAc,CAAC;QACxC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;YAC5C,gBAAgB,OAAO,WAAW,eAAe;QACnD;QACA;IACF;IAEA,MAAM,WAAW,qHAAA,CAAA,aAAU,CAAC,IAAI;IAEhC,MAAM,cAAc,WAChB,4BACA;IAEJ,qBACE,8OAAC;QAAI,WAAW,GAAG,YAAY,CAAC,EAAE,WAAW;kBAC1C,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC;gBAEC,SAAS,IAAM,gBAAgB,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,KAAK,KAAK;gBACrE,WAAW,CAAC;;;YAGV,EAAE,WACE,0FACA,UACH;UACH,CAAC;gBACD,cAAY,CAAC,YAAY,EAAE,KAAK,KAAK,CAAC,QAAQ,CAAC;0BAE9C,KAAK,KAAK;eAZN,KAAK,IAAI;;;;;;;;;;AAiBxB;uCAEe", "debugId": null}}, {"offset": {"line": 1380, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/layout/MobileMenu.tsx"], "sourcesContent": ["/**\n * Mobile Menu Component\n *\n * Full-screen mobile navigation menu with slide-in animation from the right.\n * Includes navigation items, chat trigger, dark mode toggle, and close button.\n *\n * @component\n * @param {Object} props - Component props\n * @param {boolean} props.isOpen - Whether the mobile menu is currently open\n * @param {() => void} props.onClose - Callback function to close the menu\n *\n * @example\n * ```tsx\n * <MobileMenu\n *   isOpen={isMenuOpen}\n *   onClose={() => setIsMenuOpen(false)}\n * />\n * ```\n *\n * Features:\n * - Smooth slide-in/out animation using Framer Motion\n * - Full-screen overlay with backdrop blur\n * - Integrated navigation, chat, and dark mode toggle\n * - Keyboard and click-outside support for closing\n * - Mobile-optimized touch interactions\n */\n\n'use client';\n\nimport React from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { X } from 'lucide-react';\nimport Navigation from './Navigation';\nimport SimpleHeaderChat from '../SimpleHeaderChat';\nimport SimpleDarkModeToggle from '../SimpleDarkModeToggle';\nimport { ANIMATION_CONFIG } from '@/config/site';\n\ninterface MobileMenuProps {\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nconst MobileMenu: React.FC<MobileMenuProps> = ({ isOpen, onClose }) => {\n  return (\n    <AnimatePresence>\n      {isOpen && (\n        <>\n          {/* Backdrop */}\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            transition={{ duration: ANIMATION_CONFIG.duration.normal }}\n            className=\"fixed inset-0 bg-black/50 z-40 md:hidden\"\n            onClick={onClose}\n          />\n          \n          {/* Menu Panel */}\n          <motion.div\n            initial={{ x: '100%' }}\n            animate={{ x: 0 }}\n            exit={{ x: '100%' }}\n            transition={{ \n              duration: ANIMATION_CONFIG.duration.normal,\n              ease: ANIMATION_CONFIG.easing.easeInOut\n            }}\n            className=\"fixed top-0 right-0 h-full w-80 max-w-[90vw] bg-white dark:bg-gray-900 shadow-xl z-50 md:hidden\"\n          >\n            <div className=\"flex flex-col h-full\">\n              {/* Header */}\n              <div className=\"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700\">\n                <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                  Menu\n                </h2>\n                <button\n                  onClick={onClose}\n                  className=\"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors\"\n                  aria-label=\"Close menu\"\n                >\n                  <X className=\"h-5 w-5 text-gray-600 dark:text-gray-400\" />\n                </button>\n              </div>\n\n              {/* Navigation */}\n              <div className=\"flex-1 p-4\">\n                <Navigation \n                  isMobile={true} \n                  onItemClick={onClose}\n                  className=\"mb-8\"\n                />\n                \n                {/* Mobile-specific features */}\n                <div className=\"space-y-4 pt-8 border-t border-gray-200 dark:border-gray-700\">\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                      Dark Mode\n                    </span>\n                    <SimpleDarkModeToggle />\n                  </div>\n                  \n                  <div className=\"pt-4\">\n                    <SimpleHeaderChat />\n                  </div>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        </>\n      )}\n    </AnimatePresence>\n  );\n};\n\nexport default MobileMenu;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;CAyBC;;;;AAKD;AAAA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;AAeA,MAAM,aAAwC,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE;IAChE,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,wBACC;;8BAEE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,YAAY;wBAAE,UAAU,qHAAA,CAAA,mBAAgB,CAAC,QAAQ,CAAC,MAAM;oBAAC;oBACzD,WAAU;oBACV,SAAS;;;;;;8BAIX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,GAAG;oBAAO;oBACrB,SAAS;wBAAE,GAAG;oBAAE;oBAChB,MAAM;wBAAE,GAAG;oBAAO;oBAClB,YAAY;wBACV,UAAU,qHAAA,CAAA,mBAAgB,CAAC,QAAQ,CAAC,MAAM;wBAC1C,MAAM,qHAAA,CAAA,mBAAgB,CAAC,MAAM,CAAC,SAAS;oBACzC;oBACA,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAsD;;;;;;kDAGpE,8OAAC;wCACC,SAAS;wCACT,WAAU;wCACV,cAAW;kDAEX,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAKjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0IAAA,CAAA,UAAU;wCACT,UAAU;wCACV,aAAa;wCACb,WAAU;;;;;;kDAIZ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAuD;;;;;;kEAGvE,8OAAC,0IAAA,CAAA,UAAoB;;;;;;;;;;;0DAGvB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,sIAAA,CAAA,UAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrC;uCAEe", "debugId": null}}, {"offset": {"line": 1586, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/layout/Header.tsx"], "sourcesContent": ["/**\n * Header Component\n *\n * Main navigation header for the Mobilify website. Features responsive design\n * with desktop navigation and mobile hamburger menu. Includes logo, navigation\n * links, chat trigger, dark mode toggle, and CTA button.\n *\n * @component\n * @example\n * ```tsx\n * <Header />\n * ```\n *\n * Features:\n * - Responsive navigation (desktop/mobile)\n * - Mobile hamburger menu with slide-in animation\n * - Dark mode toggle\n * - Chat integration\n * - Analytics tracking for navigation events\n * - Fixed positioning with backdrop blur\n */\n\n'use client';\n\nimport React, { useState } from 'react';\nimport { Menu, X } from 'lucide-react';\nimport Logo from '../Logo';\nimport NoSSR from '../NoSSR';\nimport SimpleHeaderChat from '../SimpleHeaderChat';\nimport SimpleDarkModeToggle from '../SimpleDarkModeToggle';\nimport Navigation from './Navigation';\nimport MobileMenu from './MobileMenu';\nimport { useAnalytics } from '@/hooks';\n\n/**\n * Header component with responsive navigation and mobile menu\n */\nconst Header = () => {\n  // State for mobile menu visibility\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const { trackNavigation } = useAnalytics();\n\n  /**\n   * Handles mobile menu toggle and tracks the interaction\n   */\n  const handleMobileMenuToggle = () => {\n    setIsMenuOpen(!isMenuOpen);\n    trackNavigation('mobile_menu_toggle', 'header');\n  };\n\n  return (\n    <>\n      <header className=\"fixed top-0 left-0 right-0 w-full bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm border-b border-gray-100 dark:border-gray-800 z-50 transition-colors duration-300\">\n        <div className=\"w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between h-16 w-full\">\n            {/* Logo */}\n            <div className=\"flex items-center\">\n              <Logo />\n            </div>\n\n            {/* Desktop Navigation */}\n            <div className=\"hidden md:flex items-center space-x-8\">\n              <Navigation />\n              <NoSSR>\n                <SimpleHeaderChat />\n              </NoSSR>\n              <NoSSR>\n                <SimpleDarkModeToggle />\n              </NoSSR>\n            </div>\n\n            {/* Mobile menu button */}\n            <button\n              onClick={handleMobileMenuToggle}\n              className=\"md:hidden p-2 rounded-lg text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors\"\n              aria-label=\"Toggle mobile menu\"\n          >\n            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}\n          </button>\n          </div>\n        </div>\n      </header>\n\n      {/* Mobile Navigation */}\n      <MobileMenu\n        isOpen={isMenuOpen}\n        onClose={() => setIsMenuOpen(false)}\n      />\n    </>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;CAoBC;;;;AAID;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAVA;;;;;;;;;;;AAYA;;CAEC,GACD,MAAM,SAAS;IACb,mCAAmC;IACnC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAEvC;;GAEC,GACD,MAAM,yBAAyB;QAC7B,cAAc,CAAC;QACf,gBAAgB,sBAAsB;IACxC;IAEA,qBACE;;0BACE,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,0HAAA,CAAA,UAAI;;;;;;;;;;0CAIP,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0IAAA,CAAA,UAAU;;;;;kDACX,8OAAC,2HAAA,CAAA,UAAK;kDACJ,cAAA,8OAAC,sIAAA,CAAA,UAAgB;;;;;;;;;;kDAEnB,8OAAC,2HAAA,CAAA,UAAK;kDACJ,cAAA,8OAAC,0IAAA,CAAA,UAAoB;;;;;;;;;;;;;;;;0CAKzB,8OAAC;gCACC,SAAS;gCACT,WAAU;gCACV,cAAW;0CAEZ,2BAAa,8OAAC,4LAAA,CAAA,IAAC;oCAAC,MAAM;;;;;yDAAS,8OAAC,kMAAA,CAAA,OAAI;oCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOlD,8OAAC,0IAAA,CAAA,UAAU;gBACT,QAAQ;gBACR,SAAS,IAAM,cAAc;;;;;;;;AAIrC;uCAEe", "debugId": null}}, {"offset": {"line": 1757, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/layout/FooterNav.tsx"], "sourcesContent": ["/**\n * Footer Navigation Component\n * \n * Handles the navigation links in the footer section.\n * Organized into logical groups with proper accessibility.\n */\n\n'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { useAnalytics } from '@/hooks';\n\ninterface FooterNavProps {\n  className?: string;\n}\n\ninterface NavSection {\n  title: string;\n  links: Array<{\n    label: string;\n    href: string;\n    external?: boolean;\n  }>;\n}\n\nconst navSections: NavSection[] = [\n  {\n    title: 'Quick Links',\n    links: [\n      { label: 'Interactive Demo', href: '/#demo' },\n      { label: 'Our Services', href: '/#services' },\n      { label: 'How It Works', href: '/#process' },\n      { label: 'About Us', href: '/about' },\n      { label: 'Contact', href: '/#contact' }\n    ]\n  },\n  {\n    title: 'Services',\n    links: [\n      { label: 'Website to App', href: '/services#website-conversion' },\n      { label: 'Custom Mobile Apps', href: '/services#custom-apps' },\n      { label: 'App Maintenance', href: '/services#maintenance' },\n      { label: 'Consultation', href: '/services#consultation' }\n    ]\n  },\n  {\n    title: 'Resources',\n    links: [\n      { label: 'Blog', href: '/blog' },\n      { label: 'FAQ', href: '/faq' },\n      { label: 'Case Studies', href: '/blog?category=case-studies' },\n      { label: 'Pricing', href: '/services#pricing' }\n    ]\n  },\n  {\n    title: 'Legal',\n    links: [\n      { label: 'Privacy Policy', href: '/privacy' },\n      { label: 'Terms of Service', href: '/terms' },\n      { label: 'Cookie Policy', href: '/cookies' }\n    ]\n  }\n];\n\nconst FooterNav: React.FC<FooterNavProps> = ({ className = '' }) => {\n  const { trackNavigation } = useAnalytics();\n\n  const handleLinkClick = (label: string) => {\n    trackNavigation(label, 'footer_nav');\n  };\n\n  return (\n    <div className={`grid grid-cols-2 md:grid-cols-4 gap-8 ${className}`}>\n      {navSections.map((section) => (\n        <div key={section.title}>\n          <h3 className=\"text-lg font-semibold mb-4 text-white\">\n            {section.title}\n          </h3>\n          <ul className=\"space-y-2 text-sm\">\n            {section.links.map((link) => (\n              <li key={link.href}>\n                {link.external ? (\n                  <a\n                    href={link.href}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    onClick={() => handleLinkClick(link.label)}\n                    className=\"text-gray-400 dark:text-gray-300 hover:text-electric-blue transition-colors duration-200\"\n                  >\n                    {link.label}\n                  </a>\n                ) : (\n                  <Link\n                    href={link.href}\n                    onClick={() => handleLinkClick(link.label)}\n                    className=\"text-gray-400 dark:text-gray-300 hover:text-electric-blue transition-colors duration-200\"\n                  >\n                    {link.label}\n                  </Link>\n                )}\n              </li>\n            ))}\n          </ul>\n        </div>\n      ))}\n    </div>\n  );\n};\n\nexport default FooterNav;\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;AAKD;AACA;AAAA;AAJA;;;;AAmBA,MAAM,cAA4B;IAChC;QACE,OAAO;QACP,OAAO;YACL;gBAAE,OAAO;gBAAoB,MAAM;YAAS;YAC5C;gBAAE,OAAO;gBAAgB,MAAM;YAAa;YAC5C;gBAAE,OAAO;gBAAgB,MAAM;YAAY;YAC3C;gBAAE,OAAO;gBAAY,MAAM;YAAS;YACpC;gBAAE,OAAO;gBAAW,MAAM;YAAY;SACvC;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,OAAO;gBAAkB,MAAM;YAA+B;YAChE;gBAAE,OAAO;gBAAsB,MAAM;YAAwB;YAC7D;gBAAE,OAAO;gBAAmB,MAAM;YAAwB;YAC1D;gBAAE,OAAO;gBAAgB,MAAM;YAAyB;SACzD;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,OAAO;gBAAQ,MAAM;YAAQ;YAC/B;gBAAE,OAAO;gBAAO,MAAM;YAAO;YAC7B;gBAAE,OAAO;gBAAgB,MAAM;YAA8B;YAC7D;gBAAE,OAAO;gBAAW,MAAM;YAAoB;SAC/C;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,OAAO;gBAAkB,MAAM;YAAW;YAC5C;gBAAE,OAAO;gBAAoB,MAAM;YAAS;YAC5C;gBAAE,OAAO;gBAAiB,MAAM;YAAW;SAC5C;IACH;CACD;AAED,MAAM,YAAsC,CAAC,EAAE,YAAY,EAAE,EAAE;IAC7D,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAEvC,MAAM,kBAAkB,CAAC;QACvB,gBAAgB,OAAO;IACzB;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,sCAAsC,EAAE,WAAW;kBACjE,YAAY,GAAG,CAAC,CAAC,wBAChB,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCACX,QAAQ,KAAK;;;;;;kCAEhB,8OAAC;wBAAG,WAAU;kCACX,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,qBAClB,8OAAC;0CACE,KAAK,QAAQ,iBACZ,8OAAC;oCACC,MAAM,KAAK,IAAI;oCACf,QAAO;oCACP,KAAI;oCACJ,SAAS,IAAM,gBAAgB,KAAK,KAAK;oCACzC,WAAU;8CAET,KAAK,KAAK;;;;;yDAGb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAM,KAAK,IAAI;oCACf,SAAS,IAAM,gBAAgB,KAAK,KAAK;oCACzC,WAAU;8CAET,KAAK,KAAK;;;;;;+BAjBR,KAAK,IAAI;;;;;;;;;;;eANd,QAAQ,KAAK;;;;;;;;;;AAiC/B;uCAEe", "debugId": null}}, {"offset": {"line": 1929, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/layout/FooterNewsletter.tsx"], "sourcesContent": ["/**\n * Footer Newsletter Component\n * \n * Handles newsletter signup in the footer section.\n * Includes form validation and submission tracking.\n */\n\n'use client';\n\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Mail, CheckCircle, AlertCircle } from 'lucide-react';\nimport { useAnalytics } from '@/hooks';\nimport { ANIMATION_CONFIG } from '@/config/site';\n\ninterface FooterNewsletterProps {\n  className?: string;\n}\n\ninterface NewsletterState {\n  email: string;\n  isSubmitting: boolean;\n  isSubmitted: boolean;\n  error: string | null;\n}\n\nconst FooterNewsletter: React.FC<FooterNewsletterProps> = ({ className = '' }) => {\n  const [state, setState] = useState<NewsletterState>({\n    email: '',\n    isSubmitting: false,\n    isSubmitted: false,\n    error: null\n  });\n\n  const { trackFormSubmission } = useAnalytics();\n\n  const validateEmail = (email: string): boolean => {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!validateEmail(state.email)) {\n      setState(prev => ({ ...prev, error: 'Please enter a valid email address' }));\n      return;\n    }\n\n    setState(prev => ({ ...prev, isSubmitting: true, error: null }));\n\n    try {\n      // Newsletter signup API call would go here\n      // For now, simulate success\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      setState(prev => ({ \n        ...prev, \n        isSubmitting: false, \n        isSubmitted: true,\n        email: ''\n      }));\n\n      trackFormSubmission('newsletter', true, {\n        source: 'footer',\n        email_domain: state.email.split('@')[1]\n      });\n\n      // Reset success state after 3 seconds\n      setTimeout(() => {\n        setState(prev => ({ ...prev, isSubmitted: false }));\n      }, 3000);\n\n    } catch {\n      setState(prev => ({ \n        ...prev, \n        isSubmitting: false, \n        error: 'Something went wrong. Please try again.' \n      }));\n\n      trackFormSubmission('newsletter', false, {\n        source: 'footer',\n        error: 'submission_failed'\n      });\n    }\n  };\n\n  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    setState(prev => ({ \n      ...prev, \n      email: e.target.value,\n      error: null \n    }));\n  };\n\n  return (\n    <div className={`${className}`}>\n      <h3 className=\"text-lg font-semibold mb-4 text-white\">\n        Stay Updated\n      </h3>\n      <p className=\"text-gray-400 dark:text-gray-300 text-sm mb-4\">\n        Get the latest updates on mobile app development trends and Mobilify news.\n      </p>\n\n      <form onSubmit={handleSubmit} className=\"space-y-3\">\n        <div className=\"relative\">\n          <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n            <Mail className=\"h-4 w-4 text-gray-400\" />\n          </div>\n          <input\n            type=\"email\"\n            value={state.email}\n            onChange={handleEmailChange}\n            placeholder=\"Enter your email\"\n            disabled={state.isSubmitting || state.isSubmitted}\n            className=\"\n              w-full pl-10 pr-4 py-2 text-sm\n              bg-gray-800 dark:bg-gray-700 \n              border border-gray-600 dark:border-gray-600\n              rounded-lg text-white placeholder-gray-400\n              focus:ring-2 focus:ring-electric-blue focus:border-electric-blue\n              disabled:opacity-50 disabled:cursor-not-allowed\n              transition-all duration-200\n            \"\n            aria-label=\"Email address for newsletter\"\n          />\n        </div>\n\n        {state.error && (\n          <motion.div\n            initial={{ opacity: 0, y: -10 }}\n            animate={{ opacity: 1, y: 0 }}\n            className=\"flex items-center gap-2 text-red-400 text-xs\"\n          >\n            <AlertCircle className=\"h-3 w-3\" />\n            {state.error}\n          </motion.div>\n        )}\n\n        <motion.button\n          type=\"submit\"\n          disabled={state.isSubmitting || state.isSubmitted || !state.email.trim()}\n          className=\"\n            w-full py-2 px-4 text-sm font-medium rounded-lg\n            bg-electric-blue hover:bg-electric-blue/90\n            text-white transition-all duration-200\n            disabled:opacity-50 disabled:cursor-not-allowed\n            focus:ring-2 focus:ring-electric-blue focus:ring-offset-2 focus:ring-offset-gray-800\n          \"\n          whileHover={{ scale: 1.02 }}\n          whileTap={{ scale: 0.98 }}\n        >\n          {state.isSubmitting ? (\n            <div className=\"flex items-center justify-center gap-2\">\n              <div className=\"w-3 h-3 border border-white border-t-transparent rounded-full animate-spin\" />\n              Subscribing...\n            </div>\n          ) : state.isSubmitted ? (\n            <div className=\"flex items-center justify-center gap-2\">\n              <CheckCircle className=\"h-4 w-4\" />\n              Subscribed!\n            </div>\n          ) : (\n            'Subscribe'\n          )}\n        </motion.button>\n      </form>\n\n      {state.isSubmitted && (\n        <motion.p\n          initial={{ opacity: 0, y: 10 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: ANIMATION_CONFIG.duration.normal }}\n          className=\"text-green-400 text-xs mt-2\"\n        >\n          Thank you for subscribing! Check your email for confirmation.\n        </motion.p>\n      )}\n    </div>\n  );\n};\n\nexport default FooterNewsletter;\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;AAID;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AANA;;;;;;;AAmBA,MAAM,mBAAoD,CAAC,EAAE,YAAY,EAAE,EAAE;IAC3E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;QAClD,OAAO;QACP,cAAc;QACd,aAAa;QACb,OAAO;IACT;IAEA,MAAM,EAAE,mBAAmB,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAE3C,MAAM,gBAAgB,CAAC;QACrB,MAAM,aAAa;QACnB,OAAO,WAAW,IAAI,CAAC;IACzB;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,cAAc,MAAM,KAAK,GAAG;YAC/B,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,OAAO;gBAAqC,CAAC;YAC1E;QACF;QAEA,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,cAAc;gBAAM,OAAO;YAAK,CAAC;QAE9D,IAAI;YACF,2CAA2C;YAC3C,4BAA4B;YAC5B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,cAAc;oBACd,aAAa;oBACb,OAAO;gBACT,CAAC;YAED,oBAAoB,cAAc,MAAM;gBACtC,QAAQ;gBACR,cAAc,MAAM,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;YACzC;YAEA,sCAAsC;YACtC,WAAW;gBACT,SAAS,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,aAAa;oBAAM,CAAC;YACnD,GAAG;QAEL,EAAE,OAAM;YACN,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,cAAc;oBACd,OAAO;gBACT,CAAC;YAED,oBAAoB,cAAc,OAAO;gBACvC,QAAQ;gBACR,OAAO;YACT;QACF;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,SAAS,CAAA,OAAQ,CAAC;gBAChB,GAAG,IAAI;gBACP,OAAO,EAAE,MAAM,CAAC,KAAK;gBACrB,OAAO;YACT,CAAC;IACH;IAEA,qBACE,8OAAC;QAAI,WAAW,GAAG,WAAW;;0BAC5B,8OAAC;gBAAG,WAAU;0BAAwC;;;;;;0BAGtD,8OAAC;gBAAE,WAAU;0BAAgD;;;;;;0BAI7D,8OAAC;gBAAK,UAAU;gBAAc,WAAU;;kCACtC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAElB,8OAAC;gCACC,MAAK;gCACL,OAAO,MAAM,KAAK;gCAClB,UAAU;gCACV,aAAY;gCACZ,UAAU,MAAM,YAAY,IAAI,MAAM,WAAW;gCACjD,WAAU;gCASV,cAAW;;;;;;;;;;;;oBAId,MAAM,KAAK,kBACV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,WAAU;;0CAEV,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;4BACtB,MAAM,KAAK;;;;;;;kCAIhB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,MAAK;wBACL,UAAU,MAAM,YAAY,IAAI,MAAM,WAAW,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI;wBACtE,WAAU;wBAOV,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;kCAEvB,MAAM,YAAY,iBACjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;gCAA+E;;;;;;mCAG9F,MAAM,WAAW,iBACnB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,2NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;gCAAY;;;;;;mCAIrC;;;;;;;;;;;;YAKL,MAAM,WAAW,kBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gBACP,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU,qHAAA,CAAA,mBAAgB,CAAC,QAAQ,CAAC,MAAM;gBAAC;gBACzD,WAAU;0BACX;;;;;;;;;;;;AAMT;uCAEe", "debugId": null}}, {"offset": {"line": 2190, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/layout/Footer.tsx"], "sourcesContent": ["/**\n * Footer Component\n *\n * Main site footer with company information, navigation links, and newsletter signup.\n * Composed of smaller focused components for better maintainability.\n *\n * @component\n * @example\n * ```tsx\n * <Footer />\n * ```\n *\n * Features:\n * - Company branding and description\n * - Organized navigation links in multiple columns\n * - Newsletter signup with validation\n * - Dark mode toggle\n * - Copyright and legal information\n * - Responsive design with mobile-first approach\n *\n * Architecture:\n * - Uses composition with FooterNav and FooterNewsletter components\n * - CMS-driven content with fallbacks from site config\n * - Consistent theming with semantic color tokens\n */\n\n'use client';\n\nimport React from 'react';\nimport Logo from '../Logo';\nimport SimpleDarkModeToggle from '../SimpleDarkModeToggle';\nimport NoSSR from '../NoSSR';\nimport FooterNav from './FooterNav';\nimport FooterNewsletter from './FooterNewsletter';\nimport { SITE_CONFIG } from '@/config/site';\n\n/**\n * Site footer with navigation, newsletter, and company information\n */\nconst Footer = () => {\n  return (\n    <footer className=\"bg-dark-charcoal dark:bg-gray-950 text-white py-12 transition-colors duration-300\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-8 mb-8\">\n          {/* Company Info */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"flex items-center mb-4\">\n              <Logo />\n              <span className=\"ml-2 text-xl font-bold text-white\">{SITE_CONFIG.name}</span>\n            </div>\n            <p className=\"text-gray-400 dark:text-gray-300 text-sm leading-relaxed\">\n              {SITE_CONFIG.description}\n            </p>\n\n            {/* Newsletter Signup */}\n            <div className=\"mt-6\">\n              <FooterNewsletter />\n            </div>\n          </div>\n\n          {/* Navigation Links */}\n          <div className=\"lg:col-span-3\">\n            <FooterNav />\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"border-t border-gray-800 dark:border-gray-700 pt-8\">\n          <div className=\"flex flex-col md:flex-row items-center justify-between\">\n            <p className=\"text-gray-400 dark:text-gray-300 text-sm\">\n              © {new Date().getFullYear()} {SITE_CONFIG.name}. All rights reserved.\n            </p>\n            <div className=\"flex items-center space-x-6 mt-4 md:mt-0\">\n              <NoSSR>\n                <SimpleDarkModeToggle />\n              </NoSSR>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;CAwBC;;;;AAKD;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;AAUA;;CAEC,GACD,MAAM,SAAS;IACb,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0HAAA,CAAA,UAAI;;;;;sDACL,8OAAC;4CAAK,WAAU;sDAAqC,qHAAA,CAAA,cAAW,CAAC,IAAI;;;;;;;;;;;;8CAEvE,8OAAC;oCAAE,WAAU;8CACV,qHAAA,CAAA,cAAW,CAAC,WAAW;;;;;;8CAI1B,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,gJAAA,CAAA,UAAgB;;;;;;;;;;;;;;;;sCAKrB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,yIAAA,CAAA,UAAS;;;;;;;;;;;;;;;;8BAKd,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;;oCAA2C;oCACnD,IAAI,OAAO,WAAW;oCAAG;oCAAE,qHAAA,CAAA,cAAW,CAAC,IAAI;oCAAC;;;;;;;0CAEjD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,2HAAA,CAAA,UAAK;8CACJ,cAAA,8OAAC,0IAAA,CAAA,UAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrC;uCAEe", "debugId": null}}, {"offset": {"line": 2379, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/Mission.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\n\nconst Mission = () => {\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"text-center\"\n        >\n          <h2 className=\"text-3xl sm:text-4xl font-bold text-gray-900 mb-8\">\n            Our Mission\n          </h2>\n          \n          <div className=\"text-lg text-gray-600 leading-relaxed space-y-6\">\n            <p>\n              To empower every entrepreneur and business with the ability to create beautiful, \n              high-performance mobile apps, transforming brilliant ideas into market-ready \n              reality without the traditional complexity and cost.\n            </p>\n            \n            <p>\n              We believe that innovation shouldn&apos;t be limited by technical barriers or\n              prohibitive costs. Every great idea deserves the chance to reach users \n              through the most personal and powerful platform available – mobile devices.\n            </p>\n            \n            <p>\n              Our approach combines cutting-edge technology with human expertise, ensuring \n              that each app we create is not just functional, but exceptional. We&apos;re not\n              just building apps; we&apos;re building the future of mobile experiences, one\n              project at a time.\n            </p>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default Mission;\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKA,MAAM,UAAU;IACd,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,aAAa;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAChC,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,UAAU;oBAAE,MAAM;gBAAK;gBACvB,WAAU;;kCAEV,8OAAC;wBAAG,WAAU;kCAAoD;;;;;;kCAIlE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAE;;;;;;0CAMH,8OAAC;0CAAE;;;;;;0CAMH,8OAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWf;uCAEe", "debugId": null}}, {"offset": {"line": 2471, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/TeamProfiles.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Image from 'next/image';\nimport { motion } from 'framer-motion';\n\nconst TeamProfiles = () => {\n  const team = [\n    {\n      name: '<PERSON>',\n      title: 'CEO & Founder',\n      bio: 'A former Product Lead at Shopify, <PERSON> saw countless businesses struggle to make the leap from web to mobile. He founded Mobilify with a passion for democratizing technology, driven by the belief that a great idea, not a massive budget, should be the only prerequisite for a world-class mobile app.',\n      // Using custom placeholder image (replace with actual team photos when available)\n      image: '/images/placeholders/team-alex.svg',\n    },\n    {\n      name: '<PERSON>',\n      title: 'CTO & Co-Founder',\n      bio: 'With a background as a Senior Software Engineer at Twilio, <PERSON> is the technical architect behind Mobilify\\'s innovative platform. She specializes in building scalable, AI-driven systems. <PERSON> is passionate about crafting elegant code and powerful tools that make sophisticated technology feel simple and accessible to everyone.',\n      // Using custom placeholder image (replace with actual team photos when available)\n      image: '/images/placeholders/team-maria.svg',\n    },\n  ];\n\n  return (\n    <section className=\"py-20 bg-gray-50\">\n      <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-3xl sm:text-4xl font-bold text-gray-900 mb-4\">\n            Meet Our Team\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            The passionate individuals behind Mobilify, dedicated to turning your vision into reality\n          </p>\n        </motion.div>\n\n        <div className=\"grid md:grid-cols-2 gap-12 lg:gap-16\">\n          {team.map((member, index) => (\n            <motion.div\n              key={member.name}\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.2 }}\n              viewport={{ once: true }}\n              className=\"text-center\"\n            >\n              <div className=\"mb-6\">\n                <Image\n                  src={member.image}\n                  alt={member.name}\n                  width={192}\n                  height={192}\n                  className=\"w-48 h-48 rounded-full mx-auto object-cover shadow-lg\"\n                  priority={index === 0} // Prioritize first image for LCP\n                />\n              </div>\n              \n              <h3 className=\"text-2xl font-bold text-gray-900 mb-2\">\n                {member.name}\n              </h3>\n              \n              <p className=\"text-indigo-600 font-semibold mb-4\">\n                {member.title}\n              </p>\n              \n              <p className=\"text-gray-600 leading-relaxed\">\n                {member.bio}\n              </p>\n            </motion.div>\n          ))}\n        </div>\n\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.6 }}\n          viewport={{ once: true }}\n          className=\"text-center mt-16\"\n        >\n          <div className=\"bg-white rounded-xl p-8 shadow-lg max-w-4xl mx-auto\">\n            <h3 className=\"text-xl font-bold text-gray-900 mb-4\">\n              Why We Started Mobilify\n            </h3>\n            <p className=\"text-gray-600 leading-relaxed\">\n              After years of working at leading tech companies, we saw firsthand how mobile apps \n              could transform businesses. Yet, we also witnessed countless entrepreneurs and small \n              businesses struggle with the complexity and cost of mobile development. Mobilify was \n              born from our desire to bridge this gap – to make world-class mobile app development \n              accessible to everyone with a great idea.\n            </p>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default TeamProfiles;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAMA,MAAM,eAAe;IACnB,MAAM,OAAO;QACX;YACE,MAAM;YACN,OAAO;YACP,KAAK;YACL,kFAAkF;YAClF,OAAO;QACT;QACA;YACE,MAAM;YACN,OAAO;YACP,KAAK;YACL,kFAAkF;YAClF,OAAO;QACT;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAKzD,8OAAC;oBAAI,WAAU;8BACZ,KAAK,GAAG,CAAC,CAAC,QAAQ,sBACjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAK,OAAO,KAAK;wCACjB,KAAK,OAAO,IAAI;wCAChB,OAAO;wCACP,QAAQ;wCACR,WAAU;wCACV,UAAU,UAAU;;;;;;;;;;;8CAIxB,8OAAC;oCAAG,WAAU;8CACX,OAAO,IAAI;;;;;;8CAGd,8OAAC;oCAAE,WAAU;8CACV,OAAO,KAAK;;;;;;8CAGf,8OAAC;oCAAE,WAAU;8CACV,OAAO,GAAG;;;;;;;2BA3BR,OAAO,IAAI;;;;;;;;;;8BAiCtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAuC;;;;;;0CAGrD,8OAAC;gCAAE,WAAU;0CAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYzD;uCAEe", "debugId": null}}, {"offset": {"line": 2682, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/CompanyValues.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { motion } from 'framer-motion';\nimport { Award, Users, Eye, Zap } from 'lucide-react';\n\nconst CompanyValues = () => {\n  const values = [\n    {\n      icon: <Award className=\"w-8 h-8\" />,\n      title: 'Quality Craftsmanship',\n      description: 'We believe in doing things right the first time. Every line of code, every design element, and every user interaction is crafted with meticulous attention to detail.',\n    },\n    {\n      icon: <Users className=\"w-8 h-8\" />,\n      title: 'Client Partnership',\n      description: 'We\\'re not just service providers – we\\'re your partners in success. We invest in understanding your vision and work collaboratively to bring it to life.',\n    },\n    {\n      icon: <Eye className=\"w-8 h-8\" />,\n      title: 'Transparency',\n      description: 'Clear communication, honest timelines, and upfront pricing. You\\'ll always know where your project stands and what to expect next.',\n    },\n    {\n      icon: <Zap className=\"w-8 h-8\" />,\n      title: 'Innovation',\n      description: 'We stay at the forefront of mobile technology, constantly exploring new ways to create better, faster, and more engaging user experiences.',\n    },\n  ];\n\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-3xl sm:text-4xl font-bold text-gray-900 mb-4\">\n            Our Values\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            The principles that guide everything we do and every decision we make\n          </p>\n        </motion.div>\n\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {values.map((value, index) => (\n            <motion.div\n              key={value.title}\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n              viewport={{ once: true }}\n              className=\"text-center\"\n            >\n              <div className=\"inline-flex items-center justify-center w-16 h-16 bg-indigo-100 text-indigo-600 rounded-full mb-6\">\n                {value.icon}\n              </div>\n              \n              <h3 className=\"text-xl font-bold text-gray-900 mb-4\">\n                {value.title}\n              </h3>\n              \n              <p className=\"text-gray-600 leading-relaxed\">\n                {value.description}\n              </p>\n            </motion.div>\n          ))}\n        </div>\n\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.5 }}\n          viewport={{ once: true }}\n          className=\"text-center mt-16\"\n        >\n          <div className=\"bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl p-8 max-w-4xl mx-auto\">\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n              Ready to Work Together?\n            </h3>\n            <p className=\"text-gray-600 mb-6 leading-relaxed\">\n              We&apos;d love to learn about your project and explore how we can help bring your mobile app vision to life.\n              Let&apos;s start a conversation about your goals and how Mobilify can support your success.\n            </p>\n            <Link\n              href=\"/#contact\"\n              className=\"inline-flex items-center bg-indigo-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-indigo-700 transition-colors duration-200 shadow-lg hover:shadow-xl\"\n            >\n              Start Your Project\n            </Link>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default CompanyValues;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AALA;;;;;AAOA,MAAM,gBAAgB;IACpB,MAAM,SAAS;QACb;YACE,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;YACrB,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;YACrB,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAKzD,8OAAC;oBAAI,WAAU;8BACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACZ,MAAM,IAAI;;;;;;8CAGb,8OAAC;oCAAG,WAAU;8CACX,MAAM,KAAK;;;;;;8CAGd,8OAAC;oCAAE,WAAU;8CACV,MAAM,WAAW;;;;;;;2BAhBf,MAAM,KAAK;;;;;;;;;;8BAsBtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,8OAAC;gCAAE,WAAU;0CAAqC;;;;;;0CAIlD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;uCAEe", "debugId": null}}]}