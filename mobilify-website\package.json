{"name": "mobilify-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "build:analyze": "ANALYZE=true next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "lighthouse": "lighthouse http://localhost:3000 --output=html --output-path=./lighthouse-report.html --chrome-flags=\"--headless\"", "perf:audit": "npm run build && npm run lighthouse"}, "dependencies": {"@mailchimp/mailchimp_marketing": "^3.0.80", "@sanity/client": "^7.6.0", "@sanity/image-url": "^1.1.0", "clsx": "^2.1.1", "framer-motion": "^12.19.1", "lucide-react": "^0.523.0", "next": "15.3.4", "next-sanity": "^0.8.5", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1", "web-vitals": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/jest": "^29.5.8", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "msw": "^2.10.3", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5"}}