# Dependencies
node_modules/
*/node_modules/

# Build outputs
.next/
out/
dist/
build/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Vercel
.vercel

# Documentation and development files (keep in docs/ folder)
docs/
TODO*.md
FEATURE*.md
REFACTORING*.md
*_ANALYSIS.md
*_STRATEGY.md
*_SYSTEM.md
*Plan*.md

# Temporary files
*.tmp
*.temp
.cache/

# Testing
.nyc_output/
coverage/
*.lcov

# Package manager files
package-lock.json.backup
yarn.lock.backup
