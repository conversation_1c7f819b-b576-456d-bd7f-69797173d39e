[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\about\\page.tsx": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\api\\newsletter\\route.ts": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\blog\\page.tsx": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\blog\\[slug]\\page.tsx": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\faq\\FAQClient.tsx": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\faq\\page.tsx": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\layout.tsx": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\page.tsx": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\robots.ts": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\services\\page.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\sitemap.ts": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\AnimationWrapper.tsx": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ChatTrigger.tsx": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ClientOnly.tsx": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\CompanyValues.tsx": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\DarkModeToggle.tsx": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\Logo.tsx": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\Mission.tsx": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\NewsletterSignup.tsx": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\NoSSR.tsx": "20", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\OrganizationSchema.tsx": "21", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\PortableText.tsx": "22", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\PricingTable.tsx": "23", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ServicesFAQ.tsx": "24", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\SimpleDarkModeToggle.tsx": "25", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\SimpleFloatingChat.tsx": "26", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\SimpleHeaderChat.tsx": "27", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\StructuredData.tsx": "28", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\TeamProfiles.tsx": "29", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\Button.tsx": "30", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\Card.tsx": "31", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\index.ts": "32", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\Input.tsx": "33", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\__tests__\\Button.test.tsx": "34", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\__tests__\\Input.test.tsx": "35", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\__tests__\\Contact.test.tsx": "36", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\__tests__\\Header.test.tsx": "37", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\__tests__\\InteractiveDemo.test.tsx": "38", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\__tests__\\NewsletterSignup.test.tsx": "39", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\config\\site.ts": "40", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\contexts\\ThemeContext.tsx": "41", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\index.ts": "42", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\useAnalytics.ts": "43", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\useContactForm.ts": "44", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\useSiteSettings.ts": "45", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\lib\\metadata.ts": "46", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\lib\\sanity.ts": "47", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\lib\\utils.ts": "48", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\__tests__\\simple.test.js": "49", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\__tests__\\test-utils.tsx": "50", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\analytics\\CrispChat.tsx": "51", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\analytics\\GoogleAnalytics.tsx": "52", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\analytics\\index.ts": "53", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\analytics\\WebVitals.tsx": "54", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\Footer.tsx": "55", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\Header.tsx": "56", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\index.ts": "57", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\MobileMenu.tsx": "58", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\Navigation.tsx": "59", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\AboutSnippet.tsx": "60", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\Contact.tsx": "61", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\ContactForm.tsx": "62", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\DemoInput.tsx": "63", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\DemoPreview.tsx": "64", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\DemoTabs.tsx": "65", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\Hero.tsx": "66", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\index.ts": "67", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\InteractiveDemo.tsx": "68", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\Process.tsx": "69", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\ServicesOverview.tsx": "70", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\types\\analytics.ts": "71", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\types\\forms.ts": "72", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\types\\index.ts": "73", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\types\\sanity.ts": "74", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\FooterNav.tsx": "75", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\FooterNewsletter.tsx": "76"}, {"size": 2067, "mtime": 1751679940296, "results": "77", "hashOfConfig": "78"}, {"size": 3626, "mtime": 1751505504335, "results": "79", "hashOfConfig": "78"}, {"size": 10127, "mtime": 1751680016774, "results": "80", "hashOfConfig": "78"}, {"size": 9800, "mtime": 1751682806140, "results": "81", "hashOfConfig": "78"}, {"size": 12228, "mtime": 1751680051540, "results": "82", "hashOfConfig": "78"}, {"size": 5797, "mtime": 1751577101014, "results": "83", "hashOfConfig": "78"}, {"size": 3587, "mtime": 1751676697710, "results": "84", "hashOfConfig": "78"}, {"size": 1922, "mtime": 1751676758569, "results": "85", "hashOfConfig": "78"}, {"size": 415, "mtime": 1751664165349, "results": "86", "hashOfConfig": "78"}, {"size": 2090, "mtime": 1751676794792, "results": "87", "hashOfConfig": "78"}, {"size": 1525, "mtime": 1751682854554, "results": "88", "hashOfConfig": "78"}, {"size": 615, "mtime": 1751680147823, "results": "89", "hashOfConfig": "78"}, {"size": 4638, "mtime": 1751680166678, "results": "90", "hashOfConfig": "78"}, {"size": 447, "mtime": 1750993163336, "results": "91", "hashOfConfig": "78"}, {"size": 4013, "mtime": 1751680290044, "results": "92", "hashOfConfig": "78"}, {"size": 4806, "mtime": 1751511499987, "results": "93", "hashOfConfig": "78"}, {"size": 315, "mtime": 1750990215277, "results": "94", "hashOfConfig": "78"}, {"size": 1715, "mtime": 1751680446533, "results": "95", "hashOfConfig": "78"}, {"size": 6771, "mtime": 1751680524880, "results": "96", "hashOfConfig": "78"}, {"size": 415, "mtime": 1750998195180, "results": "97", "hashOfConfig": "78"}, {"size": 2390, "mtime": 1751578724741, "results": "98", "hashOfConfig": "78"}, {"size": 6780, "mtime": 1751681909643, "results": "99", "hashOfConfig": "78"}, {"size": 7674, "mtime": 1751681931476, "results": "100", "hashOfConfig": "78"}, {"size": 6402, "mtime": 1751682202896, "results": "101", "hashOfConfig": "78"}, {"size": 1149, "mtime": 1751518090332, "results": "102", "hashOfConfig": "78"}, {"size": 1498, "mtime": 1751518229057, "results": "103", "hashOfConfig": "78"}, {"size": 1145, "mtime": 1751518286486, "results": "104", "hashOfConfig": "78"}, {"size": 4809, "mtime": 1751664389950, "results": "105", "hashOfConfig": "78"}, {"size": 4256, "mtime": 1751690904523, "results": "106", "hashOfConfig": "78"}, {"size": 3690, "mtime": 1751681415689, "results": "107", "hashOfConfig": "78"}, {"size": 1834, "mtime": 1751575325593, "results": "108", "hashOfConfig": "78"}, {"size": 300, "mtime": 1751575364754, "results": "109", "hashOfConfig": "78"}, {"size": 3823, "mtime": 1751681457170, "results": "110", "hashOfConfig": "78"}, {"size": 8324, "mtime": 1751662637625, "results": "111", "hashOfConfig": "78"}, {"size": 9276, "mtime": 1751662678560, "results": "112", "hashOfConfig": "78"}, {"size": 8687, "mtime": 1751682225196, "results": "113", "hashOfConfig": "78"}, {"size": 9021, "mtime": 1751662773289, "results": "114", "hashOfConfig": "78"}, {"size": 10368, "mtime": 1751682246654, "results": "115", "hashOfConfig": "78"}, {"size": 9634, "mtime": 1751682268616, "results": "116", "hashOfConfig": "78"}, {"size": 6036, "mtime": 1751670335025, "results": "117", "hashOfConfig": "78"}, {"size": 3477, "mtime": 1751515726939, "results": "118", "hashOfConfig": "78"}, {"size": 551, "mtime": 1751670473668, "results": "119", "hashOfConfig": "78"}, {"size": 5664, "mtime": 1751683325256, "results": "120", "hashOfConfig": "78"}, {"size": 6690, "mtime": 1751671807702, "results": "121", "hashOfConfig": "78"}, {"size": 5933, "mtime": 1751671826897, "results": "122", "hashOfConfig": "78"}, {"size": 5445, "mtime": 1751682311965, "results": "123", "hashOfConfig": "78"}, {"size": 6546, "mtime": 1751670381294, "results": "124", "hashOfConfig": "78"}, {"size": 169, "mtime": 1751574258441, "results": "125", "hashOfConfig": "78"}, {"size": 232, "mtime": 1751663733726, "results": "126", "hashOfConfig": "78"}, {"size": 5645, "mtime": 1751662735678, "results": "127", "hashOfConfig": "78"}, {"size": 5535, "mtime": 1751682661527, "results": "128", "hashOfConfig": "78"}, {"size": 970, "mtime": 1751679975840, "results": "129", "hashOfConfig": "78"}, {"size": 461, "mtime": 1751676842124, "results": "130", "hashOfConfig": "78"}, {"size": 2650, "mtime": 1751664824565, "results": "131", "hashOfConfig": "78"}, {"size": 2651, "mtime": 1751681389366, "results": "132", "hashOfConfig": "78"}, {"size": 2827, "mtime": 1751681223785, "results": "133", "hashOfConfig": "78"}, {"size": 626, "mtime": 1751678612776, "results": "134", "hashOfConfig": "78"}, {"size": 3774, "mtime": 1751683113819, "results": "135", "hashOfConfig": "78"}, {"size": 2163, "mtime": 1751681250090, "results": "136", "hashOfConfig": "78"}, {"size": 2458, "mtime": 1751682079226, "results": "137", "hashOfConfig": "78"}, {"size": 1852, "mtime": 1751676812928, "results": "138", "hashOfConfig": "78"}, {"size": 9857, "mtime": 1751681372306, "results": "139", "hashOfConfig": "78"}, {"size": 4625, "mtime": 1751681328775, "results": "140", "hashOfConfig": "78"}, {"size": 7824, "mtime": 1751683149524, "results": "141", "hashOfConfig": "78"}, {"size": 3290, "mtime": 1751681311593, "results": "142", "hashOfConfig": "78"}, {"size": 4130, "mtime": 1751670521401, "results": "143", "hashOfConfig": "78"}, {"size": 685, "mtime": 1751676834675, "results": "144", "hashOfConfig": "78"}, {"size": 4185, "mtime": 1751683282671, "results": "145", "hashOfConfig": "78"}, {"size": 4837, "mtime": 1751677422096, "results": "146", "hashOfConfig": "78"}, {"size": 4495, "mtime": 1751682115179, "results": "147", "hashOfConfig": "78"}, {"size": 8012, "mtime": 1751676639727, "results": "148", "hashOfConfig": "78"}, {"size": 6032, "mtime": 1751682546493, "results": "149", "hashOfConfig": "78"}, {"size": 6626, "mtime": 1751676680436, "results": "150", "hashOfConfig": "78"}, {"size": 4695, "mtime": 1751676570640, "results": "151", "hashOfConfig": "78"}, {"size": 3104, "mtime": 1751680327855, "results": "152", "hashOfConfig": "78"}, {"size": 5517, "mtime": 1751682972890, "results": "153", "hashOfConfig": "78"}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "10vcq7z", {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\about\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\api\\newsletter\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\blog\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\blog\\[slug]\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\faq\\FAQClient.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\faq\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\robots.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\services\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\sitemap.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\AnimationWrapper.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ChatTrigger.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ClientOnly.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\CompanyValues.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\DarkModeToggle.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\Logo.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\Mission.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\NewsletterSignup.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\NoSSR.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\OrganizationSchema.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\PortableText.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\PricingTable.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ServicesFAQ.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\SimpleDarkModeToggle.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\SimpleFloatingChat.tsx", ["382", "383", "384", "385"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\SimpleHeaderChat.tsx", ["386", "387", "388", "389"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\StructuredData.tsx", ["390", "391", "392"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\TeamProfiles.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\Button.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\Card.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\Input.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\__tests__\\Button.test.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\__tests__\\Input.test.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\__tests__\\Contact.test.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\__tests__\\Header.test.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\__tests__\\InteractiveDemo.test.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\__tests__\\NewsletterSignup.test.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\config\\site.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\contexts\\ThemeContext.tsx", ["393", "394"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\useAnalytics.ts", ["395", "396", "397"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\useContactForm.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\useSiteSettings.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\lib\\metadata.ts", ["398"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\lib\\sanity.ts", ["399", "400", "401"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\__tests__\\simple.test.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\__tests__\\test-utils.tsx", ["402", "403", "404"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\analytics\\CrispChat.tsx", ["405", "406", "407", "408"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\analytics\\GoogleAnalytics.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\analytics\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\analytics\\WebVitals.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\Footer.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\MobileMenu.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\Navigation.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\AboutSnippet.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\Contact.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\ContactForm.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\DemoInput.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\DemoPreview.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\DemoTabs.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\Hero.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\InteractiveDemo.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\Process.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\ServicesOverview.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\types\\analytics.ts", ["409", "410", "411", "412", "413", "414", "415", "416", "417"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\types\\forms.ts", ["418", "419", "420", "421", "422", "423", "424", "425", "426", "427"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\types\\index.ts", ["428", "429", "430", "431", "432", "433", "434", "435", "436", "437", "438", "439", "440", "441", "442", "443", "444", "445"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\types\\sanity.ts", ["446", "447", "448", "449", "450", "451"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\FooterNav.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\FooterNewsletter.tsx", [], [], {"ruleId": "452", "severity": 1, "message": "453", "line": 9, "column": 53, "nodeType": "454", "messageId": "455", "endLine": 9, "endColumn": 56, "suggestions": "456"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 10, "column": 18, "nodeType": "454", "messageId": "455", "endLine": 10, "endColumn": 21, "suggestions": "457"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 17, "column": 53, "nodeType": "454", "messageId": "455", "endLine": 17, "endColumn": 56, "suggestions": "458"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 18, "column": 18, "nodeType": "454", "messageId": "455", "endLine": 18, "endColumn": 21, "suggestions": "459"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 9, "column": 53, "nodeType": "454", "messageId": "455", "endLine": 9, "endColumn": 56, "suggestions": "460"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 10, "column": 18, "nodeType": "454", "messageId": "455", "endLine": 10, "endColumn": 21, "suggestions": "461"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 17, "column": 53, "nodeType": "454", "messageId": "455", "endLine": 17, "endColumn": 56, "suggestions": "462"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 18, "column": 18, "nodeType": "454", "messageId": "455", "endLine": 18, "endColumn": 21, "suggestions": "463"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 5, "column": 10, "nodeType": "454", "messageId": "455", "endLine": 5, "endColumn": 13, "suggestions": "464"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 89, "column": 47, "nodeType": "454", "messageId": "455", "endLine": 89, "endColumn": 50, "suggestions": "465"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 103, "column": 60, "nodeType": "454", "messageId": "455", "endLine": 103, "endColumn": 63, "suggestions": "466"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 57, "column": 53, "nodeType": "454", "messageId": "455", "endLine": 57, "endColumn": 56, "suggestions": "467"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 58, "column": 18, "nodeType": "454", "messageId": "455", "endLine": 58, "endColumn": 21, "suggestions": "468"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 17, "column": 38, "nodeType": "454", "messageId": "455", "endLine": 17, "endColumn": 41, "suggestions": "469"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 75, "column": 53, "nodeType": "454", "messageId": "455", "endLine": 75, "endColumn": 56, "suggestions": "470"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 77, "column": 20, "nodeType": "454", "messageId": "455", "endLine": 77, "endColumn": 23, "suggestions": "471"}, {"ruleId": "472", "severity": 1, "message": "473", "line": 28, "column": 3, "nodeType": null, "messageId": "474", "endLine": 28, "endColumn": 17}, {"ruleId": "452", "severity": 1, "message": "453", "line": 22, "column": 32, "nodeType": "454", "messageId": "455", "endLine": 22, "endColumn": 35, "suggestions": "475"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 62, "column": 9, "nodeType": "454", "messageId": "455", "endLine": 62, "endColumn": 12, "suggestions": "476"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 109, "column": 11, "nodeType": "454", "messageId": "455", "endLine": 109, "endColumn": 14, "suggestions": "477"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 59, "column": 37, "nodeType": "454", "messageId": "455", "endLine": 59, "endColumn": 40, "suggestions": "478"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 160, "column": 66, "nodeType": "454", "messageId": "455", "endLine": 160, "endColumn": 69, "suggestions": "479"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 177, "column": 75, "nodeType": "454", "messageId": "455", "endLine": 177, "endColumn": 78, "suggestions": "480"}, {"ruleId": "472", "severity": 1, "message": "481", "line": 7, "column": 6, "nodeType": null, "messageId": "474", "endLine": 7, "endColumn": 18}, {"ruleId": "472", "severity": 1, "message": "482", "line": 8, "column": 6, "nodeType": null, "messageId": "474", "endLine": 8, "endColumn": 15}, {"ruleId": "472", "severity": 1, "message": "483", "line": 9, "column": 6, "nodeType": null, "messageId": "474", "endLine": 9, "endColumn": 19}, {"ruleId": "472", "severity": 1, "message": "484", "line": 12, "column": 6, "nodeType": null, "messageId": "474", "endLine": 12, "endColumn": 15}, {"ruleId": "452", "severity": 1, "message": "453", "line": 11, "column": 31, "nodeType": "454", "messageId": "455", "endLine": 11, "endColumn": 34, "suggestions": "485"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 23, "column": 38, "nodeType": "454", "messageId": "455", "endLine": 23, "endColumn": 41, "suggestions": "486"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 64, "column": 38, "nodeType": "454", "messageId": "455", "endLine": 64, "endColumn": 41, "suggestions": "487"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 228, "column": 63, "nodeType": "454", "messageId": "455", "endLine": 228, "endColumn": 66, "suggestions": "488"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 229, "column": 65, "nodeType": "454", "messageId": "455", "endLine": 229, "endColumn": 68, "suggestions": "489"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 230, "column": 89, "nodeType": "454", "messageId": "455", "endLine": 230, "endColumn": 92, "suggestions": "490"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 231, "column": 88, "nodeType": "454", "messageId": "455", "endLine": 231, "endColumn": 91, "suggestions": "491"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 232, "column": 83, "nodeType": "454", "messageId": "455", "endLine": 232, "endColumn": 86, "suggestions": "492"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 233, "column": 76, "nodeType": "454", "messageId": "455", "endLine": 233, "endColumn": 79, "suggestions": "493"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 67, "column": 36, "nodeType": "454", "messageId": "455", "endLine": 67, "endColumn": 39, "suggestions": "494"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 67, "column": 51, "nodeType": "454", "messageId": "455", "endLine": 67, "endColumn": 54, "suggestions": "495"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 104, "column": 37, "nodeType": "454", "messageId": "455", "endLine": 104, "endColumn": 40, "suggestions": "496"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 122, "column": 10, "nodeType": "454", "messageId": "455", "endLine": 122, "endColumn": 13, "suggestions": "497"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 126, "column": 44, "nodeType": "454", "messageId": "455", "endLine": 126, "endColumn": 47, "suggestions": "498"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 127, "column": 40, "nodeType": "454", "messageId": "455", "endLine": 127, "endColumn": 43, "suggestions": "499"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 131, "column": 45, "nodeType": "454", "messageId": "455", "endLine": 131, "endColumn": 48, "suggestions": "500"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 135, "column": 40, "nodeType": "454", "messageId": "455", "endLine": 135, "endColumn": 43, "suggestions": "501"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 135, "column": 55, "nodeType": "454", "messageId": "455", "endLine": 135, "endColumn": 58, "suggestions": "502"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 139, "column": 47, "nodeType": "454", "messageId": "455", "endLine": 139, "endColumn": 50, "suggestions": "503"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 98, "column": 34, "nodeType": "454", "messageId": "455", "endLine": 98, "endColumn": 37, "suggestions": "504"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 106, "column": 40, "nodeType": "454", "messageId": "455", "endLine": 106, "endColumn": 43, "suggestions": "505"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 121, "column": 13, "nodeType": "454", "messageId": "455", "endLine": 121, "endColumn": 16, "suggestions": "506"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 135, "column": 33, "nodeType": "454", "messageId": "455", "endLine": 135, "endColumn": 36, "suggestions": "507"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 160, "column": 35, "nodeType": "454", "messageId": "455", "endLine": 160, "endColumn": 38, "suggestions": "508"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 164, "column": 27, "nodeType": "454", "messageId": "455", "endLine": 164, "endColumn": 30, "suggestions": "509"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 208, "column": 13, "nodeType": "454", "messageId": "455", "endLine": 208, "endColumn": 16, "suggestions": "510"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 209, "column": 13, "nodeType": "454", "messageId": "455", "endLine": 209, "endColumn": 16, "suggestions": "511"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 210, "column": 10, "nodeType": "454", "messageId": "455", "endLine": 210, "endColumn": 13, "suggestions": "512"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 212, "column": 29, "nodeType": "454", "messageId": "455", "endLine": 212, "endColumn": 32, "suggestions": "513"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 213, "column": 16, "nodeType": "454", "messageId": "455", "endLine": 213, "endColumn": 19, "suggestions": "514"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 214, "column": 14, "nodeType": "454", "messageId": "455", "endLine": 214, "endColumn": 17, "suggestions": "515"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 215, "column": 17, "nodeType": "454", "messageId": "455", "endLine": 215, "endColumn": 20, "suggestions": "516"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 238, "column": 31, "nodeType": "454", "messageId": "455", "endLine": 238, "endColumn": 34, "suggestions": "517"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 239, "column": 31, "nodeType": "454", "messageId": "455", "endLine": 239, "endColumn": 34, "suggestions": "518"}, {"ruleId": "519", "severity": 1, "message": "520", "line": 242, "column": 35, "nodeType": "521", "messageId": "522", "endLine": 242, "endColumn": 37, "suggestions": "523"}, {"ruleId": "519", "severity": 1, "message": "520", "line": 243, "column": 39, "nodeType": "521", "messageId": "522", "endLine": 243, "endColumn": 41, "suggestions": "524"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 284, "column": 31, "nodeType": "454", "messageId": "455", "endLine": 284, "endColumn": 34, "suggestions": "525"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 69, "column": 13, "nodeType": "454", "messageId": "455", "endLine": 69, "endColumn": 16, "suggestions": "526"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 95, "column": 11, "nodeType": "454", "messageId": "455", "endLine": 95, "endColumn": 14, "suggestions": "527"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 116, "column": 9, "nodeType": "454", "messageId": "455", "endLine": 116, "endColumn": 12, "suggestions": "528"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 134, "column": 21, "nodeType": "454", "messageId": "455", "endLine": 134, "endColumn": 24, "suggestions": "529"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 173, "column": 13, "nodeType": "454", "messageId": "455", "endLine": 173, "endColumn": 16, "suggestions": "530"}, {"ruleId": "452", "severity": 1, "message": "453", "line": 223, "column": 13, "nodeType": "454", "messageId": "455", "endLine": 223, "endColumn": 16, "suggestions": "531"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["532", "533"], ["534", "535"], ["536", "537"], ["538", "539"], ["540", "541"], ["542", "543"], ["544", "545"], ["546", "547"], ["548", "549"], ["550", "551"], ["552", "553"], ["554", "555"], ["556", "557"], ["558", "559"], ["560", "561"], ["562", "563"], "@typescript-eslint/no-unused-vars", "'targetKeywords' is assigned a value but never used.", "unusedVar", ["564", "565"], ["566", "567"], ["568", "569"], ["570", "571"], ["572", "573"], ["574", "575"], "'CrispCommand' is defined but never used.", "'CrispData' is defined but never used.", "'CrispCallback' is defined but never used.", "'GtagEvent' is defined but never used.", ["576", "577"], ["578", "579"], ["580", "581"], ["582", "583"], ["584", "585"], ["586", "587"], ["588", "589"], ["590", "591"], ["592", "593"], ["594", "595"], ["596", "597"], ["598", "599"], ["600", "601"], ["602", "603"], ["604", "605"], ["606", "607"], ["608", "609"], ["610", "611"], ["612", "613"], ["614", "615"], ["616", "617"], ["618", "619"], ["620", "621"], ["622", "623"], ["624", "625"], ["626", "627"], ["628", "629"], ["630", "631"], ["632", "633"], ["634", "635"], ["636", "637"], ["638", "639"], ["640", "641"], ["642", "643"], "@typescript-eslint/no-empty-object-type", "The `{}` (\"empty object\") type allows any non-nullish value, including literals like `0` and `\"\"`.\n- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.\n- If you want a type meaning \"any object\", you probably want `object` instead.\n- If you want a type meaning \"any value\", you probably want `unknown` instead.", "TSTypeLiteral", "noEmptyObject", ["644", "645"], ["646", "647"], ["648", "649"], ["650", "651"], ["652", "653"], ["654", "655"], ["656", "657"], ["658", "659"], ["660", "661"], {"messageId": "662", "fix": "663", "desc": "664"}, {"messageId": "665", "fix": "666", "desc": "667"}, {"messageId": "662", "fix": "668", "desc": "664"}, {"messageId": "665", "fix": "669", "desc": "667"}, {"messageId": "662", "fix": "670", "desc": "664"}, {"messageId": "665", "fix": "671", "desc": "667"}, {"messageId": "662", "fix": "672", "desc": "664"}, {"messageId": "665", "fix": "673", "desc": "667"}, {"messageId": "662", "fix": "674", "desc": "664"}, {"messageId": "665", "fix": "675", "desc": "667"}, {"messageId": "662", "fix": "676", "desc": "664"}, {"messageId": "665", "fix": "677", "desc": "667"}, {"messageId": "662", "fix": "678", "desc": "664"}, {"messageId": "665", "fix": "679", "desc": "667"}, {"messageId": "662", "fix": "680", "desc": "664"}, {"messageId": "665", "fix": "681", "desc": "667"}, {"messageId": "662", "fix": "682", "desc": "664"}, {"messageId": "665", "fix": "683", "desc": "667"}, {"messageId": "662", "fix": "684", "desc": "664"}, {"messageId": "665", "fix": "685", "desc": "667"}, {"messageId": "662", "fix": "686", "desc": "664"}, {"messageId": "665", "fix": "687", "desc": "667"}, {"messageId": "662", "fix": "688", "desc": "664"}, {"messageId": "665", "fix": "689", "desc": "667"}, {"messageId": "662", "fix": "690", "desc": "664"}, {"messageId": "665", "fix": "691", "desc": "667"}, {"messageId": "662", "fix": "692", "desc": "664"}, {"messageId": "665", "fix": "693", "desc": "667"}, {"messageId": "662", "fix": "694", "desc": "664"}, {"messageId": "665", "fix": "695", "desc": "667"}, {"messageId": "662", "fix": "696", "desc": "664"}, {"messageId": "665", "fix": "697", "desc": "667"}, {"messageId": "662", "fix": "698", "desc": "664"}, {"messageId": "665", "fix": "699", "desc": "667"}, {"messageId": "662", "fix": "700", "desc": "664"}, {"messageId": "665", "fix": "701", "desc": "667"}, {"messageId": "662", "fix": "702", "desc": "664"}, {"messageId": "665", "fix": "703", "desc": "667"}, {"messageId": "662", "fix": "704", "desc": "664"}, {"messageId": "665", "fix": "705", "desc": "667"}, {"messageId": "662", "fix": "706", "desc": "664"}, {"messageId": "665", "fix": "707", "desc": "667"}, {"messageId": "662", "fix": "708", "desc": "664"}, {"messageId": "665", "fix": "709", "desc": "667"}, {"messageId": "662", "fix": "710", "desc": "664"}, {"messageId": "665", "fix": "711", "desc": "667"}, {"messageId": "662", "fix": "712", "desc": "664"}, {"messageId": "665", "fix": "713", "desc": "667"}, {"messageId": "662", "fix": "714", "desc": "664"}, {"messageId": "665", "fix": "715", "desc": "667"}, {"messageId": "662", "fix": "716", "desc": "664"}, {"messageId": "665", "fix": "717", "desc": "667"}, {"messageId": "662", "fix": "718", "desc": "664"}, {"messageId": "665", "fix": "719", "desc": "667"}, {"messageId": "662", "fix": "720", "desc": "664"}, {"messageId": "665", "fix": "721", "desc": "667"}, {"messageId": "662", "fix": "722", "desc": "664"}, {"messageId": "665", "fix": "723", "desc": "667"}, {"messageId": "662", "fix": "724", "desc": "664"}, {"messageId": "665", "fix": "725", "desc": "667"}, {"messageId": "662", "fix": "726", "desc": "664"}, {"messageId": "665", "fix": "727", "desc": "667"}, {"messageId": "662", "fix": "728", "desc": "664"}, {"messageId": "665", "fix": "729", "desc": "667"}, {"messageId": "662", "fix": "730", "desc": "664"}, {"messageId": "665", "fix": "731", "desc": "667"}, {"messageId": "662", "fix": "732", "desc": "664"}, {"messageId": "665", "fix": "733", "desc": "667"}, {"messageId": "662", "fix": "734", "desc": "664"}, {"messageId": "665", "fix": "735", "desc": "667"}, {"messageId": "662", "fix": "736", "desc": "664"}, {"messageId": "665", "fix": "737", "desc": "667"}, {"messageId": "662", "fix": "738", "desc": "664"}, {"messageId": "665", "fix": "739", "desc": "667"}, {"messageId": "662", "fix": "740", "desc": "664"}, {"messageId": "665", "fix": "741", "desc": "667"}, {"messageId": "662", "fix": "742", "desc": "664"}, {"messageId": "665", "fix": "743", "desc": "667"}, {"messageId": "662", "fix": "744", "desc": "664"}, {"messageId": "665", "fix": "745", "desc": "667"}, {"messageId": "662", "fix": "746", "desc": "664"}, {"messageId": "665", "fix": "747", "desc": "667"}, {"messageId": "662", "fix": "748", "desc": "664"}, {"messageId": "665", "fix": "749", "desc": "667"}, {"messageId": "662", "fix": "750", "desc": "664"}, {"messageId": "665", "fix": "751", "desc": "667"}, {"messageId": "662", "fix": "752", "desc": "664"}, {"messageId": "665", "fix": "753", "desc": "667"}, {"messageId": "662", "fix": "754", "desc": "664"}, {"messageId": "665", "fix": "755", "desc": "667"}, {"messageId": "662", "fix": "756", "desc": "664"}, {"messageId": "665", "fix": "757", "desc": "667"}, {"messageId": "662", "fix": "758", "desc": "664"}, {"messageId": "665", "fix": "759", "desc": "667"}, {"messageId": "662", "fix": "760", "desc": "664"}, {"messageId": "665", "fix": "761", "desc": "667"}, {"messageId": "662", "fix": "762", "desc": "664"}, {"messageId": "665", "fix": "763", "desc": "667"}, {"messageId": "662", "fix": "764", "desc": "664"}, {"messageId": "665", "fix": "765", "desc": "667"}, {"messageId": "662", "fix": "766", "desc": "664"}, {"messageId": "665", "fix": "767", "desc": "667"}, {"messageId": "662", "fix": "768", "desc": "664"}, {"messageId": "665", "fix": "769", "desc": "667"}, {"messageId": "662", "fix": "770", "desc": "664"}, {"messageId": "665", "fix": "771", "desc": "667"}, {"messageId": "662", "fix": "772", "desc": "664"}, {"messageId": "665", "fix": "773", "desc": "667"}, {"messageId": "662", "fix": "774", "desc": "664"}, {"messageId": "665", "fix": "775", "desc": "667"}, {"messageId": "662", "fix": "776", "desc": "664"}, {"messageId": "665", "fix": "777", "desc": "667"}, {"messageId": "778", "data": "779", "fix": "780", "desc": "781"}, {"messageId": "778", "data": "782", "fix": "783", "desc": "784"}, {"messageId": "778", "data": "785", "fix": "786", "desc": "781"}, {"messageId": "778", "data": "787", "fix": "788", "desc": "784"}, {"messageId": "662", "fix": "789", "desc": "664"}, {"messageId": "665", "fix": "790", "desc": "667"}, {"messageId": "662", "fix": "791", "desc": "664"}, {"messageId": "665", "fix": "792", "desc": "667"}, {"messageId": "662", "fix": "793", "desc": "664"}, {"messageId": "665", "fix": "794", "desc": "667"}, {"messageId": "662", "fix": "795", "desc": "664"}, {"messageId": "665", "fix": "796", "desc": "667"}, {"messageId": "662", "fix": "797", "desc": "664"}, {"messageId": "665", "fix": "798", "desc": "667"}, {"messageId": "662", "fix": "799", "desc": "664"}, {"messageId": "665", "fix": "800", "desc": "667"}, {"messageId": "662", "fix": "801", "desc": "664"}, {"messageId": "665", "fix": "802", "desc": "667"}, "suggestUnknown", {"range": "803", "text": "804"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "805", "text": "806"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "807", "text": "804"}, {"range": "808", "text": "806"}, {"range": "809", "text": "804"}, {"range": "810", "text": "806"}, {"range": "811", "text": "804"}, {"range": "812", "text": "806"}, {"range": "813", "text": "804"}, {"range": "814", "text": "806"}, {"range": "815", "text": "804"}, {"range": "816", "text": "806"}, {"range": "817", "text": "804"}, {"range": "818", "text": "806"}, {"range": "819", "text": "804"}, {"range": "820", "text": "806"}, {"range": "821", "text": "804"}, {"range": "822", "text": "806"}, {"range": "823", "text": "804"}, {"range": "824", "text": "806"}, {"range": "825", "text": "804"}, {"range": "826", "text": "806"}, {"range": "827", "text": "804"}, {"range": "828", "text": "806"}, {"range": "829", "text": "804"}, {"range": "830", "text": "806"}, {"range": "831", "text": "804"}, {"range": "832", "text": "806"}, {"range": "833", "text": "804"}, {"range": "834", "text": "806"}, {"range": "835", "text": "804"}, {"range": "836", "text": "806"}, {"range": "837", "text": "804"}, {"range": "838", "text": "806"}, {"range": "839", "text": "804"}, {"range": "840", "text": "806"}, {"range": "841", "text": "804"}, {"range": "842", "text": "806"}, {"range": "843", "text": "804"}, {"range": "844", "text": "806"}, {"range": "845", "text": "804"}, {"range": "846", "text": "806"}, {"range": "847", "text": "804"}, {"range": "848", "text": "806"}, {"range": "849", "text": "804"}, {"range": "850", "text": "806"}, {"range": "851", "text": "804"}, {"range": "852", "text": "806"}, {"range": "853", "text": "804"}, {"range": "854", "text": "806"}, {"range": "855", "text": "804"}, {"range": "856", "text": "806"}, {"range": "857", "text": "804"}, {"range": "858", "text": "806"}, {"range": "859", "text": "804"}, {"range": "860", "text": "806"}, {"range": "861", "text": "804"}, {"range": "862", "text": "806"}, {"range": "863", "text": "804"}, {"range": "864", "text": "806"}, {"range": "865", "text": "804"}, {"range": "866", "text": "806"}, {"range": "867", "text": "804"}, {"range": "868", "text": "806"}, {"range": "869", "text": "804"}, {"range": "870", "text": "806"}, {"range": "871", "text": "804"}, {"range": "872", "text": "806"}, {"range": "873", "text": "804"}, {"range": "874", "text": "806"}, {"range": "875", "text": "804"}, {"range": "876", "text": "806"}, {"range": "877", "text": "804"}, {"range": "878", "text": "806"}, {"range": "879", "text": "804"}, {"range": "880", "text": "806"}, {"range": "881", "text": "804"}, {"range": "882", "text": "806"}, {"range": "883", "text": "804"}, {"range": "884", "text": "806"}, {"range": "885", "text": "804"}, {"range": "886", "text": "806"}, {"range": "887", "text": "804"}, {"range": "888", "text": "806"}, {"range": "889", "text": "804"}, {"range": "890", "text": "806"}, {"range": "891", "text": "804"}, {"range": "892", "text": "806"}, {"range": "893", "text": "804"}, {"range": "894", "text": "806"}, {"range": "895", "text": "804"}, {"range": "896", "text": "806"}, {"range": "897", "text": "804"}, {"range": "898", "text": "806"}, {"range": "899", "text": "804"}, {"range": "900", "text": "806"}, {"range": "901", "text": "804"}, {"range": "902", "text": "806"}, {"range": "903", "text": "804"}, {"range": "904", "text": "806"}, {"range": "905", "text": "804"}, {"range": "906", "text": "806"}, {"range": "907", "text": "804"}, {"range": "908", "text": "806"}, {"range": "909", "text": "804"}, {"range": "910", "text": "806"}, {"range": "911", "text": "804"}, {"range": "912", "text": "806"}, {"range": "913", "text": "804"}, {"range": "914", "text": "806"}, {"range": "915", "text": "804"}, {"range": "916", "text": "806"}, "replaceEmptyObjectType", {"replacement": "917"}, {"range": "918", "text": "917"}, "Replace `{}` with `object`.", {"replacement": "804"}, {"range": "919", "text": "804"}, "Replace `{}` with `unknown`.", {"replacement": "917"}, {"range": "920", "text": "917"}, {"replacement": "804"}, {"range": "921", "text": "804"}, {"range": "922", "text": "804"}, {"range": "923", "text": "806"}, {"range": "924", "text": "804"}, {"range": "925", "text": "806"}, {"range": "926", "text": "804"}, {"range": "927", "text": "806"}, {"range": "928", "text": "804"}, {"range": "929", "text": "806"}, {"range": "930", "text": "804"}, {"range": "931", "text": "806"}, {"range": "932", "text": "804"}, {"range": "933", "text": "806"}, {"range": "934", "text": "804"}, {"range": "935", "text": "806"}, [262, 265], "unknown", [262, 265], "never", [294, 297], [294, 297], [584, 587], [584, 587], [614, 617], [614, 617], [260, 263], [260, 263], [292, 295], [292, 295], [582, 585], [582, 585], [612, 615], [612, 615], [147, 150], [147, 150], [3058, 3061], [3058, 3061], [3462, 3465], [3462, 3465], [1562, 1565], [1562, 1565], [1592, 1595], [1592, 1595], [413, 416], [413, 416], [2042, 2045], [2042, 2045], [2086, 2089], [2086, 2089], [750, 753], [750, 753], [1488, 1491], [1488, 1491], [2440, 2443], [2440, 2443], [1495, 1498], [1495, 1498], [4096, 4099], [4096, 4099], [4651, 4654], [4651, 4654], [316, 319], [316, 319], [550, 553], [550, 553], [1535, 1538], [1535, 1538], [6252, 6255], [6252, 6255], [6331, 6334], [6331, 6334], [6434, 6437], [6434, 6437], [6536, 6539], [6536, 6539], [6633, 6636], [6633, 6636], [6723, 6726], [6723, 6726], [1384, 1387], [1384, 1387], [1399, 1402], [1399, 1402], [2313, 2316], [2313, 2316], [2738, 2741], [2738, 2741], [2812, 2815], [2812, 2815], [2858, 2861], [2858, 2861], [3024, 3027], [3024, 3027], [3131, 3134], [3131, 3134], [3146, 3149], [3146, 3149], [3290, 3293], [3290, 3293], [2501, 2504], [2501, 2504], [2643, 2646], [2643, 2646], [2913, 2916], [2913, 2916], [3206, 3209], [3206, 3209], [3641, 3644], [3641, 3644], [3722, 3725], [3722, 3725], [4664, 4667], [4664, 4667], [4681, 4684], [4681, 4684], [4695, 4698], [4695, 4698], [4760, 4763], [4760, 4763], [4781, 4784], [4781, 4784], [4799, 4802], [4799, 4802], [4820, 4823], [4820, 4823], [5348, 5351], [5348, 5351], [5405, 5408], [5405, 5408], "object", [5520, 5522], [5520, 5522], [5600, 5602], [5600, 5602], [6406, 6409], [6406, 6409], [1494, 1497], [1494, 1497], [1981, 1984], [1981, 1984], [2376, 2379], [2376, 2379], [2716, 2719], [2716, 2719], [3452, 3455], [3452, 3455], [4330, 4333], [4330, 4333]]