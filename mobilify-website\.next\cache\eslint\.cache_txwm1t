[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\about\\page.tsx": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\api\\newsletter\\route.ts": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\blog\\page.tsx": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\blog\\[slug]\\page.tsx": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\faq\\FAQClient.tsx": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\faq\\page.tsx": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\layout.tsx": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\page.tsx": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\robots.ts": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\services\\page.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\sitemap.ts": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\AnimationWrapper.tsx": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ChatTrigger.tsx": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ClientOnly.tsx": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\CompanyValues.tsx": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\DarkModeToggle.tsx": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\Logo.tsx": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\Mission.tsx": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\NewsletterSignup.tsx": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\NoSSR.tsx": "20", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\OrganizationSchema.tsx": "21", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\PortableText.tsx": "22", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\PricingTable.tsx": "23", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ServicesFAQ.tsx": "24", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\SimpleDarkModeToggle.tsx": "25", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\SimpleFloatingChat.tsx": "26", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\SimpleHeaderChat.tsx": "27", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\StructuredData.tsx": "28", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\TeamProfiles.tsx": "29", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\Button.tsx": "30", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\Card.tsx": "31", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\index.ts": "32", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\Input.tsx": "33", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\__tests__\\Button.test.tsx": "34", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\__tests__\\Input.test.tsx": "35", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\__tests__\\Contact.test.tsx": "36", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\__tests__\\Header.test.tsx": "37", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\__tests__\\InteractiveDemo.test.tsx": "38", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\__tests__\\NewsletterSignup.test.tsx": "39", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\config\\site.ts": "40", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\contexts\\ThemeContext.tsx": "41", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\index.ts": "42", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\useAnalytics.ts": "43", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\useContactForm.ts": "44", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\useSiteSettings.ts": "45", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\lib\\metadata.ts": "46", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\lib\\sanity.ts": "47", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\lib\\utils.ts": "48", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\__tests__\\simple.test.js": "49", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\__tests__\\test-utils.tsx": "50", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\analytics\\CrispChat.tsx": "51", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\analytics\\GoogleAnalytics.tsx": "52", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\analytics\\index.ts": "53", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\analytics\\WebVitals.tsx": "54", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\Footer.tsx": "55", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\Header.tsx": "56", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\index.ts": "57", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\MobileMenu.tsx": "58", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\Navigation.tsx": "59", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\AboutSnippet.tsx": "60", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\Contact.tsx": "61", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\ContactForm.tsx": "62", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\DemoInput.tsx": "63", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\DemoPreview.tsx": "64", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\DemoTabs.tsx": "65", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\Hero.tsx": "66", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\index.ts": "67", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\InteractiveDemo.tsx": "68", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\Process.tsx": "69", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\ServicesOverview.tsx": "70", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\types\\analytics.ts": "71", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\types\\forms.ts": "72", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\types\\index.ts": "73", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\types\\sanity.ts": "74", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\FooterNav.tsx": "75", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\FooterNewsletter.tsx": "76", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\mocks\\handlers.ts": "77", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\mocks\\server.ts": "78"}, {"size": 2067, "mtime": 1751679940296, "results": "79", "hashOfConfig": "80"}, {"size": 3626, "mtime": 1751505504335, "results": "81", "hashOfConfig": "80"}, {"size": 10127, "mtime": 1751680016774, "results": "82", "hashOfConfig": "80"}, {"size": 9800, "mtime": 1751682806140, "results": "83", "hashOfConfig": "80"}, {"size": 12228, "mtime": 1751680051540, "results": "84", "hashOfConfig": "80"}, {"size": 5797, "mtime": 1751577101014, "results": "85", "hashOfConfig": "80"}, {"size": 3587, "mtime": 1751676697710, "results": "86", "hashOfConfig": "80"}, {"size": 1922, "mtime": 1751676758569, "results": "87", "hashOfConfig": "80"}, {"size": 415, "mtime": 1751664165349, "results": "88", "hashOfConfig": "80"}, {"size": 2090, "mtime": 1751676794792, "results": "89", "hashOfConfig": "80"}, {"size": 1525, "mtime": 1751682854554, "results": "90", "hashOfConfig": "80"}, {"size": 615, "mtime": 1751680147823, "results": "91", "hashOfConfig": "80"}, {"size": 4638, "mtime": 1751680166678, "results": "92", "hashOfConfig": "80"}, {"size": 447, "mtime": 1750993163336, "results": "93", "hashOfConfig": "80"}, {"size": 4013, "mtime": 1751680290044, "results": "94", "hashOfConfig": "80"}, {"size": 4806, "mtime": 1751511499987, "results": "95", "hashOfConfig": "80"}, {"size": 315, "mtime": 1750990215277, "results": "96", "hashOfConfig": "80"}, {"size": 1715, "mtime": 1751680446533, "results": "97", "hashOfConfig": "80"}, {"size": 6878, "mtime": 1751695730383, "results": "98", "hashOfConfig": "80"}, {"size": 415, "mtime": 1750998195180, "results": "99", "hashOfConfig": "80"}, {"size": 2390, "mtime": 1751578724741, "results": "100", "hashOfConfig": "80"}, {"size": 6780, "mtime": 1751681909643, "results": "101", "hashOfConfig": "80"}, {"size": 7692, "mtime": 1751698012673, "results": "102", "hashOfConfig": "80"}, {"size": 6402, "mtime": 1751682202896, "results": "103", "hashOfConfig": "80"}, {"size": 1149, "mtime": 1751518090332, "results": "104", "hashOfConfig": "80"}, {"size": 1498, "mtime": 1751518229057, "results": "105", "hashOfConfig": "80"}, {"size": 1145, "mtime": 1751518286486, "results": "106", "hashOfConfig": "80"}, {"size": 4809, "mtime": 1751664389950, "results": "107", "hashOfConfig": "80"}, {"size": 4256, "mtime": 1751690904523, "results": "108", "hashOfConfig": "80"}, {"size": 3690, "mtime": 1751681415689, "results": "109", "hashOfConfig": "80"}, {"size": 1834, "mtime": 1751575325593, "results": "110", "hashOfConfig": "80"}, {"size": 300, "mtime": 1751575364754, "results": "111", "hashOfConfig": "80"}, {"size": 4150, "mtime": 1751695194505, "results": "112", "hashOfConfig": "80"}, {"size": 8324, "mtime": 1751662637625, "results": "113", "hashOfConfig": "80"}, {"size": 9432, "mtime": 1751695279853, "results": "114", "hashOfConfig": "80"}, {"size": 8687, "mtime": 1751682225196, "results": "115", "hashOfConfig": "80"}, {"size": 9021, "mtime": 1751662773289, "results": "116", "hashOfConfig": "80"}, {"size": 10368, "mtime": 1751682246654, "results": "117", "hashOfConfig": "80"}, {"size": 12399, "mtime": 1751696110659, "results": "118", "hashOfConfig": "80"}, {"size": 6036, "mtime": 1751670335025, "results": "119", "hashOfConfig": "80"}, {"size": 3477, "mtime": 1751515726939, "results": "120", "hashOfConfig": "80"}, {"size": 551, "mtime": 1751670473668, "results": "121", "hashOfConfig": "80"}, {"size": 5664, "mtime": 1751683325256, "results": "122", "hashOfConfig": "80"}, {"size": 6690, "mtime": 1751671807702, "results": "123", "hashOfConfig": "80"}, {"size": 5933, "mtime": 1751671826897, "results": "124", "hashOfConfig": "80"}, {"size": 5445, "mtime": 1751682311965, "results": "125", "hashOfConfig": "80"}, {"size": 7002, "mtime": 1751696794822, "results": "126", "hashOfConfig": "80"}, {"size": 169, "mtime": 1751574258441, "results": "127", "hashOfConfig": "80"}, {"size": 232, "mtime": 1751663733726, "results": "128", "hashOfConfig": "80"}, {"size": 5645, "mtime": 1751662735678, "results": "129", "hashOfConfig": "80"}, {"size": 6145, "mtime": 1751698089298, "results": "130", "hashOfConfig": "80"}, {"size": 970, "mtime": 1751679975840, "results": "131", "hashOfConfig": "80"}, {"size": 461, "mtime": 1751676842124, "results": "132", "hashOfConfig": "80"}, {"size": 2650, "mtime": 1751664824565, "results": "133", "hashOfConfig": "80"}, {"size": 2651, "mtime": 1751681389366, "results": "134", "hashOfConfig": "80"}, {"size": 2827, "mtime": 1751681223785, "results": "135", "hashOfConfig": "80"}, {"size": 626, "mtime": 1751678612776, "results": "136", "hashOfConfig": "80"}, {"size": 3774, "mtime": 1751683113819, "results": "137", "hashOfConfig": "80"}, {"size": 3090, "mtime": 1751698137144, "results": "138", "hashOfConfig": "80"}, {"size": 2458, "mtime": 1751682079226, "results": "139", "hashOfConfig": "80"}, {"size": 1852, "mtime": 1751676812928, "results": "140", "hashOfConfig": "80"}, {"size": 9857, "mtime": 1751681372306, "results": "141", "hashOfConfig": "80"}, {"size": 4625, "mtime": 1751681328775, "results": "142", "hashOfConfig": "80"}, {"size": 7824, "mtime": 1751683149524, "results": "143", "hashOfConfig": "80"}, {"size": 3290, "mtime": 1751681311593, "results": "144", "hashOfConfig": "80"}, {"size": 4130, "mtime": 1751670521401, "results": "145", "hashOfConfig": "80"}, {"size": 685, "mtime": 1751676834675, "results": "146", "hashOfConfig": "80"}, {"size": 4185, "mtime": 1751683282671, "results": "147", "hashOfConfig": "80"}, {"size": 4837, "mtime": 1751677422096, "results": "148", "hashOfConfig": "80"}, {"size": 4495, "mtime": 1751682115179, "results": "149", "hashOfConfig": "80"}, {"size": 8012, "mtime": 1751676639727, "results": "150", "hashOfConfig": "80"}, {"size": 6032, "mtime": 1751682546493, "results": "151", "hashOfConfig": "80"}, {"size": 6626, "mtime": 1751676680436, "results": "152", "hashOfConfig": "80"}, {"size": 4695, "mtime": 1751676570640, "results": "153", "hashOfConfig": "80"}, {"size": 3104, "mtime": 1751680327855, "results": "154", "hashOfConfig": "80"}, {"size": 5517, "mtime": 1751682972890, "results": "155", "hashOfConfig": "80"}, {"size": 2062, "mtime": 1751696960691, "results": "156", "hashOfConfig": "80"}, {"size": 466, "mtime": 1751696973397, "results": "157", "hashOfConfig": "80"}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "10vcq7z", {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\about\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\api\\newsletter\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\blog\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\blog\\[slug]\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\faq\\FAQClient.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\faq\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\robots.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\services\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\sitemap.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\AnimationWrapper.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ChatTrigger.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ClientOnly.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\CompanyValues.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\DarkModeToggle.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\Logo.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\Mission.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\NewsletterSignup.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\NoSSR.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\OrganizationSchema.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\PortableText.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\PricingTable.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ServicesFAQ.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\SimpleDarkModeToggle.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\SimpleFloatingChat.tsx", ["392", "393", "394", "395"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\SimpleHeaderChat.tsx", ["396", "397", "398", "399"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\StructuredData.tsx", ["400", "401", "402"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\TeamProfiles.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\Button.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\Card.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\Input.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\__tests__\\Button.test.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\__tests__\\Input.test.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\__tests__\\Contact.test.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\__tests__\\Header.test.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\__tests__\\InteractiveDemo.test.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\__tests__\\NewsletterSignup.test.tsx", ["403"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\config\\site.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\contexts\\ThemeContext.tsx", ["404", "405"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\useAnalytics.ts", ["406", "407", "408"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\useContactForm.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\useSiteSettings.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\lib\\metadata.ts", ["409"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\lib\\sanity.ts", ["410", "411", "412"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\__tests__\\simple.test.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\__tests__\\test-utils.tsx", ["413", "414", "415"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\analytics\\CrispChat.tsx", ["416", "417", "418", "419"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\analytics\\GoogleAnalytics.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\analytics\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\analytics\\WebVitals.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\Footer.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\MobileMenu.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\Navigation.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\AboutSnippet.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\Contact.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\ContactForm.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\DemoInput.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\DemoPreview.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\DemoTabs.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\Hero.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\InteractiveDemo.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\Process.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\ServicesOverview.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\types\\analytics.ts", ["420", "421", "422", "423", "424", "425", "426", "427", "428"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\types\\forms.ts", ["429", "430", "431", "432", "433", "434", "435", "436", "437", "438"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\types\\index.ts", ["439", "440", "441", "442", "443", "444", "445", "446", "447", "448", "449", "450", "451", "452", "453", "454", "455", "456"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\types\\sanity.ts", ["457", "458", "459", "460", "461", "462"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\FooterNav.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\FooterNewsletter.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\mocks\\handlers.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\mocks\\server.ts", [], [], {"ruleId": "463", "severity": 1, "message": "464", "line": 9, "column": 53, "nodeType": "465", "messageId": "466", "endLine": 9, "endColumn": 56, "suggestions": "467"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 10, "column": 18, "nodeType": "465", "messageId": "466", "endLine": 10, "endColumn": 21, "suggestions": "468"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 17, "column": 53, "nodeType": "465", "messageId": "466", "endLine": 17, "endColumn": 56, "suggestions": "469"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 18, "column": 18, "nodeType": "465", "messageId": "466", "endLine": 18, "endColumn": 21, "suggestions": "470"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 9, "column": 53, "nodeType": "465", "messageId": "466", "endLine": 9, "endColumn": 56, "suggestions": "471"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 10, "column": 18, "nodeType": "465", "messageId": "466", "endLine": 10, "endColumn": 21, "suggestions": "472"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 17, "column": 53, "nodeType": "465", "messageId": "466", "endLine": 17, "endColumn": 56, "suggestions": "473"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 18, "column": 18, "nodeType": "465", "messageId": "466", "endLine": 18, "endColumn": 21, "suggestions": "474"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 5, "column": 10, "nodeType": "465", "messageId": "466", "endLine": 5, "endColumn": 13, "suggestions": "475"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 89, "column": 47, "nodeType": "465", "messageId": "466", "endLine": 89, "endColumn": 50, "suggestions": "476"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 103, "column": 60, "nodeType": "465", "messageId": "466", "endLine": 103, "endColumn": 63, "suggestions": "477"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 333, "column": 21, "nodeType": "465", "messageId": "466", "endLine": 333, "endColumn": 24, "suggestions": "478"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 57, "column": 53, "nodeType": "465", "messageId": "466", "endLine": 57, "endColumn": 56, "suggestions": "479"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 58, "column": 18, "nodeType": "465", "messageId": "466", "endLine": 58, "endColumn": 21, "suggestions": "480"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 17, "column": 38, "nodeType": "465", "messageId": "466", "endLine": 17, "endColumn": 41, "suggestions": "481"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 75, "column": 53, "nodeType": "465", "messageId": "466", "endLine": 75, "endColumn": 56, "suggestions": "482"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 77, "column": 20, "nodeType": "465", "messageId": "466", "endLine": 77, "endColumn": 23, "suggestions": "483"}, {"ruleId": "484", "severity": 1, "message": "485", "line": 28, "column": 3, "nodeType": null, "messageId": "486", "endLine": 28, "endColumn": 17}, {"ruleId": "463", "severity": 1, "message": "464", "line": 31, "column": 32, "nodeType": "465", "messageId": "466", "endLine": 31, "endColumn": 35, "suggestions": "487"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 71, "column": 9, "nodeType": "465", "messageId": "466", "endLine": 71, "endColumn": 12, "suggestions": "488"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 118, "column": 11, "nodeType": "465", "messageId": "466", "endLine": 118, "endColumn": 14, "suggestions": "489"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 59, "column": 37, "nodeType": "465", "messageId": "466", "endLine": 59, "endColumn": 40, "suggestions": "490"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 160, "column": 66, "nodeType": "465", "messageId": "466", "endLine": 160, "endColumn": 69, "suggestions": "491"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 177, "column": 75, "nodeType": "465", "messageId": "466", "endLine": 177, "endColumn": 78, "suggestions": "492"}, {"ruleId": "484", "severity": 1, "message": "493", "line": 7, "column": 6, "nodeType": null, "messageId": "486", "endLine": 7, "endColumn": 18}, {"ruleId": "484", "severity": 1, "message": "494", "line": 8, "column": 6, "nodeType": null, "messageId": "486", "endLine": 8, "endColumn": 15}, {"ruleId": "484", "severity": 1, "message": "495", "line": 9, "column": 6, "nodeType": null, "messageId": "486", "endLine": 9, "endColumn": 19}, {"ruleId": "484", "severity": 1, "message": "496", "line": 12, "column": 6, "nodeType": null, "messageId": "486", "endLine": 12, "endColumn": 15}, {"ruleId": "463", "severity": 1, "message": "464", "line": 11, "column": 31, "nodeType": "465", "messageId": "466", "endLine": 11, "endColumn": 34, "suggestions": "497"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 23, "column": 38, "nodeType": "465", "messageId": "466", "endLine": 23, "endColumn": 41, "suggestions": "498"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 64, "column": 38, "nodeType": "465", "messageId": "466", "endLine": 64, "endColumn": 41, "suggestions": "499"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 228, "column": 63, "nodeType": "465", "messageId": "466", "endLine": 228, "endColumn": 66, "suggestions": "500"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 229, "column": 65, "nodeType": "465", "messageId": "466", "endLine": 229, "endColumn": 68, "suggestions": "501"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 230, "column": 89, "nodeType": "465", "messageId": "466", "endLine": 230, "endColumn": 92, "suggestions": "502"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 231, "column": 88, "nodeType": "465", "messageId": "466", "endLine": 231, "endColumn": 91, "suggestions": "503"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 232, "column": 83, "nodeType": "465", "messageId": "466", "endLine": 232, "endColumn": 86, "suggestions": "504"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 233, "column": 76, "nodeType": "465", "messageId": "466", "endLine": 233, "endColumn": 79, "suggestions": "505"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 67, "column": 36, "nodeType": "465", "messageId": "466", "endLine": 67, "endColumn": 39, "suggestions": "506"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 67, "column": 51, "nodeType": "465", "messageId": "466", "endLine": 67, "endColumn": 54, "suggestions": "507"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 104, "column": 37, "nodeType": "465", "messageId": "466", "endLine": 104, "endColumn": 40, "suggestions": "508"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 122, "column": 10, "nodeType": "465", "messageId": "466", "endLine": 122, "endColumn": 13, "suggestions": "509"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 126, "column": 44, "nodeType": "465", "messageId": "466", "endLine": 126, "endColumn": 47, "suggestions": "510"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 127, "column": 40, "nodeType": "465", "messageId": "466", "endLine": 127, "endColumn": 43, "suggestions": "511"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 131, "column": 45, "nodeType": "465", "messageId": "466", "endLine": 131, "endColumn": 48, "suggestions": "512"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 135, "column": 40, "nodeType": "465", "messageId": "466", "endLine": 135, "endColumn": 43, "suggestions": "513"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 135, "column": 55, "nodeType": "465", "messageId": "466", "endLine": 135, "endColumn": 58, "suggestions": "514"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 139, "column": 47, "nodeType": "465", "messageId": "466", "endLine": 139, "endColumn": 50, "suggestions": "515"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 98, "column": 34, "nodeType": "465", "messageId": "466", "endLine": 98, "endColumn": 37, "suggestions": "516"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 106, "column": 40, "nodeType": "465", "messageId": "466", "endLine": 106, "endColumn": 43, "suggestions": "517"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 121, "column": 13, "nodeType": "465", "messageId": "466", "endLine": 121, "endColumn": 16, "suggestions": "518"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 135, "column": 33, "nodeType": "465", "messageId": "466", "endLine": 135, "endColumn": 36, "suggestions": "519"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 160, "column": 35, "nodeType": "465", "messageId": "466", "endLine": 160, "endColumn": 38, "suggestions": "520"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 164, "column": 27, "nodeType": "465", "messageId": "466", "endLine": 164, "endColumn": 30, "suggestions": "521"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 208, "column": 13, "nodeType": "465", "messageId": "466", "endLine": 208, "endColumn": 16, "suggestions": "522"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 209, "column": 13, "nodeType": "465", "messageId": "466", "endLine": 209, "endColumn": 16, "suggestions": "523"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 210, "column": 10, "nodeType": "465", "messageId": "466", "endLine": 210, "endColumn": 13, "suggestions": "524"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 212, "column": 29, "nodeType": "465", "messageId": "466", "endLine": 212, "endColumn": 32, "suggestions": "525"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 213, "column": 16, "nodeType": "465", "messageId": "466", "endLine": 213, "endColumn": 19, "suggestions": "526"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 214, "column": 14, "nodeType": "465", "messageId": "466", "endLine": 214, "endColumn": 17, "suggestions": "527"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 215, "column": 17, "nodeType": "465", "messageId": "466", "endLine": 215, "endColumn": 20, "suggestions": "528"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 238, "column": 31, "nodeType": "465", "messageId": "466", "endLine": 238, "endColumn": 34, "suggestions": "529"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 239, "column": 31, "nodeType": "465", "messageId": "466", "endLine": 239, "endColumn": 34, "suggestions": "530"}, {"ruleId": "531", "severity": 1, "message": "532", "line": 242, "column": 35, "nodeType": "533", "messageId": "534", "endLine": 242, "endColumn": 37, "suggestions": "535"}, {"ruleId": "531", "severity": 1, "message": "532", "line": 243, "column": 39, "nodeType": "533", "messageId": "534", "endLine": 243, "endColumn": 41, "suggestions": "536"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 284, "column": 31, "nodeType": "465", "messageId": "466", "endLine": 284, "endColumn": 34, "suggestions": "537"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 69, "column": 13, "nodeType": "465", "messageId": "466", "endLine": 69, "endColumn": 16, "suggestions": "538"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 95, "column": 11, "nodeType": "465", "messageId": "466", "endLine": 95, "endColumn": 14, "suggestions": "539"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 116, "column": 9, "nodeType": "465", "messageId": "466", "endLine": 116, "endColumn": 12, "suggestions": "540"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 134, "column": 21, "nodeType": "465", "messageId": "466", "endLine": 134, "endColumn": 24, "suggestions": "541"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 173, "column": 13, "nodeType": "465", "messageId": "466", "endLine": 173, "endColumn": 16, "suggestions": "542"}, {"ruleId": "463", "severity": 1, "message": "464", "line": 223, "column": 13, "nodeType": "465", "messageId": "466", "endLine": 223, "endColumn": 16, "suggestions": "543"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["544", "545"], ["546", "547"], ["548", "549"], ["550", "551"], ["552", "553"], ["554", "555"], ["556", "557"], ["558", "559"], ["560", "561"], ["562", "563"], ["564", "565"], ["566", "567"], ["568", "569"], ["570", "571"], ["572", "573"], ["574", "575"], ["576", "577"], "@typescript-eslint/no-unused-vars", "'targetKeywords' is assigned a value but never used.", "unusedVar", ["578", "579"], ["580", "581"], ["582", "583"], ["584", "585"], ["586", "587"], ["588", "589"], "'CrispCommand' is defined but never used.", "'CrispData' is defined but never used.", "'CrispCallback' is defined but never used.", "'GtagEvent' is defined but never used.", ["590", "591"], ["592", "593"], ["594", "595"], ["596", "597"], ["598", "599"], ["600", "601"], ["602", "603"], ["604", "605"], ["606", "607"], ["608", "609"], ["610", "611"], ["612", "613"], ["614", "615"], ["616", "617"], ["618", "619"], ["620", "621"], ["622", "623"], ["624", "625"], ["626", "627"], ["628", "629"], ["630", "631"], ["632", "633"], ["634", "635"], ["636", "637"], ["638", "639"], ["640", "641"], ["642", "643"], ["644", "645"], ["646", "647"], ["648", "649"], ["650", "651"], ["652", "653"], ["654", "655"], ["656", "657"], "@typescript-eslint/no-empty-object-type", "The `{}` (\"empty object\") type allows any non-nullish value, including literals like `0` and `\"\"`.\n- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.\n- If you want a type meaning \"any object\", you probably want `object` instead.\n- If you want a type meaning \"any value\", you probably want `unknown` instead.", "TSTypeLiteral", "noEmptyObject", ["658", "659"], ["660", "661"], ["662", "663"], ["664", "665"], ["666", "667"], ["668", "669"], ["670", "671"], ["672", "673"], ["674", "675"], {"messageId": "676", "fix": "677", "desc": "678"}, {"messageId": "679", "fix": "680", "desc": "681"}, {"messageId": "676", "fix": "682", "desc": "678"}, {"messageId": "679", "fix": "683", "desc": "681"}, {"messageId": "676", "fix": "684", "desc": "678"}, {"messageId": "679", "fix": "685", "desc": "681"}, {"messageId": "676", "fix": "686", "desc": "678"}, {"messageId": "679", "fix": "687", "desc": "681"}, {"messageId": "676", "fix": "688", "desc": "678"}, {"messageId": "679", "fix": "689", "desc": "681"}, {"messageId": "676", "fix": "690", "desc": "678"}, {"messageId": "679", "fix": "691", "desc": "681"}, {"messageId": "676", "fix": "692", "desc": "678"}, {"messageId": "679", "fix": "693", "desc": "681"}, {"messageId": "676", "fix": "694", "desc": "678"}, {"messageId": "679", "fix": "695", "desc": "681"}, {"messageId": "676", "fix": "696", "desc": "678"}, {"messageId": "679", "fix": "697", "desc": "681"}, {"messageId": "676", "fix": "698", "desc": "678"}, {"messageId": "679", "fix": "699", "desc": "681"}, {"messageId": "676", "fix": "700", "desc": "678"}, {"messageId": "679", "fix": "701", "desc": "681"}, {"messageId": "676", "fix": "702", "desc": "678"}, {"messageId": "679", "fix": "703", "desc": "681"}, {"messageId": "676", "fix": "704", "desc": "678"}, {"messageId": "679", "fix": "705", "desc": "681"}, {"messageId": "676", "fix": "706", "desc": "678"}, {"messageId": "679", "fix": "707", "desc": "681"}, {"messageId": "676", "fix": "708", "desc": "678"}, {"messageId": "679", "fix": "709", "desc": "681"}, {"messageId": "676", "fix": "710", "desc": "678"}, {"messageId": "679", "fix": "711", "desc": "681"}, {"messageId": "676", "fix": "712", "desc": "678"}, {"messageId": "679", "fix": "713", "desc": "681"}, {"messageId": "676", "fix": "714", "desc": "678"}, {"messageId": "679", "fix": "715", "desc": "681"}, {"messageId": "676", "fix": "716", "desc": "678"}, {"messageId": "679", "fix": "717", "desc": "681"}, {"messageId": "676", "fix": "718", "desc": "678"}, {"messageId": "679", "fix": "719", "desc": "681"}, {"messageId": "676", "fix": "720", "desc": "678"}, {"messageId": "679", "fix": "721", "desc": "681"}, {"messageId": "676", "fix": "722", "desc": "678"}, {"messageId": "679", "fix": "723", "desc": "681"}, {"messageId": "676", "fix": "724", "desc": "678"}, {"messageId": "679", "fix": "725", "desc": "681"}, {"messageId": "676", "fix": "726", "desc": "678"}, {"messageId": "679", "fix": "727", "desc": "681"}, {"messageId": "676", "fix": "728", "desc": "678"}, {"messageId": "679", "fix": "729", "desc": "681"}, {"messageId": "676", "fix": "730", "desc": "678"}, {"messageId": "679", "fix": "731", "desc": "681"}, {"messageId": "676", "fix": "732", "desc": "678"}, {"messageId": "679", "fix": "733", "desc": "681"}, {"messageId": "676", "fix": "734", "desc": "678"}, {"messageId": "679", "fix": "735", "desc": "681"}, {"messageId": "676", "fix": "736", "desc": "678"}, {"messageId": "679", "fix": "737", "desc": "681"}, {"messageId": "676", "fix": "738", "desc": "678"}, {"messageId": "679", "fix": "739", "desc": "681"}, {"messageId": "676", "fix": "740", "desc": "678"}, {"messageId": "679", "fix": "741", "desc": "681"}, {"messageId": "676", "fix": "742", "desc": "678"}, {"messageId": "679", "fix": "743", "desc": "681"}, {"messageId": "676", "fix": "744", "desc": "678"}, {"messageId": "679", "fix": "745", "desc": "681"}, {"messageId": "676", "fix": "746", "desc": "678"}, {"messageId": "679", "fix": "747", "desc": "681"}, {"messageId": "676", "fix": "748", "desc": "678"}, {"messageId": "679", "fix": "749", "desc": "681"}, {"messageId": "676", "fix": "750", "desc": "678"}, {"messageId": "679", "fix": "751", "desc": "681"}, {"messageId": "676", "fix": "752", "desc": "678"}, {"messageId": "679", "fix": "753", "desc": "681"}, {"messageId": "676", "fix": "754", "desc": "678"}, {"messageId": "679", "fix": "755", "desc": "681"}, {"messageId": "676", "fix": "756", "desc": "678"}, {"messageId": "679", "fix": "757", "desc": "681"}, {"messageId": "676", "fix": "758", "desc": "678"}, {"messageId": "679", "fix": "759", "desc": "681"}, {"messageId": "676", "fix": "760", "desc": "678"}, {"messageId": "679", "fix": "761", "desc": "681"}, {"messageId": "676", "fix": "762", "desc": "678"}, {"messageId": "679", "fix": "763", "desc": "681"}, {"messageId": "676", "fix": "764", "desc": "678"}, {"messageId": "679", "fix": "765", "desc": "681"}, {"messageId": "676", "fix": "766", "desc": "678"}, {"messageId": "679", "fix": "767", "desc": "681"}, {"messageId": "676", "fix": "768", "desc": "678"}, {"messageId": "679", "fix": "769", "desc": "681"}, {"messageId": "676", "fix": "770", "desc": "678"}, {"messageId": "679", "fix": "771", "desc": "681"}, {"messageId": "676", "fix": "772", "desc": "678"}, {"messageId": "679", "fix": "773", "desc": "681"}, {"messageId": "676", "fix": "774", "desc": "678"}, {"messageId": "679", "fix": "775", "desc": "681"}, {"messageId": "676", "fix": "776", "desc": "678"}, {"messageId": "679", "fix": "777", "desc": "681"}, {"messageId": "676", "fix": "778", "desc": "678"}, {"messageId": "679", "fix": "779", "desc": "681"}, {"messageId": "676", "fix": "780", "desc": "678"}, {"messageId": "679", "fix": "781", "desc": "681"}, {"messageId": "676", "fix": "782", "desc": "678"}, {"messageId": "679", "fix": "783", "desc": "681"}, {"messageId": "676", "fix": "784", "desc": "678"}, {"messageId": "679", "fix": "785", "desc": "681"}, {"messageId": "676", "fix": "786", "desc": "678"}, {"messageId": "679", "fix": "787", "desc": "681"}, {"messageId": "676", "fix": "788", "desc": "678"}, {"messageId": "679", "fix": "789", "desc": "681"}, {"messageId": "676", "fix": "790", "desc": "678"}, {"messageId": "679", "fix": "791", "desc": "681"}, {"messageId": "676", "fix": "792", "desc": "678"}, {"messageId": "679", "fix": "793", "desc": "681"}, {"messageId": "794", "data": "795", "fix": "796", "desc": "797"}, {"messageId": "794", "data": "798", "fix": "799", "desc": "800"}, {"messageId": "794", "data": "801", "fix": "802", "desc": "797"}, {"messageId": "794", "data": "803", "fix": "804", "desc": "800"}, {"messageId": "676", "fix": "805", "desc": "678"}, {"messageId": "679", "fix": "806", "desc": "681"}, {"messageId": "676", "fix": "807", "desc": "678"}, {"messageId": "679", "fix": "808", "desc": "681"}, {"messageId": "676", "fix": "809", "desc": "678"}, {"messageId": "679", "fix": "810", "desc": "681"}, {"messageId": "676", "fix": "811", "desc": "678"}, {"messageId": "679", "fix": "812", "desc": "681"}, {"messageId": "676", "fix": "813", "desc": "678"}, {"messageId": "679", "fix": "814", "desc": "681"}, {"messageId": "676", "fix": "815", "desc": "678"}, {"messageId": "679", "fix": "816", "desc": "681"}, {"messageId": "676", "fix": "817", "desc": "678"}, {"messageId": "679", "fix": "818", "desc": "681"}, "suggestUnknown", {"range": "819", "text": "820"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "821", "text": "822"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "823", "text": "820"}, {"range": "824", "text": "822"}, {"range": "825", "text": "820"}, {"range": "826", "text": "822"}, {"range": "827", "text": "820"}, {"range": "828", "text": "822"}, {"range": "829", "text": "820"}, {"range": "830", "text": "822"}, {"range": "831", "text": "820"}, {"range": "832", "text": "822"}, {"range": "833", "text": "820"}, {"range": "834", "text": "822"}, {"range": "835", "text": "820"}, {"range": "836", "text": "822"}, {"range": "837", "text": "820"}, {"range": "838", "text": "822"}, {"range": "839", "text": "820"}, {"range": "840", "text": "822"}, {"range": "841", "text": "820"}, {"range": "842", "text": "822"}, {"range": "843", "text": "820"}, {"range": "844", "text": "822"}, {"range": "845", "text": "820"}, {"range": "846", "text": "822"}, {"range": "847", "text": "820"}, {"range": "848", "text": "822"}, {"range": "849", "text": "820"}, {"range": "850", "text": "822"}, {"range": "851", "text": "820"}, {"range": "852", "text": "822"}, {"range": "853", "text": "820"}, {"range": "854", "text": "822"}, {"range": "855", "text": "820"}, {"range": "856", "text": "822"}, {"range": "857", "text": "820"}, {"range": "858", "text": "822"}, {"range": "859", "text": "820"}, {"range": "860", "text": "822"}, {"range": "861", "text": "820"}, {"range": "862", "text": "822"}, {"range": "863", "text": "820"}, {"range": "864", "text": "822"}, {"range": "865", "text": "820"}, {"range": "866", "text": "822"}, {"range": "867", "text": "820"}, {"range": "868", "text": "822"}, {"range": "869", "text": "820"}, {"range": "870", "text": "822"}, {"range": "871", "text": "820"}, {"range": "872", "text": "822"}, {"range": "873", "text": "820"}, {"range": "874", "text": "822"}, {"range": "875", "text": "820"}, {"range": "876", "text": "822"}, {"range": "877", "text": "820"}, {"range": "878", "text": "822"}, {"range": "879", "text": "820"}, {"range": "880", "text": "822"}, {"range": "881", "text": "820"}, {"range": "882", "text": "822"}, {"range": "883", "text": "820"}, {"range": "884", "text": "822"}, {"range": "885", "text": "820"}, {"range": "886", "text": "822"}, {"range": "887", "text": "820"}, {"range": "888", "text": "822"}, {"range": "889", "text": "820"}, {"range": "890", "text": "822"}, {"range": "891", "text": "820"}, {"range": "892", "text": "822"}, {"range": "893", "text": "820"}, {"range": "894", "text": "822"}, {"range": "895", "text": "820"}, {"range": "896", "text": "822"}, {"range": "897", "text": "820"}, {"range": "898", "text": "822"}, {"range": "899", "text": "820"}, {"range": "900", "text": "822"}, {"range": "901", "text": "820"}, {"range": "902", "text": "822"}, {"range": "903", "text": "820"}, {"range": "904", "text": "822"}, {"range": "905", "text": "820"}, {"range": "906", "text": "822"}, {"range": "907", "text": "820"}, {"range": "908", "text": "822"}, {"range": "909", "text": "820"}, {"range": "910", "text": "822"}, {"range": "911", "text": "820"}, {"range": "912", "text": "822"}, {"range": "913", "text": "820"}, {"range": "914", "text": "822"}, {"range": "915", "text": "820"}, {"range": "916", "text": "822"}, {"range": "917", "text": "820"}, {"range": "918", "text": "822"}, {"range": "919", "text": "820"}, {"range": "920", "text": "822"}, {"range": "921", "text": "820"}, {"range": "922", "text": "822"}, {"range": "923", "text": "820"}, {"range": "924", "text": "822"}, {"range": "925", "text": "820"}, {"range": "926", "text": "822"}, {"range": "927", "text": "820"}, {"range": "928", "text": "822"}, {"range": "929", "text": "820"}, {"range": "930", "text": "822"}, {"range": "931", "text": "820"}, {"range": "932", "text": "822"}, {"range": "933", "text": "820"}, {"range": "934", "text": "822"}, "replaceEmptyObjectType", {"replacement": "935"}, {"range": "936", "text": "935"}, "Replace `{}` with `object`.", {"replacement": "820"}, {"range": "937", "text": "820"}, "Replace `{}` with `unknown`.", {"replacement": "935"}, {"range": "938", "text": "935"}, {"replacement": "820"}, {"range": "939", "text": "820"}, {"range": "940", "text": "820"}, {"range": "941", "text": "822"}, {"range": "942", "text": "820"}, {"range": "943", "text": "822"}, {"range": "944", "text": "820"}, {"range": "945", "text": "822"}, {"range": "946", "text": "820"}, {"range": "947", "text": "822"}, {"range": "948", "text": "820"}, {"range": "949", "text": "822"}, {"range": "950", "text": "820"}, {"range": "951", "text": "822"}, {"range": "952", "text": "820"}, {"range": "953", "text": "822"}, [262, 265], "unknown", [262, 265], "never", [294, 297], [294, 297], [584, 587], [584, 587], [614, 617], [614, 617], [260, 263], [260, 263], [292, 295], [292, 295], [582, 585], [582, 585], [612, 615], [612, 615], [147, 150], [147, 150], [3058, 3061], [3058, 3061], [3462, 3465], [3462, 3465], [12072, 12075], [12072, 12075], [1562, 1565], [1562, 1565], [1592, 1595], [1592, 1595], [413, 416], [413, 416], [2042, 2045], [2042, 2045], [2086, 2089], [2086, 2089], [1206, 1209], [1206, 1209], [1944, 1947], [1944, 1947], [2896, 2899], [2896, 2899], [1495, 1498], [1495, 1498], [4096, 4099], [4096, 4099], [4651, 4654], [4651, 4654], [316, 319], [316, 319], [550, 553], [550, 553], [1535, 1538], [1535, 1538], [6252, 6255], [6252, 6255], [6331, 6334], [6331, 6334], [6434, 6437], [6434, 6437], [6536, 6539], [6536, 6539], [6633, 6636], [6633, 6636], [6723, 6726], [6723, 6726], [1384, 1387], [1384, 1387], [1399, 1402], [1399, 1402], [2313, 2316], [2313, 2316], [2738, 2741], [2738, 2741], [2812, 2815], [2812, 2815], [2858, 2861], [2858, 2861], [3024, 3027], [3024, 3027], [3131, 3134], [3131, 3134], [3146, 3149], [3146, 3149], [3290, 3293], [3290, 3293], [2501, 2504], [2501, 2504], [2643, 2646], [2643, 2646], [2913, 2916], [2913, 2916], [3206, 3209], [3206, 3209], [3641, 3644], [3641, 3644], [3722, 3725], [3722, 3725], [4664, 4667], [4664, 4667], [4681, 4684], [4681, 4684], [4695, 4698], [4695, 4698], [4760, 4763], [4760, 4763], [4781, 4784], [4781, 4784], [4799, 4802], [4799, 4802], [4820, 4823], [4820, 4823], [5348, 5351], [5348, 5351], [5405, 5408], [5405, 5408], "object", [5520, 5522], [5520, 5522], [5600, 5602], [5600, 5602], [6406, 6409], [6406, 6409], [1494, 1497], [1494, 1497], [1981, 1984], [1981, 1984], [2376, 2379], [2376, 2379], [2716, 2719], [2716, 2719], [3452, 3455], [3452, 3455], [4330, 4333], [4330, 4333]]