{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/event-source-polyfill/src/eventsource.js"], "sourcesContent": ["/** @license\r\n * eventsource.js\r\n * Available under MIT License (MIT)\r\n * https://github.com/Yaffle/EventSource/\r\n */\r\n\r\n/*jslint indent: 2, vars: true, plusplus: true */\r\n/*global setTimeout, clearTimeout */\r\n\r\n(function (global) {\r\n  \"use strict\";\r\n\r\n  var setTimeout = global.setTimeout;\r\n  var clearTimeout = global.clearTimeout;\r\n  var XMLHttpRequest = global.XMLHttpRequest;\r\n  var XDomainRequest = global.XDomainRequest;\r\n  var ActiveXObject = global.ActiveXObject;\r\n  var NativeEventSource = global.EventSource;\r\n\r\n  var document = global.document;\r\n  var Promise = global.Promise;\r\n  var fetch = global.fetch;\r\n  var Response = global.Response;\r\n  var TextDecoder = global.TextDecoder;\r\n  var TextEncoder = global.TextEncoder;\r\n  var AbortController = global.AbortController;\r\n\r\n  if (typeof window !== \"undefined\" && typeof document !== \"undefined\" && !(\"readyState\" in document) && document.body == null) { // Firefox 2\r\n    document.readyState = \"loading\";\r\n    window.addEventListener(\"load\", function (event) {\r\n      document.readyState = \"complete\";\r\n    }, false);\r\n  }\r\n\r\n  if (XMLHttpRequest == null && ActiveXObject != null) { // https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/Using_XMLHttpRequest_in_IE6\r\n    XMLHttpRequest = function () {\r\n      return new ActiveXObject(\"Microsoft.XMLHTTP\");\r\n    };\r\n  }\r\n\r\n  if (Object.create == undefined) {\r\n    Object.create = function (C) {\r\n      function F(){}\r\n      F.prototype = C;\r\n      return new F();\r\n    };\r\n  }\r\n\r\n  if (!Date.now) {\r\n    Date.now = function now() {\r\n      return new Date().getTime();\r\n    };\r\n  }\r\n\r\n  // see #118 (Promise#finally with polyfilled Promise)\r\n  // see #123 (data URLs crash Edge)\r\n  // see #125 (CSP violations)\r\n  // see pull/#138\r\n  // => No way to polyfill Promise#finally\r\n\r\n  if (AbortController == undefined) {\r\n    var originalFetch2 = fetch;\r\n    fetch = function (url, options) {\r\n      var signal = options.signal;\r\n      return originalFetch2(url, {headers: options.headers, credentials: options.credentials, cache: options.cache}).then(function (response) {\r\n        var reader = response.body.getReader();\r\n        signal._reader = reader;\r\n        if (signal._aborted) {\r\n          signal._reader.cancel();\r\n        }\r\n        return {\r\n          status: response.status,\r\n          statusText: response.statusText,\r\n          headers: response.headers,\r\n          body: {\r\n            getReader: function () {\r\n              return reader;\r\n            }\r\n          }\r\n        };\r\n      });\r\n    };\r\n    AbortController = function () {\r\n      this.signal = {\r\n        _reader: null,\r\n        _aborted: false\r\n      };\r\n      this.abort = function () {\r\n        if (this.signal._reader != null) {\r\n          this.signal._reader.cancel();\r\n        }\r\n        this.signal._aborted = true;\r\n      };\r\n    };\r\n  }\r\n\r\n  function TextDecoderPolyfill() {\r\n    this.bitsNeeded = 0;\r\n    this.codePoint = 0;\r\n  }\r\n\r\n  TextDecoderPolyfill.prototype.decode = function (octets) {\r\n    function valid(codePoint, shift, octetsCount) {\r\n      if (octetsCount === 1) {\r\n        return codePoint >= 0x0080 >> shift && codePoint << shift <= 0x07FF;\r\n      }\r\n      if (octetsCount === 2) {\r\n        return codePoint >= 0x0800 >> shift && codePoint << shift <= 0xD7FF || codePoint >= 0xE000 >> shift && codePoint << shift <= 0xFFFF;\r\n      }\r\n      if (octetsCount === 3) {\r\n        return codePoint >= 0x010000 >> shift && codePoint << shift <= 0x10FFFF;\r\n      }\r\n      throw new Error();\r\n    }\r\n    function octetsCount(bitsNeeded, codePoint) {\r\n      if (bitsNeeded === 6 * 1) {\r\n        return codePoint >> 6 > 15 ? 3 : codePoint > 31 ? 2 : 1;\r\n      }\r\n      if (bitsNeeded === 6 * 2) {\r\n        return codePoint > 15 ? 3 : 2;\r\n      }\r\n      if (bitsNeeded === 6 * 3) {\r\n        return 3;\r\n      }\r\n      throw new Error();\r\n    }\r\n    var REPLACER = 0xFFFD;\r\n    var string = \"\";\r\n    var bitsNeeded = this.bitsNeeded;\r\n    var codePoint = this.codePoint;\r\n    for (var i = 0; i < octets.length; i += 1) {\r\n      var octet = octets[i];\r\n      if (bitsNeeded !== 0) {\r\n        if (octet < 128 || octet > 191 || !valid(codePoint << 6 | octet & 63, bitsNeeded - 6, octetsCount(bitsNeeded, codePoint))) {\r\n          bitsNeeded = 0;\r\n          codePoint = REPLACER;\r\n          string += String.fromCharCode(codePoint);\r\n        }\r\n      }\r\n      if (bitsNeeded === 0) {\r\n        if (octet >= 0 && octet <= 127) {\r\n          bitsNeeded = 0;\r\n          codePoint = octet;\r\n        } else if (octet >= 192 && octet <= 223) {\r\n          bitsNeeded = 6 * 1;\r\n          codePoint = octet & 31;\r\n        } else if (octet >= 224 && octet <= 239) {\r\n          bitsNeeded = 6 * 2;\r\n          codePoint = octet & 15;\r\n        } else if (octet >= 240 && octet <= 247) {\r\n          bitsNeeded = 6 * 3;\r\n          codePoint = octet & 7;\r\n        } else {\r\n          bitsNeeded = 0;\r\n          codePoint = REPLACER;\r\n        }\r\n        if (bitsNeeded !== 0 && !valid(codePoint, bitsNeeded, octetsCount(bitsNeeded, codePoint))) {\r\n          bitsNeeded = 0;\r\n          codePoint = REPLACER;\r\n        }\r\n      } else {\r\n        bitsNeeded -= 6;\r\n        codePoint = codePoint << 6 | octet & 63;\r\n      }\r\n      if (bitsNeeded === 0) {\r\n        if (codePoint <= 0xFFFF) {\r\n          string += String.fromCharCode(codePoint);\r\n        } else {\r\n          string += String.fromCharCode(0xD800 + (codePoint - 0xFFFF - 1 >> 10));\r\n          string += String.fromCharCode(0xDC00 + (codePoint - 0xFFFF - 1 & 0x3FF));\r\n        }\r\n      }\r\n    }\r\n    this.bitsNeeded = bitsNeeded;\r\n    this.codePoint = codePoint;\r\n    return string;\r\n  };\r\n\r\n  // Firefox < 38 throws an error with stream option\r\n  var supportsStreamOption = function () {\r\n    try {\r\n      return new TextDecoder().decode(new TextEncoder().encode(\"test\"), {stream: true}) === \"test\";\r\n    } catch (error) {\r\n      console.debug(\"TextDecoder does not support streaming option. Using polyfill instead: \" + error);\r\n    }\r\n    return false;\r\n  };\r\n\r\n  // IE, Edge\r\n  if (TextDecoder == undefined || TextEncoder == undefined || !supportsStreamOption()) {\r\n    TextDecoder = TextDecoderPolyfill;\r\n  }\r\n\r\n  var k = function () {\r\n  };\r\n\r\n  function XHRWrapper(xhr) {\r\n    this.withCredentials = false;\r\n    this.readyState = 0;\r\n    this.status = 0;\r\n    this.statusText = \"\";\r\n    this.responseText = \"\";\r\n    this.onprogress = k;\r\n    this.onload = k;\r\n    this.onerror = k;\r\n    this.onreadystatechange = k;\r\n    this._contentType = \"\";\r\n    this._xhr = xhr;\r\n    this._sendTimeout = 0;\r\n    this._abort = k;\r\n  }\r\n\r\n  XHRWrapper.prototype.open = function (method, url) {\r\n    this._abort(true);\r\n\r\n    var that = this;\r\n    var xhr = this._xhr;\r\n    var state = 1;\r\n    var timeout = 0;\r\n\r\n    this._abort = function (silent) {\r\n      if (that._sendTimeout !== 0) {\r\n        clearTimeout(that._sendTimeout);\r\n        that._sendTimeout = 0;\r\n      }\r\n      if (state === 1 || state === 2 || state === 3) {\r\n        state = 4;\r\n        xhr.onload = k;\r\n        xhr.onerror = k;\r\n        xhr.onabort = k;\r\n        xhr.onprogress = k;\r\n        xhr.onreadystatechange = k;\r\n        // IE 8 - 9: XDomainRequest#abort() does not fire any event\r\n        // Opera < 10: XMLHttpRequest#abort() does not fire any event\r\n        xhr.abort();\r\n        if (timeout !== 0) {\r\n          clearTimeout(timeout);\r\n          timeout = 0;\r\n        }\r\n        if (!silent) {\r\n          that.readyState = 4;\r\n          that.onabort(null);\r\n          that.onreadystatechange();\r\n        }\r\n      }\r\n      state = 0;\r\n    };\r\n\r\n    var onStart = function () {\r\n      if (state === 1) {\r\n        //state = 2;\r\n        var status = 0;\r\n        var statusText = \"\";\r\n        var contentType = undefined;\r\n        if (!(\"contentType\" in xhr)) {\r\n          try {\r\n            status = xhr.status;\r\n            statusText = xhr.statusText;\r\n            contentType = xhr.getResponseHeader(\"Content-Type\");\r\n          } catch (error) {\r\n            // IE < 10 throws exception for `xhr.status` when xhr.readyState === 2 || xhr.readyState === 3\r\n            // Opera < 11 throws exception for `xhr.status` when xhr.readyState === 2\r\n            // https://bugs.webkit.org/show_bug.cgi?id=29121\r\n            status = 0;\r\n            statusText = \"\";\r\n            contentType = undefined;\r\n            // Firefox < 14, Chrome ?, Safari ?\r\n            // https://bugs.webkit.org/show_bug.cgi?id=29658\r\n            // https://bugs.webkit.org/show_bug.cgi?id=77854\r\n          }\r\n        } else {\r\n          status = 200;\r\n          statusText = \"OK\";\r\n          contentType = xhr.contentType;\r\n        }\r\n        if (status !== 0) {\r\n          state = 2;\r\n          that.readyState = 2;\r\n          that.status = status;\r\n          that.statusText = statusText;\r\n          that._contentType = contentType;\r\n          that.onreadystatechange();\r\n        }\r\n      }\r\n    };\r\n    var onProgress = function () {\r\n      onStart();\r\n      if (state === 2 || state === 3) {\r\n        state = 3;\r\n        var responseText = \"\";\r\n        try {\r\n          responseText = xhr.responseText;\r\n        } catch (error) {\r\n          // IE 8 - 9 with XMLHttpRequest\r\n        }\r\n        that.readyState = 3;\r\n        that.responseText = responseText;\r\n        that.onprogress();\r\n      }\r\n    };\r\n    var onFinish = function (type, event) {\r\n      if (event == null || event.preventDefault == null) {\r\n        event = {\r\n          preventDefault: k\r\n        };\r\n      }\r\n      // Firefox 52 fires \"readystatechange\" (xhr.readyState === 4) without final \"readystatechange\" (xhr.readyState === 3)\r\n      // IE 8 fires \"onload\" without \"onprogress\"\r\n      onProgress();\r\n      if (state === 1 || state === 2 || state === 3) {\r\n        state = 4;\r\n        if (timeout !== 0) {\r\n          clearTimeout(timeout);\r\n          timeout = 0;\r\n        }\r\n        that.readyState = 4;\r\n        if (type === \"load\") {\r\n          that.onload(event);\r\n        } else if (type === \"error\") {\r\n          that.onerror(event);\r\n        } else if (type === \"abort\") {\r\n          that.onabort(event);\r\n        } else {\r\n          throw new TypeError();\r\n        }\r\n        that.onreadystatechange();\r\n      }\r\n    };\r\n    var onReadyStateChange = function (event) {\r\n      if (xhr != undefined) { // Opera 12\r\n        if (xhr.readyState === 4) {\r\n          if (!(\"onload\" in xhr) || !(\"onerror\" in xhr) || !(\"onabort\" in xhr)) {\r\n            onFinish(xhr.responseText === \"\" ? \"error\" : \"load\", event);\r\n          }\r\n        } else if (xhr.readyState === 3) {\r\n          if (!(\"onprogress\" in xhr)) { // testing XMLHttpRequest#responseText too many times is too slow in IE 11\r\n            // and in Firefox 3.6\r\n            onProgress();\r\n          }\r\n        } else if (xhr.readyState === 2) {\r\n          onStart();\r\n        }\r\n      }\r\n    };\r\n    var onTimeout = function () {\r\n      timeout = setTimeout(function () {\r\n        onTimeout();\r\n      }, 500);\r\n      if (xhr.readyState === 3) {\r\n        onProgress();\r\n      }\r\n    };\r\n\r\n    // XDomainRequest#abort removes onprogress, onerror, onload\r\n    if (\"onload\" in xhr) {\r\n      xhr.onload = function (event) {\r\n        onFinish(\"load\", event);\r\n      };\r\n    }\r\n    if (\"onerror\" in xhr) {\r\n      xhr.onerror = function (event) {\r\n        onFinish(\"error\", event);\r\n      };\r\n    }\r\n    // improper fix to match Firefox behaviour, but it is better than just ignore abort\r\n    // see https://bugzilla.mozilla.org/show_bug.cgi?id=768596\r\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=880200\r\n    // https://code.google.com/p/chromium/issues/detail?id=153570\r\n    // IE 8 fires \"onload\" without \"onprogress\r\n    if (\"onabort\" in xhr) {\r\n      xhr.onabort = function (event) {\r\n        onFinish(\"abort\", event);\r\n      };\r\n    }\r\n\r\n    if (\"onprogress\" in xhr) {\r\n      xhr.onprogress = onProgress;\r\n    }\r\n\r\n    // IE 8 - 9 (XMLHTTPRequest)\r\n    // Opera < 12\r\n    // Firefox < 3.5\r\n    // Firefox 3.5 - 3.6 - ? < 9.0\r\n    // onprogress is not fired sometimes or delayed\r\n    // see also #64 (significant lag in IE 11)\r\n    if (\"onreadystatechange\" in xhr) {\r\n      xhr.onreadystatechange = function (event) {\r\n        onReadyStateChange(event);\r\n      };\r\n    }\r\n\r\n    if (\"contentType\" in xhr || !(\"ontimeout\" in XMLHttpRequest.prototype)) {\r\n      url += (url.indexOf(\"?\") === -1 ? \"?\" : \"&\") + \"padding=true\";\r\n    }\r\n    xhr.open(method, url, true);\r\n\r\n    if (\"readyState\" in xhr) {\r\n      // workaround for Opera 12 issue with \"progress\" events\r\n      // #91 (XMLHttpRequest onprogress not fired for streaming response in Edge 14-15-?)\r\n      timeout = setTimeout(function () {\r\n        onTimeout();\r\n      }, 0);\r\n    }\r\n  };\r\n  XHRWrapper.prototype.abort = function () {\r\n    this._abort(false);\r\n  };\r\n  XHRWrapper.prototype.getResponseHeader = function (name) {\r\n    return this._contentType;\r\n  };\r\n  XHRWrapper.prototype.setRequestHeader = function (name, value) {\r\n    var xhr = this._xhr;\r\n    if (\"setRequestHeader\" in xhr) {\r\n      xhr.setRequestHeader(name, value);\r\n    }\r\n  };\r\n  XHRWrapper.prototype.getAllResponseHeaders = function () {\r\n    // XMLHttpRequest#getAllResponseHeaders returns null for CORS requests in Firefox 3.6.28\r\n    return this._xhr.getAllResponseHeaders != undefined ? this._xhr.getAllResponseHeaders() || \"\" : \"\";\r\n  };\r\n  XHRWrapper.prototype.send = function () {\r\n    // loading indicator in Safari < ? (6), Chrome < 14, Firefox\r\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=736723\r\n    if ((!(\"ontimeout\" in XMLHttpRequest.prototype) || (!(\"sendAsBinary\" in XMLHttpRequest.prototype) && !(\"mozAnon\" in XMLHttpRequest.prototype))) &&\r\n        document != undefined &&\r\n        document.readyState != undefined &&\r\n        document.readyState !== \"complete\") {\r\n      var that = this;\r\n      that._sendTimeout = setTimeout(function () {\r\n        that._sendTimeout = 0;\r\n        that.send();\r\n      }, 4);\r\n      return;\r\n    }\r\n\r\n    var xhr = this._xhr;\r\n    // withCredentials should be set after \"open\" for Safari and Chrome (< 19 ?)\r\n    if (\"withCredentials\" in xhr) {\r\n      xhr.withCredentials = this.withCredentials;\r\n    }\r\n    try {\r\n      // xhr.send(); throws \"Not enough arguments\" in Firefox 3.0\r\n      xhr.send(undefined);\r\n    } catch (error1) {\r\n      // Safari 5.1.7, Opera 12\r\n      throw error1;\r\n    }\r\n  };\r\n\r\n  function toLowerCase(name) {\r\n    return name.replace(/[A-Z]/g, function (c) {\r\n      return String.fromCharCode(c.charCodeAt(0) + 0x20);\r\n    });\r\n  }\r\n\r\n  function HeadersPolyfill(all) {\r\n    // Get headers: implemented according to mozilla's example code: https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/getAllResponseHeaders#Example\r\n    var map = Object.create(null);\r\n    var array = all.split(\"\\r\\n\");\r\n    for (var i = 0; i < array.length; i += 1) {\r\n      var line = array[i];\r\n      var parts = line.split(\": \");\r\n      var name = parts.shift();\r\n      var value = parts.join(\": \");\r\n      map[toLowerCase(name)] = value;\r\n    }\r\n    this._map = map;\r\n  }\r\n  HeadersPolyfill.prototype.get = function (name) {\r\n    return this._map[toLowerCase(name)];\r\n  };\r\n\r\n  if (XMLHttpRequest != null && XMLHttpRequest.HEADERS_RECEIVED == null) { // IE < 9, Firefox 3.6\r\n    XMLHttpRequest.HEADERS_RECEIVED = 2;\r\n  }\r\n\r\n  function XHRTransport() {\r\n  }\r\n\r\n  XHRTransport.prototype.open = function (xhr, onStartCallback, onProgressCallback, onFinishCallback, url, withCredentials, headers) {\r\n    xhr.open(\"GET\", url);\r\n    var offset = 0;\r\n    xhr.onprogress = function () {\r\n      var responseText = xhr.responseText;\r\n      var chunk = responseText.slice(offset);\r\n      offset += chunk.length;\r\n      onProgressCallback(chunk);\r\n    };\r\n    xhr.onerror = function (event) {\r\n      event.preventDefault();\r\n      onFinishCallback(new Error(\"NetworkError\"));\r\n    };\r\n    xhr.onload = function () {\r\n      onFinishCallback(null);\r\n    };\r\n    xhr.onabort = function () {\r\n      onFinishCallback(null);\r\n    };\r\n    xhr.onreadystatechange = function () {\r\n      if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {\r\n        var status = xhr.status;\r\n        var statusText = xhr.statusText;\r\n        var contentType = xhr.getResponseHeader(\"Content-Type\");\r\n        var headers = xhr.getAllResponseHeaders();\r\n        onStartCallback(status, statusText, contentType, new HeadersPolyfill(headers));\r\n      }\r\n    };\r\n    xhr.withCredentials = withCredentials;\r\n    for (var name in headers) {\r\n      if (Object.prototype.hasOwnProperty.call(headers, name)) {\r\n        xhr.setRequestHeader(name, headers[name]);\r\n      }\r\n    }\r\n    xhr.send();\r\n    return xhr;\r\n  };\r\n\r\n  function HeadersWrapper(headers) {\r\n    this._headers = headers;\r\n  }\r\n  HeadersWrapper.prototype.get = function (name) {\r\n    return this._headers.get(name);\r\n  };\r\n\r\n  function FetchTransport() {\r\n  }\r\n\r\n  FetchTransport.prototype.open = function (xhr, onStartCallback, onProgressCallback, onFinishCallback, url, withCredentials, headers) {\r\n    var reader = null;\r\n    var controller = new AbortController();\r\n    var signal = controller.signal;\r\n    var textDecoder = new TextDecoder();\r\n    fetch(url, {\r\n      headers: headers,\r\n      credentials: withCredentials ? \"include\" : \"same-origin\",\r\n      signal: signal,\r\n      cache: \"no-store\"\r\n    }).then(function (response) {\r\n      reader = response.body.getReader();\r\n      onStartCallback(response.status, response.statusText, response.headers.get(\"Content-Type\"), new HeadersWrapper(response.headers));\r\n      // see https://github.com/promises-aplus/promises-spec/issues/179\r\n      return new Promise(function (resolve, reject) {\r\n        var readNextChunk = function () {\r\n          reader.read().then(function (result) {\r\n            if (result.done) {\r\n              //Note: bytes in textDecoder are ignored\r\n              resolve(undefined);\r\n            } else {\r\n              var chunk = textDecoder.decode(result.value, {stream: true});\r\n              onProgressCallback(chunk);\r\n              readNextChunk();\r\n            }\r\n          })[\"catch\"](function (error) {\r\n            reject(error);\r\n          });\r\n        };\r\n        readNextChunk();\r\n      });\r\n    })[\"catch\"](function (error) {\r\n      if (error.name === \"AbortError\") {\r\n        return undefined;\r\n      } else {\r\n        return error;\r\n      }\r\n    }).then(function (error) {\r\n      onFinishCallback(error);\r\n    });\r\n    return {\r\n      abort: function () {\r\n        if (reader != null) {\r\n          reader.cancel(); // https://bugzilla.mozilla.org/show_bug.cgi?id=1583815\r\n        }\r\n        controller.abort();\r\n      }\r\n    };\r\n  };\r\n\r\n  function EventTarget() {\r\n    this._listeners = Object.create(null);\r\n  }\r\n\r\n  function throwError(e) {\r\n    setTimeout(function () {\r\n      throw e;\r\n    }, 0);\r\n  }\r\n\r\n  EventTarget.prototype.dispatchEvent = function (event) {\r\n    event.target = this;\r\n    var typeListeners = this._listeners[event.type];\r\n    if (typeListeners != undefined) {\r\n      var length = typeListeners.length;\r\n      for (var i = 0; i < length; i += 1) {\r\n        var listener = typeListeners[i];\r\n        try {\r\n          if (typeof listener.handleEvent === \"function\") {\r\n            listener.handleEvent(event);\r\n          } else {\r\n            listener.call(this, event);\r\n          }\r\n        } catch (e) {\r\n          throwError(e);\r\n        }\r\n      }\r\n    }\r\n  };\r\n  EventTarget.prototype.addEventListener = function (type, listener) {\r\n    type = String(type);\r\n    var listeners = this._listeners;\r\n    var typeListeners = listeners[type];\r\n    if (typeListeners == undefined) {\r\n      typeListeners = [];\r\n      listeners[type] = typeListeners;\r\n    }\r\n    var found = false;\r\n    for (var i = 0; i < typeListeners.length; i += 1) {\r\n      if (typeListeners[i] === listener) {\r\n        found = true;\r\n      }\r\n    }\r\n    if (!found) {\r\n      typeListeners.push(listener);\r\n    }\r\n  };\r\n  EventTarget.prototype.removeEventListener = function (type, listener) {\r\n    type = String(type);\r\n    var listeners = this._listeners;\r\n    var typeListeners = listeners[type];\r\n    if (typeListeners != undefined) {\r\n      var filtered = [];\r\n      for (var i = 0; i < typeListeners.length; i += 1) {\r\n        if (typeListeners[i] !== listener) {\r\n          filtered.push(typeListeners[i]);\r\n        }\r\n      }\r\n      if (filtered.length === 0) {\r\n        delete listeners[type];\r\n      } else {\r\n        listeners[type] = filtered;\r\n      }\r\n    }\r\n  };\r\n\r\n  function Event(type) {\r\n    this.type = type;\r\n    this.target = undefined;\r\n  }\r\n\r\n  function MessageEvent(type, options) {\r\n    Event.call(this, type);\r\n    this.data = options.data;\r\n    this.lastEventId = options.lastEventId;\r\n  }\r\n\r\n  MessageEvent.prototype = Object.create(Event.prototype);\r\n\r\n  function ConnectionEvent(type, options) {\r\n    Event.call(this, type);\r\n    this.status = options.status;\r\n    this.statusText = options.statusText;\r\n    this.headers = options.headers;\r\n  }\r\n\r\n  ConnectionEvent.prototype = Object.create(Event.prototype);\r\n\r\n  function ErrorEvent(type, options) {\r\n    Event.call(this, type);\r\n    this.error = options.error;\r\n  }\r\n\r\n  ErrorEvent.prototype = Object.create(Event.prototype);\r\n\r\n  var WAITING = -1;\r\n  var CONNECTING = 0;\r\n  var OPEN = 1;\r\n  var CLOSED = 2;\r\n\r\n  var AFTER_CR = -1;\r\n  var FIELD_START = 0;\r\n  var FIELD = 1;\r\n  var VALUE_START = 2;\r\n  var VALUE = 3;\r\n\r\n  var contentTypeRegExp = /^text\\/event\\-stream(;.*)?$/i;\r\n\r\n  var MINIMUM_DURATION = 1000;\r\n  var MAXIMUM_DURATION = 18000000;\r\n\r\n  var parseDuration = function (value, def) {\r\n    var n = value == null ? def : parseInt(value, 10);\r\n    if (n !== n) {\r\n      n = def;\r\n    }\r\n    return clampDuration(n);\r\n  };\r\n  var clampDuration = function (n) {\r\n    return Math.min(Math.max(n, MINIMUM_DURATION), MAXIMUM_DURATION);\r\n  };\r\n\r\n  var fire = function (that, f, event) {\r\n    try {\r\n      if (typeof f === \"function\") {\r\n        f.call(that, event);\r\n      }\r\n    } catch (e) {\r\n      throwError(e);\r\n    }\r\n  };\r\n\r\n  function EventSourcePolyfill(url, options) {\r\n    EventTarget.call(this);\r\n    options = options || {};\r\n\r\n    this.onopen = undefined;\r\n    this.onmessage = undefined;\r\n    this.onerror = undefined;\r\n\r\n    this.url = undefined;\r\n    this.readyState = undefined;\r\n    this.withCredentials = undefined;\r\n    this.headers = undefined;\r\n\r\n    this._close = undefined;\r\n\r\n    start(this, url, options);\r\n  }\r\n\r\n  function getBestXHRTransport() {\r\n    return (XMLHttpRequest != undefined && (\"withCredentials\" in XMLHttpRequest.prototype)) || XDomainRequest == undefined\r\n        ? new XMLHttpRequest()\r\n        : new XDomainRequest();\r\n  }\r\n\r\n  var isFetchSupported = fetch != undefined && Response != undefined && \"body\" in Response.prototype;\r\n\r\n  function start(es, url, options) {\r\n    url = String(url);\r\n    var withCredentials = Boolean(options.withCredentials);\r\n    var lastEventIdQueryParameterName = options.lastEventIdQueryParameterName || \"lastEventId\";\r\n\r\n    var initialRetry = clampDuration(1000);\r\n    var heartbeatTimeout = parseDuration(options.heartbeatTimeout, 45000);\r\n\r\n    var lastEventId = \"\";\r\n    var retry = initialRetry;\r\n    var wasActivity = false;\r\n    var textLength = 0;\r\n    var headers = options.headers || {};\r\n    var TransportOption = options.Transport;\r\n    var xhr = isFetchSupported && TransportOption == undefined ? undefined : new XHRWrapper(TransportOption != undefined ? new TransportOption() : getBestXHRTransport());\r\n    var transport = TransportOption != null && typeof TransportOption !== \"string\" ? new TransportOption() : (xhr == undefined ? new FetchTransport() : new XHRTransport());\r\n    var abortController = undefined;\r\n    var timeout = 0;\r\n    var currentState = WAITING;\r\n    var dataBuffer = \"\";\r\n    var lastEventIdBuffer = \"\";\r\n    var eventTypeBuffer = \"\";\r\n\r\n    var textBuffer = \"\";\r\n    var state = FIELD_START;\r\n    var fieldStart = 0;\r\n    var valueStart = 0;\r\n\r\n    var onStart = function (status, statusText, contentType, headers) {\r\n      if (currentState === CONNECTING) {\r\n        if (status === 200 && contentType != undefined && contentTypeRegExp.test(contentType)) {\r\n          currentState = OPEN;\r\n          wasActivity = Date.now();\r\n          retry = initialRetry;\r\n          es.readyState = OPEN;\r\n          var event = new ConnectionEvent(\"open\", {\r\n            status: status,\r\n            statusText: statusText,\r\n            headers: headers\r\n          });\r\n          es.dispatchEvent(event);\r\n          fire(es, es.onopen, event);\r\n        } else {\r\n          var message = \"\";\r\n          if (status !== 200) {\r\n            if (statusText) {\r\n              statusText = statusText.replace(/\\s+/g, \" \");\r\n            }\r\n            message = \"EventSource's response has a status \" + status + \" \" + statusText + \" that is not 200. Aborting the connection.\";\r\n          } else {\r\n            message = \"EventSource's response has a Content-Type specifying an unsupported type: \" + (contentType == undefined ? \"-\" : contentType.replace(/\\s+/g, \" \")) + \". Aborting the connection.\";\r\n          }\r\n          close();\r\n          var event = new ConnectionEvent(\"error\", {\r\n            status: status,\r\n            statusText: statusText,\r\n            headers: headers\r\n          });\r\n          es.dispatchEvent(event);\r\n          fire(es, es.onerror, event);\r\n          console.error(message);\r\n        }\r\n      }\r\n    };\r\n\r\n    var onProgress = function (textChunk) {\r\n      if (currentState === OPEN) {\r\n        var n = -1;\r\n        for (var i = 0; i < textChunk.length; i += 1) {\r\n          var c = textChunk.charCodeAt(i);\r\n          if (c === \"\\n\".charCodeAt(0) || c === \"\\r\".charCodeAt(0)) {\r\n            n = i;\r\n          }\r\n        }\r\n        var chunk = (n !== -1 ? textBuffer : \"\") + textChunk.slice(0, n + 1);\r\n        textBuffer = (n === -1 ? textBuffer : \"\") + textChunk.slice(n + 1);\r\n        if (textChunk !== \"\") {\r\n          wasActivity = Date.now();\r\n          textLength += textChunk.length;\r\n        }\r\n        for (var position = 0; position < chunk.length; position += 1) {\r\n          var c = chunk.charCodeAt(position);\r\n          if (state === AFTER_CR && c === \"\\n\".charCodeAt(0)) {\r\n            state = FIELD_START;\r\n          } else {\r\n            if (state === AFTER_CR) {\r\n              state = FIELD_START;\r\n            }\r\n            if (c === \"\\r\".charCodeAt(0) || c === \"\\n\".charCodeAt(0)) {\r\n              if (state !== FIELD_START) {\r\n                if (state === FIELD) {\r\n                  valueStart = position + 1;\r\n                }\r\n                var field = chunk.slice(fieldStart, valueStart - 1);\r\n                var value = chunk.slice(valueStart + (valueStart < position && chunk.charCodeAt(valueStart) === \" \".charCodeAt(0) ? 1 : 0), position);\r\n                if (field === \"data\") {\r\n                  dataBuffer += \"\\n\";\r\n                  dataBuffer += value;\r\n                } else if (field === \"id\") {\r\n                  lastEventIdBuffer = value;\r\n                } else if (field === \"event\") {\r\n                  eventTypeBuffer = value;\r\n                } else if (field === \"retry\") {\r\n                  initialRetry = parseDuration(value, initialRetry);\r\n                  retry = initialRetry;\r\n                } else if (field === \"heartbeatTimeout\") {\r\n                  heartbeatTimeout = parseDuration(value, heartbeatTimeout);\r\n                  if (timeout !== 0) {\r\n                    clearTimeout(timeout);\r\n                    timeout = setTimeout(function () {\r\n                      onTimeout();\r\n                    }, heartbeatTimeout);\r\n                  }\r\n                }\r\n              }\r\n              if (state === FIELD_START) {\r\n                if (dataBuffer !== \"\") {\r\n                  lastEventId = lastEventIdBuffer;\r\n                  if (eventTypeBuffer === \"\") {\r\n                    eventTypeBuffer = \"message\";\r\n                  }\r\n                  var event = new MessageEvent(eventTypeBuffer, {\r\n                    data: dataBuffer.slice(1),\r\n                    lastEventId: lastEventIdBuffer\r\n                  });\r\n                  es.dispatchEvent(event);\r\n                  if (eventTypeBuffer === \"open\") {\r\n                    fire(es, es.onopen, event);\r\n                  } else if (eventTypeBuffer === \"message\") {\r\n                    fire(es, es.onmessage, event);\r\n                  } else if (eventTypeBuffer === \"error\") {\r\n                    fire(es, es.onerror, event);\r\n                  }\r\n                  if (currentState === CLOSED) {\r\n                    return;\r\n                  }\r\n                }\r\n                dataBuffer = \"\";\r\n                eventTypeBuffer = \"\";\r\n              }\r\n              state = c === \"\\r\".charCodeAt(0) ? AFTER_CR : FIELD_START;\r\n            } else {\r\n              if (state === FIELD_START) {\r\n                fieldStart = position;\r\n                state = FIELD;\r\n              }\r\n              if (state === FIELD) {\r\n                if (c === \":\".charCodeAt(0)) {\r\n                  valueStart = position + 1;\r\n                  state = VALUE_START;\r\n                }\r\n              } else if (state === VALUE_START) {\r\n                state = VALUE;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    };\r\n\r\n    var onFinish = function (error) {\r\n      if (currentState === OPEN || currentState === CONNECTING) {\r\n        currentState = WAITING;\r\n        if (timeout !== 0) {\r\n          clearTimeout(timeout);\r\n          timeout = 0;\r\n        }\r\n        timeout = setTimeout(function () {\r\n          onTimeout();\r\n        }, retry);\r\n        retry = clampDuration(Math.min(initialRetry * 16, retry * 2));\r\n\r\n        es.readyState = CONNECTING;\r\n        var event = new ErrorEvent(\"error\", {error: error});\r\n        es.dispatchEvent(event);\r\n        fire(es, es.onerror, event);\r\n        if (error != undefined) {\r\n          console.error(error);\r\n        }\r\n      }\r\n    };\r\n\r\n    var close = function () {\r\n      currentState = CLOSED;\r\n      if (abortController != undefined) {\r\n        abortController.abort();\r\n        abortController = undefined;\r\n      }\r\n      if (timeout !== 0) {\r\n        clearTimeout(timeout);\r\n        timeout = 0;\r\n      }\r\n      es.readyState = CLOSED;\r\n    };\r\n\r\n    var onTimeout = function () {\r\n      timeout = 0;\r\n\r\n      if (currentState !== WAITING) {\r\n        if (!wasActivity && abortController != undefined) {\r\n          onFinish(new Error(\"No activity within \" + heartbeatTimeout + \" milliseconds.\" + \" \" + (currentState === CONNECTING ? \"No response received.\" : textLength + \" chars received.\") + \" \" + \"Reconnecting.\"));\r\n          if (abortController != undefined) {\r\n            abortController.abort();\r\n            abortController = undefined;\r\n          }\r\n        } else {\r\n          var nextHeartbeat = Math.max((wasActivity || Date.now()) + heartbeatTimeout - Date.now(), 1);\r\n          wasActivity = false;\r\n          timeout = setTimeout(function () {\r\n            onTimeout();\r\n          }, nextHeartbeat);\r\n        }\r\n        return;\r\n      }\r\n\r\n      wasActivity = false;\r\n      textLength = 0;\r\n      timeout = setTimeout(function () {\r\n        onTimeout();\r\n      }, heartbeatTimeout);\r\n\r\n      currentState = CONNECTING;\r\n      dataBuffer = \"\";\r\n      eventTypeBuffer = \"\";\r\n      lastEventIdBuffer = lastEventId;\r\n      textBuffer = \"\";\r\n      fieldStart = 0;\r\n      valueStart = 0;\r\n      state = FIELD_START;\r\n\r\n      // https://bugzilla.mozilla.org/show_bug.cgi?id=428916\r\n      // Request header field Last-Event-ID is not allowed by Access-Control-Allow-Headers.\r\n      var requestURL = url;\r\n      if (url.slice(0, 5) !== \"data:\" && url.slice(0, 5) !== \"blob:\") {\r\n        if (lastEventId !== \"\") {\r\n          // Remove the lastEventId parameter if it's already part of the request URL.\r\n          var i = url.indexOf(\"?\");\r\n          requestURL = i === -1 ? url : url.slice(0, i + 1) + url.slice(i + 1).replace(/(?:^|&)([^=&]*)(?:=[^&]*)?/g, function (p, paramName) {\r\n            return paramName === lastEventIdQueryParameterName ? '' : p;\r\n          });\r\n          // Append the current lastEventId to the request URL.\r\n          requestURL += (url.indexOf(\"?\") === -1 ? \"?\" : \"&\") + lastEventIdQueryParameterName +\"=\" + encodeURIComponent(lastEventId);\r\n        }\r\n      }\r\n      var withCredentials = es.withCredentials;\r\n      var requestHeaders = {};\r\n      requestHeaders[\"Accept\"] = \"text/event-stream\";\r\n      var headers = es.headers;\r\n      if (headers != undefined) {\r\n        for (var name in headers) {\r\n          if (Object.prototype.hasOwnProperty.call(headers, name)) {\r\n            requestHeaders[name] = headers[name];\r\n          }\r\n        }\r\n      }\r\n      try {\r\n        abortController = transport.open(xhr, onStart, onProgress, onFinish, requestURL, withCredentials, requestHeaders);\r\n      } catch (error) {\r\n        close();\r\n        throw error;\r\n      }\r\n    };\r\n\r\n    es.url = url;\r\n    es.readyState = CONNECTING;\r\n    es.withCredentials = withCredentials;\r\n    es.headers = headers;\r\n    es._close = close;\r\n\r\n    onTimeout();\r\n  }\r\n\r\n  EventSourcePolyfill.prototype = Object.create(EventTarget.prototype);\r\n  EventSourcePolyfill.prototype.CONNECTING = CONNECTING;\r\n  EventSourcePolyfill.prototype.OPEN = OPEN;\r\n  EventSourcePolyfill.prototype.CLOSED = CLOSED;\r\n  EventSourcePolyfill.prototype.close = function () {\r\n    this._close();\r\n  };\r\n\r\n  EventSourcePolyfill.CONNECTING = CONNECTING;\r\n  EventSourcePolyfill.OPEN = OPEN;\r\n  EventSourcePolyfill.CLOSED = CLOSED;\r\n  EventSourcePolyfill.prototype.withCredentials = undefined;\r\n\r\n  var R = NativeEventSource\r\n  if (XMLHttpRequest != undefined && (NativeEventSource == undefined || !(\"withCredentials\" in NativeEventSource.prototype))) {\r\n    // Why replace a native EventSource ?\r\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=444328\r\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=831392\r\n    // https://code.google.com/p/chromium/issues/detail?id=260144\r\n    // https://code.google.com/p/chromium/issues/detail?id=225654\r\n    // ...\r\n    R = EventSourcePolyfill;\r\n  }\r\n\r\n  (function (factory) {\r\n    if (typeof module === \"object\" && typeof module.exports === \"object\") {\r\n      var v = factory(exports);\r\n      if (v !== undefined) module.exports = v;\r\n    }\r\n    else if (typeof define === \"function\" && define.amd) {\r\n      define([\"exports\"], factory);\r\n    }\r\n    else {\r\n      factory(global);\r\n    }\r\n  })(function (exports) {\r\n    exports.EventSourcePolyfill = EventSourcePolyfill;\r\n    exports.NativeEventSource = NativeEventSource;\r\n    exports.EventSource = R;\r\n  });\r\n}(typeof globalThis === 'undefined' ? (typeof window !== 'undefined' ? window : typeof self !== 'undefined' ? self : this) : globalThis));\r\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED,+CAA+C,GAC/C,kCAAkC,GAEjC,CAAA,SAAU,MAAM;IACf;IAEA,IAAI,aAAa,OAAO,UAAU;IAClC,IAAI,eAAe,OAAO,YAAY;IACtC,IAAI,iBAAiB,OAAO,cAAc;IAC1C,IAAI,iBAAiB,OAAO,cAAc;IAC1C,IAAI,gBAAgB,OAAO,aAAa;IACxC,IAAI,oBAAoB,OAAO,WAAW;IAE1C,IAAI,WAAW,OAAO,QAAQ;IAC9B,IAAI,UAAU,OAAO,OAAO;IAC5B,IAAI,QAAQ,OAAO,KAAK;IACxB,IAAI,WAAW,OAAO,QAAQ;IAC9B,IAAI,cAAc,OAAO,WAAW;IACpC,IAAI,cAAc,OAAO,WAAW;IACpC,IAAI,kBAAkB,OAAO,eAAe;IAE5C,IAAI,OAAO,WAAW,eAAe,OAAO,aAAa,eAAe,CAAC,CAAC,gBAAgB,QAAQ,KAAK,SAAS,IAAI,IAAI,MAAM;QAC5H,SAAS,UAAU,GAAG;QACtB,OAAO,gBAAgB,CAAC,QAAQ,SAAU,KAAK;YAC7C,SAAS,UAAU,GAAG;QACxB,GAAG;IACL;IAEA,IAAI,kBAAkB,QAAQ,iBAAiB,MAAM;QACnD,iBAAiB;YACf,OAAO,IAAI,cAAc;QAC3B;IACF;IAEA,IAAI,OAAO,MAAM,IAAI,WAAW;QAC9B,OAAO,MAAM,GAAG,SAAU,CAAC;YACzB,SAAS,KAAI;YACb,EAAE,SAAS,GAAG;YACd,OAAO,IAAI;QACb;IACF;IAEA,IAAI,CAAC,KAAK,GAAG,EAAE;QACb,KAAK,GAAG,GAAG,SAAS;YAClB,OAAO,IAAI,OAAO,OAAO;QAC3B;IACF;IAEA,qDAAqD;IACrD,kCAAkC;IAClC,4BAA4B;IAC5B,gBAAgB;IAChB,wCAAwC;IAExC,IAAI,mBAAmB,WAAW;QAChC,IAAI,iBAAiB;QACrB,QAAQ,SAAU,GAAG,EAAE,OAAO;YAC5B,IAAI,SAAS,QAAQ,MAAM;YAC3B,OAAO,eAAe,KAAK;gBAAC,SAAS,QAAQ,OAAO;gBAAE,aAAa,QAAQ,WAAW;gBAAE,OAAO,QAAQ,KAAK;YAAA,GAAG,IAAI,CAAC,SAAU,QAAQ;gBACpI,IAAI,SAAS,SAAS,IAAI,CAAC,SAAS;gBACpC,OAAO,OAAO,GAAG;gBACjB,IAAI,OAAO,QAAQ,EAAE;oBACnB,OAAO,OAAO,CAAC,MAAM;gBACvB;gBACA,OAAO;oBACL,QAAQ,SAAS,MAAM;oBACvB,YAAY,SAAS,UAAU;oBAC/B,SAAS,SAAS,OAAO;oBACzB,MAAM;wBACJ,WAAW;4BACT,OAAO;wBACT;oBACF;gBACF;YACF;QACF;QACA,kBAAkB;YAChB,IAAI,CAAC,MAAM,GAAG;gBACZ,SAAS;gBACT,UAAU;YACZ;YACA,IAAI,CAAC,KAAK,GAAG;gBACX,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,MAAM;oBAC/B,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM;gBAC5B;gBACA,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG;YACzB;QACF;IACF;IAEA,SAAS;QACP,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,SAAS,GAAG;IACnB;IAEA,oBAAoB,SAAS,CAAC,MAAM,GAAG,SAAU,MAAM;QACrD,SAAS,MAAM,SAAS,EAAE,KAAK,EAAE,WAAW;YAC1C,IAAI,gBAAgB,GAAG;gBACrB,OAAO,aAAa,UAAU,SAAS,aAAa,SAAS;YAC/D;YACA,IAAI,gBAAgB,GAAG;gBACrB,OAAO,aAAa,UAAU,SAAS,aAAa,SAAS,UAAU,aAAa,UAAU,SAAS,aAAa,SAAS;YAC/H;YACA,IAAI,gBAAgB,GAAG;gBACrB,OAAO,aAAa,YAAY,SAAS,aAAa,SAAS;YACjE;YACA,MAAM,IAAI;QACZ;QACA,SAAS,YAAY,UAAU,EAAE,SAAS;YACxC,IAAI,eAAe,IAAI,GAAG;gBACxB,OAAO,aAAa,IAAI,KAAK,IAAI,YAAY,KAAK,IAAI;YACxD;YACA,IAAI,eAAe,IAAI,GAAG;gBACxB,OAAO,YAAY,KAAK,IAAI;YAC9B;YACA,IAAI,eAAe,IAAI,GAAG;gBACxB,OAAO;YACT;YACA,MAAM,IAAI;QACZ;QACA,IAAI,WAAW;QACf,IAAI,SAAS;QACb,IAAI,aAAa,IAAI,CAAC,UAAU;QAChC,IAAI,YAAY,IAAI,CAAC,SAAS;QAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,KAAK,EAAG;YACzC,IAAI,QAAQ,MAAM,CAAC,EAAE;YACrB,IAAI,eAAe,GAAG;gBACpB,IAAI,QAAQ,OAAO,QAAQ,OAAO,CAAC,MAAM,aAAa,IAAI,QAAQ,IAAI,aAAa,GAAG,YAAY,YAAY,aAAa;oBACzH,aAAa;oBACb,YAAY;oBACZ,UAAU,OAAO,YAAY,CAAC;gBAChC;YACF;YACA,IAAI,eAAe,GAAG;gBACpB,IAAI,SAAS,KAAK,SAAS,KAAK;oBAC9B,aAAa;oBACb,YAAY;gBACd,OAAO,IAAI,SAAS,OAAO,SAAS,KAAK;oBACvC,aAAa,IAAI;oBACjB,YAAY,QAAQ;gBACtB,OAAO,IAAI,SAAS,OAAO,SAAS,KAAK;oBACvC,aAAa,IAAI;oBACjB,YAAY,QAAQ;gBACtB,OAAO,IAAI,SAAS,OAAO,SAAS,KAAK;oBACvC,aAAa,IAAI;oBACjB,YAAY,QAAQ;gBACtB,OAAO;oBACL,aAAa;oBACb,YAAY;gBACd;gBACA,IAAI,eAAe,KAAK,CAAC,MAAM,WAAW,YAAY,YAAY,YAAY,aAAa;oBACzF,aAAa;oBACb,YAAY;gBACd;YACF,OAAO;gBACL,cAAc;gBACd,YAAY,aAAa,IAAI,QAAQ;YACvC;YACA,IAAI,eAAe,GAAG;gBACpB,IAAI,aAAa,QAAQ;oBACvB,UAAU,OAAO,YAAY,CAAC;gBAChC,OAAO;oBACL,UAAU,OAAO,YAAY,CAAC,SAAS,CAAC,YAAY,SAAS,KAAK,EAAE;oBACpE,UAAU,OAAO,YAAY,CAAC,SAAS,CAAC,YAAY,SAAS,IAAI,KAAK;gBACxE;YACF;QACF;QACA,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,SAAS,GAAG;QACjB,OAAO;IACT;IAEA,kDAAkD;IAClD,IAAI,uBAAuB;QACzB,IAAI;YACF,OAAO,IAAI,cAAc,MAAM,CAAC,IAAI,cAAc,MAAM,CAAC,SAAS;gBAAC,QAAQ;YAAI,OAAO;QACxF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4EAA4E;QAC5F;QACA,OAAO;IACT;IAEA,WAAW;IACX,IAAI,eAAe,aAAa,eAAe,aAAa,CAAC,wBAAwB;QACnF,cAAc;IAChB;IAEA,IAAI,IAAI,YACR;IAEA,SAAS,WAAW,GAAG;QACrB,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,kBAAkB,GAAG;QAC1B,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,MAAM,GAAG;IAChB;IAEA,WAAW,SAAS,CAAC,IAAI,GAAG,SAAU,MAAM,EAAE,GAAG;QAC/C,IAAI,CAAC,MAAM,CAAC;QAEZ,IAAI,OAAO,IAAI;QACf,IAAI,MAAM,IAAI,CAAC,IAAI;QACnB,IAAI,QAAQ;QACZ,IAAI,UAAU;QAEd,IAAI,CAAC,MAAM,GAAG,SAAU,MAAM;YAC5B,IAAI,KAAK,YAAY,KAAK,GAAG;gBAC3B,aAAa,KAAK,YAAY;gBAC9B,KAAK,YAAY,GAAG;YACtB;YACA,IAAI,UAAU,KAAK,UAAU,KAAK,UAAU,GAAG;gBAC7C,QAAQ;gBACR,IAAI,MAAM,GAAG;gBACb,IAAI,OAAO,GAAG;gBACd,IAAI,OAAO,GAAG;gBACd,IAAI,UAAU,GAAG;gBACjB,IAAI,kBAAkB,GAAG;gBACzB,2DAA2D;gBAC3D,6DAA6D;gBAC7D,IAAI,KAAK;gBACT,IAAI,YAAY,GAAG;oBACjB,aAAa;oBACb,UAAU;gBACZ;gBACA,IAAI,CAAC,QAAQ;oBACX,KAAK,UAAU,GAAG;oBAClB,KAAK,OAAO,CAAC;oBACb,KAAK,kBAAkB;gBACzB;YACF;YACA,QAAQ;QACV;QAEA,IAAI,UAAU;YACZ,IAAI,UAAU,GAAG;gBACf,YAAY;gBACZ,IAAI,SAAS;gBACb,IAAI,aAAa;gBACjB,IAAI,cAAc;gBAClB,IAAI,CAAC,CAAC,iBAAiB,GAAG,GAAG;oBAC3B,IAAI;wBACF,SAAS,IAAI,MAAM;wBACnB,aAAa,IAAI,UAAU;wBAC3B,cAAc,IAAI,iBAAiB,CAAC;oBACtC,EAAE,OAAO,OAAO;wBACd,8FAA8F;wBAC9F,yEAAyE;wBACzE,gDAAgD;wBAChD,SAAS;wBACT,aAAa;wBACb,cAAc;oBACd,mCAAmC;oBACnC,gDAAgD;oBAChD,gDAAgD;oBAClD;gBACF,OAAO;oBACL,SAAS;oBACT,aAAa;oBACb,cAAc,IAAI,WAAW;gBAC/B;gBACA,IAAI,WAAW,GAAG;oBAChB,QAAQ;oBACR,KAAK,UAAU,GAAG;oBAClB,KAAK,MAAM,GAAG;oBACd,KAAK,UAAU,GAAG;oBAClB,KAAK,YAAY,GAAG;oBACpB,KAAK,kBAAkB;gBACzB;YACF;QACF;QACA,IAAI,aAAa;YACf;YACA,IAAI,UAAU,KAAK,UAAU,GAAG;gBAC9B,QAAQ;gBACR,IAAI,eAAe;gBACnB,IAAI;oBACF,eAAe,IAAI,YAAY;gBACjC,EAAE,OAAO,OAAO;gBACd,+BAA+B;gBACjC;gBACA,KAAK,UAAU,GAAG;gBAClB,KAAK,YAAY,GAAG;gBACpB,KAAK,UAAU;YACjB;QACF;QACA,IAAI,WAAW,SAAU,IAAI,EAAE,KAAK;YAClC,IAAI,SAAS,QAAQ,MAAM,cAAc,IAAI,MAAM;gBACjD,QAAQ;oBACN,gBAAgB;gBAClB;YACF;YACA,qHAAqH;YACrH,2CAA2C;YAC3C;YACA,IAAI,UAAU,KAAK,UAAU,KAAK,UAAU,GAAG;gBAC7C,QAAQ;gBACR,IAAI,YAAY,GAAG;oBACjB,aAAa;oBACb,UAAU;gBACZ;gBACA,KAAK,UAAU,GAAG;gBAClB,IAAI,SAAS,QAAQ;oBACnB,KAAK,MAAM,CAAC;gBACd,OAAO,IAAI,SAAS,SAAS;oBAC3B,KAAK,OAAO,CAAC;gBACf,OAAO,IAAI,SAAS,SAAS;oBAC3B,KAAK,OAAO,CAAC;gBACf,OAAO;oBACL,MAAM,IAAI;gBACZ;gBACA,KAAK,kBAAkB;YACzB;QACF;QACA,IAAI,qBAAqB,SAAU,KAAK;YACtC,IAAI,OAAO,WAAW;gBACpB,IAAI,IAAI,UAAU,KAAK,GAAG;oBACxB,IAAI,CAAC,CAAC,YAAY,GAAG,KAAK,CAAC,CAAC,aAAa,GAAG,KAAK,CAAC,CAAC,aAAa,GAAG,GAAG;wBACpE,SAAS,IAAI,YAAY,KAAK,KAAK,UAAU,QAAQ;oBACvD;gBACF,OAAO,IAAI,IAAI,UAAU,KAAK,GAAG;oBAC/B,IAAI,CAAC,CAAC,gBAAgB,GAAG,GAAG;wBAC1B,qBAAqB;wBACrB;oBACF;gBACF,OAAO,IAAI,IAAI,UAAU,KAAK,GAAG;oBAC/B;gBACF;YACF;QACF;QACA,IAAI,YAAY;YACd,UAAU,WAAW;gBACnB;YACF,GAAG;YACH,IAAI,IAAI,UAAU,KAAK,GAAG;gBACxB;YACF;QACF;QAEA,2DAA2D;QAC3D,IAAI,YAAY,KAAK;YACnB,IAAI,MAAM,GAAG,SAAU,KAAK;gBAC1B,SAAS,QAAQ;YACnB;QACF;QACA,IAAI,aAAa,KAAK;YACpB,IAAI,OAAO,GAAG,SAAU,KAAK;gBAC3B,SAAS,SAAS;YACpB;QACF;QACA,mFAAmF;QACnF,0DAA0D;QAC1D,sDAAsD;QACtD,6DAA6D;QAC7D,0CAA0C;QAC1C,IAAI,aAAa,KAAK;YACpB,IAAI,OAAO,GAAG,SAAU,KAAK;gBAC3B,SAAS,SAAS;YACpB;QACF;QAEA,IAAI,gBAAgB,KAAK;YACvB,IAAI,UAAU,GAAG;QACnB;QAEA,4BAA4B;QAC5B,aAAa;QACb,gBAAgB;QAChB,8BAA8B;QAC9B,+CAA+C;QAC/C,0CAA0C;QAC1C,IAAI,wBAAwB,KAAK;YAC/B,IAAI,kBAAkB,GAAG,SAAU,KAAK;gBACtC,mBAAmB;YACrB;QACF;QAEA,IAAI,iBAAiB,OAAO,CAAC,CAAC,eAAe,eAAe,SAAS,GAAG;YACtE,OAAO,CAAC,IAAI,OAAO,CAAC,SAAS,CAAC,IAAI,MAAM,GAAG,IAAI;QACjD;QACA,IAAI,IAAI,CAAC,QAAQ,KAAK;QAEtB,IAAI,gBAAgB,KAAK;YACvB,uDAAuD;YACvD,mFAAmF;YACnF,UAAU,WAAW;gBACnB;YACF,GAAG;QACL;IACF;IACA,WAAW,SAAS,CAAC,KAAK,GAAG;QAC3B,IAAI,CAAC,MAAM,CAAC;IACd;IACA,WAAW,SAAS,CAAC,iBAAiB,GAAG,SAAU,IAAI;QACrD,OAAO,IAAI,CAAC,YAAY;IAC1B;IACA,WAAW,SAAS,CAAC,gBAAgB,GAAG,SAAU,IAAI,EAAE,KAAK;QAC3D,IAAI,MAAM,IAAI,CAAC,IAAI;QACnB,IAAI,sBAAsB,KAAK;YAC7B,IAAI,gBAAgB,CAAC,MAAM;QAC7B;IACF;IACA,WAAW,SAAS,CAAC,qBAAqB,GAAG;QAC3C,wFAAwF;QACxF,OAAO,IAAI,CAAC,IAAI,CAAC,qBAAqB,IAAI,YAAY,IAAI,CAAC,IAAI,CAAC,qBAAqB,MAAM,KAAK;IAClG;IACA,WAAW,SAAS,CAAC,IAAI,GAAG;QAC1B,4DAA4D;QAC5D,sDAAsD;QACtD,IAAI,CAAC,CAAC,CAAC,eAAe,eAAe,SAAS,KAAM,CAAC,CAAC,kBAAkB,eAAe,SAAS,KAAK,CAAC,CAAC,aAAa,eAAe,SAAS,CAAE,KAC1I,YAAY,aACZ,SAAS,UAAU,IAAI,aACvB,SAAS,UAAU,KAAK,YAAY;YACtC,IAAI,OAAO,IAAI;YACf,KAAK,YAAY,GAAG,WAAW;gBAC7B,KAAK,YAAY,GAAG;gBACpB,KAAK,IAAI;YACX,GAAG;YACH;QACF;QAEA,IAAI,MAAM,IAAI,CAAC,IAAI;QACnB,4EAA4E;QAC5E,IAAI,qBAAqB,KAAK;YAC5B,IAAI,eAAe,GAAG,IAAI,CAAC,eAAe;QAC5C;QACA,IAAI;YACF,2DAA2D;YAC3D,IAAI,IAAI,CAAC;QACX,EAAE,OAAO,QAAQ;YACf,yBAAyB;YACzB,MAAM;QACR;IACF;IAEA,SAAS,YAAY,IAAI;QACvB,OAAO,KAAK,OAAO,CAAC,UAAU,SAAU,CAAC;YACvC,OAAO,OAAO,YAAY,CAAC,EAAE,UAAU,CAAC,KAAK;QAC/C;IACF;IAEA,SAAS,gBAAgB,GAAG;QAC1B,8JAA8J;QAC9J,IAAI,MAAM,OAAO,MAAM,CAAC;QACxB,IAAI,QAAQ,IAAI,KAAK,CAAC;QACtB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,KAAK,EAAG;YACxC,IAAI,OAAO,KAAK,CAAC,EAAE;YACnB,IAAI,QAAQ,KAAK,KAAK,CAAC;YACvB,IAAI,OAAO,MAAM,KAAK;YACtB,IAAI,QAAQ,MAAM,IAAI,CAAC;YACvB,GAAG,CAAC,YAAY,MAAM,GAAG;QAC3B;QACA,IAAI,CAAC,IAAI,GAAG;IACd;IACA,gBAAgB,SAAS,CAAC,GAAG,GAAG,SAAU,IAAI;QAC5C,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY,MAAM;IACrC;IAEA,IAAI,kBAAkB,QAAQ,eAAe,gBAAgB,IAAI,MAAM;QACrE,eAAe,gBAAgB,GAAG;IACpC;IAEA,SAAS,gBACT;IAEA,aAAa,SAAS,CAAC,IAAI,GAAG,SAAU,GAAG,EAAE,eAAe,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,GAAG,EAAE,eAAe,EAAE,OAAO;QAC/H,IAAI,IAAI,CAAC,OAAO;QAChB,IAAI,SAAS;QACb,IAAI,UAAU,GAAG;YACf,IAAI,eAAe,IAAI,YAAY;YACnC,IAAI,QAAQ,aAAa,KAAK,CAAC;YAC/B,UAAU,MAAM,MAAM;YACtB,mBAAmB;QACrB;QACA,IAAI,OAAO,GAAG,SAAU,KAAK;YAC3B,MAAM,cAAc;YACpB,iBAAiB,IAAI,MAAM;QAC7B;QACA,IAAI,MAAM,GAAG;YACX,iBAAiB;QACnB;QACA,IAAI,OAAO,GAAG;YACZ,iBAAiB;QACnB;QACA,IAAI,kBAAkB,GAAG;YACvB,IAAI,IAAI,UAAU,KAAK,eAAe,gBAAgB,EAAE;gBACtD,IAAI,SAAS,IAAI,MAAM;gBACvB,IAAI,aAAa,IAAI,UAAU;gBAC/B,IAAI,cAAc,IAAI,iBAAiB,CAAC;gBACxC,IAAI,UAAU,IAAI,qBAAqB;gBACvC,gBAAgB,QAAQ,YAAY,aAAa,IAAI,gBAAgB;YACvE;QACF;QACA,IAAI,eAAe,GAAG;QACtB,IAAK,IAAI,QAAQ,QAAS;YACxB,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,OAAO;gBACvD,IAAI,gBAAgB,CAAC,MAAM,OAAO,CAAC,KAAK;YAC1C;QACF;QACA,IAAI,IAAI;QACR,OAAO;IACT;IAEA,SAAS,eAAe,OAAO;QAC7B,IAAI,CAAC,QAAQ,GAAG;IAClB;IACA,eAAe,SAAS,CAAC,GAAG,GAAG,SAAU,IAAI;QAC3C,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;IAC3B;IAEA,SAAS,kBACT;IAEA,eAAe,SAAS,CAAC,IAAI,GAAG,SAAU,GAAG,EAAE,eAAe,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,GAAG,EAAE,eAAe,EAAE,OAAO;QACjI,IAAI,SAAS;QACb,IAAI,aAAa,IAAI;QACrB,IAAI,SAAS,WAAW,MAAM;QAC9B,IAAI,cAAc,IAAI;QACtB,MAAM,KAAK;YACT,SAAS;YACT,aAAa,kBAAkB,YAAY;YAC3C,QAAQ;YACR,OAAO;QACT,GAAG,IAAI,CAAC,SAAU,QAAQ;YACxB,SAAS,SAAS,IAAI,CAAC,SAAS;YAChC,gBAAgB,SAAS,MAAM,EAAE,SAAS,UAAU,EAAE,SAAS,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,eAAe,SAAS,OAAO;YAC/H,iEAAiE;YACjE,OAAO,IAAI,QAAQ,SAAU,OAAO,EAAE,MAAM;gBAC1C,IAAI,gBAAgB;oBAClB,OAAO,IAAI,GAAG,IAAI,CAAC,SAAU,MAAM;wBACjC,IAAI,OAAO,IAAI,EAAE;4BACf,wCAAwC;4BACxC,QAAQ;wBACV,OAAO;4BACL,IAAI,QAAQ,YAAY,MAAM,CAAC,OAAO,KAAK,EAAE;gCAAC,QAAQ;4BAAI;4BAC1D,mBAAmB;4BACnB;wBACF;oBACF,EAAE,CAAC,QAAQ,CAAC,SAAU,KAAK;wBACzB,OAAO;oBACT;gBACF;gBACA;YACF;QACF,EAAE,CAAC,QAAQ,CAAC,SAAU,KAAK;YACzB,IAAI,MAAM,IAAI,KAAK,cAAc;gBAC/B,OAAO;YACT,OAAO;gBACL,OAAO;YACT;QACF,GAAG,IAAI,CAAC,SAAU,KAAK;YACrB,iBAAiB;QACnB;QACA,OAAO;YACL,OAAO;gBACL,IAAI,UAAU,MAAM;oBAClB,OAAO,MAAM,IAAI,uDAAuD;gBAC1E;gBACA,WAAW,KAAK;YAClB;QACF;IACF;IAEA,SAAS;QACP,IAAI,CAAC,UAAU,GAAG,OAAO,MAAM,CAAC;IAClC;IAEA,SAAS,WAAW,CAAC;QACnB,WAAW;YACT,MAAM;QACR,GAAG;IACL;IAEA,YAAY,SAAS,CAAC,aAAa,GAAG,SAAU,KAAK;QACnD,MAAM,MAAM,GAAG,IAAI;QACnB,IAAI,gBAAgB,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,CAAC;QAC/C,IAAI,iBAAiB,WAAW;YAC9B,IAAI,SAAS,cAAc,MAAM;YACjC,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,KAAK,EAAG;gBAClC,IAAI,WAAW,aAAa,CAAC,EAAE;gBAC/B,IAAI;oBACF,IAAI,OAAO,SAAS,WAAW,KAAK,YAAY;wBAC9C,SAAS,WAAW,CAAC;oBACvB,OAAO;wBACL,SAAS,IAAI,CAAC,IAAI,EAAE;oBACtB;gBACF,EAAE,OAAO,GAAG;oBACV,WAAW;gBACb;YACF;QACF;IACF;IACA,YAAY,SAAS,CAAC,gBAAgB,GAAG,SAAU,IAAI,EAAE,QAAQ;QAC/D,OAAO,OAAO;QACd,IAAI,YAAY,IAAI,CAAC,UAAU;QAC/B,IAAI,gBAAgB,SAAS,CAAC,KAAK;QACnC,IAAI,iBAAiB,WAAW;YAC9B,gBAAgB,EAAE;YAClB,SAAS,CAAC,KAAK,GAAG;QACpB;QACA,IAAI,QAAQ;QACZ,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,MAAM,EAAE,KAAK,EAAG;YAChD,IAAI,aAAa,CAAC,EAAE,KAAK,UAAU;gBACjC,QAAQ;YACV;QACF;QACA,IAAI,CAAC,OAAO;YACV,cAAc,IAAI,CAAC;QACrB;IACF;IACA,YAAY,SAAS,CAAC,mBAAmB,GAAG,SAAU,IAAI,EAAE,QAAQ;QAClE,OAAO,OAAO;QACd,IAAI,YAAY,IAAI,CAAC,UAAU;QAC/B,IAAI,gBAAgB,SAAS,CAAC,KAAK;QACnC,IAAI,iBAAiB,WAAW;YAC9B,IAAI,WAAW,EAAE;YACjB,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,MAAM,EAAE,KAAK,EAAG;gBAChD,IAAI,aAAa,CAAC,EAAE,KAAK,UAAU;oBACjC,SAAS,IAAI,CAAC,aAAa,CAAC,EAAE;gBAChC;YACF;YACA,IAAI,SAAS,MAAM,KAAK,GAAG;gBACzB,OAAO,SAAS,CAAC,KAAK;YACxB,OAAO;gBACL,SAAS,CAAC,KAAK,GAAG;YACpB;QACF;IACF;IAEA,SAAS,MAAM,IAAI;QACjB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,MAAM,GAAG;IAChB;IAEA,SAAS,aAAa,IAAI,EAAE,OAAO;QACjC,MAAM,IAAI,CAAC,IAAI,EAAE;QACjB,IAAI,CAAC,IAAI,GAAG,QAAQ,IAAI;QACxB,IAAI,CAAC,WAAW,GAAG,QAAQ,WAAW;IACxC;IAEA,aAAa,SAAS,GAAG,OAAO,MAAM,CAAC,MAAM,SAAS;IAEtD,SAAS,gBAAgB,IAAI,EAAE,OAAO;QACpC,MAAM,IAAI,CAAC,IAAI,EAAE;QACjB,IAAI,CAAC,MAAM,GAAG,QAAQ,MAAM;QAC5B,IAAI,CAAC,UAAU,GAAG,QAAQ,UAAU;QACpC,IAAI,CAAC,OAAO,GAAG,QAAQ,OAAO;IAChC;IAEA,gBAAgB,SAAS,GAAG,OAAO,MAAM,CAAC,MAAM,SAAS;IAEzD,SAAS,WAAW,IAAI,EAAE,OAAO;QAC/B,MAAM,IAAI,CAAC,IAAI,EAAE;QACjB,IAAI,CAAC,KAAK,GAAG,QAAQ,KAAK;IAC5B;IAEA,WAAW,SAAS,GAAG,OAAO,MAAM,CAAC,MAAM,SAAS;IAEpD,IAAI,UAAU,CAAC;IACf,IAAI,aAAa;IACjB,IAAI,OAAO;IACX,IAAI,SAAS;IAEb,IAAI,WAAW,CAAC;IAChB,IAAI,cAAc;IAClB,IAAI,QAAQ;IACZ,IAAI,cAAc;IAClB,IAAI,QAAQ;IAEZ,IAAI,oBAAoB;IAExB,IAAI,mBAAmB;IACvB,IAAI,mBAAmB;IAEvB,IAAI,gBAAgB,SAAU,KAAK,EAAE,GAAG;QACtC,IAAI,IAAI,SAAS,OAAO,MAAM,SAAS,OAAO;QAC9C,IAAI,MAAM,GAAG;YACX,IAAI;QACN;QACA,OAAO,cAAc;IACvB;IACA,IAAI,gBAAgB,SAAU,CAAC;QAC7B,OAAO,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG,mBAAmB;IACjD;IAEA,IAAI,OAAO,SAAU,IAAI,EAAE,CAAC,EAAE,KAAK;QACjC,IAAI;YACF,IAAI,OAAO,MAAM,YAAY;gBAC3B,EAAE,IAAI,CAAC,MAAM;YACf;QACF,EAAE,OAAO,GAAG;YACV,WAAW;QACb;IACF;IAEA,SAAS,oBAAoB,GAAG,EAAE,OAAO;QACvC,YAAY,IAAI,CAAC,IAAI;QACrB,UAAU,WAAW,CAAC;QAEtB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,OAAO,GAAG;QAEf,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,OAAO,GAAG;QAEf,IAAI,CAAC,MAAM,GAAG;QAEd,MAAM,IAAI,EAAE,KAAK;IACnB;IAEA,SAAS;QACP,OAAO,AAAC,kBAAkB,aAAc,qBAAqB,eAAe,SAAS,IAAM,kBAAkB,YACvG,IAAI,mBACJ,IAAI;IACZ;IAEA,IAAI,mBAAmB,SAAS,aAAa,YAAY,aAAa,UAAU,SAAS,SAAS;IAElG,SAAS,MAAM,EAAE,EAAE,GAAG,EAAE,OAAO;QAC7B,MAAM,OAAO;QACb,IAAI,kBAAkB,QAAQ,QAAQ,eAAe;QACrD,IAAI,gCAAgC,QAAQ,6BAA6B,IAAI;QAE7E,IAAI,eAAe,cAAc;QACjC,IAAI,mBAAmB,cAAc,QAAQ,gBAAgB,EAAE;QAE/D,IAAI,cAAc;QAClB,IAAI,QAAQ;QACZ,IAAI,cAAc;QAClB,IAAI,aAAa;QACjB,IAAI,UAAU,QAAQ,OAAO,IAAI,CAAC;QAClC,IAAI,kBAAkB,QAAQ,SAAS;QACvC,IAAI,MAAM,oBAAoB,mBAAmB,YAAY,YAAY,IAAI,WAAW,mBAAmB,YAAY,IAAI,oBAAoB;QAC/I,IAAI,YAAY,mBAAmB,QAAQ,OAAO,oBAAoB,WAAW,IAAI,oBAAqB,OAAO,YAAY,IAAI,mBAAmB,IAAI;QACxJ,IAAI,kBAAkB;QACtB,IAAI,UAAU;QACd,IAAI,eAAe;QACnB,IAAI,aAAa;QACjB,IAAI,oBAAoB;QACxB,IAAI,kBAAkB;QAEtB,IAAI,aAAa;QACjB,IAAI,QAAQ;QACZ,IAAI,aAAa;QACjB,IAAI,aAAa;QAEjB,IAAI,UAAU,SAAU,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,OAAO;YAC9D,IAAI,iBAAiB,YAAY;gBAC/B,IAAI,WAAW,OAAO,eAAe,aAAa,kBAAkB,IAAI,CAAC,cAAc;oBACrF,eAAe;oBACf,cAAc,KAAK,GAAG;oBACtB,QAAQ;oBACR,GAAG,UAAU,GAAG;oBAChB,IAAI,QAAQ,IAAI,gBAAgB,QAAQ;wBACtC,QAAQ;wBACR,YAAY;wBACZ,SAAS;oBACX;oBACA,GAAG,aAAa,CAAC;oBACjB,KAAK,IAAI,GAAG,MAAM,EAAE;gBACtB,OAAO;oBACL,IAAI,UAAU;oBACd,IAAI,WAAW,KAAK;wBAClB,IAAI,YAAY;4BACd,aAAa,WAAW,OAAO,CAAC,QAAQ;wBAC1C;wBACA,UAAU,yCAAyC,SAAS,MAAM,aAAa;oBACjF,OAAO;wBACL,UAAU,+EAA+E,CAAC,eAAe,YAAY,MAAM,YAAY,OAAO,CAAC,QAAQ,IAAI,IAAI;oBACjK;oBACA;oBACA,IAAI,QAAQ,IAAI,gBAAgB,SAAS;wBACvC,QAAQ;wBACR,YAAY;wBACZ,SAAS;oBACX;oBACA,GAAG,aAAa,CAAC;oBACjB,KAAK,IAAI,GAAG,OAAO,EAAE;oBACrB,QAAQ,KAAK,CAAC;gBAChB;YACF;QACF;QAEA,IAAI,aAAa,SAAU,SAAS;YAClC,IAAI,iBAAiB,MAAM;gBACzB,IAAI,IAAI,CAAC;gBACT,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,KAAK,EAAG;oBAC5C,IAAI,IAAI,UAAU,UAAU,CAAC;oBAC7B,IAAI,MAAM,KAAK,UAAU,CAAC,MAAM,MAAM,KAAK,UAAU,CAAC,IAAI;wBACxD,IAAI;oBACN;gBACF;gBACA,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,aAAa,EAAE,IAAI,UAAU,KAAK,CAAC,GAAG,IAAI;gBAClE,aAAa,CAAC,MAAM,CAAC,IAAI,aAAa,EAAE,IAAI,UAAU,KAAK,CAAC,IAAI;gBAChE,IAAI,cAAc,IAAI;oBACpB,cAAc,KAAK,GAAG;oBACtB,cAAc,UAAU,MAAM;gBAChC;gBACA,IAAK,IAAI,WAAW,GAAG,WAAW,MAAM,MAAM,EAAE,YAAY,EAAG;oBAC7D,IAAI,IAAI,MAAM,UAAU,CAAC;oBACzB,IAAI,UAAU,YAAY,MAAM,KAAK,UAAU,CAAC,IAAI;wBAClD,QAAQ;oBACV,OAAO;wBACL,IAAI,UAAU,UAAU;4BACtB,QAAQ;wBACV;wBACA,IAAI,MAAM,KAAK,UAAU,CAAC,MAAM,MAAM,KAAK,UAAU,CAAC,IAAI;4BACxD,IAAI,UAAU,aAAa;gCACzB,IAAI,UAAU,OAAO;oCACnB,aAAa,WAAW;gCAC1B;gCACA,IAAI,QAAQ,MAAM,KAAK,CAAC,YAAY,aAAa;gCACjD,IAAI,QAAQ,MAAM,KAAK,CAAC,aAAa,CAAC,aAAa,YAAY,MAAM,UAAU,CAAC,gBAAgB,IAAI,UAAU,CAAC,KAAK,IAAI,CAAC,GAAG;gCAC5H,IAAI,UAAU,QAAQ;oCACpB,cAAc;oCACd,cAAc;gCAChB,OAAO,IAAI,UAAU,MAAM;oCACzB,oBAAoB;gCACtB,OAAO,IAAI,UAAU,SAAS;oCAC5B,kBAAkB;gCACpB,OAAO,IAAI,UAAU,SAAS;oCAC5B,eAAe,cAAc,OAAO;oCACpC,QAAQ;gCACV,OAAO,IAAI,UAAU,oBAAoB;oCACvC,mBAAmB,cAAc,OAAO;oCACxC,IAAI,YAAY,GAAG;wCACjB,aAAa;wCACb,UAAU,WAAW;4CACnB;wCACF,GAAG;oCACL;gCACF;4BACF;4BACA,IAAI,UAAU,aAAa;gCACzB,IAAI,eAAe,IAAI;oCACrB,cAAc;oCACd,IAAI,oBAAoB,IAAI;wCAC1B,kBAAkB;oCACpB;oCACA,IAAI,QAAQ,IAAI,aAAa,iBAAiB;wCAC5C,MAAM,WAAW,KAAK,CAAC;wCACvB,aAAa;oCACf;oCACA,GAAG,aAAa,CAAC;oCACjB,IAAI,oBAAoB,QAAQ;wCAC9B,KAAK,IAAI,GAAG,MAAM,EAAE;oCACtB,OAAO,IAAI,oBAAoB,WAAW;wCACxC,KAAK,IAAI,GAAG,SAAS,EAAE;oCACzB,OAAO,IAAI,oBAAoB,SAAS;wCACtC,KAAK,IAAI,GAAG,OAAO,EAAE;oCACvB;oCACA,IAAI,iBAAiB,QAAQ;wCAC3B;oCACF;gCACF;gCACA,aAAa;gCACb,kBAAkB;4BACpB;4BACA,QAAQ,MAAM,KAAK,UAAU,CAAC,KAAK,WAAW;wBAChD,OAAO;4BACL,IAAI,UAAU,aAAa;gCACzB,aAAa;gCACb,QAAQ;4BACV;4BACA,IAAI,UAAU,OAAO;gCACnB,IAAI,MAAM,IAAI,UAAU,CAAC,IAAI;oCAC3B,aAAa,WAAW;oCACxB,QAAQ;gCACV;4BACF,OAAO,IAAI,UAAU,aAAa;gCAChC,QAAQ;4BACV;wBACF;oBACF;gBACF;YACF;QACF;QAEA,IAAI,WAAW,SAAU,KAAK;YAC5B,IAAI,iBAAiB,QAAQ,iBAAiB,YAAY;gBACxD,eAAe;gBACf,IAAI,YAAY,GAAG;oBACjB,aAAa;oBACb,UAAU;gBACZ;gBACA,UAAU,WAAW;oBACnB;gBACF,GAAG;gBACH,QAAQ,cAAc,KAAK,GAAG,CAAC,eAAe,IAAI,QAAQ;gBAE1D,GAAG,UAAU,GAAG;gBAChB,IAAI,QAAQ,IAAI,WAAW,SAAS;oBAAC,OAAO;gBAAK;gBACjD,GAAG,aAAa,CAAC;gBACjB,KAAK,IAAI,GAAG,OAAO,EAAE;gBACrB,IAAI,SAAS,WAAW;oBACtB,QAAQ,KAAK,CAAC;gBAChB;YACF;QACF;QAEA,IAAI,QAAQ;YACV,eAAe;YACf,IAAI,mBAAmB,WAAW;gBAChC,gBAAgB,KAAK;gBACrB,kBAAkB;YACpB;YACA,IAAI,YAAY,GAAG;gBACjB,aAAa;gBACb,UAAU;YACZ;YACA,GAAG,UAAU,GAAG;QAClB;QAEA,IAAI,YAAY;YACd,UAAU;YAEV,IAAI,iBAAiB,SAAS;gBAC5B,IAAI,CAAC,eAAe,mBAAmB,WAAW;oBAChD,SAAS,IAAI,MAAM,wBAAwB,mBAAmB,mBAAmB,MAAM,CAAC,iBAAiB,aAAa,0BAA0B,aAAa,kBAAkB,IAAI,MAAM;oBACzL,IAAI,mBAAmB,WAAW;wBAChC,gBAAgB,KAAK;wBACrB,kBAAkB;oBACpB;gBACF,OAAO;oBACL,IAAI,gBAAgB,KAAK,GAAG,CAAC,CAAC,eAAe,KAAK,GAAG,EAAE,IAAI,mBAAmB,KAAK,GAAG,IAAI;oBAC1F,cAAc;oBACd,UAAU,WAAW;wBACnB;oBACF,GAAG;gBACL;gBACA;YACF;YAEA,cAAc;YACd,aAAa;YACb,UAAU,WAAW;gBACnB;YACF,GAAG;YAEH,eAAe;YACf,aAAa;YACb,kBAAkB;YAClB,oBAAoB;YACpB,aAAa;YACb,aAAa;YACb,aAAa;YACb,QAAQ;YAER,sDAAsD;YACtD,qFAAqF;YACrF,IAAI,aAAa;YACjB,IAAI,IAAI,KAAK,CAAC,GAAG,OAAO,WAAW,IAAI,KAAK,CAAC,GAAG,OAAO,SAAS;gBAC9D,IAAI,gBAAgB,IAAI;oBACtB,4EAA4E;oBAC5E,IAAI,IAAI,IAAI,OAAO,CAAC;oBACpB,aAAa,MAAM,CAAC,IAAI,MAAM,IAAI,KAAK,CAAC,GAAG,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,GAAG,OAAO,CAAC,+BAA+B,SAAU,CAAC,EAAE,SAAS;wBAChI,OAAO,cAAc,gCAAgC,KAAK;oBAC5D;oBACA,qDAAqD;oBACrD,cAAc,CAAC,IAAI,OAAO,CAAC,SAAS,CAAC,IAAI,MAAM,GAAG,IAAI,gCAA+B,MAAM,mBAAmB;gBAChH;YACF;YACA,IAAI,kBAAkB,GAAG,eAAe;YACxC,IAAI,iBAAiB,CAAC;YACtB,cAAc,CAAC,SAAS,GAAG;YAC3B,IAAI,UAAU,GAAG,OAAO;YACxB,IAAI,WAAW,WAAW;gBACxB,IAAK,IAAI,QAAQ,QAAS;oBACxB,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,OAAO;wBACvD,cAAc,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK;oBACtC;gBACF;YACF;YACA,IAAI;gBACF,kBAAkB,UAAU,IAAI,CAAC,KAAK,SAAS,YAAY,UAAU,YAAY,iBAAiB;YACpG,EAAE,OAAO,OAAO;gBACd;gBACA,MAAM;YACR;QACF;QAEA,GAAG,GAAG,GAAG;QACT,GAAG,UAAU,GAAG;QAChB,GAAG,eAAe,GAAG;QACrB,GAAG,OAAO,GAAG;QACb,GAAG,MAAM,GAAG;QAEZ;IACF;IAEA,oBAAoB,SAAS,GAAG,OAAO,MAAM,CAAC,YAAY,SAAS;IACnE,oBAAoB,SAAS,CAAC,UAAU,GAAG;IAC3C,oBAAoB,SAAS,CAAC,IAAI,GAAG;IACrC,oBAAoB,SAAS,CAAC,MAAM,GAAG;IACvC,oBAAoB,SAAS,CAAC,KAAK,GAAG;QACpC,IAAI,CAAC,MAAM;IACb;IAEA,oBAAoB,UAAU,GAAG;IACjC,oBAAoB,IAAI,GAAG;IAC3B,oBAAoB,MAAM,GAAG;IAC7B,oBAAoB,SAAS,CAAC,eAAe,GAAG;IAEhD,IAAI,IAAI;IACR,IAAI,kBAAkB,aAAa,CAAC,qBAAqB,aAAa,CAAC,CAAC,qBAAqB,kBAAkB,SAAS,CAAC,GAAG;QAC1H,qCAAqC;QACrC,sDAAsD;QACtD,sDAAsD;QACtD,6DAA6D;QAC7D,6DAA6D;QAC7D,MAAM;QACN,IAAI;IACN;IAEA,CAAC,SAAU,OAAO;QAChB,IAAI,+CAAkB,YAAY,OAAO,OAAO,OAAO,KAAK,UAAU;YACpE,IAAI,IAAI,QAAQ;YAChB,IAAI,MAAM,WAAW,OAAO,OAAO,GAAG;QACxC,OACK,IAAI,OAAO,WAAW,cAAc,OAAO,GAAG,EAAE;YACnD,qDAAoB;QACtB,OACK;YACH,QAAQ;QACV;IACF,CAAC,EAAE,SAAU,QAAO;QAClB,SAAQ,mBAAmB,GAAG;QAC9B,SAAQ,iBAAiB,GAAG;QAC5B,SAAQ,WAAW,GAAG;IACxB;AACF,CAAA,EAAE,OAAO,eAAe,cAAe,OAAO,WAAW,cAAc,SAAS,OAAO,SAAS,cAAc,OAAO,IAAI,GAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 979, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/eventsource/browser.js"], "sourcesContent": ["module.exports = require('event-source-polyfill').EventSourcePolyfill\n"], "names": [], "mappings": "AAAA,OAAO,OAAO,GAAG,qHAAiC,mBAAmB", "ignoreList": [0], "debugId": null}}]}