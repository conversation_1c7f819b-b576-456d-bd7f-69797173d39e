{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/Logo.tsx"], "sourcesContent": ["import React from 'react';\n\nconst Logo = ({ className = \"\" }: { className?: string }) => {\n  return (\n    <div className={`inline-flex items-center justify-center w-10 h-10 bg-gray-900 rounded-lg ${className}`}>\n      <span className=\"text-white font-bold text-xl\">M</span>\n    </div>\n  );\n};\n\nexport default Logo;\n"], "names": [], "mappings": ";;;;;AAEA,MAAM,OAAO,CAAC,EAAE,YAAY,EAAE,EAA0B;IACtD,qBACE,6LAAC;QAAI,WAAW,CAAC,yEAAyE,EAAE,WAAW;kBACrG,cAAA,6LAAC;YAAK,WAAU;sBAA+B;;;;;;;;;;;AAGrD;KANM;uCAQS", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/SimpleHeaderChat.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { MessageCircle } from 'lucide-react';\n\nconst SimpleHeaderChat: React.FC = () => {\n  const handleChatOpen = () => {\n    // Try to open Crisp chat if available\n    if (typeof window !== 'undefined' && (window as any).$crisp) {\n      (window as any).$crisp.push(['do', 'chat:open']);\n    } else {\n      // Fallback to mailto if Crisp is not available\n      window.location.href = 'mailto:<EMAIL>?subject=Chat%20Request';\n    }\n\n    // Track chat interaction for analytics\n    if (typeof window !== 'undefined' && (window as any).gtag) {\n      (window as any).gtag('event', 'chat_opened', {\n        event_category: 'engagement',\n        event_label: 'header_chat'\n      });\n    }\n  };\n\n  return (\n    <button\n      onClick={handleChatOpen}\n      className=\"inline-flex items-center gap-2 text-gray-600 dark:text-gray-300 hover:text-dark-charcoal dark:hover:text-white transition-colors duration-200\"\n      aria-label=\"Open chat\"\n    >\n      <MessageCircle className=\"w-5 h-5\" />\n      <span className=\"hidden sm:inline\">Chat</span>\n    </button>\n  );\n};\n\nexport default SimpleHeaderChat;\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKA,MAAM,mBAA6B;IACjC,MAAM,iBAAiB;QACrB,sCAAsC;QACtC,IAAI,aAAkB,eAAe,AAAC,OAAe,MAAM,EAAE;YAC1D,OAAe,MAAM,CAAC,IAAI,CAAC;gBAAC;gBAAM;aAAY;QACjD,OAAO;YACL,+CAA+C;YAC/C,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;QAEA,uCAAuC;QACvC,IAAI,aAAkB,eAAe,AAAC,OAAe,IAAI,EAAE;YACxD,OAAe,IAAI,CAAC,SAAS,eAAe;gBAC3C,gBAAgB;gBAChB,aAAa;YACf;QACF;IACF;IAEA,qBACE,6LAAC;QACC,SAAS;QACT,WAAU;QACV,cAAW;;0BAEX,6LAAC,2NAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;0BACzB,6LAAC;gBAAK,WAAU;0BAAmB;;;;;;;;;;;;AAGzC;KA7BM;uCA+BS", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/SimpleDarkModeToggle.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Sun, Moon } from 'lucide-react';\nimport { useTheme } from '../contexts/ThemeContext';\n\ninterface SimpleDarkModeToggleProps {\n  className?: string;\n  size?: 'sm' | 'md' | 'lg';\n}\n\nconst SimpleDarkModeToggle: React.FC<SimpleDarkModeToggleProps> = ({\n  className = '',\n  size = 'md'\n}) => {\n  const { theme, toggleTheme } = useTheme();\n\n  const getSizeClasses = () => {\n    switch (size) {\n      case 'sm':\n        return 'w-8 h-8 text-sm';\n      case 'lg':\n        return 'w-12 h-12 text-lg';\n      default:\n        return 'w-10 h-10 text-base';\n    }\n  };\n\n  return (\n    <button\n      onClick={toggleTheme}\n      className={`${getSizeClasses()} flex items-center justify-center rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-200 ${className}`}\n      aria-label={`Switch to ${theme === 'dark' ? 'light' : 'dark'} mode`}\n    >\n      {theme === 'dark' ? (\n        <Sun className=\"w-5 h-5\" />\n      ) : (\n        <Moon className=\"w-5 h-5\" />\n      )}\n    </button>\n  );\n};\n\nexport default SimpleDarkModeToggle;\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;;;AAJA;;;AAWA,MAAM,uBAA4D,CAAC,EACjE,YAAY,EAAE,EACd,OAAO,IAAI,EACZ;;IACC,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IAEtC,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QACC,SAAS;QACT,WAAW,GAAG,iBAAiB,gLAAgL,EAAE,WAAW;QAC5N,cAAY,CAAC,UAAU,EAAE,UAAU,SAAS,UAAU,OAAO,KAAK,CAAC;kBAElE,UAAU,uBACT,6LAAC,mMAAA,CAAA,MAAG;YAAC,WAAU;;;;;iCAEf,6LAAC,qMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;;;;;;AAIxB;GA9BM;;QAI2B,mIAAA,CAAA,WAAQ;;;KAJnC;uCAgCS", "debugId": null}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/config/site.ts"], "sourcesContent": ["/**\n * Site Configuration\n * \n * Centralized configuration for static, developer-controlled values.\n * This file contains non-sensitive data that doesn't belong in the CMS.\n */\n\n// Site Identity\nexport const SITE_CONFIG = {\n  name: 'Mobilify',\n  tagline: 'Turn Your Website or Idea Into a Custom Mobile App',\n  description: 'Mobilify converts your existing website or new idea into a high-quality, native mobile app for iOS and Android. Get a beautiful, custom-designed app without the complexity.',\n  url: process.env.NEXT_PUBLIC_SITE_URL || 'https://mobilify.app',\n  author: 'Mobilify Team',\n} as const;\n\n// Contact Information\nexport const CONTACT_INFO = {\n  email: '<EMAIL>',\n  phone: '+****************', // Update with actual phone\n  address: {\n    street: '123 Tech Street',\n    city: 'San Francisco',\n    state: 'CA',\n    zip: '94105',\n    country: 'USA'\n  }\n} as const;\n\n// Social Media Links\nexport const SOCIAL_LINKS = {\n  twitter: 'https://twitter.com/mobilify',\n  linkedin: 'https://linkedin.com/company/mobilify',\n  github: 'https://github.com/mobilify',\n  facebook: 'https://facebook.com/mobilify',\n  instagram: 'https://instagram.com/mobilify'\n} as const;\n\n// Navigation Structure\nexport const NAVIGATION = {\n  main: [\n    { label: 'Services', href: '#services-overview', id: 'services-overview' },\n    { label: 'How It Works', href: '#process', id: 'process' },\n    { label: 'About Us', href: '#about', id: 'about' },\n  ],\n  footer: {\n    company: [\n      { label: 'About', href: '/about' },\n      { label: 'Services', href: '/services' },\n      { label: 'Blog', href: '/blog' },\n      { label: 'FAQ', href: '/faq' },\n    ],\n    legal: [\n      { label: 'Privacy Policy', href: '/privacy' },\n      { label: 'Terms of Service', href: '/terms' },\n      { label: 'Cookie Policy', href: '/cookies' },\n    ],\n    support: [\n      { label: 'Contact Us', href: '#contact' },\n      { label: 'Help Center', href: '/help' },\n      { label: 'Documentation', href: '/docs' },\n    ]\n  }\n} as const;\n\n// Default/Fallback Content (used when CMS is unavailable)\nexport const FALLBACK_CONTENT = {\n  hero: {\n    headline: 'Your Idea. Your App. Realized.',\n    subtext: 'Mobilify transforms your concepts and existing websites into stunning, high-performance mobile apps. We are the bridge from vision to launch.',\n    buttonText: 'See How It Works'\n  },\n  contact: {\n    headline: 'Ready to Build Your Mobile Future?',\n    subtext: \"Let's discuss your project. We're happy to provide a free, no-obligation consultation and quote.\",\n    buttonText: 'Send Message',\n    successMessage: 'Thank you! Your message has been sent successfully. We\\'ll get back to you within 24 hours.',\n    errorMessage: 'Sorry, there was an error sending your message. Please try again or contact us directly.'\n  },\n  services: {\n    headline: 'Our Services',\n    subtext: 'Choose the perfect solution for your mobile app needs'\n  },\n  process: {\n    headline: 'How It Works',\n    subtext: 'Our proven process to turn your idea into a successful mobile app'\n  },\n  about: {\n    headline: 'About Mobilify',\n    subtext: 'We are passionate about helping businesses and entrepreneurs bring their ideas to life through mobile technology.'\n  }\n} as const;\n\n// Service Offerings\nexport const SERVICES = {\n  starter: {\n    name: 'Starter App',\n    description: 'Perfect for converting existing websites into mobile apps.',\n    price: 'Starting at $5,000',\n    features: ['Website Conversion', 'iOS & Android', 'Basic Features'],\n    popular: false\n  },\n  custom: {\n    name: 'Custom App',\n    description: 'Turn your new ideas into reality with custom development.',\n    price: 'Starting at $15,000',\n    features: ['Idea to App', 'Custom UI/UX', 'Advanced Features'],\n    popular: true\n  },\n  enterprise: {\n    name: 'Enterprise Solution',\n    description: 'Comprehensive solutions for large-scale applications.',\n    price: 'Custom Pricing',\n    features: ['Full Development', 'Scalable Architecture', 'Ongoing Support'],\n    popular: false\n  }\n} as const;\n\n// External Service URLs\nexport const EXTERNAL_SERVICES = {\n  analytics: {\n    googleAnalytics: process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID,\n  },\n  forms: {\n    web3forms: process.env.NEXT_PUBLIC_WEB3FORMS_ACCESS_KEY,\n  },\n  chat: {\n    crisp: process.env.NEXT_PUBLIC_CRISP_WEBSITE_ID,\n    tawkTo: process.env.NEXT_PUBLIC_TAWK_TO_PROPERTY_ID,\n  },\n  newsletter: {\n    mailchimp: process.env.NEXT_PUBLIC_MAILCHIMP_API_KEY,\n    convertkit: process.env.CONVERTKIT_API_KEY,\n  }\n} as const;\n\n// SEO Configuration\nexport const SEO_CONFIG = {\n  defaultTitle: SITE_CONFIG.name + ' | ' + SITE_CONFIG.tagline,\n  titleTemplate: '%s | ' + SITE_CONFIG.name + ' - Custom Mobile App Development',\n  description: SITE_CONFIG.description,\n  keywords: [\n    'mobile app development',\n    'website to app conversion',\n    'custom mobile apps',\n    'iOS app development',\n    'Android app development',\n    'mobile app builder',\n    'app development services',\n    'native mobile apps',\n    'mobile app design',\n    'app development company'\n  ],\n  openGraph: {\n    type: 'website',\n    locale: 'en_US',\n    url: SITE_CONFIG.url,\n    siteName: SITE_CONFIG.name,\n    images: [\n      {\n        url: '/og-image.png',\n        width: 1200,\n        height: 630,\n        alt: SITE_CONFIG.name + ' - ' + SITE_CONFIG.tagline,\n      },\n    ],\n  },\n  twitter: {\n    handle: '@mobilify',\n    site: '@mobilify',\n    cardType: 'summary_large_image',\n  },\n} as const;\n\n// Animation Configuration\nexport const ANIMATION_CONFIG = {\n  duration: {\n    fast: 0.2,\n    normal: 0.4,\n    slow: 0.6,\n    verySlow: 1.0\n  },\n  easing: {\n    easeInOut: [0.4, 0, 0.2, 1],\n    easeOut: [0, 0, 0.2, 1],\n    easeIn: [0.4, 0, 1, 1]\n  }\n} as const;\n\n// Development Configuration\nexport const DEV_CONFIG = {\n  isDevelopment: process.env.NODE_ENV === 'development',\n  isProduction: process.env.NODE_ENV === 'production',\n  enableDebugLogs: process.env.NODE_ENV === 'development',\n  enableAnalytics: process.env.NODE_ENV === 'production',\n} as const;\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GAED,gBAAgB;;;;;;;;;;;;;AAKT;AAJA,MAAM,cAAc;IACzB,MAAM;IACN,SAAS;IACT,aAAa;IACb,KAAK,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI;IACzC,QAAQ;AACV;AAGO,MAAM,eAAe;IAC1B,OAAO;IACP,OAAO;IACP,SAAS;QACP,QAAQ;QACR,MAAM;QACN,OAAO;QACP,KAAK;QACL,SAAS;IACX;AACF;AAGO,MAAM,eAAe;IAC1B,SAAS;IACT,UAAU;IACV,QAAQ;IACR,UAAU;IACV,WAAW;AACb;AAGO,MAAM,aAAa;IACxB,MAAM;QACJ;YAAE,OAAO;YAAY,MAAM;YAAsB,IAAI;QAAoB;QACzE;YAAE,OAAO;YAAgB,MAAM;YAAY,IAAI;QAAU;QACzD;YAAE,OAAO;YAAY,MAAM;YAAU,IAAI;QAAQ;KAClD;IACD,QAAQ;QACN,SAAS;YACP;gBAAE,OAAO;gBAAS,MAAM;YAAS;YACjC;gBAAE,OAAO;gBAAY,MAAM;YAAY;YACvC;gBAAE,OAAO;gBAAQ,MAAM;YAAQ;YAC/B;gBAAE,OAAO;gBAAO,MAAM;YAAO;SAC9B;QACD,OAAO;YACL;gBAAE,OAAO;gBAAkB,MAAM;YAAW;YAC5C;gBAAE,OAAO;gBAAoB,MAAM;YAAS;YAC5C;gBAAE,OAAO;gBAAiB,MAAM;YAAW;SAC5C;QACD,SAAS;YACP;gBAAE,OAAO;gBAAc,MAAM;YAAW;YACxC;gBAAE,OAAO;gBAAe,MAAM;YAAQ;YACtC;gBAAE,OAAO;gBAAiB,MAAM;YAAQ;SACzC;IACH;AACF;AAGO,MAAM,mBAAmB;IAC9B,MAAM;QACJ,UAAU;QACV,SAAS;QACT,YAAY;IACd;IACA,SAAS;QACP,UAAU;QACV,SAAS;QACT,YAAY;QACZ,gBAAgB;QAChB,cAAc;IAChB;IACA,UAAU;QACR,UAAU;QACV,SAAS;IACX;IACA,SAAS;QACP,UAAU;QACV,SAAS;IACX;IACA,OAAO;QACL,UAAU;QACV,SAAS;IACX;AACF;AAGO,MAAM,WAAW;IACtB,SAAS;QACP,MAAM;QACN,aAAa;QACb,OAAO;QACP,UAAU;YAAC;YAAsB;YAAiB;SAAiB;QACnE,SAAS;IACX;IACA,QAAQ;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,UAAU;YAAC;YAAe;YAAgB;SAAoB;QAC9D,SAAS;IACX;IACA,YAAY;QACV,MAAM;QACN,aAAa;QACb,OAAO;QACP,UAAU;YAAC;YAAoB;YAAyB;SAAkB;QAC1E,SAAS;IACX;AACF;AAGO,MAAM,oBAAoB;IAC/B,WAAW;QACT,eAAe;IACjB;IACA,OAAO;QACL,SAAS;IACX;IACA,MAAM;QACJ,OAAO,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,4BAA4B;QAC/C,QAAQ,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,+BAA+B;IACrD;IACA,YAAY;QACV,WAAW,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,6BAA6B;QACpD,YAAY,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,kBAAkB;IAC5C;AACF;AAGO,MAAM,aAAa;IACxB,cAAc,YAAY,IAAI,GAAG,QAAQ,YAAY,OAAO;IAC5D,eAAe,UAAU,YAAY,IAAI,GAAG;IAC5C,aAAa,YAAY,WAAW;IACpC,UAAU;QACR;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,WAAW;QACT,MAAM;QACN,QAAQ;QACR,KAAK,YAAY,GAAG;QACpB,UAAU,YAAY,IAAI;QAC1B,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK,YAAY,IAAI,GAAG,QAAQ,YAAY,OAAO;YACrD;SACD;IACH;IACA,SAAS;QACP,QAAQ;QACR,MAAM;QACN,UAAU;IACZ;AACF;AAGO,MAAM,mBAAmB;IAC9B,UAAU;QACR,MAAM;QACN,QAAQ;QACR,MAAM;QACN,UAAU;IACZ;IACA,QAAQ;QACN,WAAW;YAAC;YAAK;YAAG;YAAK;SAAE;QAC3B,SAAS;YAAC;YAAG;YAAG;YAAK;SAAE;QACvB,QAAQ;YAAC;YAAK;YAAG;YAAG;SAAE;IACxB;AACF;AAGO,MAAM,aAAa;IACxB,eAAe,oDAAyB;IACxC,cAAc,oDAAyB;IACvC,iBAAiB,oDAAyB;IAC1C,iBAAiB,oDAAyB;AAC5C", "debugId": null}}, {"offset": {"line": 443, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/hooks/useAnalytics.ts"], "sourcesContent": ["/**\n * Analytics Hook\n * \n * Unified interface for all analytics tracking across the site.\n * Abstracts away the implementation details of Google Analytics.\n */\n\nimport { useCallback } from 'react';\nimport { DEV_CONFIG } from '@/config/site';\n\n// Analytics event types\nexport interface AnalyticsEvent {\n  name: string;\n  category?: string;\n  label?: string;\n  value?: number;\n  custom_parameters?: Record<string, any>;\n}\n\n// Predefined event types for type safety\nexport const ANALYTICS_EVENTS = {\n  // Navigation & CTA Events\n  HERO_CTA_CLICK: 'hero_cta_click',\n  CONTACT_CTA_CLICK: 'contact_cta_click',\n  SERVICES_CTA_CLICK: 'services_cta_click',\n  \n  // Demo Interaction Events\n  DEMO_INTERACTION: 'demo_interaction',\n  DEMO_TAB_SWITCH: 'demo_tab_switch',\n  DEMO_ANIMATION_COMPLETE: 'demo_animation_complete',\n  DEMO_PREVIEW_CLICK: 'demo_preview_click',\n  \n  // Form Events\n  FORM_SUBMIT: 'form_submit',\n  FORM_SUCCESS: 'form_success',\n  FORM_ERROR: 'form_error',\n  FORM_FIELD_FOCUS: 'form_field_focus',\n  \n  // Content Interaction Events\n  BLOG_POST_CLICK: 'blog_post_click',\n  FAQ_ITEM_EXPAND: 'faq_item_expand',\n  EXTERNAL_LINK_CLICK: 'external_link_click',\n  \n  // Chat & Support Events\n  CHAT_TRIGGER_CLICK: 'chat_trigger_click',\n  PHONE_CLICK: 'phone_click',\n  EMAIL_CLICK: 'email_click',\n  \n  // Page Events\n  PAGE_VIEW: 'page_view',\n  SCROLL_DEPTH: 'scroll_depth',\n  TIME_ON_PAGE: 'time_on_page',\n} as const;\n\n// Hook implementation\nexport function useAnalytics() {\n  const trackEvent = useCallback((\n    eventName: string,\n    eventData?: Partial<AnalyticsEvent>\n  ) => {\n    // Only track in production or when explicitly enabled\n    if (!DEV_CONFIG.enableAnalytics && !DEV_CONFIG.isDevelopment) {\n      return;\n    }\n\n    // Log in development for debugging\n    if (DEV_CONFIG.isDevelopment && DEV_CONFIG.enableDebugLogs) {\n      console.log('📊 Analytics Event:', {\n        event: eventName,\n        ...eventData\n      });\n    }\n\n    // Send to Google Analytics if available\n    if (typeof window !== 'undefined' && (window as any).gtag) {\n      try {\n        (window as any).gtag('event', eventName, {\n          event_category: eventData?.category || 'engagement',\n          event_label: eventData?.label,\n          value: eventData?.value,\n          ...eventData?.custom_parameters\n        });\n      } catch (error) {\n        console.warn('Failed to send analytics event:', error);\n      }\n    }\n  }, []);\n\n  // Convenience methods for common events\n  const trackNavigation = useCallback((destination: string, source?: string) => {\n    trackEvent(ANALYTICS_EVENTS.HERO_CTA_CLICK, {\n      category: 'navigation',\n      label: destination,\n      custom_parameters: { source }\n    });\n  }, [trackEvent]);\n\n  const trackFormInteraction = useCallback((\n    action: 'submit' | 'success' | 'error' | 'field_focus',\n    formName: string,\n    fieldName?: string\n  ) => {\n    const eventMap = {\n      submit: ANALYTICS_EVENTS.FORM_SUBMIT,\n      success: ANALYTICS_EVENTS.FORM_SUCCESS,\n      error: ANALYTICS_EVENTS.FORM_ERROR,\n      field_focus: ANALYTICS_EVENTS.FORM_FIELD_FOCUS\n    };\n\n    trackEvent(eventMap[action], {\n      category: 'form_interaction',\n      label: formName,\n      custom_parameters: { field_name: fieldName }\n    });\n  }, [trackEvent]);\n\n  const trackDemoInteraction = useCallback((\n    action: 'tab_switch' | 'preview_click' | 'animation_complete',\n    details?: string\n  ) => {\n    const eventMap = {\n      tab_switch: ANALYTICS_EVENTS.DEMO_TAB_SWITCH,\n      preview_click: ANALYTICS_EVENTS.DEMO_PREVIEW_CLICK,\n      animation_complete: ANALYTICS_EVENTS.DEMO_ANIMATION_COMPLETE\n    };\n\n    trackEvent(eventMap[action], {\n      category: 'demo_interaction',\n      label: details\n    });\n  }, [trackEvent]);\n\n  const trackContentInteraction = useCallback((\n    contentType: 'blog_post' | 'faq_item' | 'external_link',\n    contentId: string,\n    action?: string\n  ) => {\n    const eventMap = {\n      blog_post: ANALYTICS_EVENTS.BLOG_POST_CLICK,\n      faq_item: ANALYTICS_EVENTS.FAQ_ITEM_EXPAND,\n      external_link: ANALYTICS_EVENTS.EXTERNAL_LINK_CLICK\n    };\n\n    trackEvent(eventMap[contentType], {\n      category: 'content_interaction',\n      label: contentId,\n      custom_parameters: { action }\n    });\n  }, [trackEvent]);\n\n  const trackSupportInteraction = useCallback((\n    method: 'chat' | 'phone' | 'email',\n    source?: string\n  ) => {\n    const eventMap = {\n      chat: ANALYTICS_EVENTS.CHAT_TRIGGER_CLICK,\n      phone: ANALYTICS_EVENTS.PHONE_CLICK,\n      email: ANALYTICS_EVENTS.EMAIL_CLICK\n    };\n\n    trackEvent(eventMap[method], {\n      category: 'support_interaction',\n      label: method,\n      custom_parameters: { source }\n    });\n  }, [trackEvent]);\n\n  const trackPageView = useCallback((pagePath: string, pageTitle?: string) => {\n    trackEvent(ANALYTICS_EVENTS.PAGE_VIEW, {\n      category: 'page_interaction',\n      label: pagePath,\n      custom_parameters: {\n        page_title: pageTitle,\n        page_path: pagePath\n      }\n    });\n  }, [trackEvent]);\n\n  const trackFormSubmission = useCallback((formName: string, success: boolean, metadata?: Record<string, unknown>) => {\n    trackEvent(ANALYTICS_EVENTS.FORM_SUBMIT, {\n      category: 'form',\n      label: formName,\n      custom_parameters: {\n        success,\n        ...metadata\n      }\n    });\n  }, [trackEvent]);\n\n  return {\n    // Core tracking function\n    trackEvent,\n\n    // Convenience methods\n    trackNavigation,\n    trackFormInteraction,\n    trackFormSubmission,\n    trackDemoInteraction,\n    trackContentInteraction,\n    trackSupportInteraction,\n    trackPageView,\n\n    // Event constants for external use\n    EVENTS: ANALYTICS_EVENTS\n  };\n}\n\n\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;AAED;AACA;;;;AAYO,MAAM,mBAAmB;IAC9B,0BAA0B;IAC1B,gBAAgB;IAChB,mBAAmB;IACnB,oBAAoB;IAEpB,0BAA0B;IAC1B,kBAAkB;IAClB,iBAAiB;IACjB,yBAAyB;IACzB,oBAAoB;IAEpB,cAAc;IACd,aAAa;IACb,cAAc;IACd,YAAY;IACZ,kBAAkB;IAElB,6BAA6B;IAC7B,iBAAiB;IACjB,iBAAiB;IACjB,qBAAqB;IAErB,wBAAwB;IACxB,oBAAoB;IACpB,aAAa;IACb,aAAa;IAEb,cAAc;IACd,WAAW;IACX,cAAc;IACd,cAAc;AAChB;AAGO,SAAS;;IACd,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE,CAC7B,WACA;YAEA,sDAAsD;YACtD,IAAI,CAAC,wHAAA,CAAA,aAAU,CAAC,eAAe,IAAI,CAAC,wHAAA,CAAA,aAAU,CAAC,aAAa,EAAE;gBAC5D;YACF;YAEA,mCAAmC;YACnC,IAAI,wHAAA,CAAA,aAAU,CAAC,aAAa,IAAI,wHAAA,CAAA,aAAU,CAAC,eAAe,EAAE;gBAC1D,QAAQ,GAAG,CAAC,uBAAuB;oBACjC,OAAO;oBACP,GAAG,SAAS;gBACd;YACF;YAEA,wCAAwC;YACxC,IAAI,aAAkB,eAAe,AAAC,OAAe,IAAI,EAAE;gBACzD,IAAI;oBACD,OAAe,IAAI,CAAC,SAAS,WAAW;wBACvC,gBAAgB,WAAW,YAAY;wBACvC,aAAa,WAAW;wBACxB,OAAO,WAAW;wBAClB,GAAG,WAAW,iBAAiB;oBACjC;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,IAAI,CAAC,mCAAmC;gBAClD;YACF;QACF;+CAAG,EAAE;IAEL,wCAAwC;IACxC,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,CAAC,aAAqB;YACxD,WAAW,iBAAiB,cAAc,EAAE;gBAC1C,UAAU;gBACV,OAAO;gBACP,mBAAmB;oBAAE;gBAAO;YAC9B;QACF;oDAAG;QAAC;KAAW;IAEf,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE,CACvC,QACA,UACA;YAEA,MAAM,WAAW;gBACf,QAAQ,iBAAiB,WAAW;gBACpC,SAAS,iBAAiB,YAAY;gBACtC,OAAO,iBAAiB,UAAU;gBAClC,aAAa,iBAAiB,gBAAgB;YAChD;YAEA,WAAW,QAAQ,CAAC,OAAO,EAAE;gBAC3B,UAAU;gBACV,OAAO;gBACP,mBAAmB;oBAAE,YAAY;gBAAU;YAC7C;QACF;yDAAG;QAAC;KAAW;IAEf,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE,CACvC,QACA;YAEA,MAAM,WAAW;gBACf,YAAY,iBAAiB,eAAe;gBAC5C,eAAe,iBAAiB,kBAAkB;gBAClD,oBAAoB,iBAAiB,uBAAuB;YAC9D;YAEA,WAAW,QAAQ,CAAC,OAAO,EAAE;gBAC3B,UAAU;gBACV,OAAO;YACT;QACF;yDAAG;QAAC;KAAW;IAEf,MAAM,0BAA0B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE,CAC1C,aACA,WACA;YAEA,MAAM,WAAW;gBACf,WAAW,iBAAiB,eAAe;gBAC3C,UAAU,iBAAiB,eAAe;gBAC1C,eAAe,iBAAiB,mBAAmB;YACrD;YAEA,WAAW,QAAQ,CAAC,YAAY,EAAE;gBAChC,UAAU;gBACV,OAAO;gBACP,mBAAmB;oBAAE;gBAAO;YAC9B;QACF;4DAAG;QAAC;KAAW;IAEf,MAAM,0BAA0B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE,CAC1C,QACA;YAEA,MAAM,WAAW;gBACf,MAAM,iBAAiB,kBAAkB;gBACzC,OAAO,iBAAiB,WAAW;gBACnC,OAAO,iBAAiB,WAAW;YACrC;YAEA,WAAW,QAAQ,CAAC,OAAO,EAAE;gBAC3B,UAAU;gBACV,OAAO;gBACP,mBAAmB;oBAAE;gBAAO;YAC9B;QACF;4DAAG;QAAC;KAAW;IAEf,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,CAAC,UAAkB;YACnD,WAAW,iBAAiB,SAAS,EAAE;gBACrC,UAAU;gBACV,OAAO;gBACP,mBAAmB;oBACjB,YAAY;oBACZ,WAAW;gBACb;YACF;QACF;kDAAG;QAAC;KAAW;IAEf,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,CAAC,UAAkB,SAAkB;YAC3E,WAAW,iBAAiB,WAAW,EAAE;gBACvC,UAAU;gBACV,OAAO;gBACP,mBAAmB;oBACjB;oBACA,GAAG,QAAQ;gBACb;YACF;QACF;wDAAG;QAAC;KAAW;IAEf,OAAO;QACL,yBAAyB;QACzB;QAEA,sBAAsB;QACtB;QACA;QACA;QACA;QACA;QACA;QACA;QAEA,mCAAmC;QACnC,QAAQ;IACV;AACF;GAtJgB", "debugId": null}}, {"offset": {"line": 652, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/hooks/useContactForm.ts"], "sourcesContent": ["/**\n * Contact Form Hook\n * \n * Handles form state management, validation, and submission logic\n * for the contact form component.\n */\n\nimport { useState, useCallback } from 'react';\nimport { EXTERNAL_SERVICES } from '@/config/site';\nimport { useAnalytics } from './useAnalytics';\n\n// Form data interface\nexport interface ContactFormData {\n  name: string;\n  email: string;\n  company: string;\n  projectType: string;\n  message: string;\n}\n\n// Form validation errors\nexport interface FormErrors {\n  name?: string;\n  email?: string;\n  company?: string;\n  projectType?: string;\n  message?: string;\n  general?: string;\n}\n\n// Form state\nexport interface FormState {\n  data: ContactFormData;\n  errors: FormErrors;\n  isSubmitting: boolean;\n  isSubmitted: boolean;\n  submitSuccess: boolean;\n}\n\n// Initial form data\nconst initialFormData: ContactFormData = {\n  name: '',\n  email: '',\n  company: '',\n  projectType: '',\n  message: ''\n};\n\n// Initial form state\nconst initialFormState: FormState = {\n  data: initialFormData,\n  errors: {},\n  isSubmitting: false,\n  isSubmitted: false,\n  submitSuccess: false\n};\n\n// Validation rules\nconst validateField = (name: keyof ContactFormData, value: string): string | undefined => {\n  switch (name) {\n    case 'name':\n      if (!value.trim()) return 'Name is required';\n      if (value.trim().length < 2) return 'Name must be at least 2 characters';\n      return undefined;\n      \n    case 'email':\n      if (!value.trim()) return 'Email is required';\n      const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n      if (!emailRegex.test(value)) return 'Please enter a valid email address';\n      return undefined;\n      \n    case 'company':\n      // Company is optional, but if provided should be reasonable length\n      if (value.trim() && value.trim().length < 2) return 'Company name must be at least 2 characters';\n      return undefined;\n      \n    case 'projectType':\n      if (!value.trim()) return 'Please select a project type';\n      return undefined;\n      \n    case 'message':\n      if (!value.trim()) return 'Message is required';\n      if (value.trim().length < 10) return 'Message must be at least 10 characters';\n      if (value.trim().length > 1000) return 'Message must be less than 1000 characters';\n      return undefined;\n      \n    default:\n      return undefined;\n  }\n};\n\n// Validate entire form\nconst validateForm = (data: ContactFormData): FormErrors => {\n  const errors: FormErrors = {};\n  \n  (Object.keys(data) as Array<keyof ContactFormData>).forEach(field => {\n    const error = validateField(field, data[field]);\n    if (error) {\n      errors[field] = error;\n    }\n  });\n  \n  return errors;\n};\n\n// Hook implementation\nexport function useContactForm() {\n  const [formState, setFormState] = useState<FormState>(initialFormState);\n  const { trackFormInteraction } = useAnalytics();\n\n  // Update form data\n  const updateField = useCallback((field: keyof ContactFormData, value: string) => {\n    setFormState(prev => ({\n      ...prev,\n      data: {\n        ...prev.data,\n        [field]: value\n      },\n      // Clear field error when user starts typing\n      errors: {\n        ...prev.errors,\n        [field]: undefined,\n        general: undefined\n      }\n    }));\n  }, []);\n\n  // Handle field focus for analytics\n  const handleFieldFocus = useCallback((field: keyof ContactFormData) => {\n    trackFormInteraction('field_focus', 'contact_form', field);\n  }, [trackFormInteraction]);\n\n  // Validate single field\n  const validateSingleField = useCallback((field: keyof ContactFormData) => {\n    const error = validateField(field, formState.data[field]);\n    setFormState(prev => ({\n      ...prev,\n      errors: {\n        ...prev.errors,\n        [field]: error\n      }\n    }));\n    return !error;\n  }, [formState.data]);\n\n  // Reset form\n  const resetForm = useCallback(() => {\n    setFormState(initialFormState);\n  }, []);\n\n  // Submit form\n  const submitForm = useCallback(async () => {\n    // Validate form\n    const errors = validateForm(formState.data);\n    \n    if (Object.keys(errors).length > 0) {\n      setFormState(prev => ({\n        ...prev,\n        errors\n      }));\n      return false;\n    }\n\n    // Check if Web3Forms is configured\n    if (!EXTERNAL_SERVICES.forms.web3forms) {\n      setFormState(prev => ({\n        ...prev,\n        errors: {\n          general: 'Form service is not configured. Please contact us directly.'\n        }\n      }));\n      trackFormInteraction('error', 'contact_form');\n      return false;\n    }\n\n    setFormState(prev => ({\n      ...prev,\n      isSubmitting: true,\n      errors: {}\n    }));\n\n    trackFormInteraction('submit', 'contact_form');\n\n    try {\n      const formData = new FormData();\n      formData.append('access_key', EXTERNAL_SERVICES.forms.web3forms);\n      formData.append('name', formState.data.name);\n      formData.append('email', formState.data.email);\n      formData.append('company', formState.data.company);\n      formData.append('project_type', formState.data.projectType);\n      formData.append('message', formState.data.message);\n      formData.append('from_name', 'Mobilify Contact Form');\n      formData.append('subject', `New Contact Form Submission from ${formState.data.name}`);\n\n      const response = await fetch('https://api.web3forms.com/submit', {\n        method: 'POST',\n        body: formData\n      });\n\n      const result = await response.json();\n\n      if (result.success) {\n        setFormState(prev => ({\n          ...prev,\n          isSubmitting: false,\n          isSubmitted: true,\n          submitSuccess: true\n        }));\n        trackFormInteraction('success', 'contact_form');\n        return true;\n      } else {\n        throw new Error(result.message || 'Form submission failed');\n      }\n    } catch (error) {\n      console.error('Form submission error:', error);\n      setFormState(prev => ({\n        ...prev,\n        isSubmitting: false,\n        isSubmitted: true,\n        submitSuccess: false,\n        errors: {\n          general: 'There was an error sending your message. Please try again or contact us directly.'\n        }\n      }));\n      trackFormInteraction('error', 'contact_form');\n      return false;\n    }\n  }, [formState.data, trackFormInteraction]);\n\n  return {\n    // Form state\n    formData: formState.data,\n    errors: formState.errors,\n    isSubmitting: formState.isSubmitting,\n    isSubmitted: formState.isSubmitted,\n    submitSuccess: formState.submitSuccess,\n    \n    // Form actions\n    updateField,\n    handleFieldFocus,\n    validateSingleField,\n    submitForm,\n    resetForm,\n    \n    // Validation helpers\n    isValid: Object.keys(formState.errors).length === 0,\n    hasErrors: Object.keys(formState.errors).length > 0\n  };\n}\n\n\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;AAED;AACA;AACA;;;;;AA8BA,oBAAoB;AACpB,MAAM,kBAAmC;IACvC,MAAM;IACN,OAAO;IACP,SAAS;IACT,aAAa;IACb,SAAS;AACX;AAEA,qBAAqB;AACrB,MAAM,mBAA8B;IAClC,MAAM;IACN,QAAQ,CAAC;IACT,cAAc;IACd,aAAa;IACb,eAAe;AACjB;AAEA,mBAAmB;AACnB,MAAM,gBAAgB,CAAC,MAA6B;IAClD,OAAQ;QACN,KAAK;YACH,IAAI,CAAC,MAAM,IAAI,IAAI,OAAO;YAC1B,IAAI,MAAM,IAAI,GAAG,MAAM,GAAG,GAAG,OAAO;YACpC,OAAO;QAET,KAAK;YACH,IAAI,CAAC,MAAM,IAAI,IAAI,OAAO;YAC1B,MAAM,aAAa;YACnB,IAAI,CAAC,WAAW,IAAI,CAAC,QAAQ,OAAO;YACpC,OAAO;QAET,KAAK;YACH,mEAAmE;YACnE,IAAI,MAAM,IAAI,MAAM,MAAM,IAAI,GAAG,MAAM,GAAG,GAAG,OAAO;YACpD,OAAO;QAET,KAAK;YACH,IAAI,CAAC,MAAM,IAAI,IAAI,OAAO;YAC1B,OAAO;QAET,KAAK;YACH,IAAI,CAAC,MAAM,IAAI,IAAI,OAAO;YAC1B,IAAI,MAAM,IAAI,GAAG,MAAM,GAAG,IAAI,OAAO;YACrC,IAAI,MAAM,IAAI,GAAG,MAAM,GAAG,MAAM,OAAO;YACvC,OAAO;QAET;YACE,OAAO;IACX;AACF;AAEA,uBAAuB;AACvB,MAAM,eAAe,CAAC;IACpB,MAAM,SAAqB,CAAC;IAE3B,OAAO,IAAI,CAAC,MAAuC,OAAO,CAAC,CAAA;QAC1D,MAAM,QAAQ,cAAc,OAAO,IAAI,CAAC,MAAM;QAC9C,IAAI,OAAO;YACT,MAAM,CAAC,MAAM,GAAG;QAClB;IACF;IAEA,OAAO;AACT;AAGO,SAAS;;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;IACtD,MAAM,EAAE,oBAAoB,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD;IAE5C,mBAAmB;IACnB,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,CAAC,OAA8B;YAC7D;2DAAa,CAAA,OAAQ,CAAC;wBACpB,GAAG,IAAI;wBACP,MAAM;4BACJ,GAAG,KAAK,IAAI;4BACZ,CAAC,MAAM,EAAE;wBACX;wBACA,4CAA4C;wBAC5C,QAAQ;4BACN,GAAG,KAAK,MAAM;4BACd,CAAC,MAAM,EAAE;4BACT,SAAS;wBACX;oBACF,CAAC;;QACH;kDAAG,EAAE;IAEL,mCAAmC;IACnC,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,CAAC;YACpC,qBAAqB,eAAe,gBAAgB;QACtD;uDAAG;QAAC;KAAqB;IAEzB,wBAAwB;IACxB,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE,CAAC;YACvC,MAAM,QAAQ,cAAc,OAAO,UAAU,IAAI,CAAC,MAAM;YACxD;mEAAa,CAAA,OAAQ,CAAC;wBACpB,GAAG,IAAI;wBACP,QAAQ;4BACN,GAAG,KAAK,MAAM;4BACd,CAAC,MAAM,EAAE;wBACX;oBACF,CAAC;;YACD,OAAO,CAAC;QACV;0DAAG;QAAC,UAAU,IAAI;KAAC;IAEnB,aAAa;IACb,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE;YAC5B,aAAa;QACf;gDAAG,EAAE;IAEL,cAAc;IACd,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE;YAC7B,gBAAgB;YAChB,MAAM,SAAS,aAAa,UAAU,IAAI;YAE1C,IAAI,OAAO,IAAI,CAAC,QAAQ,MAAM,GAAG,GAAG;gBAClC;8DAAa,CAAA,OAAQ,CAAC;4BACpB,GAAG,IAAI;4BACP;wBACF,CAAC;;gBACD,OAAO;YACT;YAEA,mCAAmC;YACnC,IAAI,CAAC,wHAAA,CAAA,oBAAiB,CAAC,KAAK,CAAC,SAAS,EAAE;gBACtC;8DAAa,CAAA,OAAQ,CAAC;4BACpB,GAAG,IAAI;4BACP,QAAQ;gCACN,SAAS;4BACX;wBACF,CAAC;;gBACD,qBAAqB,SAAS;gBAC9B,OAAO;YACT;YAEA;0DAAa,CAAA,OAAQ,CAAC;wBACpB,GAAG,IAAI;wBACP,cAAc;wBACd,QAAQ,CAAC;oBACX,CAAC;;YAED,qBAAqB,UAAU;YAE/B,IAAI;gBACF,MAAM,WAAW,IAAI;gBACrB,SAAS,MAAM,CAAC,cAAc,wHAAA,CAAA,oBAAiB,CAAC,KAAK,CAAC,SAAS;gBAC/D,SAAS,MAAM,CAAC,QAAQ,UAAU,IAAI,CAAC,IAAI;gBAC3C,SAAS,MAAM,CAAC,SAAS,UAAU,IAAI,CAAC,KAAK;gBAC7C,SAAS,MAAM,CAAC,WAAW,UAAU,IAAI,CAAC,OAAO;gBACjD,SAAS,MAAM,CAAC,gBAAgB,UAAU,IAAI,CAAC,WAAW;gBAC1D,SAAS,MAAM,CAAC,WAAW,UAAU,IAAI,CAAC,OAAO;gBACjD,SAAS,MAAM,CAAC,aAAa;gBAC7B,SAAS,MAAM,CAAC,WAAW,CAAC,iCAAiC,EAAE,UAAU,IAAI,CAAC,IAAI,EAAE;gBAEpF,MAAM,WAAW,MAAM,MAAM,oCAAoC;oBAC/D,QAAQ;oBACR,MAAM;gBACR;gBAEA,MAAM,SAAS,MAAM,SAAS,IAAI;gBAElC,IAAI,OAAO,OAAO,EAAE;oBAClB;kEAAa,CAAA,OAAQ,CAAC;gCACpB,GAAG,IAAI;gCACP,cAAc;gCACd,aAAa;gCACb,eAAe;4BACjB,CAAC;;oBACD,qBAAqB,WAAW;oBAChC,OAAO;gBACT,OAAO;oBACL,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;gBACpC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0BAA0B;gBACxC;8DAAa,CAAA,OAAQ,CAAC;4BACpB,GAAG,IAAI;4BACP,cAAc;4BACd,aAAa;4BACb,eAAe;4BACf,QAAQ;gCACN,SAAS;4BACX;wBACF,CAAC;;gBACD,qBAAqB,SAAS;gBAC9B,OAAO;YACT;QACF;iDAAG;QAAC,UAAU,IAAI;QAAE;KAAqB;IAEzC,OAAO;QACL,aAAa;QACb,UAAU,UAAU,IAAI;QACxB,QAAQ,UAAU,MAAM;QACxB,cAAc,UAAU,YAAY;QACpC,aAAa,UAAU,WAAW;QAClC,eAAe,UAAU,aAAa;QAEtC,eAAe;QACf;QACA;QACA;QACA;QACA;QAEA,qBAAqB;QACrB,SAAS,OAAO,IAAI,CAAC,UAAU,MAAM,EAAE,MAAM,KAAK;QAClD,WAAW,OAAO,IAAI,CAAC,UAAU,MAAM,EAAE,MAAM,GAAG;IACpD;AACF;GA9IgB;;QAEmB,+HAAA,CAAA,eAAY", "debugId": null}}, {"offset": {"line": 895, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/lib/sanity.ts"], "sourcesContent": ["import { createClient } from '@sanity/client';\nimport imageUrlBuilder from '@sanity/image-url';\n\n// Check if Sanity is configured\nconst isConfigured = !!(\n  process.env.NEXT_PUBLIC_SANITY_PROJECT_ID &&\n  process.env.NEXT_PUBLIC_SANITY_DATASET\n);\n\n// Sanity client configuration (only if configured)\nexport const client = isConfigured ? createClient({\n  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID!,\n  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',\n  apiVersion: '2024-01-01',\n  useCdn: process.env.NODE_ENV === 'production',\n  token: process.env.SANITY_API_TOKEN,\n}) : null;\n\n// Image URL builder (only if configured)\nconst builder = isConfigured && client ? imageUrlBuilder(client) : null;\n\nexport function urlFor(source: any) {\n  if (!builder) {\n    console.warn('Sanity not configured, returning placeholder image URL');\n    return { url: () => '/placeholder-image.jpg' };\n  }\n  return builder.image(source);\n}\n\n// Type definitions for Sanity documents\nexport interface SanityImage {\n  _type: 'image';\n  asset: {\n    _ref: string;\n    _type: 'reference';\n  };\n  alt?: string;\n}\n\nexport interface Category {\n  _id: string;\n  _type: 'category';\n  title: string;\n  slug: {\n    current: string;\n  };\n  description?: string;\n}\n\nexport interface BlogPost {\n  _id: string;\n  _type: 'post';\n  title: string;\n  slug: {\n    current: string;\n  };\n  author: string;\n  mainImage?: SanityImage;\n  categories: Category[];\n  publishedAt: string;\n  excerpt?: string;\n  body: any[]; // Portable Text\n  _createdAt: string;\n  _updatedAt: string;\n}\n\nexport interface FAQTopic {\n  _id: string;\n  _type: 'faqTopic';\n  title: string;\n  slug: {\n    current: string;\n  };\n  description?: string;\n}\n\nexport interface SiteSettings {\n  _id: string;\n  _type: 'siteSettings';\n  // Hero Section\n  heroHeadline?: string;\n  heroSubtext?: string;\n  heroButtonText?: string;\n  // Contact Section\n  contactHeadline?: string;\n  contactSubtext?: string;\n  contactButtonText?: string;\n  // Form Messages\n  formSuccessMessage?: string;\n  formErrorMessage?: string;\n  // Services Section\n  servicesHeadline?: string;\n  servicesSubtext?: string;\n  // Process Section\n  processHeadline?: string;\n  processSubtext?: string;\n  // About Section\n  aboutHeadline?: string;\n  aboutSubtext?: string;\n  // Footer\n  footerTagline?: string;\n  footerCopyright?: string;\n}\n\nexport interface FAQItem {\n  _id: string;\n  _type: 'faqItem';\n  question: string;\n  answer: any[]; // Portable Text\n  topic: FAQTopic;\n  relatedPosts?: BlogPost[];\n  _createdAt: string;\n  _updatedAt: string;\n}\n\n// GROQ queries\nexport const POSTS_QUERY = `*[_type == \"post\" && defined(slug.current)] | order(publishedAt desc) {\n  _id,\n  title,\n  slug,\n  author,\n  mainImage,\n  categories[]-> {\n    _id,\n    title,\n    slug\n  },\n  publishedAt,\n  excerpt,\n  _createdAt\n}`;\n\nexport const POST_QUERY = `*[_type == \"post\" && slug.current == $slug][0] {\n  _id,\n  title,\n  slug,\n  author,\n  mainImage,\n  categories[]-> {\n    _id,\n    title,\n    slug,\n    description\n  },\n  publishedAt,\n  excerpt,\n  body,\n  _createdAt,\n  _updatedAt\n}`;\n\nexport const CATEGORIES_QUERY = `*[_type == \"category\"] | order(title asc) {\n  _id,\n  title,\n  slug,\n  description\n}`;\n\nexport const POSTS_BY_CATEGORY_QUERY = `*[_type == \"post\" && $categoryId in categories[]._ref] | order(publishedAt desc) {\n  _id,\n  title,\n  slug,\n  author,\n  mainImage,\n  categories[]-> {\n    _id,\n    title,\n    slug\n  },\n  publishedAt,\n  excerpt,\n  _createdAt\n}`;\n\nexport const FAQ_ITEMS_QUERY = `*[_type == \"faqItem\"] | order(topic->title asc, _createdAt asc) {\n  _id,\n  question,\n  answer,\n  topic-> {\n    _id,\n    title,\n    slug,\n    description\n  },\n  relatedPosts[]-> {\n    _id,\n    title,\n    slug\n  },\n  _createdAt\n}`;\n\nexport const FAQ_TOPICS_QUERY = `*[_type == \"faqTopic\"] | order(title asc) {\n  _id,\n  title,\n  slug,\n  description\n}`;\n\nexport const RELATED_POSTS_QUERY = `*[_type == \"post\" && _id != $postId && count(categories[@._ref in $categoryIds]) > 0] | order(publishedAt desc)[0...3] {\n  _id,\n  title,\n  slug,\n  mainImage,\n  publishedAt,\n  excerpt\n}`;\n\n// Site Settings Query (Singleton)\nexport const SITE_SETTINGS_QUERY = `*[_type == \"siteSettings\"][0] {\n  _id,\n  heroHeadline,\n  heroSubtext,\n  heroButtonText,\n  contactHeadline,\n  contactSubtext,\n  contactButtonText,\n  formSuccessMessage,\n  formErrorMessage,\n  servicesHeadline,\n  servicesSubtext,\n  processHeadline,\n  processSubtext,\n  aboutHeadline,\n  aboutSubtext,\n  footerTagline,\n  footerCopyright\n}`;\n\n// Utility functions\nexport async function getAllPosts(): Promise<BlogPost[]> {\n  if (!client) {\n    console.warn('Sanity not configured, returning empty posts array');\n    return [];\n  }\n  return await client.fetch(POSTS_QUERY);\n}\n\nexport async function getPostBySlug(slug: string): Promise<BlogPost | null> {\n  if (!client) {\n    console.warn('Sanity not configured, returning null for post');\n    return null;\n  }\n  return await client.fetch(POST_QUERY, { slug });\n}\n\nexport async function getAllCategories(): Promise<Category[]> {\n  if (!client) {\n    console.warn('Sanity not configured, returning empty categories array');\n    return [];\n  }\n  return await client.fetch(CATEGORIES_QUERY);\n}\n\nexport async function getPostsByCategory(categoryId: string): Promise<BlogPost[]> {\n  if (!client) {\n    console.warn('Sanity not configured, returning empty posts array');\n    return [];\n  }\n  return await client.fetch(POSTS_BY_CATEGORY_QUERY, { categoryId });\n}\n\nexport async function getAllFAQItems(): Promise<FAQItem[]> {\n  if (!client) {\n    console.warn('Sanity not configured, returning empty FAQ items array');\n    return [];\n  }\n  return await client.fetch(FAQ_ITEMS_QUERY);\n}\n\nexport async function getAllFAQTopics(): Promise<FAQTopic[]> {\n  if (!client) {\n    console.warn('Sanity not configured, returning empty FAQ topics array');\n    return [];\n  }\n  return await client.fetch(FAQ_TOPICS_QUERY);\n}\n\nexport async function getRelatedPosts(postId: string, categoryIds: string[]): Promise<BlogPost[]> {\n  if (!client) {\n    console.warn('Sanity not configured, returning empty related posts array');\n    return [];\n  }\n  return await client.fetch(RELATED_POSTS_QUERY, { postId, categoryIds });\n}\n\nexport async function getSiteSettings(): Promise<SiteSettings | null> {\n  if (!client) {\n    console.warn('Sanity not configured, returning null for site settings');\n    return null;\n  }\n  try {\n    return await client.fetch(SITE_SETTINGS_QUERY);\n  } catch (error) {\n    console.warn('Failed to fetch site settings from Sanity:', error);\n    return null;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAKE;AALF;AACA;;;AAEA,gCAAgC;AAChC,MAAM,eAAe,CAAC,CAAC,CACrB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,6BAA6B,IACzC,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,0BAA0B,AACxC;AAGO,MAAM,SAAS,eAAe,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE;IAChD,WAAW,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,6BAA6B;IACpD,SAAS,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,0BAA0B,IAAI;IACnD,YAAY;IACZ,QAAQ,oDAAyB;IACjC,OAAO,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,gBAAgB;AACrC,KAAK;AAEL,yCAAyC;AACzC,MAAM,UAAU,gBAAgB,SAAS,CAAA,GAAA,oLAAA,CAAA,UAAe,AAAD,EAAE,UAAU;AAE5D,SAAS,OAAO,MAAW;IAChC,IAAI,CAAC,SAAS;QACZ,QAAQ,IAAI,CAAC;QACb,OAAO;YAAE,KAAK,IAAM;QAAyB;IAC/C;IACA,OAAO,QAAQ,KAAK,CAAC;AACvB;AAyFO,MAAM,cAAc,CAAC;;;;;;;;;;;;;;CAc3B,CAAC;AAEK,MAAM,aAAa,CAAC;;;;;;;;;;;;;;;;;CAiB1B,CAAC;AAEK,MAAM,mBAAmB,CAAC;;;;;CAKhC,CAAC;AAEK,MAAM,0BAA0B,CAAC;;;;;;;;;;;;;;CAcvC,CAAC;AAEK,MAAM,kBAAkB,CAAC;;;;;;;;;;;;;;;;CAgB/B,CAAC;AAEK,MAAM,mBAAmB,CAAC;;;;;CAKhC,CAAC;AAEK,MAAM,sBAAsB,CAAC;;;;;;;CAOnC,CAAC;AAGK,MAAM,sBAAsB,CAAC;;;;;;;;;;;;;;;;;;CAkBnC,CAAC;AAGK,eAAe;IACpB,IAAI,CAAC,QAAQ;QACX,QAAQ,IAAI,CAAC;QACb,OAAO,EAAE;IACX;IACA,OAAO,MAAM,OAAO,KAAK,CAAC;AAC5B;AAEO,eAAe,cAAc,IAAY;IAC9C,IAAI,CAAC,QAAQ;QACX,QAAQ,IAAI,CAAC;QACb,OAAO;IACT;IACA,OAAO,MAAM,OAAO,KAAK,CAAC,YAAY;QAAE;IAAK;AAC/C;AAEO,eAAe;IACpB,IAAI,CAAC,QAAQ;QACX,QAAQ,IAAI,CAAC;QACb,OAAO,EAAE;IACX;IACA,OAAO,MAAM,OAAO,KAAK,CAAC;AAC5B;AAEO,eAAe,mBAAmB,UAAkB;IACzD,IAAI,CAAC,QAAQ;QACX,QAAQ,IAAI,CAAC;QACb,OAAO,EAAE;IACX;IACA,OAAO,MAAM,OAAO,KAAK,CAAC,yBAAyB;QAAE;IAAW;AAClE;AAEO,eAAe;IACpB,IAAI,CAAC,QAAQ;QACX,QAAQ,IAAI,CAAC;QACb,OAAO,EAAE;IACX;IACA,OAAO,MAAM,OAAO,KAAK,CAAC;AAC5B;AAEO,eAAe;IACpB,IAAI,CAAC,QAAQ;QACX,QAAQ,IAAI,CAAC;QACb,OAAO,EAAE;IACX;IACA,OAAO,MAAM,OAAO,KAAK,CAAC;AAC5B;AAEO,eAAe,gBAAgB,MAAc,EAAE,WAAqB;IACzE,IAAI,CAAC,QAAQ;QACX,QAAQ,IAAI,CAAC;QACb,OAAO,EAAE;IACX;IACA,OAAO,MAAM,OAAO,KAAK,CAAC,qBAAqB;QAAE;QAAQ;IAAY;AACvE;AAEO,eAAe;IACpB,IAAI,CAAC,QAAQ;QACX,QAAQ,IAAI,CAAC;QACb,OAAO;IACT;IACA,IAAI;QACF,OAAO,MAAM,OAAO,KAAK,CAAC;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,8CAA8C;QAC3D,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 1121, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/hooks/useSiteSettings.ts"], "sourcesContent": ["/**\n * Site Settings Hook\n * \n * Fetches site settings from Sanity CMS with fallback to static configuration.\n * Provides a unified interface for accessing dynamic content across the site.\n */\n\nimport { useState, useEffect } from 'react';\nimport { getSiteSettings, type SiteSettings } from '@/lib/sanity';\nimport { FALLBACK_CONTENT } from '@/config/site';\n\n// Combined settings interface with fallbacks\nexport interface SiteSettingsWithFallbacks {\n  // Hero Section\n  heroHeadline: string;\n  heroSubtext: string;\n  heroButtonText: string;\n  \n  // Contact Section\n  contactHeadline: string;\n  contactSubtext: string;\n  contactButtonText: string;\n  \n  // Form Messages\n  formSuccessMessage: string;\n  formErrorMessage: string;\n  \n  // Services Section\n  servicesHeadline: string;\n  servicesSubtext: string;\n  \n  // Process Section\n  processHeadline: string;\n  processSubtext: string;\n  \n  // About Section\n  aboutHeadline: string;\n  aboutSubtext: string;\n  \n  // Footer\n  footerTagline: string;\n  footerCopyright: string;\n  \n  // Meta information\n  isLoading: boolean;\n  isFromCMS: boolean;\n  error: string | null;\n}\n\n// Create settings with fallbacks\nconst createSettingsWithFallbacks = (\n  cmsSettings: SiteSettings | null,\n  isLoading: boolean = false,\n  error: string | null = null\n): SiteSettingsWithFallbacks => {\n  const currentYear = new Date().getFullYear();\n  \n  return {\n    // Hero Section\n    heroHeadline: cmsSettings?.heroHeadline || FALLBACK_CONTENT.hero.headline,\n    heroSubtext: cmsSettings?.heroSubtext || FALLBACK_CONTENT.hero.subtext,\n    heroButtonText: cmsSettings?.heroButtonText || FALLBACK_CONTENT.hero.buttonText,\n    \n    // Contact Section\n    contactHeadline: cmsSettings?.contactHeadline || FALLBACK_CONTENT.contact.headline,\n    contactSubtext: cmsSettings?.contactSubtext || FALLBACK_CONTENT.contact.subtext,\n    contactButtonText: cmsSettings?.contactButtonText || FALLBACK_CONTENT.contact.buttonText,\n    \n    // Form Messages\n    formSuccessMessage: cmsSettings?.formSuccessMessage || FALLBACK_CONTENT.contact.successMessage,\n    formErrorMessage: cmsSettings?.formErrorMessage || FALLBACK_CONTENT.contact.errorMessage,\n    \n    // Services Section\n    servicesHeadline: cmsSettings?.servicesHeadline || FALLBACK_CONTENT.services.headline,\n    servicesSubtext: cmsSettings?.servicesSubtext || FALLBACK_CONTENT.services.subtext,\n    \n    // Process Section\n    processHeadline: cmsSettings?.processHeadline || FALLBACK_CONTENT.process.headline,\n    processSubtext: cmsSettings?.processSubtext || FALLBACK_CONTENT.process.subtext,\n    \n    // About Section\n    aboutHeadline: cmsSettings?.aboutHeadline || FALLBACK_CONTENT.about.headline,\n    aboutSubtext: cmsSettings?.aboutSubtext || FALLBACK_CONTENT.about.subtext,\n    \n    // Footer\n    footerTagline: cmsSettings?.footerTagline || 'Building the future of mobile apps',\n    footerCopyright: cmsSettings?.footerCopyright || `© ${currentYear} Mobilify. All rights reserved.`,\n    \n    // Meta information\n    isLoading,\n    isFromCMS: !!cmsSettings,\n    error\n  };\n};\n\n// Hook implementation\nexport function useSiteSettings(): SiteSettingsWithFallbacks {\n  const [cmsSettings, setCmsSettings] = useState<SiteSettings | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    let isMounted = true;\n\n    const fetchSettings = async () => {\n      try {\n        setIsLoading(true);\n        setError(null);\n        \n        const settings = await getSiteSettings();\n        \n        if (isMounted) {\n          setCmsSettings(settings);\n          setIsLoading(false);\n        }\n      } catch (err) {\n        if (isMounted) {\n          const errorMessage = err instanceof Error ? err.message : 'Failed to fetch site settings';\n          setError(errorMessage);\n          setIsLoading(false);\n          console.warn('Failed to fetch site settings, using fallbacks:', errorMessage);\n        }\n      }\n    };\n\n    fetchSettings();\n\n    return () => {\n      isMounted = false;\n    };\n  }, []);\n\n  return createSettingsWithFallbacks(cmsSettings, isLoading, error);\n}\n\n// Static version for server-side rendering or when you need immediate access\nexport function getStaticSiteSettings(): SiteSettingsWithFallbacks {\n  return createSettingsWithFallbacks(null, false, null);\n}\n\n// Utility function to get specific section settings\nexport function useSectionSettings(section: 'hero' | 'contact' | 'services' | 'process' | 'about') {\n  const settings = useSiteSettings();\n  \n  switch (section) {\n    case 'hero':\n      return {\n        headline: settings.heroHeadline,\n        subtext: settings.heroSubtext,\n        buttonText: settings.heroButtonText,\n        isLoading: settings.isLoading,\n        isFromCMS: settings.isFromCMS\n      };\n      \n    case 'contact':\n      return {\n        headline: settings.contactHeadline,\n        subtext: settings.contactSubtext,\n        buttonText: settings.contactButtonText,\n        successMessage: settings.formSuccessMessage,\n        errorMessage: settings.formErrorMessage,\n        isLoading: settings.isLoading,\n        isFromCMS: settings.isFromCMS\n      };\n      \n    case 'services':\n      return {\n        headline: settings.servicesHeadline,\n        subtext: settings.servicesSubtext,\n        isLoading: settings.isLoading,\n        isFromCMS: settings.isFromCMS\n      };\n      \n    case 'process':\n      return {\n        headline: settings.processHeadline,\n        subtext: settings.processSubtext,\n        isLoading: settings.isLoading,\n        isFromCMS: settings.isFromCMS\n      };\n      \n    case 'about':\n      return {\n        headline: settings.aboutHeadline,\n        subtext: settings.aboutSubtext,\n        isLoading: settings.isLoading,\n        isFromCMS: settings.isFromCMS\n      };\n      \n    default:\n      return {\n        isLoading: settings.isLoading,\n        isFromCMS: settings.isFromCMS\n      };\n  }\n}\n\n\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;AAED;AACA;AACA;;;;;AAwCA,iCAAiC;AACjC,MAAM,8BAA8B,CAClC,aACA,YAAqB,KAAK,EAC1B,QAAuB,IAAI;IAE3B,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,OAAO;QACL,eAAe;QACf,cAAc,aAAa,gBAAgB,wHAAA,CAAA,mBAAgB,CAAC,IAAI,CAAC,QAAQ;QACzE,aAAa,aAAa,eAAe,wHAAA,CAAA,mBAAgB,CAAC,IAAI,CAAC,OAAO;QACtE,gBAAgB,aAAa,kBAAkB,wHAAA,CAAA,mBAAgB,CAAC,IAAI,CAAC,UAAU;QAE/E,kBAAkB;QAClB,iBAAiB,aAAa,mBAAmB,wHAAA,CAAA,mBAAgB,CAAC,OAAO,CAAC,QAAQ;QAClF,gBAAgB,aAAa,kBAAkB,wHAAA,CAAA,mBAAgB,CAAC,OAAO,CAAC,OAAO;QAC/E,mBAAmB,aAAa,qBAAqB,wHAAA,CAAA,mBAAgB,CAAC,OAAO,CAAC,UAAU;QAExF,gBAAgB;QAChB,oBAAoB,aAAa,sBAAsB,wHAAA,CAAA,mBAAgB,CAAC,OAAO,CAAC,cAAc;QAC9F,kBAAkB,aAAa,oBAAoB,wHAAA,CAAA,mBAAgB,CAAC,OAAO,CAAC,YAAY;QAExF,mBAAmB;QACnB,kBAAkB,aAAa,oBAAoB,wHAAA,CAAA,mBAAgB,CAAC,QAAQ,CAAC,QAAQ;QACrF,iBAAiB,aAAa,mBAAmB,wHAAA,CAAA,mBAAgB,CAAC,QAAQ,CAAC,OAAO;QAElF,kBAAkB;QAClB,iBAAiB,aAAa,mBAAmB,wHAAA,CAAA,mBAAgB,CAAC,OAAO,CAAC,QAAQ;QAClF,gBAAgB,aAAa,kBAAkB,wHAAA,CAAA,mBAAgB,CAAC,OAAO,CAAC,OAAO;QAE/E,gBAAgB;QAChB,eAAe,aAAa,iBAAiB,wHAAA,CAAA,mBAAgB,CAAC,KAAK,CAAC,QAAQ;QAC5E,cAAc,aAAa,gBAAgB,wHAAA,CAAA,mBAAgB,CAAC,KAAK,CAAC,OAAO;QAEzE,SAAS;QACT,eAAe,aAAa,iBAAiB;QAC7C,iBAAiB,aAAa,mBAAmB,CAAC,EAAE,EAAE,YAAY,+BAA+B,CAAC;QAElG,mBAAmB;QACnB;QACA,WAAW,CAAC,CAAC;QACb;IACF;AACF;AAGO,SAAS;;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IACpE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,YAAY;YAEhB,MAAM;2DAAgB;oBACpB,IAAI;wBACF,aAAa;wBACb,SAAS;wBAET,MAAM,WAAW,MAAM,CAAA,GAAA,uHAAA,CAAA,kBAAe,AAAD;wBAErC,IAAI,WAAW;4BACb,eAAe;4BACf,aAAa;wBACf;oBACF,EAAE,OAAO,KAAK;wBACZ,IAAI,WAAW;4BACb,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;4BAC1D,SAAS;4BACT,aAAa;4BACb,QAAQ,IAAI,CAAC,mDAAmD;wBAClE;oBACF;gBACF;;YAEA;YAEA;6CAAO;oBACL,YAAY;gBACd;;QACF;oCAAG,EAAE;IAEL,OAAO,4BAA4B,aAAa,WAAW;AAC7D;GArCgB;AAwCT,SAAS;IACd,OAAO,4BAA4B,MAAM,OAAO;AAClD;AAGO,SAAS,mBAAmB,OAA8D;;IAC/F,MAAM,WAAW;IAEjB,OAAQ;QACN,KAAK;YACH,OAAO;gBACL,UAAU,SAAS,YAAY;gBAC/B,SAAS,SAAS,WAAW;gBAC7B,YAAY,SAAS,cAAc;gBACnC,WAAW,SAAS,SAAS;gBAC7B,WAAW,SAAS,SAAS;YAC/B;QAEF,KAAK;YACH,OAAO;gBACL,UAAU,SAAS,eAAe;gBAClC,SAAS,SAAS,cAAc;gBAChC,YAAY,SAAS,iBAAiB;gBACtC,gBAAgB,SAAS,kBAAkB;gBAC3C,cAAc,SAAS,gBAAgB;gBACvC,WAAW,SAAS,SAAS;gBAC7B,WAAW,SAAS,SAAS;YAC/B;QAEF,KAAK;YACH,OAAO;gBACL,UAAU,SAAS,gBAAgB;gBACnC,SAAS,SAAS,eAAe;gBACjC,WAAW,SAAS,SAAS;gBAC7B,WAAW,SAAS,SAAS;YAC/B;QAEF,KAAK;YACH,OAAO;gBACL,UAAU,SAAS,eAAe;gBAClC,SAAS,SAAS,cAAc;gBAChC,WAAW,SAAS,SAAS;gBAC7B,WAAW,SAAS,SAAS;YAC/B;QAEF,KAAK;YACH,OAAO;gBACL,UAAU,SAAS,aAAa;gBAChC,SAAS,SAAS,YAAY;gBAC9B,WAAW,SAAS,SAAS;gBAC7B,WAAW,SAAS,SAAS;YAC/B;QAEF;YACE,OAAO;gBACL,WAAW,SAAS,SAAS;gBAC7B,WAAW,SAAS,SAAS;YAC/B;IACJ;AACF;IAtDgB;;QACG", "debugId": null}}, {"offset": {"line": 1277, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/hooks/index.ts"], "sourcesContent": ["/**\n * Hooks Index\n * \n * Central export point for all custom hooks.\n */\n\n// Analytics hook\nexport { useAnalytics, ANALYTICS_EVENTS } from './useAnalytics';\nexport type { AnalyticsEvent } from './useAnalytics';\n\n// Contact form hook\nexport { useContactForm } from './useContactForm';\nexport type { ContactFormData, FormErrors, FormState } from './useContactForm';\n\n// Site settings hook\nexport { useSiteSettings, useSectionSettings, getStaticSiteSettings } from './useSiteSettings';\nexport type { SiteSettingsWithFallbacks } from './useSiteSettings';\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED,iBAAiB;;AACjB;AAGA,oBAAoB;AACpB;AAGA,qBAAqB;AACrB", "debugId": null}}, {"offset": {"line": 1311, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/layout/Navigation.tsx"], "sourcesContent": ["/**\n * Navigation Component\n *\n * Handles the main navigation menu for both desktop and mobile views.\n * Supports smooth scrolling to sections and analytics tracking.\n *\n * @component\n * @param {Object} props - Component props\n * @param {string} [props.className] - Additional CSS classes to apply\n *\n * @example\n * ```tsx\n * <Navigation className=\"hidden md:flex space-x-8\" />\n * ```\n *\n * Features:\n * - Smooth scrolling to page sections\n * - Analytics tracking for navigation events\n * - Configurable navigation items from site config\n * - Responsive design support\n */\n\n'use client';\n\nimport React from 'react';\nimport { NAVIGATION } from '@/config/site';\nimport { useAnalytics } from '@/hooks';\n\ninterface NavigationProps {\n  isMobile?: boolean;\n  onItemClick?: () => void;\n  className?: string;\n}\n\nconst Navigation: React.FC<NavigationProps> = ({ \n  isMobile = false, \n  onItemClick,\n  className = ''\n}) => {\n  const { trackNavigation } = useAnalytics();\n\n  const scrollToSection = (sectionId: string, label: string) => {\n    const element = document.getElementById(sectionId);\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' });\n      trackNavigation(label, isMobile ? 'mobile_nav' : 'desktop_nav');\n    }\n    onItemClick?.();\n  };\n\n  const navItems = NAVIGATION.main;\n\n  const baseClasses = isMobile \n    ? \"flex flex-col space-y-4\"\n    : \"hidden md:flex md:items-center md:space-x-8\";\n\n  return (\n    <nav className={`${baseClasses} ${className}`}>\n      {navItems.map((item) => (\n        <button\n          key={item.href}\n          onClick={() => scrollToSection(item.href.replace('#', ''), item.label)}\n          className={`\n            text-gray-700 dark:text-gray-300 hover:text-electric-blue dark:hover:text-electric-blue \n            transition-colors duration-200 font-medium\n            ${isMobile \n              ? 'text-lg py-2 text-left w-full hover:bg-gray-50 dark:hover:bg-gray-800 px-4 rounded-lg' \n              : 'text-sm'\n            }\n          `}\n          aria-label={`Navigate to ${item.label} section`}\n        >\n          {item.label}\n        </button>\n      ))}\n    </nav>\n  );\n};\n\nexport default Navigation;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;CAoBC;;;;AAKD;AACA;AAAA;;;AAJA;;;AAYA,MAAM,aAAwC,CAAC,EAC7C,WAAW,KAAK,EAChB,WAAW,EACX,YAAY,EAAE,EACf;;IACC,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD;IAEvC,MAAM,kBAAkB,CAAC,WAAmB;QAC1C,MAAM,UAAU,SAAS,cAAc,CAAC;QACxC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;YAC5C,gBAAgB,OAAO,WAAW,eAAe;QACnD;QACA;IACF;IAEA,MAAM,WAAW,wHAAA,CAAA,aAAU,CAAC,IAAI;IAEhC,MAAM,cAAc,WAChB,4BACA;IAEJ,qBACE,6LAAC;QAAI,WAAW,GAAG,YAAY,CAAC,EAAE,WAAW;kBAC1C,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC;gBAEC,SAAS,IAAM,gBAAgB,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,KAAK,KAAK;gBACrE,WAAW,CAAC;;;YAGV,EAAE,WACE,0FACA,UACH;UACH,CAAC;gBACD,cAAY,CAAC,YAAY,EAAE,KAAK,KAAK,CAAC,QAAQ,CAAC;0BAE9C,KAAK,KAAK;eAZN,KAAK,IAAI;;;;;;;;;;AAiBxB;GA3CM;;QAKwB,+HAAA,CAAA,eAAY;;;KALpC;uCA6CS", "debugId": null}}, {"offset": {"line": 1398, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/layout/MobileMenu.tsx"], "sourcesContent": ["/**\n * Mobile Menu Component\n *\n * Full-screen mobile navigation menu with slide-in animation from the right.\n * Includes navigation items, chat trigger, dark mode toggle, and close button.\n *\n * @component\n * @param {Object} props - Component props\n * @param {boolean} props.isOpen - Whether the mobile menu is currently open\n * @param {() => void} props.onClose - Callback function to close the menu\n *\n * @example\n * ```tsx\n * <MobileMenu\n *   isOpen={isMenuOpen}\n *   onClose={() => setIsMenuOpen(false)}\n * />\n * ```\n *\n * Features:\n * - Smooth slide-in/out animation using Framer Motion\n * - Full-screen overlay with backdrop blur\n * - Integrated navigation, chat, and dark mode toggle\n * - Keyboard and click-outside support for closing\n * - Mobile-optimized touch interactions\n */\n\n'use client';\n\nimport React from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { X } from 'lucide-react';\nimport Navigation from './Navigation';\nimport SimpleHeaderChat from '../SimpleHeaderChat';\nimport SimpleDarkModeToggle from '../SimpleDarkModeToggle';\nimport { ANIMATION_CONFIG } from '@/config/site';\n\ninterface MobileMenuProps {\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nconst MobileMenu: React.FC<MobileMenuProps> = ({ isOpen, onClose }) => {\n  return (\n    <AnimatePresence>\n      {isOpen && (\n        <>\n          {/* Backdrop */}\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            transition={{ duration: ANIMATION_CONFIG.duration.normal }}\n            className=\"fixed inset-0 bg-black/50 z-40 md:hidden\"\n            onClick={onClose}\n          />\n          \n          {/* Menu Panel */}\n          <motion.div\n            initial={{ x: '100%' }}\n            animate={{ x: 0 }}\n            exit={{ x: '100%' }}\n            transition={{ \n              duration: ANIMATION_CONFIG.duration.normal,\n              ease: ANIMATION_CONFIG.easing.easeInOut\n            }}\n            className=\"fixed top-0 right-0 h-full w-80 max-w-[90vw] bg-white dark:bg-gray-900 shadow-xl z-50 md:hidden\"\n          >\n            <div className=\"flex flex-col h-full\">\n              {/* Header */}\n              <div className=\"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700\">\n                <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                  Menu\n                </h2>\n                <button\n                  onClick={onClose}\n                  className=\"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors\"\n                  aria-label=\"Close menu\"\n                >\n                  <X className=\"h-5 w-5 text-gray-600 dark:text-gray-400\" />\n                </button>\n              </div>\n\n              {/* Navigation */}\n              <div className=\"flex-1 p-4\">\n                <Navigation \n                  isMobile={true} \n                  onItemClick={onClose}\n                  className=\"mb-8\"\n                />\n                \n                {/* Mobile-specific features */}\n                <div className=\"space-y-4 pt-8 border-t border-gray-200 dark:border-gray-700\">\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                      Dark Mode\n                    </span>\n                    <SimpleDarkModeToggle />\n                  </div>\n                  \n                  <div className=\"pt-4\">\n                    <SimpleHeaderChat />\n                  </div>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        </>\n      )}\n    </AnimatePresence>\n  );\n};\n\nexport default MobileMenu;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;CAyBC;;;;AAKD;AAAA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;AAeA,MAAM,aAAwC,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE;IAChE,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACb,wBACC;;8BAEE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,YAAY;wBAAE,UAAU,wHAAA,CAAA,mBAAgB,CAAC,QAAQ,CAAC,MAAM;oBAAC;oBACzD,WAAU;oBACV,SAAS;;;;;;8BAIX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,GAAG;oBAAO;oBACrB,SAAS;wBAAE,GAAG;oBAAE;oBAChB,MAAM;wBAAE,GAAG;oBAAO;oBAClB,YAAY;wBACV,UAAU,wHAAA,CAAA,mBAAgB,CAAC,QAAQ,CAAC,MAAM;wBAC1C,MAAM,wHAAA,CAAA,mBAAgB,CAAC,MAAM,CAAC,SAAS;oBACzC;oBACA,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAsD;;;;;;kDAGpE,6LAAC;wCACC,SAAS;wCACT,WAAU;wCACV,cAAW;kDAEX,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAKjB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6IAAA,CAAA,UAAU;wCACT,UAAU;wCACV,aAAa;wCACb,WAAU;;;;;;kDAIZ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAuD;;;;;;kEAGvE,6LAAC,6IAAA,CAAA,UAAoB;;;;;;;;;;;0DAGvB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,yIAAA,CAAA,UAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrC;KArEM;uCAuES", "debugId": null}}, {"offset": {"line": 1610, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/layout/Header.tsx"], "sourcesContent": ["/**\n * Header Component\n *\n * Main navigation header for the Mobilify website. Features responsive design\n * with desktop navigation and mobile hamburger menu. Includes logo, navigation\n * links, chat trigger, dark mode toggle, and CTA button.\n *\n * @component\n * @example\n * ```tsx\n * <Header />\n * ```\n *\n * Features:\n * - Responsive navigation (desktop/mobile)\n * - Mobile hamburger menu with slide-in animation\n * - Dark mode toggle\n * - Chat integration\n * - Analytics tracking for navigation events\n * - Fixed positioning with backdrop blur\n */\n\n'use client';\n\nimport React, { useState } from 'react';\nimport { Menu, X } from 'lucide-react';\nimport Logo from '../Logo';\nimport NoSSR from '../NoSSR';\nimport SimpleHeaderChat from '../SimpleHeaderChat';\nimport SimpleDarkModeToggle from '../SimpleDarkModeToggle';\nimport Navigation from './Navigation';\nimport MobileMenu from './MobileMenu';\nimport { useAnalytics } from '@/hooks';\n\n/**\n * Header component with responsive navigation and mobile menu\n */\nconst Header = () => {\n  // State for mobile menu visibility\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const { trackNavigation } = useAnalytics();\n\n  /**\n   * Handles mobile menu toggle and tracks the interaction\n   */\n  const handleMobileMenuToggle = () => {\n    setIsMenuOpen(!isMenuOpen);\n    trackNavigation('mobile_menu_toggle', 'header');\n  };\n\n  return (\n    <>\n      <header className=\"fixed top-0 left-0 right-0 w-full bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm border-b border-gray-100 dark:border-gray-800 z-50 transition-colors duration-300\">\n        <div className=\"w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between h-16 w-full\">\n            {/* Logo */}\n            <div className=\"flex items-center\">\n              <Logo />\n            </div>\n\n            {/* Desktop Navigation */}\n            <div className=\"hidden md:flex items-center space-x-8\">\n              <Navigation />\n              <NoSSR>\n                <SimpleHeaderChat />\n              </NoSSR>\n              <NoSSR>\n                <SimpleDarkModeToggle />\n              </NoSSR>\n            </div>\n\n            {/* Mobile menu button */}\n            <button\n              onClick={handleMobileMenuToggle}\n              className=\"md:hidden p-2 rounded-lg text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors\"\n              aria-label=\"Toggle mobile menu\"\n          >\n            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}\n          </button>\n          </div>\n        </div>\n      </header>\n\n      {/* Mobile Navigation */}\n      <MobileMenu\n        isOpen={isMenuOpen}\n        onClose={() => setIsMenuOpen(false)}\n      />\n    </>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;CAoBC;;;;AAID;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;AAVA;;;;;;;;;;AAYA;;CAEC,GACD,MAAM,SAAS;;IACb,mCAAmC;IACnC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD;IAEvC;;GAEC,GACD,MAAM,yBAAyB;QAC7B,cAAc,CAAC;QACf,gBAAgB,sBAAsB;IACxC;IAEA,qBACE;;0BACE,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,6HAAA,CAAA,UAAI;;;;;;;;;;0CAIP,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6IAAA,CAAA,UAAU;;;;;kDACX,6LAAC,8HAAA,CAAA,UAAK;kDACJ,cAAA,6LAAC,yIAAA,CAAA,UAAgB;;;;;;;;;;kDAEnB,6LAAC,8HAAA,CAAA,UAAK;kDACJ,cAAA,6LAAC,6IAAA,CAAA,UAAoB;;;;;;;;;;;;;;;;0CAKzB,6LAAC;gCACC,SAAS;gCACT,WAAU;gCACV,cAAW;0CAEZ,2BAAa,6LAAC,+LAAA,CAAA,IAAC;oCAAC,MAAM;;;;;yDAAS,6LAAC,qMAAA,CAAA,OAAI;oCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOlD,6LAAC,6IAAA,CAAA,UAAU;gBACT,QAAQ;gBACR,SAAS,IAAM,cAAc;;;;;;;;AAIrC;GArDM;;QAGwB,+HAAA,CAAA,eAAY;;;KAHpC;uCAuDS", "debugId": null}}, {"offset": {"line": 1794, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 1813, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/ui/Button.tsx"], "sourcesContent": ["/**\n * Button Component\n *\n * Reusable button component with multiple variants, sizes, and loading states.\n * Built with accessibility and consistent design in mind.\n *\n * @component\n * @param {Object} props - Component props extending HTML button attributes\n * @param {'primary' | 'secondary' | 'ghost'} [props.variant='primary'] - Button style variant\n * @param {'sm' | 'md' | 'lg'} [props.size='md'] - Button size\n * @param {boolean} [props.isLoading=false] - Whether to show loading state\n * @param {React.ReactNode} props.children - Button content\n * @param {string} [props.className] - Additional CSS classes\n *\n * @example\n * ```tsx\n * <Button variant=\"primary\" size=\"lg\" onClick={handleClick}>\n *   Get Started\n * </Button>\n *\n * <Button variant=\"secondary\" isLoading={isSubmitting}>\n *   Submit Form\n * </Button>\n * ```\n *\n * Features:\n * - Three variants: primary (filled), secondary (outlined), ghost (text only)\n * - Three sizes: sm, md, lg with appropriate padding and text sizes\n * - Loading state with spinner animation\n * - Disabled state with visual feedback\n * - Focus management and keyboard accessibility\n * - Consistent hover and active states\n * - Forward ref support for advanced use cases\n */\n\n'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\nexport interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  /** Button style variant */\n  variant?: 'primary' | 'secondary' | 'ghost';\n  /** Button size */\n  size?: 'sm' | 'md' | 'lg';\n  /** Whether to show loading spinner */\n  isLoading?: boolean;\n  /** Button content */\n  children: React.ReactNode;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', isLoading = false, disabled, children, ...props }, ref) => {\n    const baseClasses = 'inline-flex items-center justify-center rounded-lg font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\n    \n    const variants = {\n      primary: 'bg-electric-blue text-white hover:opacity-90 focus:ring-electric-blue shadow-lg hover:shadow-xl',\n      secondary: 'border border-electric-blue text-electric-blue hover:bg-electric-blue hover:text-white focus:ring-electric-blue',\n      ghost: 'text-electric-blue hover:bg-electric-blue hover:bg-opacity-10 focus:ring-electric-blue'\n    };\n\n    const sizes = {\n      sm: 'px-4 py-2 text-sm',\n      md: 'px-6 py-3 text-base',\n      lg: 'px-8 py-4 text-lg'\n    };\n\n    const buttonClasses = cn(\n      baseClasses,\n      variants[variant],\n      sizes[size],\n      className\n    );\n\n    return (\n      <button\n        ref={ref}\n        className={buttonClasses}\n        disabled={disabled || isLoading}\n        {...props}\n      >\n        {isLoading ? (\n          <>\n            <svg\n              className=\"animate-spin -ml-1 mr-3 h-5 w-5\"\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n            >\n              <circle\n                className=\"opacity-25\"\n                cx=\"12\"\n                cy=\"12\"\n                r=\"10\"\n                stroke=\"currentColor\"\n                strokeWidth=\"4\"\n              ></circle>\n              <path\n                className=\"opacity-75\"\n                fill=\"currentColor\"\n                d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n              ></path>\n            </svg>\n            Loading...\n          </>\n        ) : (\n          children\n        )}\n      </button>\n    );\n  }\n);\n\nButton.displayName = 'Button';\n\nexport default Button;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAiCC;;;;AAID;AACA;AAHA;;;;AAgBA,MAAM,uBAAS,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC7B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,YAAY,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACjG,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,OAAO;IACT;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,gBAAgB,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACrB,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;IAGF,qBACE,6LAAC;QACC,KAAK;QACL,WAAW;QACX,UAAU,YAAY;QACrB,GAAG,KAAK;kBAER,0BACC;;8BACE,6LAAC;oBACC,WAAU;oBACV,OAAM;oBACN,MAAK;oBACL,SAAQ;;sCAER,6LAAC;4BACC,WAAU;4BACV,IAAG;4BACH,IAAG;4BACH,GAAE;4BACF,QAAO;4BACP,aAAY;;;;;;sCAEd,6LAAC;4BACC,WAAU;4BACV,MAAK;4BACL,GAAE;;;;;;;;;;;;gBAEA;;2BAIR;;;;;;AAIR;;AAGF,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 1933, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/ui/Input.tsx"], "sourcesContent": ["/**\n * Input Component\n *\n * Reusable input field component with validation states, labels, and helper text.\n * Supports different variants for visual feedback and accessibility features.\n *\n * @component\n * @param {Object} props - Component props extending HTML input attributes\n * @param {'base' | 'error' | 'success'} [props.variant='base'] - Input validation state\n * @param {string} [props.label] - Input label text\n * @param {string} [props.errorMessage] - Error message to display\n * @param {string} [props.helperText] - Helper text for additional guidance\n * @param {string} [props.className] - Additional CSS classes\n *\n * @example\n * ```tsx\n * <Input\n *   label=\"Email Address\"\n *   type=\"email\"\n *   placeholder=\"Enter your email\"\n *   variant=\"base\"\n *   helperText=\"We'll never share your email\"\n * />\n *\n * <Input\n *   label=\"Password\"\n *   type=\"password\"\n *   variant=\"error\"\n *   errorMessage=\"Password must be at least 8 characters\"\n * />\n * ```\n *\n * Features:\n * - Three validation states: base, error, success with appropriate styling\n * - Optional label with proper association\n * - Error message display with accessibility support\n * - Helper text for additional context\n * - Consistent focus states and transitions\n * - Dark mode support with semantic color tokens\n * - Forward ref support for form libraries\n */\n\n'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\nexport interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {\n  /** Input validation state affecting visual styling */\n  variant?: 'base' | 'error' | 'success';\n  /** Label text displayed above the input */\n  label?: string;\n  /** Helper text for additional guidance */\n  helperText?: string;\n  /** Error message displayed below the input */\n  errorMessage?: string;\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, variant = 'base', label, helperText, errorMessage, ...props }, ref) => {\n    const baseClasses = 'w-full px-4 py-3 rounded-lg text-base transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\n    \n    const variants = {\n      base: 'border border-border-light dark:border-border-dark focus:ring-electric-blue focus:border-electric-blue bg-surface-light dark:bg-surface-gray-dark text-text-primary dark:text-text-primary-dark placeholder-text-muted dark:placeholder-text-muted-dark',\n      error: 'border border-error focus:ring-error focus:border-error bg-surface-light dark:bg-surface-gray-dark text-text-primary dark:text-text-primary-dark placeholder-text-muted dark:placeholder-text-muted-dark',\n      success: 'border border-success focus:ring-success focus:border-success bg-surface-light dark:bg-surface-gray-dark text-text-primary dark:text-text-primary-dark placeholder-text-muted dark:placeholder-text-muted-dark'\n    };\n\n    const inputClasses = cn(\n      baseClasses,\n      variants[variant],\n      className\n    );\n\n    const displayErrorMessage = variant === 'error' && errorMessage;\n    const displayHelperText = variant !== 'error' && helperText;\n\n    return (\n      <div className=\"w-full\">\n        {label && (\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            {label}\n          </label>\n        )}\n        <input\n          ref={ref}\n          className={inputClasses}\n          {...props}\n        />\n        {displayErrorMessage && (\n          <p className=\"mt-2 text-sm text-red-600 dark:text-red-400\">\n            {errorMessage}\n          </p>\n        )}\n        {displayHelperText && (\n          <p className=\"mt-2 text-sm text-gray-600 dark:text-gray-400\">\n            {helperText}\n          </p>\n        )}\n      </div>\n    );\n  }\n);\n\nInput.displayName = 'Input';\n\nexport default Input;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAwCC;;;;AAID;AACA;AAHA;;;;AAgBA,MAAM,sBAAQ,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC5B,CAAC,EAAE,SAAS,EAAE,UAAU,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG,OAAO,EAAE;IAC3E,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,MAAM;QACN,OAAO;QACP,SAAS;IACX;IAEA,MAAM,eAAe,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACpB,aACA,QAAQ,CAAC,QAAQ,EACjB;IAGF,MAAM,sBAAsB,YAAY,WAAW;IACnD,MAAM,oBAAoB,YAAY,WAAW;IAEjD,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBAAM,WAAU;0BACd;;;;;;0BAGL,6LAAC;gBACC,KAAK;gBACL,WAAW;gBACV,GAAG,KAAK;;;;;;YAEV,qCACC,6LAAC;gBAAE,WAAU;0BACV;;;;;;YAGJ,mCACC,6LAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;;AAGF,MAAM,WAAW,GAAG;uCAEL", "debugId": null}}, {"offset": {"line": 2051, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/ui/Card.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\nexport interface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  variant?: 'base' | 'hover' | 'interactive';\n  children: React.ReactNode;\n}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, variant = 'base', children, ...props }, ref) => {\n    const baseClasses = 'bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700';\n    \n    const variants = {\n      base: '',\n      hover: 'hover:shadow-md transition-shadow duration-200',\n      interactive: 'hover:shadow-lg cursor-pointer transition-shadow duration-200'\n    };\n\n    const cardClasses = cn(\n      baseClasses,\n      variants[variant],\n      className\n    );\n\n    return (\n      <div\n        ref={ref}\n        className={cardClasses}\n        {...props}\n      >\n        {children}\n      </div>\n    );\n  }\n);\n\nCard.displayName = 'Card';\n\n// Card sub-components for better composition\nexport const CardHeader = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('p-6 pb-0', className)}\n      {...props}\n    />\n  )\n);\nCardHeader.displayName = 'CardHeader';\n\nexport const CardContent = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('p-6', className)}\n      {...props}\n    />\n  )\n);\nCardContent.displayName = 'CardContent';\n\nexport const CardFooter = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('p-6 pt-0', className)}\n      {...props}\n    />\n  )\n);\nCardFooter.displayName = 'CardFooter';\n\nexport default Card;\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAHA;;;;AAUA,MAAM,qBAAO,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC3B,CAAC,EAAE,SAAS,EAAE,UAAU,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACpD,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,MAAM;QACN,OAAO;QACP,aAAa;IACf;IAEA,MAAM,cAAc,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACnB,aACA,QAAQ,CAAC,QAAQ,EACjB;IAGF,qBACE,6LAAC;QACC,KAAK;QACL,WAAW;QACV,GAAG,KAAK;kBAER;;;;;;AAGP;;AAGF,KAAK,WAAW,GAAG;AAGZ,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,OACxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;;;;;;AAIf,WAAW,WAAW,GAAG;AAElB,MAAM,4BAAc,6JAAA,CAAA,UAAK,CAAC,UAAU,OACzC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,OAAO;QACpB,GAAG,KAAK;;;;;;;AAIf,YAAY,WAAW,GAAG;AAEnB,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,OACxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;;;;;;AAIf,WAAW,WAAW,GAAG;uCAEV", "debugId": null}}, {"offset": {"line": 2137, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/ui/index.ts"], "sourcesContent": ["export { default as But<PERSON> } from './Button';\nexport type { ButtonProps } from './Button';\n\nexport { default as Input } from './Input';\nexport type { InputProps } from './Input';\n\nexport { default as Card, CardHeader, CardContent, CardFooter } from './Card';\nexport type { CardProps } from './Card';\n"], "names": [], "mappings": ";AAAA;AAGA;AAGA", "debugId": null}}, {"offset": {"line": 2174, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/sections/Hero.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { Button } from '@/components/ui';\nimport { useSectionSettings, useAnalytics } from '@/hooks';\nimport { ANIMATION_CONFIG } from '@/config/site';\n\nconst Hero = () => {\n  const heroSettings = useSectionSettings('hero');\n  const { trackNavigation } = useAnalytics();\n\n  const scrollToSection = (sectionId: string) => {\n    const element = document.getElementById(sectionId);\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' });\n      trackNavigation(sectionId, 'hero_cta');\n    }\n  };\n\n  return (\n    <section id=\"hero\" className=\"pt-16 bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 min-h-screen flex items-center w-full transition-colors duration-300\">\n      <div className=\"w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20\">\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center w-full\">\n          {/* Content */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: ANIMATION_CONFIG.duration.slow }}\n            className=\"text-center lg:text-left\"\n          >\n            <h1 className=\"text-4xl sm:text-5xl lg:text-6xl font-bold text-dark-charcoal dark:text-white leading-tight\">\n              {heroSettings.headline}\n            </h1>\n\n            <p className=\"mt-6 text-xl text-gray-600 dark:text-gray-300 leading-relaxed\">\n              {heroSettings.subtext}\n            </p>\n\n            <div className=\"mt-8\">\n              <Button\n                onClick={() => scrollToSection('demo')}\n                variant=\"primary\"\n                size=\"lg\"\n                disabled={heroSettings.isLoading}\n              >\n                {heroSettings.buttonText}\n              </Button>\n            </div>\n          </motion.div>\n\n          {/* Visual */}\n          <motion.div\n            initial={{ opacity: 0, scale: 0.9 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{\n              duration: ANIMATION_CONFIG.duration.slow,\n              delay: ANIMATION_CONFIG.duration.fast\n            }}\n            className=\"relative\"\n          >\n            <div className=\"relative mx-auto w-64 h-96 bg-gray-900 rounded-3xl p-2 shadow-2xl\">\n              {/* Phone frame */}\n              <div className=\"w-full h-full bg-white rounded-2xl overflow-hidden relative\">\n                {/* Status bar */}\n                <div className=\"h-6 bg-gray-900 flex items-center justify-center\">\n                  <div className=\"w-16 h-1 bg-white rounded-full\"></div>\n                </div>\n                \n                {/* App content placeholder */}\n                <div className=\"p-4 space-y-4\">\n                  <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ duration: 0.5, delay: 1 }}\n                    className=\"h-4 bg-gray-200 rounded animate-pulse\"\n                  ></motion.div>\n                  <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ duration: 0.5, delay: 1.2 }}\n                    className=\"h-32 bg-gradient-to-r from-electric-blue/10 to-electric-blue/20 rounded-lg flex items-center justify-center\"\n                  >\n                    <div className=\"text-electric-blue font-semibold\">Your App Here</div>\n                  </motion.div>\n                  <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ duration: 0.5, delay: 1.4 }}\n                    className=\"space-y-2\"\n                  >\n                    <div className=\"h-3 bg-gray-200 rounded\"></div>\n                    <div className=\"h-3 bg-gray-200 rounded w-3/4\"></div>\n                  </motion.div>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Hero;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AACA;AAAA;AAAA;AACA;;;AANA;;;;;AAQA,MAAM,OAAO;;IACX,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,qBAAkB,AAAD,EAAE;IACxC,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD;IAEvC,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,SAAS,cAAc,CAAC;QACxC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;YAC5C,gBAAgB,WAAW;QAC7B;IACF;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAO,WAAU;kBAC3B,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU,wHAAA,CAAA,mBAAgB,CAAC,QAAQ,CAAC,IAAI;wBAAC;wBACvD,WAAU;;0CAEV,6LAAC;gCAAG,WAAU;0CACX,aAAa,QAAQ;;;;;;0CAGxB,6LAAC;gCAAE,WAAU;0CACV,aAAa,OAAO;;;;;;0CAGvB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,0KAAA,CAAA,SAAM;oCACL,SAAS,IAAM,gBAAgB;oCAC/B,SAAQ;oCACR,MAAK;oCACL,UAAU,aAAa,SAAS;8CAE/B,aAAa,UAAU;;;;;;;;;;;;;;;;;kCAM9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAI;wBAClC,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAE;wBAChC,YAAY;4BACV,UAAU,wHAAA,CAAA,mBAAgB,CAAC,QAAQ,CAAC,IAAI;4BACxC,OAAO,wHAAA,CAAA,mBAAgB,CAAC,QAAQ,CAAC,IAAI;wBACvC;wBACA,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;sCAEb,cAAA,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;;;;;;;;;;kDAIjB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,UAAU;oDAAK,OAAO;gDAAE;gDACtC,WAAU;;;;;;0DAEZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,UAAU;oDAAK,OAAO;gDAAI;gDACxC,WAAU;0DAEV,cAAA,6LAAC;oDAAI,WAAU;8DAAmC;;;;;;;;;;;0DAEpD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,UAAU;oDAAK,OAAO;gDAAI;gDACxC,WAAU;;kEAEV,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUnC;GA9FM;;QACiB,kIAAA,CAAA,qBAAkB;QACX,+HAAA,CAAA,eAAY;;;KAFpC;uCAgGS", "debugId": null}}, {"offset": {"line": 2442, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/sections/DemoTabs.tsx"], "sourcesContent": ["/**\n * Demo Tabs Component\n *\n * Tab navigation interface for selecting different demo types in the interactive demo.\n * Features animated tab switching with icons and descriptions.\n *\n * @component\n * @param {Object} props - Component props\n * @param {string} props.activeTab - Currently active tab ID\n * @param {(tabId: string) => void} props.onTabChange - Callback when tab is changed\n * @param {string} [props.className] - Additional CSS classes\n *\n * @example\n * ```tsx\n * <DemoTabs\n *   activeTab=\"website\"\n *   onTabChange={(tabId) => setActiveTab(tabId)}\n * />\n * ```\n *\n * Features:\n * - Three demo types: Website to App, Mobile App, Tablet App\n * - Animated active state indicator using Framer Motion\n * - Icons and descriptions for each tab\n * - Responsive design (stacked on mobile, horizontal on desktop)\n * - Hover and tap animations for better UX\n */\n\n'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { Globe, Smartphone, Tablet } from 'lucide-react';\n\ninterface DemoTab {\n  id: string;\n  label: string;\n  icon: React.ReactNode;\n  description: string;\n}\n\ninterface DemoTabsProps {\n  activeTab: string;\n  onTabChange: (tabId: string) => void;\n  className?: string;\n}\n\nconst demoTabs: DemoTab[] = [\n  {\n    id: 'website',\n    label: 'Website to App',\n    icon: <Globe className=\"w-5 h-5\" />,\n    description: 'Convert your existing website into a mobile app'\n  },\n  {\n    id: 'mobile',\n    label: 'Mobile App',\n    icon: <Smartphone className=\"w-5 h-5\" />,\n    description: 'Build a custom mobile application from scratch'\n  },\n  {\n    id: 'tablet',\n    label: 'Tablet App',\n    icon: <Tablet className=\"w-5 h-5\" />,\n    description: 'Create tablet-optimized applications'\n  }\n];\n\nconst DemoTabs: React.FC<DemoTabsProps> = ({ \n  activeTab, \n  onTabChange, \n  className = '' \n}) => {\n  return (\n    <div className={`flex flex-col sm:flex-row gap-4 mb-8 ${className}`}>\n      {demoTabs.map((tab) => (\n        <motion.button\n          key={tab.id}\n          onClick={() => onTabChange(tab.id)}\n          className={`\n            relative flex items-center gap-3 p-4 rounded-xl border-2 transition-all duration-300\n            ${activeTab === tab.id\n              ? 'border-electric-blue bg-electric-blue/10 text-electric-blue'\n              : 'border-gray-200 dark:border-gray-700 hover:border-electric-blue/50 text-gray-700 dark:text-gray-300'\n            }\n          `}\n          whileHover={{ scale: 1.02 }}\n          whileTap={{ scale: 0.98 }}\n          aria-label={`Switch to ${tab.label} demo`}\n        >\n          {/* Active indicator */}\n          {activeTab === tab.id && (\n            <motion.div\n              layoutId=\"activeTab\"\n              className=\"absolute inset-0 bg-electric-blue/5 rounded-xl\"\n              initial={false}\n              transition={{ type: \"spring\", bounce: 0.2, duration: 0.6 }}\n            />\n          )}\n          \n          <div className=\"relative z-10 flex items-center gap-3\">\n            {tab.icon}\n            <div className=\"text-left\">\n              <div className=\"font-semibold\">{tab.label}</div>\n              <div className=\"text-sm opacity-75\">{tab.description}</div>\n            </div>\n          </div>\n        </motion.button>\n      ))}\n    </div>\n  );\n};\n\nexport default DemoTabs;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;CA0BC;;;;AAKD;AACA;AAAA;AAAA;AAJA;;;;AAmBA,MAAM,WAAsB;IAC1B;QACE,IAAI;QACJ,OAAO;QACP,oBAAM,6LAAC,uMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QACvB,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,oBAAM,6LAAC,iNAAA,CAAA,aAAU;YAAC,WAAU;;;;;;QAC5B,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,oBAAM,6LAAC,yMAAA,CAAA,SAAM;YAAC,WAAU;;;;;;QACxB,aAAa;IACf;CACD;AAED,MAAM,WAAoC,CAAC,EACzC,SAAS,EACT,WAAW,EACX,YAAY,EAAE,EACf;IACC,qBACE,6LAAC;QAAI,WAAW,CAAC,qCAAqC,EAAE,WAAW;kBAChE,SAAS,GAAG,CAAC,CAAC,oBACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gBAEZ,SAAS,IAAM,YAAY,IAAI,EAAE;gBACjC,WAAW,CAAC;;YAEV,EAAE,cAAc,IAAI,EAAE,GAClB,gEACA,sGACH;UACH,CAAC;gBACD,YAAY;oBAAE,OAAO;gBAAK;gBAC1B,UAAU;oBAAE,OAAO;gBAAK;gBACxB,cAAY,CAAC,UAAU,EAAE,IAAI,KAAK,CAAC,KAAK,CAAC;;oBAGxC,cAAc,IAAI,EAAE,kBACnB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,UAAS;wBACT,WAAU;wBACV,SAAS;wBACT,YAAY;4BAAE,MAAM;4BAAU,QAAQ;4BAAK,UAAU;wBAAI;;;;;;kCAI7D,6LAAC;wBAAI,WAAU;;4BACZ,IAAI,IAAI;0CACT,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAiB,IAAI,KAAK;;;;;;kDACzC,6LAAC;wCAAI,WAAU;kDAAsB,IAAI,WAAW;;;;;;;;;;;;;;;;;;;eA3BnD,IAAI,EAAE;;;;;;;;;;AAkCrB;KA3CM;uCA6CS", "debugId": null}}, {"offset": {"line": 2609, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/sections/DemoInput.tsx"], "sourcesContent": ["/**\n * Demo Input Component\n *\n * Input field with validation for the interactive demo section.\n * Adapts placeholder text and validation rules based on the selected demo type.\n *\n * @component\n * @param {Object} props - Component props\n * @param {string} props.activeTab - Currently active demo tab\n * @param {string} props.value - Current input value\n * @param {(value: string) => void} props.onChange - Callback when input changes\n * @param {() => void} props.onPreview - Callback to generate preview\n * @param {string} [props.placeholder] - Custom placeholder text\n * @param {string} [props.className] - Additional CSS classes\n *\n * @example\n * ```tsx\n * <DemoInput\n *   activeTab=\"website\"\n *   value={inputValue}\n *   onChange={setInputValue}\n *   onPreview={handlePreview}\n * />\n * ```\n *\n * Features:\n * - Dynamic placeholder text based on demo type\n * - Real-time input validation (URL validation for website tab)\n * - Visual feedback for validation state\n * - Enter key support for quick preview generation\n * - Responsive design with proper touch targets\n * - Accessibility support with proper ARIA labels\n */\n\n'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { ExternalLink, Search } from 'lucide-react';\n\ninterface DemoInputProps {\n  activeTab: string;\n  value: string;\n  onChange: (value: string) => void;\n  onPreview: () => void;\n  placeholder?: string;\n  className?: string;\n}\n\nconst DemoInput: React.FC<DemoInputProps> = ({\n  activeTab,\n  value,\n  onChange,\n  onPreview,\n  placeholder,\n  className = ''\n}) => {\n  const getPlaceholder = () => {\n    switch (activeTab) {\n      case 'website':\n        return placeholder || 'Enter your website URL (e.g., https://example.com)';\n      case 'mobile':\n        return placeholder || 'Describe your mobile app idea';\n      case 'tablet':\n        return placeholder || 'Describe your tablet app concept';\n      default:\n        return placeholder || 'Enter your input';\n    }\n  };\n\n  const getInputType = () => {\n    return activeTab === 'website' ? 'url' : 'text';\n  };\n\n  const isValidInput = () => {\n    if (!value.trim()) return false;\n    \n    if (activeTab === 'website') {\n      try {\n        new URL(value);\n        return true;\n      } catch {\n        return false;\n      }\n    }\n    \n    return value.trim().length >= 3;\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && isValidInput()) {\n      onPreview();\n    }\n  };\n\n  return (\n    <div className={`space-y-4 ${className}`}>\n      <div className=\"relative\">\n        <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\n          {activeTab === 'website' ? (\n            <ExternalLink className=\"h-5 w-5 text-gray-400\" />\n          ) : (\n            <Search className=\"h-5 w-5 text-gray-400\" />\n          )}\n        </div>\n        \n        <input\n          type={getInputType()}\n          value={value}\n          onChange={(e) => onChange(e.target.value)}\n          onKeyPress={handleKeyPress}\n          placeholder={getPlaceholder()}\n          className=\"\n            w-full pl-12 pr-4 py-4 text-lg border-2 border-gray-200 dark:border-gray-700 \n            rounded-xl focus:border-electric-blue focus:ring-2 focus:ring-electric-blue/20 \n            bg-white dark:bg-gray-800 text-gray-900 dark:text-white\n            placeholder-gray-500 dark:placeholder-gray-400\n            transition-all duration-300\n          \"\n          aria-label={`Input for ${activeTab} demo`}\n        />\n      </div>\n      \n      <motion.button\n        onClick={onPreview}\n        disabled={!isValidInput()}\n        className={`\n          w-full py-4 px-6 rounded-xl font-semibold text-lg transition-all duration-300\n          ${isValidInput()\n            ? 'bg-electric-blue text-white hover:bg-electric-blue/90 shadow-lg hover:shadow-xl'\n            : 'bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed'\n          }\n        `}\n        whileHover={isValidInput() ? { scale: 1.02 } : {}}\n        whileTap={isValidInput() ? { scale: 0.98 } : {}}\n        aria-label=\"Generate preview\"\n      >\n        {activeTab === 'website' ? 'Preview App Conversion' : 'Generate Preview'}\n      </motion.button>\n      \n      {activeTab === 'website' && value && !isValidInput() && (\n        <motion.p\n          initial={{ opacity: 0, y: -10 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"text-sm text-red-500 dark:text-red-400\"\n        >\n          Please enter a valid URL (including http:// or https://)\n        </motion.p>\n      )}\n    </div>\n  );\n};\n\nexport default DemoInput;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAgCC;;;;AAKD;AACA;AAAA;AAJA;;;;AAeA,MAAM,YAAsC,CAAC,EAC3C,SAAS,EACT,KAAK,EACL,QAAQ,EACR,SAAS,EACT,WAAW,EACX,YAAY,EAAE,EACf;IACC,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO,eAAe;YACxB,KAAK;gBACH,OAAO,eAAe;YACxB,KAAK;gBACH,OAAO,eAAe;YACxB;gBACE,OAAO,eAAe;QAC1B;IACF;IAEA,MAAM,eAAe;QACnB,OAAO,cAAc,YAAY,QAAQ;IAC3C;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,MAAM,IAAI,IAAI,OAAO;QAE1B,IAAI,cAAc,WAAW;YAC3B,IAAI;gBACF,IAAI,IAAI;gBACR,OAAO;YACT,EAAE,OAAM;gBACN,OAAO;YACT;QACF;QAEA,OAAO,MAAM,IAAI,GAAG,MAAM,IAAI;IAChC;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,gBAAgB;YACvC;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BACtC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACZ,cAAc,0BACb,6LAAC,yNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;iDAExB,6LAAC,yMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;kCAItB,6LAAC;wBACC,MAAM;wBACN,OAAO;wBACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wBACxC,YAAY;wBACZ,aAAa;wBACb,WAAU;wBAOV,cAAY,CAAC,UAAU,EAAE,UAAU,KAAK,CAAC;;;;;;;;;;;;0BAI7C,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,SAAS;gBACT,UAAU,CAAC;gBACX,WAAW,CAAC;;UAEV,EAAE,iBACE,oFACA,mFACH;QACH,CAAC;gBACD,YAAY,iBAAiB;oBAAE,OAAO;gBAAK,IAAI,CAAC;gBAChD,UAAU,iBAAiB;oBAAE,OAAO;gBAAK,IAAI,CAAC;gBAC9C,cAAW;0BAEV,cAAc,YAAY,2BAA2B;;;;;;YAGvD,cAAc,aAAa,SAAS,CAAC,gCACpC,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gBACP,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,WAAU;0BACX;;;;;;;;;;;;AAMT;KAtGM;uCAwGS", "debugId": null}}, {"offset": {"line": 2786, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/sections/DemoPreview.tsx"], "sourcesContent": ["/**\n * Demo Preview Component\n *\n * Animated preview display that shows a mockup of the user's app based on their input.\n * Features a realistic phone mockup with dynamic content and smooth animations.\n *\n * @component\n * @param {Object} props - Component props\n * @param {string} props.activeTab - Currently active demo tab\n * @param {string} props.inputValue - User's input value\n * @param {boolean} props.isVisible - Whether the preview should be shown\n * @param {() => void} [props.onAnimationComplete] - Callback when animation completes\n * @param {string} [props.className] - Additional CSS classes\n *\n * @example\n * ```tsx\n * <DemoPreview\n *   activeTab=\"website\"\n *   inputValue=\"https://example.com\"\n *   isVisible={showPreview}\n *   onAnimationComplete={handleAnimationComplete}\n * />\n * ```\n *\n * Features:\n * - Realistic phone mockup with status bar and app content\n * - Dynamic content based on demo type and user input\n * - Smooth enter/exit animations using Framer Motion\n * - Feature list highlighting app capabilities\n * - Call-to-action buttons for next steps\n * - Responsive design that works on all screen sizes\n * - Semantic color tokens for consistent theming\n */\n\n'use client';\n\nimport React from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Smartphone, Monitor, Tablet, Download, Share2 } from 'lucide-react';\nimport { ANIMATION_CONFIG } from '@/config/site';\n\ninterface DemoPreviewProps {\n  activeTab: string;\n  inputValue: string;\n  isVisible: boolean;\n  onAnimationComplete?: () => void;\n  className?: string;\n}\n\nconst DemoPreview: React.FC<DemoPreviewProps> = ({\n  activeTab,\n  inputValue,\n  isVisible,\n  onAnimationComplete,\n  className = ''\n}) => {\n  const getPreviewContent = () => {\n    switch (activeTab) {\n      case 'website':\n        return {\n          title: 'Website to App Conversion',\n          description: `Converting ${inputValue} into a mobile app`,\n          icon: <Smartphone className=\"w-8 h-8\" />,\n          features: [\n            'Native mobile navigation',\n            'Offline functionality',\n            'Push notifications',\n            'App store optimization'\n          ]\n        };\n      case 'mobile':\n        return {\n          title: 'Custom Mobile App',\n          description: `Building: ${inputValue}`,\n          icon: <Smartphone className=\"w-8 h-8\" />,\n          features: [\n            'Custom UI/UX design',\n            'Native performance',\n            'Cross-platform compatibility',\n            'Backend integration'\n          ]\n        };\n      case 'tablet':\n        return {\n          title: 'Tablet Application',\n          description: `Creating: ${inputValue}`,\n          icon: <Tablet className=\"w-8 h-8\" />,\n          features: [\n            'Tablet-optimized layout',\n            'Multi-window support',\n            'Enhanced productivity features',\n            'Responsive design'\n          ]\n        };\n      default:\n        return null;\n    }\n  };\n\n  const content = getPreviewContent();\n  if (!content) return null;\n\n  return (\n    <AnimatePresence>\n      {isVisible && (\n        <motion.div\n          initial={{ opacity: 0, y: 50, scale: 0.9 }}\n          animate={{ opacity: 1, y: 0, scale: 1 }}\n          exit={{ opacity: 0, y: -50, scale: 0.9 }}\n          transition={{\n            duration: ANIMATION_CONFIG.duration.normal,\n            ease: ANIMATION_CONFIG.easing.easeInOut\n          }}\n          onAnimationComplete={onAnimationComplete}\n          className={`bg-surface-light dark:bg-surface-gray-dark rounded-2xl shadow-2xl border border-border-light dark:border-border-dark overflow-hidden ${className}`}\n        >\n          {/* Header */}\n          <div className=\"bg-gradient-to-r from-electric-blue to-blue-600 text-white p-6\">\n            <div className=\"flex items-center gap-4\">\n              <div className=\"p-3 bg-white/20 rounded-xl\">\n                {content.icon}\n              </div>\n              <div>\n                <h3 className=\"text-xl font-bold\">{content.title}</h3>\n                <p className=\"text-white/80 mt-1\">{content.description}</p>\n              </div>\n            </div>\n          </div>\n\n          {/* Preview Content */}\n          <div className=\"p-6\">\n            {/* Mock Device Frame */}\n            <div className=\"relative mx-auto mb-6\" style={{ width: '280px', height: '500px' }}>\n              <div className=\"absolute inset-0 bg-dark-charcoal rounded-[2.5rem] p-2\">\n                <div className=\"w-full h-full bg-surface-light rounded-[2rem] overflow-hidden relative\">\n                  {/* Status Bar */}\n                  <div className=\"h-6 bg-surface-gray flex items-center justify-between px-4 text-xs\">\n                    <span>9:41</span>\n                    <div className=\"flex gap-1\">\n                      <div className=\"w-4 h-2 bg-success rounded-sm\"></div>\n                      <div className=\"w-4 h-2 bg-border-light rounded-sm\"></div>\n                      <div className=\"w-4 h-2 bg-border-light rounded-sm\"></div>\n                    </div>\n                  </div>\n                  \n                  {/* App Content */}\n                  <div className=\"p-4 h-full bg-gradient-to-b from-blue-50 to-white\">\n                    <div className=\"text-center mb-4\">\n                      <div className=\"w-16 h-16 bg-electric-blue rounded-xl mx-auto mb-2 flex items-center justify-center\">\n                        <Monitor className=\"w-8 h-8 text-white\" />\n                      </div>\n                      <h4 className=\"font-semibold text-text-primary\">Your App</h4>\n                    </div>\n                    \n                    {/* Mock Content */}\n                    <div className=\"space-y-3\">\n                      {[1, 2, 3].map((i) => (\n                        <div key={i} className=\"h-12 bg-surface-gray rounded-lg animate-pulse\"></div>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Features List */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6\">\n              {content.features.map((feature, index) => (\n                <motion.div\n                  key={feature}\n                  initial={{ opacity: 0, x: -20 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  transition={{ delay: index * 0.1 }}\n                  className=\"flex items-center gap-3 p-3 bg-surface-gray dark:bg-surface-gray-dark rounded-lg\"\n                >\n                  <div className=\"w-2 h-2 bg-electric-blue rounded-full\"></div>\n                  <span className=\"text-sm font-medium text-text-secondary dark:text-text-secondary-dark\">\n                    {feature}\n                  </span>\n                </motion.div>\n              ))}\n            </div>\n\n            {/* Action Buttons */}\n            <div className=\"flex gap-3\">\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"flex-1 bg-electric-blue text-white py-3 px-4 rounded-xl font-semibold flex items-center justify-center gap-2 hover:bg-electric-blue/90 transition-colors\"\n              >\n                <Download className=\"w-4 h-4\" />\n                Get Quote\n              </motion.button>\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"px-4 py-3 border-2 border-electric-blue text-electric-blue rounded-xl font-semibold flex items-center justify-center hover:bg-electric-blue/10 transition-colors\"\n              >\n                <Share2 className=\"w-4 h-4\" />\n              </motion.button>\n            </div>\n          </div>\n        </motion.div>\n      )}\n    </AnimatePresence>\n  );\n};\n\nexport default DemoPreview;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAgCC;;;;AAKD;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AALA;;;;;AAeA,MAAM,cAA0C,CAAC,EAC/C,SAAS,EACT,UAAU,EACV,SAAS,EACT,mBAAmB,EACnB,YAAY,EAAE,EACf;IACC,MAAM,oBAAoB;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,OAAO;oBACP,aAAa,CAAC,WAAW,EAAE,WAAW,kBAAkB,CAAC;oBACzD,oBAAM,6LAAC,iNAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;oBAC5B,UAAU;wBACR;wBACA;wBACA;wBACA;qBACD;gBACH;YACF,KAAK;gBACH,OAAO;oBACL,OAAO;oBACP,aAAa,CAAC,UAAU,EAAE,YAAY;oBACtC,oBAAM,6LAAC,iNAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;oBAC5B,UAAU;wBACR;wBACA;wBACA;wBACA;qBACD;gBACH;YACF,KAAK;gBACH,OAAO;oBACL,OAAO;oBACP,aAAa,CAAC,UAAU,EAAE,YAAY;oBACtC,oBAAM,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;oBACxB,UAAU;wBACR;wBACA;wBACA;wBACA;qBACD;gBACH;YACF;gBACE,OAAO;QACX;IACF;IAEA,MAAM,UAAU;IAChB,IAAI,CAAC,SAAS,OAAO;IAErB,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACb,2BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,GAAG;gBAAI,OAAO;YAAI;YACzC,SAAS;gBAAE,SAAS;gBAAG,GAAG;gBAAG,OAAO;YAAE;YACtC,MAAM;gBAAE,SAAS;gBAAG,GAAG,CAAC;gBAAI,OAAO;YAAI;YACvC,YAAY;gBACV,UAAU,wHAAA,CAAA,mBAAgB,CAAC,QAAQ,CAAC,MAAM;gBAC1C,MAAM,wHAAA,CAAA,mBAAgB,CAAC,MAAM,CAAC,SAAS;YACzC;YACA,qBAAqB;YACrB,WAAW,CAAC,qIAAqI,EAAE,WAAW;;8BAG9J,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACZ,QAAQ,IAAI;;;;;;0CAEf,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAqB,QAAQ,KAAK;;;;;;kDAChD,6LAAC;wCAAE,WAAU;kDAAsB,QAAQ,WAAW;;;;;;;;;;;;;;;;;;;;;;;8BAM5D,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;4BAAwB,OAAO;gCAAE,OAAO;gCAAS,QAAQ;4BAAQ;sCAC9E,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAK;;;;;;8DACN,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;;;;;;;;;;;;;sDAKnB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,2MAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;;;;;;sEAErB,6LAAC;4DAAG,WAAU;sEAAkC;;;;;;;;;;;;8DAIlD,6LAAC;oDAAI,WAAU;8DACZ;wDAAC;wDAAG;wDAAG;qDAAE,CAAC,GAAG,CAAC,CAAC,kBACd,6LAAC;4DAAY,WAAU;2DAAb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAStB,6LAAC;4BAAI,WAAU;sCACZ,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO,QAAQ;oCAAI;oCACjC,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAK,WAAU;sDACb;;;;;;;mCARE;;;;;;;;;;sCAeX,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;;sDAEV,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAGlC,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;8CAEV,cAAA,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQlC;KA7JM;uCA+JS", "debugId": null}}, {"offset": {"line": 3229, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/sections/InteractiveDemo.tsx"], "sourcesContent": ["/**\n * Interactive Demo Section Component\n *\n * Main demo section that allows users to interact with different app conversion types.\n * Composed of three main sub-components: DemoTabs, DemoInput, and DemoPreview.\n *\n * @component\n * @example\n * ```tsx\n * <InteractiveDemo />\n * ```\n *\n * Features:\n * - Tab-based demo type selection (website, mobile, tablet)\n * - Real-time input validation and preview generation\n * - Analytics tracking for user interactions\n * - Responsive design with mobile-first approach\n * - Smooth animations and transitions\n * - CMS-driven content with fallbacks\n *\n * Architecture:\n * - Uses composition pattern with smaller focused components\n * - State management for active tab and input values\n * - Integrated analytics tracking for demo interactions\n */\n\n'use client';\n\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { useAnalytics, useSectionSettings } from '@/hooks';\nimport { ANIMATION_CONFIG } from '@/config/site';\nimport DemoTabs from './DemoTabs';\nimport DemoInput from './DemoInput';\nimport DemoPreview from './DemoPreview';\n\n/**\n * Interactive demo section with tabbed interface and live preview\n */\nconst InteractiveDemo = () => {\n  const [activeTab, setActiveTab] = useState('website');\n  const [inputValue, setInputValue] = useState('');\n  const [showDemo, setShowDemo] = useState(false);\n  const { trackDemoInteraction } = useAnalytics();\n  const demoSettings = useSectionSettings('services'); // Use services settings for demo section\n\n  const handlePreview = () => {\n    if (inputValue.trim()) {\n      setShowDemo(true);\n      trackDemoInteraction('preview_click', activeTab);\n    }\n  };\n\n  const handleTabSwitch = (tab: string) => {\n    setActiveTab(tab);\n    setShowDemo(false);\n    setInputValue('');\n    trackDemoInteraction('tab_switch', tab);\n  };\n\n  const handleAnimationComplete = () => {\n    trackDemoInteraction('animation_complete', activeTab);\n  };\n\n  return (\n    <section id=\"demo\" className=\"py-20 bg-gray-50 dark:bg-gray-900 transition-colors duration-300\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: ANIMATION_CONFIG.duration.normal }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-3xl md:text-4xl font-bold text-dark-charcoal dark:text-white mb-4\">\n            {demoSettings.headline || 'From Zero to App, Instantly.'}\n          </h2>\n          <p className=\"text-lg md:text-xl leading-relaxed text-gray-600 dark:text-gray-300 max-w-3xl mx-auto\">\n            {demoSettings.subtext || 'See how quickly we can transform your vision into a beautiful mobile app'}\n          </p>\n        </motion.div>\n\n        <div className=\"grid lg:grid-cols-2 gap-12 items-start\">\n          {/* Input Section */}\n          <motion.div\n            initial={{ opacity: 0, x: -30 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: ANIMATION_CONFIG.duration.normal, delay: 0.2 }}\n            viewport={{ once: true }}\n            className=\"space-y-8\"\n          >\n            <DemoTabs\n              activeTab={activeTab}\n              onTabChange={handleTabSwitch}\n            />\n\n            <DemoInput\n              activeTab={activeTab}\n              value={inputValue}\n              onChange={setInputValue}\n              onPreview={handlePreview}\n            />\n\n          </motion.div>\n\n          {/* Preview Section */}\n          <motion.div\n            initial={{ opacity: 0, x: 30 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: ANIMATION_CONFIG.duration.normal, delay: 0.4 }}\n            viewport={{ once: true }}\n            className=\"flex justify-center\"\n          >\n            <DemoPreview\n              activeTab={activeTab}\n              inputValue={inputValue}\n              isVisible={showDemo}\n              onAnimationComplete={handleAnimationComplete}\n            />\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default InteractiveDemo;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;CAwBC;;;;AAID;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAUA;;CAEC,GACD,MAAM,kBAAkB;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,EAAE,oBAAoB,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD;IAC5C,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,qBAAkB,AAAD,EAAE,aAAa,yCAAyC;IAE9F,MAAM,gBAAgB;QACpB,IAAI,WAAW,IAAI,IAAI;YACrB,YAAY;YACZ,qBAAqB,iBAAiB;QACxC;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,aAAa;QACb,YAAY;QACZ,cAAc;QACd,qBAAqB,cAAc;IACrC;IAEA,MAAM,0BAA0B;QAC9B,qBAAqB,sBAAsB;IAC7C;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAO,WAAU;kBAC3B,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU,wHAAA,CAAA,mBAAgB,CAAC,QAAQ,CAAC,MAAM;oBAAC;oBACzD,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;sCACX,aAAa,QAAQ,IAAI;;;;;;sCAE5B,6LAAC;4BAAE,WAAU;sCACV,aAAa,OAAO,IAAI;;;;;;;;;;;;8BAI7B,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU,wHAAA,CAAA,mBAAgB,CAAC,QAAQ,CAAC,MAAM;gCAAE,OAAO;4BAAI;4BACrE,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,6LAAC,6IAAA,CAAA,UAAQ;oCACP,WAAW;oCACX,aAAa;;;;;;8CAGf,6LAAC,8IAAA,CAAA,UAAS;oCACR,WAAW;oCACX,OAAO;oCACP,UAAU;oCACV,WAAW;;;;;;;;;;;;sCAMf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU,wHAAA,CAAA,mBAAgB,CAAC,QAAQ,CAAC,MAAM;gCAAE,OAAO;4BAAI;4BACrE,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;sCAEV,cAAA,6LAAC,gJAAA,CAAA,UAAW;gCACV,WAAW;gCACX,YAAY;gCACZ,WAAW;gCACX,qBAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnC;GArFM;;QAI6B,+HAAA,CAAA,eAAY;QACxB,kIAAA,CAAA,qBAAkB;;;KALnC;uCAuFS", "debugId": null}}, {"offset": {"line": 3459, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/sections/ServicesOverview.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { ArrowRight, Smartphone, Lightbulb, Building } from 'lucide-react';\nimport Link from 'next/link';\nimport { useSectionSettings } from '@/hooks';\nimport { SERVICES, ANIMATION_CONFIG } from '@/config/site';\n\nconst ServicesOverview = () => {\n  const servicesSettings = useSectionSettings('services');\n\n  const services = [\n    {\n      icon: <Smartphone className=\"w-8 h-8\" />,\n      ...SERVICES.starter,\n    },\n    {\n      icon: <Lightbulb className=\"w-8 h-8\" />,\n      ...SERVICES.custom,\n    },\n    {\n      icon: <Building className=\"w-8 h-8\" />,\n      ...SERVICES.enterprise,\n    },\n  ];\n\n  const handleViewServices = () => {\n    // Track view services details event (placeholder for GA4)\n    if (typeof window !== 'undefined' && window.gtag) {\n      window.gtag('event', 'view_services_details', {\n        event_category: 'engagement',\n        event_label: 'services_overview'\n      });\n    }\n  };\n\n  return (\n    <section id=\"services-overview\" className=\"py-16 md:py-20 bg-gray-50 dark:bg-gray-800 transition-colors duration-300\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: ANIMATION_CONFIG.duration.normal }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-3xl md:text-4xl font-bold text-dark-charcoal dark:text-white mb-4\">\n            {servicesSettings.headline}\n          </h2>\n          <p className=\"text-lg md:text-xl leading-relaxed text-gray-600 dark:text-gray-300 max-w-3xl mx-auto\">\n            {servicesSettings.subtext}\n          </p>\n        </motion.div>\n\n        <div className=\"grid md:grid-cols-3 gap-8 md:gap-12 mb-12\">\n          {services.map((service, index) => (\n            <motion.div\n              key={index}\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n              viewport={{ once: true }}\n              className={`relative bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300 ${\n                service.popular ? 'ring-2 ring-electric-blue' : ''\n              }`}\n            >\n              {service.popular && (\n                <div className=\"absolute -top-4 left-1/2 transform -translate-x-1/2\">\n                  <span className=\"bg-electric-blue text-white px-4 py-1 rounded-full text-sm font-medium\">\n                    Most Popular\n                  </span>\n                </div>\n              )}\n              \n              <div className=\"text-electric-blue mb-4\">\n                {service.icon}\n              </div>\n              \n              <h3 className=\"text-xl font-semibold text-dark-charcoal dark:text-white mb-2\">\n                {service.name}\n              </h3>\n\n              <p className=\"text-base leading-relaxed text-gray-600 dark:text-gray-300 mb-4\">\n                {service.description}\n              </p>\n\n              <div className=\"text-2xl font-bold text-dark-charcoal dark:text-white mb-4\">\n                {service.price}\n              </div>\n\n              <ul className=\"space-y-2 mb-6\">\n                {service.features.map((feature, featureIndex) => (\n                  <li key={featureIndex} className=\"flex items-center text-base text-gray-600 dark:text-gray-300\">\n                    <div className=\"w-2 h-2 bg-electric-blue rounded-full mr-3\"></div>\n                    {feature}\n                  </li>\n                ))}\n              </ul>\n            </motion.div>\n          ))}\n        </div>\n\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          viewport={{ once: true }}\n          className=\"text-center\"\n        >\n          <Link\n            href=\"/services\"\n            onClick={handleViewServices}\n            className=\"inline-flex items-center bg-electric-blue text-white px-8 py-4 rounded-lg font-semibold hover:opacity-90 transition-opacity duration-200 shadow-lg hover:shadow-xl\"\n          >\n            Compare All Features & Pricing\n            <ArrowRight className=\"ml-2 w-5 h-5\" />\n          </Link>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default ServicesOverview;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AACA;;;AAPA;;;;;;AASA,MAAM,mBAAmB;;IACvB,MAAM,mBAAmB,CAAA,GAAA,kIAAA,CAAA,qBAAkB,AAAD,EAAE;IAE5C,MAAM,WAAW;QACf;YACE,oBAAM,6LAAC,iNAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;YAC5B,GAAG,wHAAA,CAAA,WAAQ,CAAC,OAAO;QACrB;QACA;YACE,oBAAM,6LAAC,+MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAC3B,GAAG,wHAAA,CAAA,WAAQ,CAAC,MAAM;QACpB;QACA;YACE,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,GAAG,wHAAA,CAAA,WAAQ,CAAC,UAAU;QACxB;KACD;IAED,MAAM,qBAAqB;QACzB,0DAA0D;QAC1D,IAAI,aAAkB,eAAe,OAAO,IAAI,EAAE;YAChD,OAAO,IAAI,CAAC,SAAS,yBAAyB;gBAC5C,gBAAgB;gBAChB,aAAa;YACf;QACF;IACF;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAoB,WAAU;kBACxC,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU,wHAAA,CAAA,mBAAgB,CAAC,QAAQ,CAAC,MAAM;oBAAC;oBACzD,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;sCACX,iBAAiB,QAAQ;;;;;;sCAE5B,6LAAC;4BAAE,WAAU;sCACV,iBAAiB,OAAO;;;;;;;;;;;;8BAI7B,6LAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAW,CAAC,2GAA2G,EACrH,QAAQ,OAAO,GAAG,8BAA8B,IAChD;;gCAED,QAAQ,OAAO,kBACd,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAAyE;;;;;;;;;;;8CAM7F,6LAAC;oCAAI,WAAU;8CACZ,QAAQ,IAAI;;;;;;8CAGf,6LAAC;oCAAG,WAAU;8CACX,QAAQ,IAAI;;;;;;8CAGf,6LAAC;oCAAE,WAAU;8CACV,QAAQ,WAAW;;;;;;8CAGtB,6LAAC;oCAAI,WAAU;8CACZ,QAAQ,KAAK;;;;;;8CAGhB,6LAAC;oCAAG,WAAU;8CACX,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC9B,6LAAC;4CAAsB,WAAU;;8DAC/B,6LAAC;oDAAI,WAAU;;;;;;gDACd;;2CAFM;;;;;;;;;;;2BAnCR;;;;;;;;;;8BA6CX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,SAAS;wBACT,WAAU;;4BACX;0CAEC,6LAAC,qNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlC;GAjHM;;QACqB,kIAAA,CAAA,qBAAkB;;;KADvC;uCAmHS", "debugId": null}}, {"offset": {"line": 3752, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/sections/Process.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { Search, Palette, Rocket } from 'lucide-react';\nimport { Card, CardContent } from '../ui';\n\nconst Process = () => {\n  const steps = [\n    {\n      number: '01',\n      icon: <Search className=\"w-8 h-8\" />,\n      title: 'Discovery & Strategy',\n      description: 'We dive deep into your vision and goals, understanding your target audience and business objectives to create the perfect roadmap.',\n    },\n    {\n      number: '02',\n      icon: <Palette className=\"w-8 h-8\" />,\n      title: 'Design & Development',\n      description: 'Our team builds your app with precision and care, focusing on user experience, performance, and beautiful design that represents your brand.',\n    },\n    {\n      number: '03',\n      icon: <Rocket className=\"w-8 h-8\" />,\n      title: 'Launch & Support',\n      description: 'We handle app store submission and provide ongoing support to ensure your app succeeds in the market and continues to evolve.',\n    },\n  ];\n\n  return (\n    <section id=\"process\" className=\"py-20 bg-white dark:bg-gray-900 transition-colors duration-300\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-3xl md:text-4xl font-bold text-dark-charcoal dark:text-white mb-4\">\n            Your Clear Path to Launch\n          </h2>\n          <p className=\"text-lg md:text-xl leading-relaxed text-gray-600 dark:text-gray-300 max-w-3xl mx-auto\">\n            Our proven process ensures your app is built right, launched successfully, and supported long-term\n          </p>\n        </motion.div>\n\n        <div className=\"grid md:grid-cols-3 gap-8 lg:gap-12\">\n          {steps.map((step, index) => (\n            <motion.div\n              key={step.number}\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.2 }}\n              viewport={{ once: true }}\n              className=\"relative text-center\"\n            >\n              {/* Connection line for desktop */}\n              {index < steps.length - 1 && (\n                <div className=\"hidden md:block absolute top-16 left-1/2 w-full h-0.5 bg-gray-200 dark:bg-gray-700 z-0\">\n                  <motion.div\n                    initial={{ width: 0 }}\n                    whileInView={{ width: '100%' }}\n                    transition={{ duration: 1, delay: index * 0.3 + 0.5 }}\n                    viewport={{ once: true }}\n                    className=\"h-full bg-electric-blue\"\n                  />\n                </div>\n              )}\n              \n              {/* Step number and icon */}\n              <div className=\"relative z-10 mb-6\">\n                <div className=\"inline-flex items-center justify-center w-16 h-16 bg-electric-blue text-white rounded-full mb-4 relative\">\n                  {step.icon}\n                  <div className=\"absolute -top-2 -right-2 w-8 h-8 bg-white dark:bg-gray-800 text-electric-blue rounded-full flex items-center justify-center text-sm font-bold shadow-lg\">\n                    {step.number}\n                  </div>\n                </div>\n              </div>\n              \n              {/* Content */}\n              <div className=\"space-y-4\">\n                <h3 className=\"text-xl font-semibold text-dark-charcoal dark:text-white\">\n                  {step.title}\n                </h3>\n                <p className=\"text-base leading-relaxed text-gray-600 dark:text-gray-300\">\n                  {step.description}\n                </p>\n              </div>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Additional info */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.8 }}\n          viewport={{ once: true }}\n          className=\"mt-16 text-center\"\n        >\n          <Card className=\"max-w-4xl mx-auto\">\n            <CardContent className=\"p-8 text-center\">\n              <h3 className=\"text-xl font-semibold text-dark-charcoal dark:text-white mb-2\">\n                Transparent Timeline\n              </h3>\n              <p className=\"text-base leading-relaxed text-gray-600 dark:text-gray-300\">\n                Most projects are completed within 4-8 weeks, depending on complexity.\n                We provide regular updates and maintain open communication throughout the entire process.\n              </p>\n            </CardContent>\n          </Card>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default Process;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AALA;;;;;AAOA,MAAM,UAAU;IACd,MAAM,QAAQ;QACZ;YACE,QAAQ;YACR,oBAAM,6LAAC,yMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,OAAO;YACP,aAAa;QACf;QACA;YACE,QAAQ;YACR,oBAAM,6LAAC,2MAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YACzB,OAAO;YACP,aAAa;QACf;QACA;YACE,QAAQ;YACR,oBAAM,6LAAC,yMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,6LAAC;QAAQ,IAAG;QAAU,WAAU;kBAC9B,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;sCAAyE;;;;;;sCAGvF,6LAAC;4BAAE,WAAU;sCAAwF;;;;;;;;;;;;8BAKvG,6LAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;gCAGT,QAAQ,MAAM,MAAM,GAAG,mBACtB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,OAAO;wCAAE;wCACpB,aAAa;4CAAE,OAAO;wCAAO;wCAC7B,YAAY;4CAAE,UAAU;4CAAG,OAAO,QAAQ,MAAM;wCAAI;wCACpD,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;;;;;;;;;;8CAMhB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;4CACZ,KAAK,IAAI;0DACV,6LAAC;gDAAI,WAAU;0DACZ,KAAK,MAAM;;;;;;;;;;;;;;;;;8CAMlB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDACX,KAAK,KAAK;;;;;;sDAEb,6LAAC;4CAAE,WAAU;sDACV,KAAK,WAAW;;;;;;;;;;;;;2BApChB,KAAK,MAAM;;;;;;;;;;8BA4CtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,6LAAC,sKAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC;oCAAG,WAAU;8CAAgE;;;;;;8CAG9E,6LAAC;oCAAE,WAAU;8CAA6D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUxF;KA7GM;uCA+GS", "debugId": null}}, {"offset": {"line": 4039, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/sections/AboutSnippet.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport Link from 'next/link';\nimport { ArrowRight } from 'lucide-react';\nimport { Card, CardContent } from '../ui';\n\nconst AboutSnippet = () => {\n  return (\n    <section id=\"about\" className=\"py-20 bg-gray-50 dark:bg-gray-800 transition-colors duration-300\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"max-w-4xl mx-auto\"\n        >\n          <Card variant=\"hover\" className=\"text-center\">\n            <CardContent className=\"p-8 md:p-12\">\n              <h2 className=\"text-3xl sm:text-4xl font-bold text-dark-charcoal dark:text-white mb-6\">\n                We&apos;re More Than Just Developers\n              </h2>\n\n              <div className=\"text-lg text-gray-600 dark:text-gray-300 leading-relaxed mb-8 space-y-4\">\n                <p>\n                  At Mobilify, we believe that every great idea deserves to become reality.\n                  We&apos;re passionate about helping founders and businesses succeed by removing\n                  the traditional barriers to mobile app development.\n                </p>\n\n                <p>\n                  Our commitment goes beyond just writing code – we&apos;re your partners in\n                  bringing your vision to life. We focus on quality over quantity, ensuring\n                  each app we create is crafted with care, attention to detail, and a deep\n                  understanding of your unique needs.\n                </p>\n              </div>\n\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.3 }}\n                viewport={{ once: true }}\n              >\n                <Link\n                  href=\"/about\"\n                  className=\"inline-flex items-center text-electric-blue hover:text-electric-blue/80 font-semibold transition-colors duration-200\"\n                >\n                  Meet the Team\n                  <ArrowRight className=\"ml-2 w-4 h-4\" />\n                </Link>\n              </motion.div>\n            </CardContent>\n          </Card>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default AboutSnippet;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AANA;;;;;;AAQA,MAAM,eAAe;IACnB,qBACE,6LAAC;QAAQ,IAAG;QAAQ,WAAU;kBAC5B,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,aAAa;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAChC,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,UAAU;oBAAE,MAAM;gBAAK;gBACvB,WAAU;0BAEV,cAAA,6LAAC,sKAAA,CAAA,OAAI;oBAAC,SAAQ;oBAAQ,WAAU;8BAC9B,cAAA,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAG,WAAU;0CAAyE;;;;;;0CAIvF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDAAE;;;;;;kDAMH,6LAAC;kDAAE;;;;;;;;;;;;0CAQL,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;0CAEvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;wCACX;sDAEC,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxC;KApDM;uCAsDS", "debugId": null}}, {"offset": {"line": 4203, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/NewsletterSignup.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Button, Input } from './ui';\n\ninterface NewsletterSignupProps {\n  variant?: 'inline' | 'footer' | 'section';\n  className?: string;\n}\n\nconst NewsletterSignup: React.FC<NewsletterSignupProps> = ({ \n  variant = 'inline',\n  className = ''\n}) => {\n  const [email, setEmail] = useState('');\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!email.trim()) return;\n\n    setIsSubmitting(true);\n    setSubmitStatus('idle');\n\n    try {\n      // Get Mailchimp API credentials from environment variables\n      const apiKey = process.env.NEXT_PUBLIC_MAILCHIMP_API_KEY;\n      const listId = process.env.MAILCHIMP_LIST_ID;\n\n      if (!apiKey || !listId) {\n        console.warn('Mailchimp credentials not configured');\n        // Simulate success for demo purposes\n        await new Promise(resolve => setTimeout(resolve, 1000));\n        setSubmitStatus('success');\n        setEmail('');\n        \n        // Track newsletter signup event\n        if (typeof window !== 'undefined' && window.gtag) {\n          window.gtag('event', 'newsletter_signup', {\n            event_category: 'engagement',\n            event_label: variant\n          });\n        }\n        return;\n      }\n\n      // In a real implementation, you would call your API endpoint here\n      // that handles the Mailchimp subscription\n      const response = await fetch('/api/newsletter', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          email: email,\n          source: variant\n        }),\n      });\n\n      if (response.ok) {\n        setSubmitStatus('success');\n        setEmail('');\n        \n        // Track newsletter signup event\n        if (typeof window !== 'undefined' && window.gtag) {\n          window.gtag('event', 'newsletter_signup', {\n            event_category: 'engagement',\n            event_label: variant\n          });\n        }\n      } else {\n        setSubmitStatus('error');\n      }\n    } catch (error) {\n      console.error('Newsletter signup error:', error);\n      setSubmitStatus('error');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n\n\n  return (\n    <div className={`${className}`}>\n      {(variant === 'inline' || variant === 'section') && (\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"py-16 md:py-20 bg-electric-blue\"\n        >\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-white mb-4\">\n              Stay Updated on Mobile Innovation\n            </h2>\n            <p className=\"text-lg md:text-xl leading-relaxed text-blue-100 max-w-3xl mx-auto mb-8\">\n              Get insights on mobile app development, industry trends, and exclusive tips delivered to your inbox.\n            </p>\n            \n            <form onSubmit={handleSubmit} className=\"max-w-md mx-auto\">\n              <div className=\"flex flex-col sm:flex-row gap-4\">\n                <Input\n                  type=\"email\"\n                  value={email}\n                  onChange={(e) => setEmail(e.target.value)}\n                  placeholder=\"Enter your email address\"\n                  required\n                  className=\"flex-1 bg-white border-white focus:ring-white focus:border-white\"\n                  disabled={isSubmitting}\n                />\n                <Button\n                  type=\"submit\"\n                  variant=\"secondary\"\n                  isLoading={isSubmitting}\n                  disabled={!email.trim() || isSubmitting}\n                  className=\"bg-white text-electric-blue hover:bg-gray-50 border-white\"\n                >\n                  Subscribe\n                </Button>\n              </div>\n              \n              {submitStatus === 'success' && (\n                <motion.p\n                  initial={{ opacity: 0, y: 10 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  className=\"mt-4 text-green-100 text-sm\"\n                >\n                  ✓ Successfully subscribed! Check your email for confirmation.\n                </motion.p>\n              )}\n              \n              {submitStatus === 'error' && (\n                <motion.p\n                  initial={{ opacity: 0, y: 10 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  className=\"mt-4 text-red-100 text-sm\"\n                >\n                  ✗ Something went wrong. Please try again.\n                </motion.p>\n              )}\n            </form>\n          </div>\n        </motion.div>\n      )}\n\n      {variant === 'footer' && (\n        <div>\n          <h3 className=\"text-xl font-semibold mb-4 text-white\">Stay Connected</h3>\n          <p className=\"text-sm text-gray-400 dark:text-gray-300 mb-4 leading-relaxed\">\n            Get the latest updates on mobile app development and industry insights.\n          </p>\n          \n          <form onSubmit={handleSubmit} className=\"space-y-4\">\n            <Input\n              type=\"email\"\n              value={email}\n              onChange={(e) => setEmail(e.target.value)}\n              placeholder=\"Enter your email\"\n              required\n              className=\"bg-gray-800 border-gray-600 text-white placeholder-gray-400 focus:ring-electric-blue focus:border-electric-blue\"\n              disabled={isSubmitting}\n            />\n            <Button\n              type=\"submit\"\n              variant=\"primary\"\n              size=\"sm\"\n              isLoading={isSubmitting}\n              disabled={!email.trim() || isSubmitting}\n              className=\"w-full\"\n            >\n              Subscribe\n            </Button>\n            \n            {submitStatus === 'success' && (\n              <motion.p\n                initial={{ opacity: 0, y: 10 }}\n                animate={{ opacity: 1, y: 0 }}\n                className=\"text-green-400 text-sm\"\n              >\n                ✓ Successfully subscribed!\n              </motion.p>\n            )}\n            \n            {submitStatus === 'error' && (\n              <motion.p\n                initial={{ opacity: 0, y: 10 }}\n                animate={{ opacity: 1, y: 0 }}\n                className=\"text-red-400 text-sm\"\n              >\n                ✗ Please try again.\n              </motion.p>\n            )}\n          </form>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default NewsletterSignup;\n"], "names": [], "mappings": ";;;AA4BqB;;AA1BrB;AACA;AACA;AAAA;AAAA;;;AAJA;;;;AAWA,MAAM,mBAAoD,CAAC,EACzD,UAAU,QAAQ,EAClB,YAAY,EAAE,EACf;;IACC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgC;IAE/E,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI,CAAC,MAAM,IAAI,IAAI;QAEnB,gBAAgB;QAChB,gBAAgB;QAEhB,IAAI;YACF,2DAA2D;YAC3D,MAAM,SAAS,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,6BAA6B;YACxD,MAAM,SAAS,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,iBAAiB;YAE5C,IAAI,CAAC,UAAU,CAAC,QAAQ;gBACtB,QAAQ,IAAI,CAAC;gBACb,qCAAqC;gBACrC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBACjD,gBAAgB;gBAChB,SAAS;gBAET,gCAAgC;gBAChC,IAAI,aAAkB,eAAe,OAAO,IAAI,EAAE;oBAChD,OAAO,IAAI,CAAC,SAAS,qBAAqB;wBACxC,gBAAgB;wBAChB,aAAa;oBACf;gBACF;gBACA;YACF;YAEA,kEAAkE;YAClE,0CAA0C;YAC1C,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,OAAO;oBACP,QAAQ;gBACV;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,gBAAgB;gBAChB,SAAS;gBAET,gCAAgC;gBAChC,IAAI,aAAkB,eAAe,OAAO,IAAI,EAAE;oBAChD,OAAO,IAAI,CAAC,SAAS,qBAAqB;wBACxC,gBAAgB;wBAChB,aAAa;oBACf;gBACF;YACF,OAAO;gBACL,gBAAgB;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,gBAAgB;QAClB,SAAU;YACR,gBAAgB;QAClB;IACF;IAIA,qBACE,6LAAC;QAAI,WAAW,GAAG,WAAW;;YAC3B,CAAC,YAAY,YAAY,YAAY,SAAS,mBAC7C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,aAAa;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAChC,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,UAAU;oBAAE,MAAM;gBAAK;gBACvB,WAAU;0BAEV,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAiD;;;;;;sCAG/D,6LAAC;4BAAE,WAAU;sCAA0E;;;;;;sCAIvF,6LAAC;4BAAK,UAAU;4BAAc,WAAU;;8CACtC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,wKAAA,CAAA,QAAK;4CACJ,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4CACxC,aAAY;4CACZ,QAAQ;4CACR,WAAU;4CACV,UAAU;;;;;;sDAEZ,6LAAC,0KAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,WAAW;4CACX,UAAU,CAAC,MAAM,IAAI,MAAM;4CAC3B,WAAU;sDACX;;;;;;;;;;;;gCAKF,iBAAiB,2BAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,WAAU;8CACX;;;;;;gCAKF,iBAAiB,yBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;YASV,YAAY,0BACX,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC;wBAAE,WAAU;kCAAgE;;;;;;kCAI7E,6LAAC;wBAAK,UAAU;wBAAc,WAAU;;0CACtC,6LAAC,wKAAA,CAAA,QAAK;gCACJ,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gCACxC,aAAY;gCACZ,QAAQ;gCACR,WAAU;gCACV,UAAU;;;;;;0CAEZ,6LAAC,0KAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAW;gCACX,UAAU,CAAC,MAAM,IAAI,MAAM;gCAC3B,WAAU;0CACX;;;;;;4BAIA,iBAAiB,2BAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,WAAU;0CACX;;;;;;4BAKF,iBAAiB,yBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;AASf;GA9LM;KAAA;uCAgMS", "debugId": null}}, {"offset": {"line": 4518, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/ChatTrigger.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { MessageCircle, HelpCircle, Phone } from 'lucide-react';\nimport { motion } from 'framer-motion';\nimport { crispUtils } from '../analytics/CrispChat';\n\ninterface ChatTriggerProps {\n  variant?: 'button' | 'floating' | 'inline';\n  text?: string;\n  icon?: 'message' | 'help' | 'phone';\n  className?: string;\n  size?: 'sm' | 'md' | 'lg';\n  context?: string; // For analytics and session data\n}\n\nconst ChatTrigger: React.FC<ChatTriggerProps> = ({\n  variant = 'button',\n  text = 'Chat with us',\n  icon = 'message',\n  className = '',\n  size = 'md',\n  context = 'general'\n}) => {\n  const handleChatOpen = () => {\n    // Set context data for better support\n    crispUtils.setSessionData({\n      trigger_context: context,\n      trigger_page: window.location.pathname,\n      trigger_time: new Date().toISOString()\n    });\n\n    // Open the chat\n    crispUtils.openChat();\n\n    // Track the interaction\n    if (typeof window !== 'undefined' && window.gtag) {\n      window.gtag('event', 'chat_trigger_clicked', {\n        event_category: 'engagement',\n        event_label: context,\n        custom_parameter_1: variant\n      });\n    }\n  };\n\n  const getIcon = () => {\n    const iconProps = {\n      className: size === 'sm' ? 'w-4 h-4' : size === 'lg' ? 'w-6 h-6' : 'w-5 h-5'\n    };\n\n    switch (icon) {\n      case 'help':\n        return <HelpCircle {...iconProps} />;\n      case 'phone':\n        return <Phone {...iconProps} />;\n      default:\n        return <MessageCircle {...iconProps} />;\n    }\n  };\n\n  const getSizeClasses = () => {\n    switch (size) {\n      case 'sm':\n        return 'px-3 py-2 text-sm';\n      case 'lg':\n        return 'px-6 py-4 text-lg';\n      default:\n        return 'px-4 py-3 text-base';\n    }\n  };\n\n  if (variant === 'floating') {\n    return (\n      <motion.button\n        onClick={handleChatOpen}\n        className={`fixed bottom-6 right-6 bg-electric-blue text-white rounded-full p-4 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 z-40 ${className}`}\n        whileHover={{ scale: 1.05 }}\n        whileTap={{ scale: 0.95 }}\n        initial={{ opacity: 0, scale: 0 }}\n        animate={{ opacity: 1, scale: 1 }}\n        transition={{ delay: 2, duration: 0.3 }}\n        title={text}\n      >\n        {getIcon()}\n        <span className=\"sr-only\">{text}</span>\n      </motion.button>\n    );\n  }\n\n  if (variant === 'inline') {\n    return (\n      <button\n        onClick={handleChatOpen}\n        className={`inline-flex items-center gap-2 text-electric-blue hover:text-indigo-700 font-medium transition-colors duration-200 ${className}`}\n      >\n        {getIcon()}\n        <span>{text}</span>\n      </button>\n    );\n  }\n\n  // Default button variant\n  return (\n    <motion.button\n      onClick={handleChatOpen}\n      className={`inline-flex items-center gap-2 bg-electric-blue text-white rounded-lg font-semibold hover:opacity-90 transition-all duration-200 shadow-md hover:shadow-lg ${getSizeClasses()} ${className}`}\n      whileHover={{ scale: 1.02 }}\n      whileTap={{ scale: 0.98 }}\n    >\n      {getIcon()}\n      <span>{text}</span>\n    </motion.button>\n  );\n};\n\nexport default ChatTrigger;\n\n// Pre-configured chat triggers for common use cases\nexport const ChatTriggers = {\n  // For the header/navigation\n  HeaderChat: () => (\n    <ChatTrigger\n      variant=\"inline\"\n      text=\"Live Chat\"\n      icon=\"message\"\n      context=\"header\"\n      size=\"sm\"\n    />\n  ),\n\n  // For the contact section\n  ContactChat: () => (\n    <ChatTrigger\n      variant=\"button\"\n      text=\"Chat with Our Team\"\n      icon=\"message\"\n      context=\"contact\"\n      size=\"lg\"\n      className=\"w-full sm:w-auto\"\n    />\n  ),\n\n  // For the services page\n  ServicesChat: () => (\n    <ChatTrigger\n      variant=\"button\"\n      text=\"Ask About Pricing\"\n      icon=\"help\"\n      context=\"services\"\n      size=\"md\"\n    />\n  ),\n\n  // For the FAQ page\n  FAQChat: () => (\n    <ChatTrigger\n      variant=\"inline\"\n      text=\"Still have questions? Chat with us\"\n      icon=\"help\"\n      context=\"faq\"\n      size=\"md\"\n    />\n  ),\n\n  // For blog posts\n  BlogChat: () => (\n    <ChatTrigger\n      variant=\"inline\"\n      text=\"Discuss this article\"\n      icon=\"message\"\n      context=\"blog\"\n      size=\"sm\"\n    />\n  ),\n\n  // Floating action button (always visible)\n  FloatingChat: () => (\n    <ChatTrigger\n      variant=\"floating\"\n      text=\"Chat with us\"\n      icon=\"message\"\n      context=\"floating\"\n    />\n  ),\n\n  // For the demo section\n  DemoChat: () => (\n    <ChatTrigger\n      variant=\"button\"\n      text=\"Get Help with Demo\"\n      icon=\"help\"\n      context=\"demo\"\n      size=\"md\"\n    />\n  )\n};\n"], "names": [], "mappings": ";;;;;AAGA;AAAA;AAAA;AACA;AACA;AALA;;;;;AAgBA,MAAM,cAA0C,CAAC,EAC/C,UAAU,QAAQ,EAClB,OAAO,cAAc,EACrB,OAAO,SAAS,EAChB,YAAY,EAAE,EACd,OAAO,IAAI,EACX,UAAU,SAAS,EACpB;IACC,MAAM,iBAAiB;QACrB,sCAAsC;QACtC,iIAAA,CAAA,aAAU,CAAC,cAAc,CAAC;YACxB,iBAAiB;YACjB,cAAc,OAAO,QAAQ,CAAC,QAAQ;YACtC,cAAc,IAAI,OAAO,WAAW;QACtC;QAEA,gBAAgB;QAChB,iIAAA,CAAA,aAAU,CAAC,QAAQ;QAEnB,wBAAwB;QACxB,IAAI,aAAkB,eAAe,OAAO,IAAI,EAAE;YAChD,OAAO,IAAI,CAAC,SAAS,wBAAwB;gBAC3C,gBAAgB;gBAChB,aAAa;gBACb,oBAAoB;YACtB;QACF;IACF;IAEA,MAAM,UAAU;QACd,MAAM,YAAY;YAChB,WAAW,SAAS,OAAO,YAAY,SAAS,OAAO,YAAY;QACrE;QAEA,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,iOAAA,CAAA,aAAU;oBAAE,GAAG,SAAS;;;;;;YAClC,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAE,GAAG,SAAS;;;;;;YAC7B;gBACE,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAE,GAAG,SAAS;;;;;;QACvC;IACF;IAEA,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,YAAY,YAAY;QAC1B,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;YACZ,SAAS;YACT,WAAW,CAAC,+IAA+I,EAAE,WAAW;YACxK,YAAY;gBAAE,OAAO;YAAK;YAC1B,UAAU;gBAAE,OAAO;YAAK;YACxB,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAE;YAChC,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAE;YAChC,YAAY;gBAAE,OAAO;gBAAG,UAAU;YAAI;YACtC,OAAO;;gBAEN;8BACD,6LAAC;oBAAK,WAAU;8BAAW;;;;;;;;;;;;IAGjC;IAEA,IAAI,YAAY,UAAU;QACxB,qBACE,6LAAC;YACC,SAAS;YACT,WAAW,CAAC,mHAAmH,EAAE,WAAW;;gBAE3I;8BACD,6LAAC;8BAAM;;;;;;;;;;;;IAGb;IAEA,yBAAyB;IACzB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,SAAS;QACT,WAAW,CAAC,2JAA2J,EAAE,iBAAiB,CAAC,EAAE,WAAW;QACxM,YAAY;YAAE,OAAO;QAAK;QAC1B,UAAU;YAAE,OAAO;QAAK;;YAEvB;0BACD,6LAAC;0BAAM;;;;;;;;;;;;AAGb;KAjGM;uCAmGS;AAGR,MAAM,eAAe;IAC1B,4BAA4B;IAC5B,YAAY,kBACV,6LAAC;YACC,SAAQ;YACR,MAAK;YACL,MAAK;YACL,SAAQ;YACR,MAAK;;;;;;IAIT,0BAA0B;IAC1B,aAAa,kBACX,6LAAC;YACC,SAAQ;YACR,MAAK;YACL,MAAK;YACL,SAAQ;YACR,MAAK;YACL,WAAU;;;;;;IAId,wBAAwB;IACxB,cAAc,kBACZ,6LAAC;YACC,SAAQ;YACR,MAAK;YACL,MAAK;YACL,SAAQ;YACR,MAAK;;;;;;IAIT,mBAAmB;IACnB,SAAS,kBACP,6LAAC;YACC,SAAQ;YACR,MAAK;YACL,MAAK;YACL,SAAQ;YACR,MAAK;;;;;;IAIT,iBAAiB;IACjB,UAAU,kBACR,6LAAC;YACC,SAAQ;YACR,MAAK;YACL,MAAK;YACL,SAAQ;YACR,MAAK;;;;;;IAIT,0CAA0C;IAC1C,cAAc,kBACZ,6LAAC;YACC,SAAQ;YACR,MAAK;YACL,MAAK;YACL,SAAQ;;;;;;IAIZ,uBAAuB;IACvB,UAAU,kBACR,6LAAC;YACC,SAAQ;YACR,MAAK;YACL,MAAK;YACL,SAAQ;YACR,MAAK;;;;;;AAGX", "debugId": null}}, {"offset": {"line": 4778, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/sections/ContactForm.tsx"], "sourcesContent": ["/**\n * Contact Form Component\n *\n * Comprehensive contact form with validation, submission handling, and analytics tracking.\n * Features real-time validation, loading states, and success/error feedback.\n *\n * @component\n * @example\n * ```tsx\n * <ContactForm />\n * ```\n *\n * Features:\n * - Real-time form validation with field-level feedback\n * - Form submission with loading states and success/error handling\n * - Analytics tracking for form interactions and submissions\n * - Responsive design with mobile-optimized inputs\n * - Accessibility support with proper ARIA labels and error announcements\n * - Integration with external form services (Formspree/Web3Forms)\n * - Smooth animations for state transitions\n * - CMS-driven content with fallbacks\n *\n * Form Fields:\n * - Name (required)\n * - Email (required, validated)\n * - Company (optional)\n * - Project type (select dropdown)\n * - Budget range (select dropdown)\n * - Message (required, textarea)\n *\n * State Management:\n * - Uses custom useContactForm hook for all form logic\n * - Handles validation, submission, and reset functionality\n * - Integrates with analytics for tracking user interactions\n */\n\n'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { useContactForm, useSectionSettings } from '@/hooks';\nimport { ANIMATION_CONFIG } from '@/config/site';\n\n/**\n * Contact form with comprehensive validation and submission handling\n */\nconst ContactForm = () => {\n  const {\n    formData,\n    errors,\n    isSubmitting,\n    isSubmitted,\n    submitSuccess,\n    updateField,\n    handleFieldFocus,\n    validateSingleField,\n    submitForm,\n    resetForm\n  } = useContactForm();\n\n  const contactSettings = useSectionSettings('contact');\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    await submitForm();\n  };\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    updateField(e.target.name as keyof typeof formData, e.target.value);\n  };\n\n  const handleBlur = (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    validateSingleField(e.target.name as keyof typeof formData);\n  };\n\n  const handleFocus = (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    handleFieldFocus(e.target.name as keyof typeof formData);\n  };\n\n  // Show success message\n  if (isSubmitted && submitSuccess) {\n    return (\n      <motion.div\n        initial={{ opacity: 0, scale: 0.9 }}\n        animate={{ opacity: 1, scale: 1 }}\n        transition={{ duration: ANIMATION_CONFIG.duration.normal }}\n        className=\"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-6 text-center\"\n      >\n        <div className=\"text-green-600 dark:text-green-400 text-lg font-semibold mb-2\">\n          Message Sent Successfully!\n        </div>\n        <p className=\"text-green-700 dark:text-green-300 mb-4\">\n          {contactSettings.successMessage}\n        </p>\n        <button\n          onClick={resetForm}\n          className=\"text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300 font-medium\"\n        >\n          Send Another Message\n        </button>\n      </motion.div>\n    );\n  }\n\n  return (\n    <form onSubmit={handleSubmit} className=\"space-y-6\">\n      {/* General Error Message */}\n      {errors.general && (\n        <div className=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4\">\n          <p className=\"text-red-600 dark:text-red-400 text-sm\">{errors.general}</p>\n        </div>\n      )}\n\n      {/* Name Field */}\n      <div>\n        <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n          Full Name *\n        </label>\n        <input\n          type=\"text\"\n          id=\"name\"\n          name=\"name\"\n          value={formData.name}\n          onChange={handleChange}\n          onBlur={handleBlur}\n          onFocus={handleFocus}\n          className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-electric-blue focus:border-electric-blue transition-colors duration-200 dark:bg-gray-800 dark:border-gray-600 dark:text-white ${\n            errors.name ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : 'border-gray-300'\n          }`}\n          placeholder=\"Your full name\"\n          disabled={isSubmitting}\n        />\n        {errors.name && (\n          <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">{errors.name}</p>\n        )}\n      </div>\n\n      {/* Email Field */}\n      <div>\n        <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n          Email Address *\n        </label>\n        <input\n          type=\"email\"\n          id=\"email\"\n          name=\"email\"\n          value={formData.email}\n          onChange={handleChange}\n          onBlur={handleBlur}\n          onFocus={handleFocus}\n          className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-electric-blue focus:border-electric-blue transition-colors duration-200 dark:bg-gray-800 dark:border-gray-600 dark:text-white ${\n            errors.email ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : 'border-gray-300'\n          }`}\n          placeholder=\"<EMAIL>\"\n          disabled={isSubmitting}\n        />\n        {errors.email && (\n          <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">{errors.email}</p>\n        )}\n      </div>\n\n      {/* Company Field */}\n      <div>\n        <label htmlFor=\"company\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n          Company (Optional)\n        </label>\n        <input\n          type=\"text\"\n          id=\"company\"\n          name=\"company\"\n          value={formData.company}\n          onChange={handleChange}\n          onBlur={handleBlur}\n          onFocus={handleFocus}\n          className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-electric-blue focus:border-electric-blue transition-colors duration-200 dark:bg-gray-800 dark:border-gray-600 dark:text-white ${\n            errors.company ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : 'border-gray-300'\n          }`}\n          placeholder=\"Your company name\"\n          disabled={isSubmitting}\n        />\n        {errors.company && (\n          <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">{errors.company}</p>\n        )}\n      </div>\n\n      {/* Project Type Field */}\n      <div>\n        <label htmlFor=\"projectType\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n          Project Type *\n        </label>\n        <select\n          id=\"projectType\"\n          name=\"projectType\"\n          value={formData.projectType}\n          onChange={handleChange}\n          onBlur={handleBlur}\n          onFocus={handleFocus}\n          className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-electric-blue focus:border-electric-blue transition-colors duration-200 dark:bg-gray-800 dark:border-gray-600 dark:text-white ${\n            errors.projectType ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : 'border-gray-300'\n          }`}\n          disabled={isSubmitting}\n        >\n          <option value=\"\">Select a project type</option>\n          <option value=\"website-conversion\">Website to App Conversion</option>\n          <option value=\"custom-app\">Custom App Development</option>\n          <option value=\"enterprise\">Enterprise Solution</option>\n          <option value=\"consultation\">Consultation Only</option>\n          <option value=\"other\">Other</option>\n        </select>\n        {errors.projectType && (\n          <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">{errors.projectType}</p>\n        )}\n      </div>\n\n      {/* Message Field */}\n      <div>\n        <label htmlFor=\"message\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n          Project Details *\n        </label>\n        <textarea\n          id=\"message\"\n          name=\"message\"\n          rows={5}\n          value={formData.message}\n          onChange={handleChange}\n          onBlur={handleBlur}\n          onFocus={handleFocus}\n          className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-electric-blue focus:border-electric-blue transition-colors duration-200 dark:bg-gray-800 dark:border-gray-600 dark:text-white resize-vertical ${\n            errors.message ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : 'border-gray-300'\n          }`}\n          placeholder=\"Tell us about your project, timeline, and any specific requirements...\"\n          disabled={isSubmitting}\n        />\n        {errors.message && (\n          <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">{errors.message}</p>\n        )}\n        <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n          {formData.message.length}/1000 characters\n        </p>\n      </div>\n\n      {/* Submit Button */}\n      <button\n        type=\"submit\"\n        disabled={isSubmitting}\n        className=\"w-full bg-electric-blue text-white py-4 px-6 rounded-lg font-semibold hover:opacity-90 focus:ring-2 focus:ring-electric-blue focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl\"\n      >\n        {isSubmitting ? 'Sending...' : contactSettings.buttonText}\n      </button>\n\n      {/* Error Message */}\n      {isSubmitted && !submitSuccess && (\n        <div className=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4\">\n          <p className=\"text-red-600 dark:text-red-400 text-sm\">\n            {contactSettings.errorMessage}\n          </p>\n        </div>\n      )}\n    </form>\n  );\n};\n\nexport default ContactForm;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAkCC;;;;AAKD;AACA;AAAA;AAAA;AACA;;;AALA;;;;AAOA;;CAEC,GACD,MAAM,cAAc;;IAClB,MAAM,EACJ,QAAQ,EACR,MAAM,EACN,YAAY,EACZ,WAAW,EACX,aAAa,EACb,WAAW,EACX,gBAAgB,EAChB,mBAAmB,EACnB,UAAU,EACV,SAAS,EACV,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAEjB,MAAM,kBAAkB,CAAA,GAAA,kIAAA,CAAA,qBAAkB,AAAD,EAAE;IAE3C,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,MAAM;IACR;IAEA,MAAM,eAAe,CAAC;QACpB,YAAY,EAAE,MAAM,CAAC,IAAI,EAA2B,EAAE,MAAM,CAAC,KAAK;IACpE;IAEA,MAAM,aAAa,CAAC;QAClB,oBAAoB,EAAE,MAAM,CAAC,IAAI;IACnC;IAEA,MAAM,cAAc,CAAC;QACnB,iBAAiB,EAAE,MAAM,CAAC,IAAI;IAChC;IAEA,uBAAuB;IACvB,IAAI,eAAe,eAAe;QAChC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAI;YAClC,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAE;YAChC,YAAY;gBAAE,UAAU,wHAAA,CAAA,mBAAgB,CAAC,QAAQ,CAAC,MAAM;YAAC;YACzD,WAAU;;8BAEV,6LAAC;oBAAI,WAAU;8BAAgE;;;;;;8BAG/E,6LAAC;oBAAE,WAAU;8BACV,gBAAgB,cAAc;;;;;;8BAEjC,6LAAC;oBACC,SAAS;oBACT,WAAU;8BACX;;;;;;;;;;;;IAKP;IAEA,qBACE,6LAAC;QAAK,UAAU;QAAc,WAAU;;YAErC,OAAO,OAAO,kBACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;8BAA0C,OAAO,OAAO;;;;;;;;;;;0BAKzE,6LAAC;;kCACC,6LAAC;wBAAM,SAAQ;wBAAO,WAAU;kCAAkE;;;;;;kCAGlG,6LAAC;wBACC,MAAK;wBACL,IAAG;wBACH,MAAK;wBACL,OAAO,SAAS,IAAI;wBACpB,UAAU;wBACV,QAAQ;wBACR,SAAS;wBACT,WAAW,CAAC,yLAAyL,EACnM,OAAO,IAAI,GAAG,2DAA2D,mBACzE;wBACF,aAAY;wBACZ,UAAU;;;;;;oBAEX,OAAO,IAAI,kBACV,6LAAC;wBAAE,WAAU;kCAA+C,OAAO,IAAI;;;;;;;;;;;;0BAK3E,6LAAC;;kCACC,6LAAC;wBAAM,SAAQ;wBAAQ,WAAU;kCAAkE;;;;;;kCAGnG,6LAAC;wBACC,MAAK;wBACL,IAAG;wBACH,MAAK;wBACL,OAAO,SAAS,KAAK;wBACrB,UAAU;wBACV,QAAQ;wBACR,SAAS;wBACT,WAAW,CAAC,yLAAyL,EACnM,OAAO,KAAK,GAAG,2DAA2D,mBAC1E;wBACF,aAAY;wBACZ,UAAU;;;;;;oBAEX,OAAO,KAAK,kBACX,6LAAC;wBAAE,WAAU;kCAA+C,OAAO,KAAK;;;;;;;;;;;;0BAK5E,6LAAC;;kCACC,6LAAC;wBAAM,SAAQ;wBAAU,WAAU;kCAAkE;;;;;;kCAGrG,6LAAC;wBACC,MAAK;wBACL,IAAG;wBACH,MAAK;wBACL,OAAO,SAAS,OAAO;wBACvB,UAAU;wBACV,QAAQ;wBACR,SAAS;wBACT,WAAW,CAAC,yLAAyL,EACnM,OAAO,OAAO,GAAG,2DAA2D,mBAC5E;wBACF,aAAY;wBACZ,UAAU;;;;;;oBAEX,OAAO,OAAO,kBACb,6LAAC;wBAAE,WAAU;kCAA+C,OAAO,OAAO;;;;;;;;;;;;0BAK9E,6LAAC;;kCACC,6LAAC;wBAAM,SAAQ;wBAAc,WAAU;kCAAkE;;;;;;kCAGzG,6LAAC;wBACC,IAAG;wBACH,MAAK;wBACL,OAAO,SAAS,WAAW;wBAC3B,UAAU;wBACV,QAAQ;wBACR,SAAS;wBACT,WAAW,CAAC,yLAAyL,EACnM,OAAO,WAAW,GAAG,2DAA2D,mBAChF;wBACF,UAAU;;0CAEV,6LAAC;gCAAO,OAAM;0CAAG;;;;;;0CACjB,6LAAC;gCAAO,OAAM;0CAAqB;;;;;;0CACnC,6LAAC;gCAAO,OAAM;0CAAa;;;;;;0CAC3B,6LAAC;gCAAO,OAAM;0CAAa;;;;;;0CAC3B,6LAAC;gCAAO,OAAM;0CAAe;;;;;;0CAC7B,6LAAC;gCAAO,OAAM;0CAAQ;;;;;;;;;;;;oBAEvB,OAAO,WAAW,kBACjB,6LAAC;wBAAE,WAAU;kCAA+C,OAAO,WAAW;;;;;;;;;;;;0BAKlF,6LAAC;;kCACC,6LAAC;wBAAM,SAAQ;wBAAU,WAAU;kCAAkE;;;;;;kCAGrG,6LAAC;wBACC,IAAG;wBACH,MAAK;wBACL,MAAM;wBACN,OAAO,SAAS,OAAO;wBACvB,UAAU;wBACV,QAAQ;wBACR,SAAS;wBACT,WAAW,CAAC,yMAAyM,EACnN,OAAO,OAAO,GAAG,2DAA2D,mBAC5E;wBACF,aAAY;wBACZ,UAAU;;;;;;oBAEX,OAAO,OAAO,kBACb,6LAAC;wBAAE,WAAU;kCAA+C,OAAO,OAAO;;;;;;kCAE5E,6LAAC;wBAAE,WAAU;;4BACV,SAAS,OAAO,CAAC,MAAM;4BAAC;;;;;;;;;;;;;0BAK7B,6LAAC;gBACC,MAAK;gBACL,UAAU;gBACV,WAAU;0BAET,eAAe,eAAe,gBAAgB,UAAU;;;;;;YAI1D,eAAe,CAAC,+BACf,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;8BACV,gBAAgB,YAAY;;;;;;;;;;;;;;;;;AAMzC;GAtNM;;QAYA,iIAAA,CAAA,iBAAc;QAEM,kIAAA,CAAA,qBAAkB;;;KAdtC;uCAwNS", "debugId": null}}, {"offset": {"line": 5228, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/sections/Contact.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { ChatTriggers } from '../ChatTrigger';\nimport { useSectionSettings } from '@/hooks';\nimport { ANIMATION_CONFIG } from '@/config/site';\nimport ContactForm from './ContactForm';\n\nconst Contact = () => {\n  const contactSettings = useSectionSettings('contact');\n\n  return (\n    <section id=\"contact\" className=\"py-16 md:py-20 bg-white dark:bg-gray-900 transition-colors duration-300\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: ANIMATION_CONFIG.duration.normal }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-3xl md:text-4xl font-bold text-dark-charcoal dark:text-white mb-4\">\n            {contactSettings.headline}\n          </h2>\n          <p className=\"text-lg md:text-xl leading-relaxed text-gray-600 dark:text-gray-300 max-w-3xl mx-auto\">\n            {contactSettings.subtext}\n          </p>\n        </motion.div>\n\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: ANIMATION_CONFIG.duration.normal, delay: ANIMATION_CONFIG.duration.fast }}\n          viewport={{ once: true }}\n          className=\"max-w-2xl mx-auto\"\n        >\n          <ContactForm />\n\n          {/* Alternative Contact Method */}\n          <div className=\"mt-8 text-center\">\n            <p className=\"text-gray-600 dark:text-gray-300 mb-4\">\n              Prefer to chat? Get instant answers to your questions.\n            </p>\n            <ChatTriggers.ContactChat />\n          </div>\n\n\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default Contact;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AACA;AACA;;;AAPA;;;;;;AASA,MAAM,UAAU;;IACd,MAAM,kBAAkB,CAAA,GAAA,kIAAA,CAAA,qBAAkB,AAAD,EAAE;IAE3C,qBACE,6LAAC;QAAQ,IAAG;QAAU,WAAU;kBAC9B,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU,wHAAA,CAAA,mBAAgB,CAAC,QAAQ,CAAC,MAAM;oBAAC;oBACzD,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;sCACX,gBAAgB,QAAQ;;;;;;sCAE3B,6LAAC;4BAAE,WAAU;sCACV,gBAAgB,OAAO;;;;;;;;;;;;8BAI5B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU,wHAAA,CAAA,mBAAgB,CAAC,QAAQ,CAAC,MAAM;wBAAE,OAAO,wHAAA,CAAA,mBAAgB,CAAC,QAAQ,CAAC,IAAI;oBAAC;oBAChG,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC,gJAAA,CAAA,UAAW;;;;;sCAGZ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAwC;;;;;;8CAGrD,6LAAC,oIAAA,CAAA,eAAY,CAAC,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrC;GA3CM;;QACoB,kIAAA,CAAA,qBAAkB;;;KADtC;uCA6CS", "debugId": null}}, {"offset": {"line": 5375, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/layout/FooterNav.tsx"], "sourcesContent": ["/**\n * Footer Navigation Component\n * \n * Handles the navigation links in the footer section.\n * Organized into logical groups with proper accessibility.\n */\n\n'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { useAnalytics } from '@/hooks';\n\ninterface FooterNavProps {\n  className?: string;\n}\n\ninterface NavSection {\n  title: string;\n  links: Array<{\n    label: string;\n    href: string;\n    external?: boolean;\n  }>;\n}\n\nconst navSections: NavSection[] = [\n  {\n    title: 'Quick Links',\n    links: [\n      { label: 'Interactive Demo', href: '/#demo' },\n      { label: 'Our Services', href: '/#services' },\n      { label: 'How It Works', href: '/#process' },\n      { label: 'About Us', href: '/about' },\n      { label: 'Contact', href: '/#contact' }\n    ]\n  },\n  {\n    title: 'Services',\n    links: [\n      { label: 'Website to App', href: '/services#website-conversion' },\n      { label: 'Custom Mobile Apps', href: '/services#custom-apps' },\n      { label: 'App Maintenance', href: '/services#maintenance' },\n      { label: 'Consultation', href: '/services#consultation' }\n    ]\n  },\n  {\n    title: 'Resources',\n    links: [\n      { label: 'Blog', href: '/blog' },\n      { label: 'FAQ', href: '/faq' },\n      { label: 'Case Studies', href: '/blog?category=case-studies' },\n      { label: 'Pricing', href: '/services#pricing' }\n    ]\n  },\n  {\n    title: 'Legal',\n    links: [\n      { label: 'Privacy Policy', href: '/privacy' },\n      { label: 'Terms of Service', href: '/terms' },\n      { label: 'Cookie Policy', href: '/cookies' }\n    ]\n  }\n];\n\nconst FooterNav: React.FC<FooterNavProps> = ({ className = '' }) => {\n  const { trackNavigation } = useAnalytics();\n\n  const handleLinkClick = (label: string) => {\n    trackNavigation(label, 'footer_nav');\n  };\n\n  return (\n    <div className={`grid grid-cols-2 md:grid-cols-4 gap-8 ${className}`}>\n      {navSections.map((section) => (\n        <div key={section.title}>\n          <h3 className=\"text-lg font-semibold mb-4 text-white\">\n            {section.title}\n          </h3>\n          <ul className=\"space-y-2 text-sm\">\n            {section.links.map((link) => (\n              <li key={link.href}>\n                {link.external ? (\n                  <a\n                    href={link.href}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    onClick={() => handleLinkClick(link.label)}\n                    className=\"text-gray-400 dark:text-gray-300 hover:text-electric-blue transition-colors duration-200\"\n                  >\n                    {link.label}\n                  </a>\n                ) : (\n                  <Link\n                    href={link.href}\n                    onClick={() => handleLinkClick(link.label)}\n                    className=\"text-gray-400 dark:text-gray-300 hover:text-electric-blue transition-colors duration-200\"\n                  >\n                    {link.label}\n                  </Link>\n                )}\n              </li>\n            ))}\n          </ul>\n        </div>\n      ))}\n    </div>\n  );\n};\n\nexport default FooterNav;\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;AAKD;AACA;AAAA;;;AAJA;;;AAmBA,MAAM,cAA4B;IAChC;QACE,OAAO;QACP,OAAO;YACL;gBAAE,OAAO;gBAAoB,MAAM;YAAS;YAC5C;gBAAE,OAAO;gBAAgB,MAAM;YAAa;YAC5C;gBAAE,OAAO;gBAAgB,MAAM;YAAY;YAC3C;gBAAE,OAAO;gBAAY,MAAM;YAAS;YACpC;gBAAE,OAAO;gBAAW,MAAM;YAAY;SACvC;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,OAAO;gBAAkB,MAAM;YAA+B;YAChE;gBAAE,OAAO;gBAAsB,MAAM;YAAwB;YAC7D;gBAAE,OAAO;gBAAmB,MAAM;YAAwB;YAC1D;gBAAE,OAAO;gBAAgB,MAAM;YAAyB;SACzD;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,OAAO;gBAAQ,MAAM;YAAQ;YAC/B;gBAAE,OAAO;gBAAO,MAAM;YAAO;YAC7B;gBAAE,OAAO;gBAAgB,MAAM;YAA8B;YAC7D;gBAAE,OAAO;gBAAW,MAAM;YAAoB;SAC/C;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,OAAO;gBAAkB,MAAM;YAAW;YAC5C;gBAAE,OAAO;gBAAoB,MAAM;YAAS;YAC5C;gBAAE,OAAO;gBAAiB,MAAM;YAAW;SAC5C;IACH;CACD;AAED,MAAM,YAAsC,CAAC,EAAE,YAAY,EAAE,EAAE;;IAC7D,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD;IAEvC,MAAM,kBAAkB,CAAC;QACvB,gBAAgB,OAAO;IACzB;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,sCAAsC,EAAE,WAAW;kBACjE,YAAY,GAAG,CAAC,CAAC,wBAChB,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCACX,QAAQ,KAAK;;;;;;kCAEhB,6LAAC;wBAAG,WAAU;kCACX,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,qBAClB,6LAAC;0CACE,KAAK,QAAQ,iBACZ,6LAAC;oCACC,MAAM,KAAK,IAAI;oCACf,QAAO;oCACP,KAAI;oCACJ,SAAS,IAAM,gBAAgB,KAAK,KAAK;oCACzC,WAAU;8CAET,KAAK,KAAK;;;;;yDAGb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAM,KAAK,IAAI;oCACf,SAAS,IAAM,gBAAgB,KAAK,KAAK;oCACzC,WAAU;8CAET,KAAK,KAAK;;;;;;+BAjBR,KAAK,IAAI;;;;;;;;;;;eANd,QAAQ,KAAK;;;;;;;;;;AAiC/B;GA3CM;;QACwB,+HAAA,CAAA,eAAY;;;KADpC;uCA6CS", "debugId": null}}, {"offset": {"line": 5560, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/layout/FooterNewsletter.tsx"], "sourcesContent": ["/**\n * Footer Newsletter Component\n * \n * Handles newsletter signup in the footer section.\n * Includes form validation and submission tracking.\n */\n\n'use client';\n\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Mail, CheckCircle, AlertCircle } from 'lucide-react';\nimport { useAnalytics } from '@/hooks';\nimport { ANIMATION_CONFIG } from '@/config/site';\n\ninterface FooterNewsletterProps {\n  className?: string;\n}\n\ninterface NewsletterState {\n  email: string;\n  isSubmitting: boolean;\n  isSubmitted: boolean;\n  error: string | null;\n}\n\nconst FooterNewsletter: React.FC<FooterNewsletterProps> = ({ className = '' }) => {\n  const [state, setState] = useState<NewsletterState>({\n    email: '',\n    isSubmitting: false,\n    isSubmitted: false,\n    error: null\n  });\n\n  const { trackFormSubmission } = useAnalytics();\n\n  const validateEmail = (email: string): boolean => {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!validateEmail(state.email)) {\n      setState(prev => ({ ...prev, error: 'Please enter a valid email address' }));\n      return;\n    }\n\n    setState(prev => ({ ...prev, isSubmitting: true, error: null }));\n\n    try {\n      // Newsletter signup API call would go here\n      // For now, simulate success\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      setState(prev => ({ \n        ...prev, \n        isSubmitting: false, \n        isSubmitted: true,\n        email: ''\n      }));\n\n      trackFormSubmission('newsletter', true, {\n        source: 'footer',\n        email_domain: state.email.split('@')[1]\n      });\n\n      // Reset success state after 3 seconds\n      setTimeout(() => {\n        setState(prev => ({ ...prev, isSubmitted: false }));\n      }, 3000);\n\n    } catch {\n      setState(prev => ({ \n        ...prev, \n        isSubmitting: false, \n        error: 'Something went wrong. Please try again.' \n      }));\n\n      trackFormSubmission('newsletter', false, {\n        source: 'footer',\n        error: 'submission_failed'\n      });\n    }\n  };\n\n  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    setState(prev => ({ \n      ...prev, \n      email: e.target.value,\n      error: null \n    }));\n  };\n\n  return (\n    <div className={`${className}`}>\n      <h3 className=\"text-lg font-semibold mb-4 text-white\">\n        Stay Updated\n      </h3>\n      <p className=\"text-gray-400 dark:text-gray-300 text-sm mb-4\">\n        Get the latest updates on mobile app development trends and Mobilify news.\n      </p>\n\n      <form onSubmit={handleSubmit} className=\"space-y-3\">\n        <div className=\"relative\">\n          <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n            <Mail className=\"h-4 w-4 text-gray-400\" />\n          </div>\n          <input\n            type=\"email\"\n            value={state.email}\n            onChange={handleEmailChange}\n            placeholder=\"Enter your email\"\n            disabled={state.isSubmitting || state.isSubmitted}\n            className=\"\n              w-full pl-10 pr-4 py-2 text-sm\n              bg-gray-800 dark:bg-gray-700 \n              border border-gray-600 dark:border-gray-600\n              rounded-lg text-white placeholder-gray-400\n              focus:ring-2 focus:ring-electric-blue focus:border-electric-blue\n              disabled:opacity-50 disabled:cursor-not-allowed\n              transition-all duration-200\n            \"\n            aria-label=\"Email address for newsletter\"\n          />\n        </div>\n\n        {state.error && (\n          <motion.div\n            initial={{ opacity: 0, y: -10 }}\n            animate={{ opacity: 1, y: 0 }}\n            className=\"flex items-center gap-2 text-red-400 text-xs\"\n          >\n            <AlertCircle className=\"h-3 w-3\" />\n            {state.error}\n          </motion.div>\n        )}\n\n        <motion.button\n          type=\"submit\"\n          disabled={state.isSubmitting || state.isSubmitted || !state.email.trim()}\n          className=\"\n            w-full py-2 px-4 text-sm font-medium rounded-lg\n            bg-electric-blue hover:bg-electric-blue/90\n            text-white transition-all duration-200\n            disabled:opacity-50 disabled:cursor-not-allowed\n            focus:ring-2 focus:ring-electric-blue focus:ring-offset-2 focus:ring-offset-gray-800\n          \"\n          whileHover={{ scale: 1.02 }}\n          whileTap={{ scale: 0.98 }}\n        >\n          {state.isSubmitting ? (\n            <div className=\"flex items-center justify-center gap-2\">\n              <div className=\"w-3 h-3 border border-white border-t-transparent rounded-full animate-spin\" />\n              Subscribing...\n            </div>\n          ) : state.isSubmitted ? (\n            <div className=\"flex items-center justify-center gap-2\">\n              <CheckCircle className=\"h-4 w-4\" />\n              Subscribed!\n            </div>\n          ) : (\n            'Subscribe'\n          )}\n        </motion.button>\n      </form>\n\n      {state.isSubmitted && (\n        <motion.p\n          initial={{ opacity: 0, y: 10 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: ANIMATION_CONFIG.duration.normal }}\n          className=\"text-green-400 text-xs mt-2\"\n        >\n          Thank you for subscribing! Check your email for confirmation.\n        </motion.p>\n      )}\n    </div>\n  );\n};\n\nexport default FooterNewsletter;\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;AAID;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;;;AANA;;;;;;AAmBA,MAAM,mBAAoD,CAAC,EAAE,YAAY,EAAE,EAAE;;IAC3E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;QAClD,OAAO;QACP,cAAc;QACd,aAAa;QACb,OAAO;IACT;IAEA,MAAM,EAAE,mBAAmB,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD;IAE3C,MAAM,gBAAgB,CAAC;QACrB,MAAM,aAAa;QACnB,OAAO,WAAW,IAAI,CAAC;IACzB;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,cAAc,MAAM,KAAK,GAAG;YAC/B,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,OAAO;gBAAqC,CAAC;YAC1E;QACF;QAEA,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,cAAc;gBAAM,OAAO;YAAK,CAAC;QAE9D,IAAI;YACF,2CAA2C;YAC3C,4BAA4B;YAC5B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,cAAc;oBACd,aAAa;oBACb,OAAO;gBACT,CAAC;YAED,oBAAoB,cAAc,MAAM;gBACtC,QAAQ;gBACR,cAAc,MAAM,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;YACzC;YAEA,sCAAsC;YACtC,WAAW;gBACT,SAAS,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,aAAa;oBAAM,CAAC;YACnD,GAAG;QAEL,EAAE,OAAM;YACN,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,cAAc;oBACd,OAAO;gBACT,CAAC;YAED,oBAAoB,cAAc,OAAO;gBACvC,QAAQ;gBACR,OAAO;YACT;QACF;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,SAAS,CAAA,OAAQ,CAAC;gBAChB,GAAG,IAAI;gBACP,OAAO,EAAE,MAAM,CAAC,KAAK;gBACrB,OAAO;YACT,CAAC;IACH;IAEA,qBACE,6LAAC;QAAI,WAAW,GAAG,WAAW;;0BAC5B,6LAAC;gBAAG,WAAU;0BAAwC;;;;;;0BAGtD,6LAAC;gBAAE,WAAU;0BAAgD;;;;;;0BAI7D,6LAAC;gBAAK,UAAU;gBAAc,WAAU;;kCACtC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAElB,6LAAC;gCACC,MAAK;gCACL,OAAO,MAAM,KAAK;gCAClB,UAAU;gCACV,aAAY;gCACZ,UAAU,MAAM,YAAY,IAAI,MAAM,WAAW;gCACjD,WAAU;gCASV,cAAW;;;;;;;;;;;;oBAId,MAAM,KAAK,kBACV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,WAAU;;0CAEV,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;4BACtB,MAAM,KAAK;;;;;;;kCAIhB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,MAAK;wBACL,UAAU,MAAM,YAAY,IAAI,MAAM,WAAW,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI;wBACtE,WAAU;wBAOV,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;kCAEvB,MAAM,YAAY,iBACjB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;gCAA+E;;;;;;mCAG9F,MAAM,WAAW,iBACnB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,8NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;gCAAY;;;;;;mCAIrC;;;;;;;;;;;;YAKL,MAAM,WAAW,kBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gBACP,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU,wHAAA,CAAA,mBAAgB,CAAC,QAAQ,CAAC,MAAM;gBAAC;gBACzD,WAAU;0BACX;;;;;;;;;;;;AAMT;GA1JM;;QAQ4B,+HAAA,CAAA,eAAY;;;KARxC;uCA4JS", "debugId": null}}, {"offset": {"line": 5834, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/layout/Footer.tsx"], "sourcesContent": ["/**\n * Footer Component\n *\n * Main site footer with company information, navigation links, and newsletter signup.\n * Composed of smaller focused components for better maintainability.\n *\n * @component\n * @example\n * ```tsx\n * <Footer />\n * ```\n *\n * Features:\n * - Company branding and description\n * - Organized navigation links in multiple columns\n * - Newsletter signup with validation\n * - Dark mode toggle\n * - Copyright and legal information\n * - Responsive design with mobile-first approach\n *\n * Architecture:\n * - Uses composition with FooterNav and FooterNewsletter components\n * - CMS-driven content with fallbacks from site config\n * - Consistent theming with semantic color tokens\n */\n\n'use client';\n\nimport React from 'react';\nimport Logo from '../Logo';\nimport SimpleDarkModeToggle from '../SimpleDarkModeToggle';\nimport NoSSR from '../NoSSR';\nimport FooterNav from './FooterNav';\nimport FooterNewsletter from './FooterNewsletter';\nimport { SITE_CONFIG } from '@/config/site';\n\n/**\n * Site footer with navigation, newsletter, and company information\n */\nconst Footer = () => {\n  return (\n    <footer className=\"bg-dark-charcoal dark:bg-gray-950 text-white py-12 transition-colors duration-300\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-8 mb-8\">\n          {/* Company Info */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"flex items-center mb-4\">\n              <Logo />\n              <span className=\"ml-2 text-xl font-bold text-white\">{SITE_CONFIG.name}</span>\n            </div>\n            <p className=\"text-gray-400 dark:text-gray-300 text-sm leading-relaxed\">\n              {SITE_CONFIG.description}\n            </p>\n\n            {/* Newsletter Signup */}\n            <div className=\"mt-6\">\n              <FooterNewsletter />\n            </div>\n          </div>\n\n          {/* Navigation Links */}\n          <div className=\"lg:col-span-3\">\n            <FooterNav />\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"border-t border-gray-800 dark:border-gray-700 pt-8\">\n          <div className=\"flex flex-col md:flex-row items-center justify-between\">\n            <p className=\"text-gray-400 dark:text-gray-300 text-sm\">\n              © {new Date().getFullYear()} {SITE_CONFIG.name}. All rights reserved.\n            </p>\n            <div className=\"flex items-center space-x-6 mt-4 md:mt-0\">\n              <NoSSR>\n                <SimpleDarkModeToggle />\n              </NoSSR>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;CAwBC;;;;AAKD;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;AAUA;;CAEC,GACD,MAAM,SAAS;IACb,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6HAAA,CAAA,UAAI;;;;;sDACL,6LAAC;4CAAK,WAAU;sDAAqC,wHAAA,CAAA,cAAW,CAAC,IAAI;;;;;;;;;;;;8CAEvE,6LAAC;oCAAE,WAAU;8CACV,wHAAA,CAAA,cAAW,CAAC,WAAW;;;;;;8CAI1B,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,mJAAA,CAAA,UAAgB;;;;;;;;;;;;;;;;sCAKrB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,4IAAA,CAAA,UAAS;;;;;;;;;;;;;;;;8BAKd,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;;oCAA2C;oCACnD,IAAI,OAAO,WAAW;oCAAG;oCAAE,wHAAA,CAAA,cAAW,CAAC,IAAI;oCAAC;;;;;;;0CAEjD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,8HAAA,CAAA,UAAK;8CACJ,cAAA,6LAAC,6IAAA,CAAA,UAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrC;KA3CM;uCA6CS", "debugId": null}}, {"offset": {"line": 6029, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/SimpleFloatingChat.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { MessageCircle } from 'lucide-react';\n\nconst SimpleFloatingChat: React.FC = () => {\n  const handleChatOpen = () => {\n    // Try to open Crisp chat if available\n    if (typeof window !== 'undefined' && (window as any).$crisp) {\n      (window as any).$crisp.push(['do', 'chat:open']);\n    } else {\n      // Fallback to mailto if Crisp is not available\n      window.location.href = 'mailto:<EMAIL>?subject=Chat%20Request';\n    }\n\n    // Track chat interaction for analytics\n    if (typeof window !== 'undefined' && (window as any).gtag) {\n      (window as any).gtag('event', 'chat_opened', {\n        event_category: 'engagement',\n        event_label: 'floating_chat'\n      });\n    }\n  };\n\n  return (\n    <button\n      onClick={handleChatOpen}\n      className=\"fixed bottom-6 right-6 z-50 bg-electric-blue text-white p-4 rounded-full shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-200 group\"\n      aria-label=\"Open chat\"\n    >\n      <MessageCircle className=\"w-6 h-6\" />\n      \n      {/* Tooltip */}\n      <div className=\"absolute bottom-full right-0 mb-2 px-3 py-1 bg-gray-900 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap\">\n        Chat with us\n        <div className=\"absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900\"></div>\n      </div>\n    </button>\n  );\n};\n\nexport default SimpleFloatingChat;\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKA,MAAM,qBAA+B;IACnC,MAAM,iBAAiB;QACrB,sCAAsC;QACtC,IAAI,aAAkB,eAAe,AAAC,OAAe,MAAM,EAAE;YAC1D,OAAe,MAAM,CAAC,IAAI,CAAC;gBAAC;gBAAM;aAAY;QACjD,OAAO;YACL,+CAA+C;YAC/C,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;QAEA,uCAAuC;QACvC,IAAI,aAAkB,eAAe,AAAC,OAAe,IAAI,EAAE;YACxD,OAAe,IAAI,CAAC,SAAS,eAAe;gBAC3C,gBAAgB;gBAChB,aAAa;YACf;QACF;IACF;IAEA,qBACE,6LAAC;QACC,SAAS;QACT,WAAU;QACV,cAAW;;0BAEX,6LAAC,2NAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;0BAGzB,6LAAC;gBAAI,WAAU;;oBAA4K;kCAEzL,6LAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;AAIvB;KAlCM;uCAoCS", "debugId": null}}]}