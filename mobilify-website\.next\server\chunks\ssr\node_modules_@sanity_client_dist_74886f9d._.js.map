{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "isRecord.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/util/isRecord.ts"], "sourcesContent": ["/** @internal */\nexport function isRecord(value: unknown): value is Record<string, unknown> {\n  return typeof value === 'object' && value !== null && !Array.isArray(value)\n}\n"], "names": [], "mappings": ";;;AACO,SAAS,SAAS,KAAA,EAAkD;IAClE,OAAA,OAAO,SAAU,YAAY,UAAU,QAAQ,CAAC,MAAM,OAAA,CAAQ,KAAK;AAC5E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "file": "stegaClean.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/util/isRecord.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/node_modules/%40vercel/stega/dist/index.mjs", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/stega/stegaClean.ts"], "sourcesContent": ["/** @internal */\nexport function isRecord(value: unknown): value is Record<string, unknown> {\n  return typeof value === 'object' && value !== null && !Array.isArray(value)\n}\n", "var s={0:8203,1:8204,2:8205,3:8290,4:8291,5:8288,6:65279,7:8289,8:119155,9:119156,a:119157,b:119158,c:119159,d:119160,e:119161,f:119162},c={0:8203,1:8204,2:8205,3:65279},u=new Array(4).fill(String.fromCodePoint(c[0])).join(\"\"),m=String.fromCharCode(0);function E(t){let e=JSON.stringify(t);return`${u}${Array.from(e).map(r=>{let n=r.charCodeAt(0);if(n>255)throw new Error(`Only ASCII edit info can be encoded. Error attempting to encode ${e} on character ${r} (${n})`);return Array.from(n.toString(4).padStart(4,\"0\")).map(o=>String.fromCodePoint(c[o])).join(\"\")}).join(\"\")}`}function y(t){let e=JSON.stringify(t);return Array.from(e).map(r=>{let n=r.charCodeAt(0);if(n>255)throw new Error(`Only ASCII edit info can be encoded. Error attempting to encode ${e} on character ${r} (${n})`);return Array.from(n.toString(16).padStart(2,\"0\")).map(o=>String.fromCodePoint(s[o])).join(\"\")}).join(\"\")}function I(t){return!Number.isNaN(Number(t))||/[a-z]/i.test(t)&&!/\\d+(?:[-:\\/]\\d+){2}(?:T\\d+(?:[-:\\/]\\d+){1,2}(\\.\\d+)?Z?)?/.test(t)?!1:Boolean(Date.parse(t))}function T(t){try{new URL(t,t.startsWith(\"/\")?\"https://acme.com\":void 0)}catch{return!1}return!0}function C(t,e,r=\"auto\"){return r===!0||r===\"auto\"&&(I(t)||T(t))?t:`${t}${E(e)}`}var x=Object.fromEntries(Object.entries(c).map(t=>t.reverse())),g=Object.fromEntries(Object.entries(s).map(t=>t.reverse())),S=`${Object.values(s).map(t=>`\\\\u{${t.toString(16)}}`).join(\"\")}`,f=new RegExp(`[${S}]{4,}`,\"gu\");function G(t){let e=t.match(f);if(!!e)return h(e[0],!0)[0]}function $(t){let e=t.match(f);if(!!e)return e.map(r=>h(r)).flat()}function h(t,e=!1){let r=Array.from(t);if(r.length%2===0){if(r.length%4||!t.startsWith(u))return A(r,e)}else throw new Error(\"Encoded data has invalid length\");let n=[];for(let o=r.length*.25;o--;){let p=r.slice(o*4,o*4+4).map(d=>x[d.codePointAt(0)]).join(\"\");n.unshift(String.fromCharCode(parseInt(p,4)))}if(e){n.shift();let o=n.indexOf(m);return o===-1&&(o=n.length),[JSON.parse(n.slice(0,o).join(\"\"))]}return n.join(\"\").split(m).filter(Boolean).map(o=>JSON.parse(o))}function A(t,e){var d;let r=[];for(let i=t.length*.5;i--;){let a=`${g[t[i*2].codePointAt(0)]}${g[t[i*2+1].codePointAt(0)]}`;r.unshift(String.fromCharCode(parseInt(a,16)))}let n=[],o=[r.join(\"\")],p=10;for(;o.length;){let i=o.shift();try{if(n.push(JSON.parse(i)),e)return n}catch(a){if(!p--)throw a;let l=+((d=a.message.match(/\\sposition\\s(\\d+)$/))==null?void 0:d[1]);if(!l)throw a;o.unshift(i.substring(0,l),i.substring(l))}}return n}function _(t){var e;return{cleaned:t.replace(f,\"\"),encoded:((e=t.match(f))==null?void 0:e[0])||\"\"}}function O(t){return t&&JSON.parse(_(JSON.stringify(t)).cleaned)}export{f as VERCEL_STEGA_REGEX,y as legacyStegaEncode,O as vercelStegaClean,C as vercelStegaCombine,G as vercelStegaDecode,$ as vercelStegaDecodeAll,E as vercelStegaEncode,_ as vercelStegaSplit};\n", "import {vercelStegaClean} from '@vercel/stega'\n\n/**\n * Can take a `result` JSON from a `const {result} = client.fetch(query, params, {filterResponse: false})`\n * and remove all stega-encoded data from it.\n * @public\n */\nexport function stegaClean<Result = unknown>(result: Result): Result {\n  return vercelStegaClean<Result>(result)\n}\n\n/**\n * Can take a `result` JSON from a `const {result} = client.fetch(query, params, {filterResponse: false})`\n * and remove all stega-encoded data from it.\n * @alpha\n * @deprecated Use `stegaClean` instead\n */\nexport const vercelStegaCleanAll = stegaClean\n"], "names": ["vercelStegaClean"], "mappings": ";;;;;;AACO,SAAS,SAAS,KAAA,EAAkD;IAClE,OAAA,OAAO,SAAU,YAAY,UAAU,QAAQ,CAAC,MAAM,OAAA,CAAQ,KAAK;AAC5E;ACHG,IAAC,IAAE;IAAC,GAAE;IAAK,GAAE;IAAK,GAAE;IAAK,GAAE;IAAK,GAAE;IAAK,GAAE;IAAK,GAAE;IAAM,GAAE;IAAK,GAAE;IAAO,GAAE;IAAO,GAAE;IAAO,GAAE;IAAO,GAAE;IAAO,GAAE;IAAO,GAAE;IAAO,GAAE;AAAM,GAAE,IAAE;IAAC,GAAE;IAAK,GAAE;IAAK,GAAE;IAAK,GAAE;AAAK,GAAE,IAAE,IAAI,MAAM,CAAC,EAAE,IAAA,CAAK,OAAO,aAAA,CAAc,CAAA,CAAE,CAAC,CAAC,CAAC,EAAE,IAAA,CAAK,EAAE;AAA2B,SAAS,EAAE,CAAA,EAAE;IAAC,IAAI,IAAE,KAAK,SAAA,CAAU,CAAC;IAAE,OAAM,GAAG,CAAC,GAAG,MAAM,IAAA,CAAK,CAAC,EAAE,GAAA,CAAI,CAAA,MAAG;QAAC,IAAI,IAAE,EAAE,UAAA,CAAW,CAAC;QAAE,IAAG,IAAE,IAAI,CAAA,MAAM,IAAI,MAAM,CAAA,gEAAA,EAAmE,CAAC,CAAA,cAAA,EAAiB,CAAC,CAAA,EAAA,EAAK,CAAC,CAAA,CAAA,CAAG;QAAE,OAAO,MAAM,IAAA,CAAK,EAAE,QAAA,CAAS,CAAC,EAAE,QAAA,CAAS,GAAE,GAAG,CAAC,EAAE,GAAA,CAAI,CAAA,IAAG,OAAO,aAAA,CAAc,CAAA,CAAE,CAAC,CAAC,CAAC,EAAE,IAAA,CAAK,EAAE;IAAC,CAAC,EAAE,IAAA,CAAK,EAAE,CAAC,EAAA;AAAE;AAA6T,SAAS,EAAE,CAAA,EAAE;IAAC,OAAM,CAAC,OAAO,KAAA,CAAM,OAAO,CAAC,CAAC,KAAG,SAAS,IAAA,CAAK,CAAC,KAAG,CAAC,2DAA2D,IAAA,CAAK,CAAC,IAAE,CAAA,IAAG,CAAA,CAAQ,KAAK,KAAA,CAAM,CAAC;AAAE;AAAC,SAAS,EAAE,CAAA,EAAE;IAAC,IAAG;QAAC,IAAI,IAAI,GAAE,EAAE,UAAA,CAAW,GAAG,IAAE,qBAAmB,KAAA,CAAM;IAAC,EAAA,OAAM;QAAC,OAAQ,CAAA;IAAA;IAAC,OAAM,CAAA;AAAE;AAAC,SAAS,EAAE,CAAA,EAAE,CAAA,EAAE,IAAE,MAAA,EAAO;IAAC,OAAO,MAAI,CAAA,KAAI,MAAI,UAAA,CAAS,EAAE,CAAC,KAAG,EAAE,CAAC,CAAA,IAAG,IAAE,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,EAAA;AAAE;AAAO,OAAO,WAAA,CAAY,OAAO,OAAA,CAAQ,CAAC,EAAE,GAAA,CAAI,CAAA,IAAG,EAAE,OAAA,CAAO,CAAE,CAAC;AAAI,OAAO,WAAA,CAAY,OAAO,OAAA,CAAQ,CAAC,EAAE,GAAA,CAAI,CAAA,IAAG,EAAE,OAAA,CAAS,CAAA,CAAC;AAAC,IAAC,IAAE,GAAG,OAAO,MAAA,CAAO,CAAC,EAAE,GAAA,CAAI,CAAA,IAAG,CAAA,IAAA,EAAO,EAAE,QAAA,CAAS,EAAE,CAAC,CAAA,CAAA,CAAG,EAAE,IAAA,CAAK,EAAE,CAAC,EAAA,EAAG,IAAE,IAAI,OAAO,CAAA,CAAA,EAAI,CAAC,CAAA,KAAA,CAAA,EAAQ,IAAI;AAAugC,SAAS,EAAE,CAAA,EAAE;IAAC,IAAI;IAAE,OAAM;QAAC,SAAQ,EAAE,OAAA,CAAQ,GAAE,EAAE;QAAE,SAAA,CAAA,CAAU,IAAE,EAAE,KAAA,CAAM,CAAC,CAAA,KAAI,OAAK,KAAA,IAAO,CAAA,CAAE,CAAC,CAAA,KAAI;IAAE;AAAC;AAAC,SAAS,EAAE,CAAA,EAAE;IAAC,OAAO,KAAG,KAAK,KAAA,CAAM,EAAE,KAAK,SAAA,CAAU,CAAC,CAAC,EAAE,OAAO;AAAC;ACO1kF,SAAS,WAA6B,MAAA,EAAwB;IACnE,OAAOA,EAAyB,MAAM;AACxC;AAQO,MAAM,sBAAsB", "ignoreList": [0, 1, 2], "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "file": "resolveEditInfo.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/csm/studioPath.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/csm/draftUtils.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/csm/jsonPath.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/csm/resolveMapping.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/csm/isArray.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/csm/walkMap.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/csm/createEditUrl.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/csm/resolveEditInfo.ts"], "sourcesContent": ["/** @alpha */\nexport type KeyedSegment = {_key: string}\n\n/** @alpha */\nexport type IndexTuple = [number | '', number | '']\n\n/** @alpha */\nexport type PathSegment = string | number | KeyedSegment | IndexTuple\n\n/** @alpha */\nexport type Path = PathSegment[]\n\nconst rePropName =\n  /[^.[\\]]+|\\[(?:(-?\\d+(?:\\.\\d+)?)|([\"'])((?:(?!\\2)[^\\\\]|\\\\.)*?)\\2)\\]|(?=(?:\\.|\\[\\])(?:\\.|\\[\\]|$))/g\n/** @internal */\nexport const reKeySegment = /_key\\s*==\\s*['\"](.*)['\"]/\nconst reIndexTuple = /^\\d*:\\d*$/\n\n/** @internal */\nexport function isIndexSegment(segment: PathSegment): segment is number {\n  return typeof segment === 'number' || (typeof segment === 'string' && /^\\[\\d+\\]$/.test(segment))\n}\n\n/** @internal */\nexport function isKeySegment(segment: PathSegment): segment is KeyedSegment {\n  if (typeof segment === 'string') {\n    return reKeySegment.test(segment.trim())\n  }\n\n  return typeof segment === 'object' && '_key' in segment\n}\n\n/** @internal */\nexport function isIndexTuple(segment: PathSegment): segment is IndexTuple {\n  if (typeof segment === 'string' && reIndexTuple.test(segment)) {\n    return true\n  }\n\n  if (!Array.isArray(segment) || segment.length !== 2) {\n    return false\n  }\n\n  const [from, to] = segment\n  return (typeof from === 'number' || from === '') && (typeof to === 'number' || to === '')\n}\n\n/** @internal */\nexport function get<Result = unknown, Fallback = unknown>(\n  obj: unknown,\n  path: Path | string,\n  defaultVal?: Fallback,\n): Result | typeof defaultVal {\n  const select = typeof path === 'string' ? fromString(path) : path\n  if (!Array.isArray(select)) {\n    throw new Error('Path must be an array or a string')\n  }\n\n  let acc: unknown | undefined = obj\n  for (let i = 0; i < select.length; i++) {\n    const segment = select[i]\n    if (isIndexSegment(segment)) {\n      if (!Array.isArray(acc)) {\n        return defaultVal\n      }\n\n      acc = acc[segment]\n    }\n\n    if (isKeySegment(segment)) {\n      if (!Array.isArray(acc)) {\n        return defaultVal\n      }\n\n      acc = acc.find((item) => item._key === segment._key)\n    }\n\n    if (typeof segment === 'string') {\n      acc =\n        typeof acc === 'object' && acc !== null\n          ? ((acc as Record<string, unknown>)[segment] as Result)\n          : undefined\n    }\n\n    if (typeof acc === 'undefined') {\n      return defaultVal\n    }\n  }\n\n  return acc as Result\n}\n\n/** @alpha */\nexport function toString(path: Path): string {\n  if (!Array.isArray(path)) {\n    throw new Error('Path is not an array')\n  }\n\n  return path.reduce<string>((target, segment, i) => {\n    const segmentType = typeof segment\n    if (segmentType === 'number') {\n      return `${target}[${segment}]`\n    }\n\n    if (segmentType === 'string') {\n      const separator = i === 0 ? '' : '.'\n      return `${target}${separator}${segment}`\n    }\n\n    if (isKeySegment(segment) && segment._key) {\n      return `${target}[_key==\"${segment._key}\"]`\n    }\n\n    if (Array.isArray(segment)) {\n      const [from, to] = segment\n      return `${target}[${from}:${to}]`\n    }\n\n    throw new Error(`Unsupported path segment \\`${JSON.stringify(segment)}\\``)\n  }, '')\n}\n\n/** @alpha */\nexport function fromString(path: string): Path {\n  if (typeof path !== 'string') {\n    throw new Error('Path is not a string')\n  }\n\n  const segments = path.match(rePropName)\n  if (!segments) {\n    throw new Error('Invalid path string')\n  }\n\n  return segments.map(parsePathSegment)\n}\n\nfunction parsePathSegment(segment: string): PathSegment {\n  if (isIndexSegment(segment)) {\n    return parseIndexSegment(segment)\n  }\n\n  if (isKeySegment(segment)) {\n    return parseKeySegment(segment)\n  }\n\n  if (isIndexTuple(segment)) {\n    return parseIndexTupleSegment(segment)\n  }\n\n  return segment\n}\n\nfunction parseIndexSegment(segment: string): PathSegment {\n  return Number(segment.replace(/[^\\d]/g, ''))\n}\n\nfunction parseKeySegment(segment: string): KeyedSegment {\n  const segments = segment.match(reKeySegment)\n  return {_key: segments![1]}\n}\n\nfunction parseIndexTupleSegment(segment: string): IndexTuple {\n  const [from, to] = segment.split(':').map((seg) => (seg === '' ? seg : Number(seg)))\n  return [from, to]\n}\n", "// nominal/opaque type hack\ntype Opaque<T, K> = T & {__opaqueId__: K}\n\n/** @internal */\nexport type DraftId = Opaque<string, 'draftId'>\n\n/** @internal */\nexport type PublishedId = Opaque<string, 'publishedId'>\n\n/** @internal */\nexport const DRAFTS_FOLDER = 'drafts'\n\n/** @internal */\nexport const VERSION_FOLDER = 'versions'\n\nconst PATH_SEPARATOR = '.'\nconst DRAFTS_PREFIX = `${DRAFTS_FOLDER}${PATH_SEPARATOR}`\nconst VERSION_PREFIX = `${VERSION_FOLDER}${PATH_SEPARATOR}`\n\n/** @internal */\nexport function isDraftId(id: string): id is DraftId {\n  return id.startsWith(DRAFTS_PREFIX)\n}\n\n/** @internal */\nexport function isVersionId(id: string): boolean {\n  return id.startsWith(VERSION_PREFIX)\n}\n\n/** @internal */\nexport function isPublishedId(id: string): id is PublishedId {\n  return !isDraftId(id) && !isVersionId(id)\n}\n\n/** @internal */\nexport function getDraftId(id: string): DraftId {\n  if (isVersionId(id)) {\n    const publishedId = getPublishedId(id)\n    return (DRAFTS_PREFIX + publishedId) as DraftId\n  }\n\n  return isDraftId(id) ? id : ((DRAFTS_PREFIX + id) as DraftId)\n}\n\n/**  @internal */\nexport function getVersionId(id: string, version: string): string {\n  if (version === 'drafts' || version === 'published') {\n    throw new Error('Version can not be \"published\" or \"drafts\"')\n  }\n\n  return `${VERSION_PREFIX}${version}${PATH_SEPARATOR}${getPublishedId(id)}`\n}\n\n/**\n *  @internal\n *  Given an id, returns the versionId if it exists.\n *  e.g. `versions.summer-drop.foo` = `summer-drop`\n *  e.g. `drafts.foo` = `undefined`\n *  e.g. `foo` = `undefined`\n */\nexport function getVersionFromId(id: string): string | undefined {\n  if (!isVersionId(id)) return undefined\n  // eslint-disable-next-line unused-imports/no-unused-vars\n  const [_versionPrefix, versionId, ..._publishedId] = id.split(PATH_SEPARATOR)\n\n  return versionId\n}\n\n/** @internal */\nexport function getPublishedId(id: string): PublishedId {\n  if (isVersionId(id)) {\n    // make sure to only remove the versions prefix and the bundle name\n    return id.split(PATH_SEPARATOR).slice(2).join(PATH_SEPARATOR) as PublishedId as PublishedId\n  }\n\n  if (isDraftId(id)) {\n    return id.slice(DRAFTS_PREFIX.length) as PublishedId\n  }\n\n  return id as PublishedId\n}\n", "import * as studioPath from './studioPath'\nimport type {\n  ContentSourceMapParsedPath,\n  ContentSourceMapParsedPathKeyedSegment,\n  ContentSourceMapPaths,\n  Path,\n} from './types'\n\nconst ESCAPE: Record<string, string> = {\n  '\\f': '\\\\f',\n  '\\n': '\\\\n',\n  '\\r': '\\\\r',\n  '\\t': '\\\\t',\n  \"'\": \"\\\\'\",\n  '\\\\': '\\\\\\\\',\n}\n\nconst UNESCAPE: Record<string, string> = {\n  '\\\\f': '\\f',\n  '\\\\n': '\\n',\n  '\\\\r': '\\r',\n  '\\\\t': '\\t',\n  \"\\\\'\": \"'\",\n  '\\\\\\\\': '\\\\',\n}\n\n/**\n * @internal\n */\nexport function jsonPath(path: ContentSourceMapParsedPath): ContentSourceMapPaths[number] {\n  return `$${path\n    .map((segment) => {\n      if (typeof segment === 'string') {\n        const escapedKey = segment.replace(/[\\f\\n\\r\\t'\\\\]/g, (match) => {\n          return ESCAPE[match]\n        })\n        return `['${escapedKey}']`\n      }\n\n      if (typeof segment === 'number') {\n        return `[${segment}]`\n      }\n\n      if (segment._key !== '') {\n        const escapedKey = segment._key.replace(/['\\\\]/g, (match) => {\n          return ESCAPE[match]\n        })\n        return `[?(@._key=='${escapedKey}')]`\n      }\n\n      return `[${segment._index}]`\n    })\n    .join('')}`\n}\n\n/**\n * @internal\n */\nexport function parseJsonPath(path: ContentSourceMapPaths[number]): ContentSourceMapParsedPath {\n  const parsed: ContentSourceMapParsedPath = []\n\n  const parseRe = /\\['(.*?)'\\]|\\[(\\d+)\\]|\\[\\?\\(@\\._key=='(.*?)'\\)\\]/g\n  let match: RegExpExecArray | null\n\n  while ((match = parseRe.exec(path)) !== null) {\n    if (match[1] !== undefined) {\n      const key = match[1].replace(/\\\\(\\\\|f|n|r|t|')/g, (m) => {\n        return UNESCAPE[m]\n      })\n\n      parsed.push(key)\n      continue\n    }\n\n    if (match[2] !== undefined) {\n      parsed.push(parseInt(match[2], 10))\n      continue\n    }\n\n    if (match[3] !== undefined) {\n      const _key = match[3].replace(/\\\\(\\\\')/g, (m) => {\n        return UNESCAPE[m]\n      })\n\n      parsed.push({\n        _key,\n        _index: -1,\n      })\n      continue\n    }\n  }\n\n  return parsed\n}\n\n/**\n * @internal\n */\nexport function jsonPathToStudioPath(path: ContentSourceMapParsedPath): Path {\n  return path.map((segment) => {\n    if (typeof segment === 'string') {\n      return segment\n    }\n\n    if (typeof segment === 'number') {\n      return segment\n    }\n\n    if (segment._key !== '') {\n      return {_key: segment._key}\n    }\n\n    if (segment._index !== -1) {\n      return segment._index\n    }\n\n    throw new Error(`invalid segment:${JSON.stringify(segment)}`)\n  })\n}\n\n/**\n * @internal\n */\nexport function studioPathToJsonPath(path: Path | string): ContentSourceMapParsedPath {\n  const parsedPath = typeof path === 'string' ? studioPath.fromString(path) : path\n\n  return parsedPath.map((segment) => {\n    if (typeof segment === 'string') {\n      return segment\n    }\n\n    if (typeof segment === 'number') {\n      return segment\n    }\n\n    if (Array.isArray(segment)) {\n      throw new Error(`IndexTuple segments aren't supported:${JSON.stringify(segment)}`)\n    }\n\n    if (isContentSourceMapParsedPathKeyedSegment(segment)) {\n      return segment\n    }\n\n    if (segment._key) {\n      return {_key: segment._key, _index: -1}\n    }\n\n    throw new Error(`invalid segment:${JSON.stringify(segment)}`)\n  })\n}\n\nfunction isContentSourceMapParsedPathKeyedSegment(\n  segment: studioPath.PathSegment | ContentSourceMapParsedPath[number],\n): segment is ContentSourceMapParsedPathKeyedSegment {\n  return typeof segment === 'object' && '_key' in segment && '_index' in segment\n}\n\n/**\n * @internal\n */\nexport function jsonPathToMappingPath(path: ContentSourceMapParsedPath): (string | number)[] {\n  return path.map((segment) => {\n    if (typeof segment === 'string') {\n      return segment\n    }\n\n    if (typeof segment === 'number') {\n      return segment\n    }\n\n    if (segment._index !== -1) {\n      return segment._index\n    }\n\n    throw new Error(`invalid segment:${JSON.stringify(segment)}`)\n  })\n}\n", "import {jsonPath, jsonPathToMappingPath} from './jsonPath'\nimport type {ContentSourceMap, ContentSourceMapMapping, ContentSourceMapParsedPath} from './types'\n\n/**\n * @internal\n */\nexport function resolveMapping(\n  resultPath: ContentSourceMapParsedPath,\n  csm?: ContentSourceMap,\n):\n  | {\n      mapping: ContentSourceMapMapping\n      matchedPath: string\n      pathSuffix: string\n    }\n  | undefined {\n  if (!csm?.mappings) {\n    return undefined\n  }\n  const resultMappingPath = jsonPath(jsonPathToMappingPath(resultPath))\n\n  if (csm.mappings[resultMappingPath] !== undefined) {\n    return {\n      mapping: csm.mappings[resultMappingPath],\n      matchedPath: resultMappingPath,\n      pathSuffix: '',\n    }\n  }\n\n  const mappings = Object.entries(csm.mappings)\n    .filter(([key]) => resultMappingPath.startsWith(key))\n    .sort(([key1], [key2]) => key2.length - key1.length)\n\n  if (mappings.length == 0) {\n    return undefined\n  }\n\n  const [matchedPath, mapping] = mappings[0]\n  const pathSuffix = resultMappingPath.substring(matchedPath.length)\n  return {mapping, matchedPath, pathSuffix}\n}\n", "/** @internal */\nexport function isArray(value: unknown): value is Array<unknown> {\n  return value !== null && Array.isArray(value)\n}\n", "import {isRecord} from '../util/isRecord'\nimport {isArray} from './isArray'\nimport type {ContentSourceMapParsedPath, WalkMapFn} from './types'\n\n/**\n * generic way to walk a nested object or array and apply a mapping function to each value\n * @internal\n */\nexport function walkMap(\n  value: unknown,\n  mappingFn: WalkMapFn,\n  path: ContentSourceMapParsedPath = [],\n): unknown {\n  if (isArray(value)) {\n    return value.map((v, idx) => {\n      if (isRecord(v)) {\n        const _key = v['_key']\n        if (typeof _key === 'string') {\n          return walkMap(v, mappingFn, path.concat({_key, _index: idx}))\n        }\n      }\n\n      return walkMap(v, mappingFn, path.concat(idx))\n    })\n  }\n\n  if (isRecord(value)) {\n    // Handle Portable Text in a faster way\n    if (value._type === 'block' || value._type === 'span') {\n      const result = {...value}\n      if (value._type === 'block') {\n        result.children = walkMap(value.children, mappingFn, path.concat('children'))\n      } else if (value._type === 'span') {\n        result.text = walkMap(value.text, mappingFn, path.concat('text'))\n      }\n      return result\n    }\n\n    return Object.fromEntries(\n      Object.entries(value).map(([k, v]) => [k, walkMap(v, mappingFn, path.concat(k))]),\n    )\n  }\n\n  return mappingFn(value, path)\n}\n", "import {getPublishedId, getVersionFromId, isPublishedId, isVersionId} from './draftUtils'\nimport {jsonPathToStudioPath} from './jsonPath'\nimport * as studioPath from './studioPath'\nimport type {CreateEditUrlOptions, EditIntentUrl, StudioBaseUrl} from './types'\n\n/** @internal */\nexport function createEditUrl(options: CreateEditUrlOptions): `${StudioBaseUrl}${EditIntentUrl}` {\n  const {\n    baseUrl,\n    workspace: _workspace = 'default',\n    tool: _tool = 'default',\n    id: _id,\n    type,\n    path,\n    projectId,\n    dataset,\n  } = options\n\n  if (!baseUrl) {\n    throw new Error('baseUrl is required')\n  }\n  if (!path) {\n    throw new Error('path is required')\n  }\n  if (!_id) {\n    throw new Error('id is required')\n  }\n  if (baseUrl !== '/' && baseUrl.endsWith('/')) {\n    throw new Error('baseUrl must not end with a slash')\n  }\n\n  const workspace = _workspace === 'default' ? undefined : _workspace\n  const tool = _tool === 'default' ? undefined : _tool\n  const id = getPublishedId(_id)\n  const stringifiedPath = Array.isArray(path)\n    ? studioPath.toString(jsonPathToStudioPath(path))\n    : path\n\n  // eslint-disable-next-line no-warning-comments\n  // @TODO Using searchParams as a temporary workaround until `@sanity/overlays` can decode state from the path reliably\n  const searchParams = new URLSearchParams({\n    baseUrl,\n    id,\n    type,\n    path: stringifiedPath,\n  })\n  if (workspace) {\n    searchParams.set('workspace', workspace)\n  }\n  if (tool) {\n    searchParams.set('tool', tool)\n  }\n  if (projectId) {\n    searchParams.set('projectId', projectId)\n  }\n  if (dataset) {\n    searchParams.set('dataset', dataset)\n  }\n  if (isPublishedId(_id)) {\n    searchParams.set('perspective', 'published')\n  } else if (isVersionId(_id)) {\n    const versionId = getVersionFromId(_id)!\n    searchParams.set('perspective', versionId)\n  }\n\n  const segments = [baseUrl === '/' ? '' : baseUrl]\n  if (workspace) {\n    segments.push(workspace)\n  }\n  const routerParams = [\n    'mode=presentation',\n    `id=${id}`,\n    `type=${type}`,\n    `path=${encodeURIComponent(stringifiedPath)}`,\n  ]\n  if (tool) {\n    routerParams.push(`tool=${tool}`)\n  }\n  segments.push('intent', 'edit', `${routerParams.join(';')}?${searchParams}`)\n  return segments.join('/') as unknown as `${StudioBaseUrl}${EditIntentUrl}`\n}\n", "import {parseJsonPath} from './jsonPath'\nimport {resolveMapping} from './resolveMapping'\nimport type {\n  CreateEditUrlOptions,\n  ResolveEditInfoOptions,\n  StudioBaseRoute,\n  StudioBaseUrl,\n  StudioUrl,\n} from './types'\n\n/** @internal */\nexport function resolveEditInfo(options: ResolveEditInfoOptions): CreateEditUrlOptions | undefined {\n  const {resultSourceMap: csm, resultPath} = options\n  const {mapping, pathSuffix} = resolveMapping(resultPath, csm) || {}\n\n  if (!mapping) {\n    // console.warn('no mapping for path', { path: resultPath, sourceMap: csm })\n    return undefined\n  }\n\n  if (mapping.source.type === 'literal') {\n    return undefined\n  }\n\n  if (mapping.source.type === 'unknown') {\n    return undefined\n  }\n\n  const sourceDoc = csm.documents[mapping.source.document]\n  const sourcePath = csm.paths[mapping.source.path]\n\n  if (sourceDoc && sourcePath) {\n    const {baseUrl, workspace, tool} = resolveStudioBaseRoute(\n      typeof options.studioUrl === 'function' ? options.studioUrl(sourceDoc) : options.studioUrl,\n    )\n    if (!baseUrl) return undefined\n    const {_id, _type, _projectId, _dataset} = sourceDoc\n    return {\n      baseUrl,\n      workspace,\n      tool,\n      id: _id,\n      type: _type,\n      path: parseJsonPath(sourcePath + pathSuffix),\n      projectId: _projectId,\n      dataset: _dataset,\n    } satisfies CreateEditUrlOptions\n  }\n\n  return undefined\n}\n\n/** @internal */\nexport function resolveStudioBaseRoute(studioUrl: StudioUrl): StudioBaseRoute {\n  let baseUrl: StudioBaseUrl = typeof studioUrl === 'string' ? studioUrl : studioUrl.baseUrl\n  if (baseUrl !== '/') {\n    baseUrl = baseUrl.replace(/\\/$/, '')\n  }\n  if (typeof studioUrl === 'string') {\n    return {baseUrl}\n  }\n  return {...studioUrl, baseUrl}\n}\n"], "names": ["studioPath.fromString", "studioPath.toString"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAYA,MAAM,aACJ,oGAEW,eAAe,4BACtB,eAAe;AAGd,SAAS,eAAe,OAAA,EAAyC;IAC/D,OAAA,OAAO,WAAY,YAAa,OAAO,WAAY,YAAY,YAAY,IAAA,CAAK,OAAO;AAChG;AAGO,SAAS,aAAa,OAAA,EAA+C;IAC1E,OAAI,OAAO,WAAY,WACd,aAAa,IAAA,CAAK,QAAQ,IAAA,CAAK,CAAC,IAGlC,OAAO,WAAY,YAAY,UAAU;AAClD;AAGO,SAAS,aAAa,OAAA,EAA6C;IACxE,IAAI,OAAO,WAAY,YAAY,aAAa,IAAA,CAAK,OAAO,GACnD,OAAA,CAAA;IAGT,IAAI,CAAC,MAAM,OAAA,CAAQ,OAAO,KAAK,QAAQ,MAAA,KAAW,GACzC,OAAA,CAAA;IAGH,MAAA,CAAC,MAAM,EAAE,CAAA,GAAI;IACX,OAAA,CAAA,OAAO,QAAS,YAAY,SAAS,EAAA,KAAA,CAAQ,OAAO,MAAO,YAAY,OAAO,EAAA;AACxF;AAGgB,SAAA,IACd,GAAA,EACA,IAAA,EACA,UAAA,EAC4B;IAC5B,MAAM,SAAS,OAAO,QAAS,WAAW,WAAW,IAAI,IAAI;IACzD,IAAA,CAAC,MAAM,OAAA,CAAQ,MAAM,GACjB,MAAA,IAAI,MAAM,mCAAmC;IAGrD,IAAI,MAA2B;IAC/B,IAAA,IAAS,IAAI,GAAG,IAAI,OAAO,MAAA,EAAQ,IAAK;QAChC,MAAA,UAAU,MAAA,CAAO,CAAC,CAAA;QACpB,IAAA,eAAe,OAAO,GAAG;YACvB,IAAA,CAAC,MAAM,OAAA,CAAQ,GAAG,GACb,OAAA;YAGT,MAAM,GAAA,CAAI,OAAO,CAAA;QAAA;QAGf,IAAA,aAAa,OAAO,GAAG;YACrB,IAAA,CAAC,MAAM,OAAA,CAAQ,GAAG,GACb,OAAA;YAGT,MAAM,IAAI,IAAA,CAAK,CAAC,OAAS,KAAK,IAAA,KAAS,QAAQ,IAAI;QAAA;QAUrD,IAPI,OAAO,WAAY,YAAA,CACrB,MACE,OAAO,OAAQ,YAAY,QAAQ,OAC7B,GAAA,CAAgC,OAAO,CAAA,GACzC,KAAA,CAAA,GAGJ,OAAO,MAAQ,KACV,OAAA;IAAA;IAIJ,OAAA;AACT;AAGO,SAAS,SAAS,IAAA,EAAoB;IACvC,IAAA,CAAC,MAAM,OAAA,CAAQ,IAAI,GACf,MAAA,IAAI,MAAM,sBAAsB;IAGxC,OAAO,KAAK,MAAA,CAAe,CAAC,QAAQ,SAAS,MAAM;QACjD,MAAM,cAAc,OAAO;QAC3B,IAAI,gBAAgB,UACX,OAAA,GAAG,MAAM,CAAA,CAAA,EAAI,OAAO,CAAA,CAAA,CAAA;QAG7B,IAAI,gBAAgB,UAEX,OAAA,GAAG,MAAM,GADE,MAAM,IAAI,KAAK,GACL,GAAG,OAAO,EAAA;QAGpC,IAAA,aAAa,OAAO,KAAK,QAAQ,IAAA,EACnC,OAAO,GAAG,MAAM,CAAA,QAAA,EAAW,QAAQ,IAAI,CAAA,EAAA,CAAA;QAGrC,IAAA,MAAM,OAAA,CAAQ,OAAO,GAAG;YACpB,MAAA,CAAC,MAAM,EAAE,CAAA,GAAI;YACnB,OAAO,GAAG,MAAM,CAAA,CAAA,EAAI,IAAI,CAAA,CAAA,EAAI,EAAE,CAAA,CAAA,CAAA;QAAA;QAGhC,MAAM,IAAI,MAAM,CAAA,2BAAA,EAA8B,KAAK,SAAA,CAAU,OAAO,CAAC,CAAA,EAAA,CAAI;IAAA,GACxE,EAAE;AACP;AAGO,SAAS,WAAW,IAAA,EAAoB;IAC7C,IAAI,OAAO,QAAS,UACZ,MAAA,IAAI,MAAM,sBAAsB;IAGlC,MAAA,WAAW,KAAK,KAAA,CAAM,UAAU;IACtC,IAAI,CAAC,UACG,MAAA,IAAI,MAAM,qBAAqB;IAGhC,OAAA,SAAS,GAAA,CAAI,gBAAgB;AACtC;AAEA,SAAS,iBAAiB,OAAA,EAA8B;IACtD,OAAI,eAAe,OAAO,IACjB,kBAAkB,OAAO,IAG9B,aAAa,OAAO,IACf,gBAAgB,OAAO,IAG5B,aAAa,OAAO,IACf,uBAAuB,OAAO,IAGhC;AACT;AAEA,SAAS,kBAAkB,OAAA,EAA8B;IACvD,OAAO,OAAO,QAAQ,OAAA,CAAQ,UAAU,EAAE,CAAC;AAC7C;AAEA,SAAS,gBAAgB,OAAA,EAA+B;IAEtD,OAAO;QAAC,MADS,QAAQ,KAAA,CAAM,YAAY,CAAA,CACnB,CAAC,CAAA;IAAC;AAC5B;AAEA,SAAS,uBAAuB,OAAA,EAA6B;IAC3D,MAAM,CAAC,MAAM,EAAE,CAAA,GAAI,QAAQ,KAAA,CAAM,GAAG,EAAE,GAAA,CAAI,CAAC,MAAS,QAAQ,KAAK,MAAM,OAAO,GAAG,CAAE;IAC5E,OAAA;QAAC;QAAM,EAAE;KAAA;AAClB;;;;;;;;;;;ACzJa,MAAA,gBAAgB,UAGhB,iBAAiB,YAExB,iBAAiB,KACjB,gBAAgB,GAAG,aAAa,GAAG,cAAc,EAAA,EACjD,iBAAiB,GAAG,cAAc,GAAG,cAAc,EAAA;AAGlD,SAAS,UAAU,EAAA,EAA2B;IAC5C,OAAA,GAAG,UAAA,CAAW,aAAa;AACpC;AAGO,SAAS,YAAY,EAAA,EAAqB;IACxC,OAAA,GAAG,UAAA,CAAW,cAAc;AACrC;AAGO,SAAS,cAAc,EAAA,EAA+B;IAC3D,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,YAAY,EAAE;AAC1C;AAGO,SAAS,WAAW,EAAA,EAAqB;IAC1C,IAAA,YAAY,EAAE,GAAG;QACb,MAAA,cAAc,eAAe,EAAE;QACrC,OAAQ,gBAAgB;IAAA;IAG1B,OAAO,UAAU,EAAE,IAAI,KAAO,gBAAgB;AAChD;AAGgB,SAAA,aAAa,EAAA,EAAY,OAAA,EAAyB;IAC5D,IAAA,YAAY,YAAY,YAAY,aAChC,MAAA,IAAI,MAAM,4CAA4C;IAGvD,OAAA,GAAG,cAAc,GAAG,OAAO,GAAG,cAAc,GAAG,eAAe,EAAE,CAAC,EAAA;AAC1E;AASO,SAAS,iBAAiB,EAAA,EAAgC;IAC3D,IAAA,CAAC,YAAY,EAAE,EAAG,CAAA;IAEhB,MAAA,CAAC,gBAAgB,WAAW,GAAG,YAAY,CAAA,GAAI,GAAG,KAAA,CAAM,cAAc;IAErE,OAAA;AACT;AAGO,SAAS,eAAe,EAAA,EAAyB;IAClD,OAAA,YAAY,EAAE,IAET,GAAG,KAAA,CAAM,cAAc,EAAE,KAAA,CAAM,CAAC,EAAE,IAAA,CAAK,cAAc,IAG1D,UAAU,EAAE,IACP,GAAG,KAAA,CAAM,cAAc,MAAM,IAG/B;AACT;ACxEA,MAAM,SAAiC;IACrC,MAAM;IACN,MAAM;IACN,MAAM;IACN,KAAM;IACN,KAAK;IACL,MAAM;AACR,GAEM,WAAmC;IACvC,OAAO;IACP,OAAO,CAAA;AAAA,CAAA;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,QAAQ;AACV;AAKO,SAAS,SAAS,IAAA,EAAiE;IACjF,OAAA,CAAA,CAAA,EAAI,KACR,GAAA,CAAI,CAAC,UACA,OAAO,WAAY,WAId,CAAA,EAAA,EAHY,QAAQ,OAAA,CAAQ,kBAAkB,CAAC,QAC7C,MAAA,CAAO,KAAK,CACpB,CACqB,CAAA,EAAA,CAAA,GAGpB,OAAO,WAAY,WACd,CAAA,CAAA,EAAI,OAAO,CAAA,CAAA,CAAA,GAGhB,QAAQ,IAAA,KAAS,KAIZ,CAAA,YAAA,EAHY,QAAQ,IAAA,CAAK,OAAA,CAAQ,UAAU,CAAC,QAC1C,MAAA,CAAO,KAAK,CACpB,CAC+B,CAAA,GAAA,CAAA,GAG3B,CAAA,CAAA,EAAI,QAAQ,MAAM,CAAA,CAAA,CAC1B,EACA,IAAA,CAAK,EAAE,CAAC,EAAA;AACb;AAKO,SAAS,cAAc,IAAA,EAAiE;IACvF,MAAA,SAAqC,EAAA,EAErC,UAAU;IACZ,IAAA;IAEJ,MAAA,CAAQ,QAAQ,QAAQ,IAAA,CAAK,IAAI,CAAA,MAAO,MAAM;QACxC,IAAA,KAAA,CAAM,CAAC,CAAA,KAAM,KAAA,GAAW;YACpB,MAAA,MAAM,KAAA,CAAM,CAAC,CAAA,CAAE,OAAA,CAAQ,qBAAqB,CAAC,IAC1C,QAAA,CAAS,CAAC,CAClB;YAED,OAAO,IAAA,CAAK,GAAG;YACf;QAAA;QAGE,IAAA,KAAA,CAAM,CAAC,CAAA,KAAM,KAAA,GAAW;YAC1B,OAAO,IAAA,CAAK,SAAS,KAAA,CAAM,CAAC,CAAA,EAAG,EAAE,CAAC;YAClC;QAAA;QAGE,IAAA,KAAA,CAAM,CAAC,CAAA,KAAM,KAAA,GAAW;YACpB,MAAA,OAAO,KAAA,CAAM,CAAC,CAAA,CAAE,OAAA,CAAQ,YAAY,CAAC,IAClC,QAAA,CAAS,CAAC,CAClB;YAED,OAAO,IAAA,CAAK;gBACV;gBACA,QAAQ,CAAA;YAAA,CACT;YACD;QAAA;IACF;IAGK,OAAA;AACT;AAKO,SAAS,qBAAqB,IAAA,EAAwC;IACpE,OAAA,KAAK,GAAA,CAAI,CAAC,YAAY;QAK3B,IAJI,OAAO,WAAY,YAInB,OAAO,WAAY,UACd,OAAA;QAGT,IAAI,QAAQ,IAAA,KAAS,IACZ,OAAA;YAAC,MAAM,QAAQ,IAAA;QAAI;QAG5B,IAAI,QAAQ,MAAA,KAAW,CAAA,GACrB,OAAO,QAAQ,MAAA;QAGjB,MAAM,IAAI,MAAM,CAAA,gBAAA,EAAmB,KAAK,SAAA,CAAU,OAAO,CAAC,EAAE;IAAA,CAC7D;AACH;AAKO,SAAS,qBAAqB,IAAA,EAAiD;IACjE,OAAA,CAAA,OAAO,QAAS,WAAWA,WAAsB,IAAI,IAAI,IAAA,EAE1D,GAAA,CAAI,CAAC,YAAY;QAKjC,IAJI,OAAO,WAAY,YAInB,OAAO,WAAY,UACd,OAAA;QAGL,IAAA,MAAM,OAAA,CAAQ,OAAO,GACvB,MAAM,IAAI,MAAM,CAAA,qCAAA,EAAwC,KAAK,SAAA,CAAU,OAAO,CAAC,EAAE;QAGnF,IAAI,yCAAyC,OAAO,GAC3C,OAAA;QAGT,IAAI,QAAQ,IAAA,EACV,OAAO;YAAC,MAAM,QAAQ,IAAA;YAAM,QAAQ,CAAA;QAAE;QAGxC,MAAM,IAAI,MAAM,CAAA,gBAAA,EAAmB,KAAK,SAAA,CAAU,OAAO,CAAC,EAAE;IAAA,CAC7D;AACH;AAEA,SAAS,yCACP,OAAA,EACmD;IACnD,OAAO,OAAO,WAAY,YAAY,UAAU,WAAW,YAAY;AACzE;AAKO,SAAS,sBAAsB,IAAA,EAAuD;IACpF,OAAA,KAAK,GAAA,CAAI,CAAC,YAAY;QAK3B,IAJI,OAAO,WAAY,YAInB,OAAO,WAAY,UACd,OAAA;QAGT,IAAI,QAAQ,MAAA,KAAW,CAAA,GACrB,OAAO,QAAQ,MAAA;QAGjB,MAAM,IAAI,MAAM,CAAA,gBAAA,EAAmB,KAAK,SAAA,CAAU,OAAO,CAAC,EAAE;IAAA,CAC7D;AACH;AC1KgB,SAAA,eACd,UAAA,EACA,GAAA,EAOY;IACZ,IAAI,CAAC,KAAK,UACR;IAEF,MAAM,oBAAoB,SAAS,sBAAsB,UAAU,CAAC;IAEhE,IAAA,IAAI,QAAA,CAAS,iBAAiB,CAAA,KAAM,KAAA,GAC/B,OAAA;QACL,SAAS,IAAI,QAAA,CAAS,iBAAiB,CAAA;QACvC,aAAa;QACb,YAAY;IACd;IAGI,MAAA,WAAW,OAAO,OAAA,CAAQ,IAAI,QAAQ,EACzC,MAAA,CAAO,CAAC,CAAC,GAAG,CAAA,GAAM,kBAAkB,UAAA,CAAW,GAAG,CAAC,EACnD,IAAA,CAAK,CAAC,CAAC,IAAI,CAAA,EAAG,CAAC,IAAI,CAAA,GAAM,KAAK,MAAA,GAAS,KAAK,MAAM;IAErD,IAAI,SAAS,MAAA,IAAU,GACrB;IAGI,MAAA,CAAC,aAAa,OAAO,CAAA,GAAI,QAAA,CAAS,CAAC,CAAA,EACnC,aAAa,kBAAkB,SAAA,CAAU,YAAY,MAAM;IAC1D,OAAA;QAAC;QAAS;QAAa;IAAU;AAC1C;ACvCO,SAAS,QAAQ,KAAA,EAAyC;IAC/D,OAAO,UAAU,QAAQ,MAAM,OAAA,CAAQ,KAAK;AAC9C;ACKO,SAAS,QACd,KAAA,EACA,SAAA,EACA,OAAmC,CAAA,CAAA,EAC1B;IACT,IAAI,QAAQ,KAAK,GACf,OAAO,MAAM,GAAA,CAAI,CAAC,GAAG,QAAQ;QACvB,QAAA,mLAAA,EAAS,CAAC,GAAG;YACf,MAAM,OAAO,EAAE,IAAA;YACf,IAAI,OAAO,QAAS,UACX,OAAA,QAAQ,GAAG,WAAW,KAAK,MAAA,CAAO;gBAAC;gBAAM,QAAQ;YAAG,CAAC,CAAC;QAAA;QAIjE,OAAO,QAAQ,GAAG,WAAW,KAAK,MAAA,CAAO,GAAG,CAAC;IAAA,CAC9C;IAGC,gLAAA,WAAA,EAAS,KAAK,GAAG;QAEnB,IAAI,MAAM,KAAA,KAAU,WAAW,MAAM,KAAA,KAAU,QAAQ;YAC/C,MAAA,SAAS;gBAAC,GAAG,KAAA;YAAK;YACpB,OAAA,MAAM,KAAA,KAAU,UAClB,OAAO,QAAA,GAAW,QAAQ,MAAM,QAAA,EAAU,WAAW,KAAK,MAAA,CAAO,UAAU,CAAC,IACnE,MAAM,KAAA,KAAU,UAAA,CACzB,OAAO,IAAA,GAAO,QAAQ,MAAM,IAAA,EAAM,WAAW,KAAK,MAAA,CAAO,MAAM,CAAC,CAAA,GAE3D;QAAA;QAGT,OAAO,OAAO,WAAA,CACZ,OAAO,OAAA,CAAQ,KAAK,EAAE,GAAA,CAAI,CAAC,CAAC,GAAG,CAAC,CAAA,GAAM;gBAAC;gBAAG,QAAQ,GAAG,WAAW,KAAK,MAAA,CAAO,CAAC,CAAC,CAAC;aAAC;IAClF;IAGK,OAAA,UAAU,OAAO,IAAI;AAC9B;ACtCO,SAAS,cAAc,OAAA,EAAmE;IACzF,MAAA,EACJ,OAAA,EACA,WAAW,aAAa,SAAA,EACxB,MAAM,QAAQ,SAAA,EACd,IAAI,GAAA,EACJ,IAAA,EACA,IAAA,EACA,SAAA,EACA,OAAA,EAAA,GACE;IAEJ,IAAI,CAAC,SACG,MAAA,IAAI,MAAM,qBAAqB;IAEvC,IAAI,CAAC,MACG,MAAA,IAAI,MAAM,kBAAkB;IAEpC,IAAI,CAAC,KACG,MAAA,IAAI,MAAM,gBAAgB;IAElC,IAAI,YAAY,OAAO,QAAQ,QAAA,CAAS,GAAG,GACnC,MAAA,IAAI,MAAM,mCAAmC;IAGrD,MAAM,YAAY,eAAe,YAAY,KAAA,IAAY,YACnD,OAAO,UAAU,YAAY,KAAA,IAAY,OACzC,KAAK,eAAe,GAAG,GACvB,kBAAkB,MAAM,OAAA,CAAQ,IAAI,IACtCC,SAAoB,qBAAqB,IAAI,CAAC,IAC9C,MAIE,eAAe,IAAI,gBAAgB;QACvC;QACA;QACA;QACA,MAAM;IAAA,CACP;IACG,IAAA,aACF,aAAa,GAAA,CAAI,aAAa,SAAS,GAErC,QACF,aAAa,GAAA,CAAI,QAAQ,IAAI,GAE3B,aACF,aAAa,GAAA,CAAI,aAAa,SAAS,GAErC,WACF,aAAa,GAAA,CAAI,WAAW,OAAO,GAEjC,cAAc,GAAG,GACN,aAAA,GAAA,CAAI,eAAe,WAAW;SAAA,IAClC,YAAY,GAAG,GAAG;QACrB,MAAA,YAAY,iBAAiB,GAAG;QACzB,aAAA,GAAA,CAAI,eAAe,SAAS;IAAA;IAG3C,MAAM,WAAW;QAAC,YAAY,MAAM,KAAK,OAAO;KAAA;IAC5C,aACF,SAAS,IAAA,CAAK,SAAS;IAEzB,MAAM,eAAe;QACnB;QACA,CAAA,GAAA,EAAM,EAAE,EAAA;QACR,CAAA,KAAA,EAAQ,IAAI,EAAA;QACZ,CAAA,KAAA,EAAQ,mBAAmB,eAAe,CAAC,EAAA;KAC7C;IACI,OAAA,QACF,aAAa,IAAA,CAAK,CAAA,KAAA,EAAQ,IAAI,EAAE,GAElC,SAAS,IAAA,CAAK,UAAU,QAAQ,GAAG,aAAa,IAAA,CAAK,GAAG,CAAC,CAAA,CAAA,EAAI,YAAY,EAAE,GACpE,SAAS,IAAA,CAAK,GAAG;AAC1B;ACrEO,SAAS,gBAAgB,OAAA,EAAmE;IACjG,MAAM,EAAC,iBAAiB,GAAA,EAAK,UAAA,CAAA,CAAA,GAAc,SACrC,EAAC,OAAA,EAAS,UAAA,CAAc,CAAA,GAAA,eAAe,YAAY,GAAG,KAAK,CAAC;IAE9D,IAAA,CAAC,WAKD,QAAQ,MAAA,CAAO,IAAA,KAAS,aAIxB,QAAQ,MAAA,CAAO,IAAA,KAAS,WAC1B;IAGF,MAAM,YAAY,IAAI,SAAA,CAAU,QAAQ,MAAA,CAAO,QAAQ,CAAA,EACjD,aAAa,IAAI,KAAA,CAAM,QAAQ,MAAA,CAAO,IAAI,CAAA;IAEhD,IAAI,aAAa,YAAY;QAC3B,MAAM,EAAC,OAAA,EAAS,SAAA,EAAW,IAAA,CAAQ,CAAA,GAAA,uBACjC,OAAO,QAAQ,SAAA,IAAc,aAAa,QAAQ,SAAA,CAAU,SAAS,IAAI,QAAQ,SAAA;QAEnF,IAAI,CAAC,QAAS,CAAA;QACd,MAAM,EAAC,GAAA,EAAK,KAAA,EAAO,UAAA,EAAY,QAAA,CAAY,CAAA,GAAA;QACpC,OAAA;YACL;YACA;YACA;YACA,IAAI;YACJ,MAAM;YACN,MAAM,cAAc,aAAa,UAAU;YAC3C,WAAW;YACX,SAAS;QACX;IAAA;AAIJ;AAGO,SAAS,uBAAuB,SAAA,EAAuC;IAC5E,IAAI,UAAyB,OAAO,aAAc,WAAW,YAAY,UAAU,OAAA;IAInF,OAHI,YAAY,OAAA,CACd,UAAU,QAAQ,OAAA,CAAQ,OAAO,EAAE,CAAA,GAEjC,OAAO,aAAc,WAChB;QAAC;IAAA,IAEH;QAAC,GAAG,SAAA;QAAW;IAAO;AAC/B", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7], "debugId": null}}, {"offset": {"line": 420, "column": 0}, "map": {"version": 3, "file": "config.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/generateHelpUrl.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/validators.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/util/once.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/warnings.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/config.ts"], "sourcesContent": ["const BASE_URL = 'https://www.sanity.io/help/'\n\nexport function generateHelpUrl(slug: string) {\n  return BASE_URL + slug\n}\n", "import type {Any, InitializedClientConfig, SanityDocumentStub} from './types'\n\nconst VALID_ASSET_TYPES = ['image', 'file']\nconst VALID_INSERT_LOCATIONS = ['before', 'after', 'replace']\n\nexport const dataset = (name: string) => {\n  if (!/^(~[a-z0-9]{1}[-\\w]{0,63}|[a-z0-9]{1}[-\\w]{0,63})$/.test(name)) {\n    throw new Error(\n      'Datasets can only contain lowercase characters, numbers, underscores and dashes, and start with tilde, and be maximum 64 characters',\n    )\n  }\n}\n\nexport const projectId = (id: string) => {\n  if (!/^[-a-z0-9]+$/i.test(id)) {\n    throw new Error('`projectId` can only contain only a-z, 0-9 and dashes')\n  }\n}\n\nexport const validateAssetType = (type: string) => {\n  if (VALID_ASSET_TYPES.indexOf(type) === -1) {\n    throw new Error(`Invalid asset type: ${type}. Must be one of ${VALID_ASSET_TYPES.join(', ')}`)\n  }\n}\n\nexport const validateObject = (op: string, val: Any) => {\n  if (val === null || typeof val !== 'object' || Array.isArray(val)) {\n    throw new Error(`${op}() takes an object of properties`)\n  }\n}\n\nexport const validateDocumentId = (op: string, id: string) => {\n  if (typeof id !== 'string' || !/^[a-z0-9_][a-z0-9_.-]{0,127}$/i.test(id) || id.includes('..')) {\n    throw new Error(`${op}(): \"${id}\" is not a valid document ID`)\n  }\n}\n\nexport const requireDocumentId = (op: string, doc: Record<string, Any>) => {\n  if (!doc._id) {\n    throw new Error(`${op}() requires that the document contains an ID (\"_id\" property)`)\n  }\n\n  validateDocumentId(op, doc._id)\n}\n\nexport const validateDocumentType = (op: string, type: string) => {\n  if (typeof type !== 'string') {\n    throw new Error(`\\`${op}()\\`: \\`${type}\\` is not a valid document type`)\n  }\n}\n\nexport const requireDocumentType = (op: string, doc: Record<string, Any>) => {\n  if (!doc._type) {\n    throw new Error(`\\`${op}()\\` requires that the document contains a type (\\`_type\\` property)`)\n  }\n\n  validateDocumentType(op, doc._type)\n}\n\nexport const validateVersionIdMatch = (builtVersionId: string, document: SanityDocumentStub) => {\n  if (document._id && document._id !== builtVersionId) {\n    throw new Error(\n      `The provided document ID (\\`${document._id}\\`) does not match the generated version ID (\\`${builtVersionId}\\`)`,\n    )\n  }\n}\n\nexport const validateInsert = (at: string, selector: string, items: Any[]) => {\n  const signature = 'insert(at, selector, items)'\n  if (VALID_INSERT_LOCATIONS.indexOf(at) === -1) {\n    const valid = VALID_INSERT_LOCATIONS.map((loc) => `\"${loc}\"`).join(', ')\n    throw new Error(`${signature} takes an \"at\"-argument which is one of: ${valid}`)\n  }\n\n  if (typeof selector !== 'string') {\n    throw new Error(`${signature} takes a \"selector\"-argument which must be a string`)\n  }\n\n  if (!Array.isArray(items)) {\n    throw new Error(`${signature} takes an \"items\"-argument which must be an array`)\n  }\n}\n\nexport const hasDataset = (config: InitializedClientConfig): string => {\n  if (!config.dataset) {\n    throw new Error('`dataset` must be provided to perform queries')\n  }\n\n  return config.dataset || ''\n}\n\nexport const requestTag = (tag: string) => {\n  if (typeof tag !== 'string' || !/^[a-z0-9._-]{1,75}$/i.test(tag)) {\n    throw new Error(\n      `Tag can only contain alphanumeric characters, underscores, dashes and dots, and be between one and 75 characters long.`,\n    )\n  }\n\n  return tag\n}\n\nexport const resourceConfig = (config: InitializedClientConfig): void => {\n  if (!config['~experimental_resource']) {\n    throw new Error('`resource` must be provided to perform resource queries')\n  }\n  const {type, id} = config['~experimental_resource']\n\n  switch (type) {\n    case 'dataset': {\n      const segments = id.split('.')\n      if (segments.length !== 2) {\n        throw new Error('Dataset resource ID must be in the format \"project.dataset\"')\n      }\n      return\n    }\n    case 'dashboard':\n    case 'media-library':\n    case 'canvas': {\n      return\n    }\n    default:\n      // @ts-expect-error - handle all supported resource types\n      throw new Error(`Unsupported resource type: ${type.toString()}`)\n  }\n}\n\nexport const resourceGuard = (service: string, config: InitializedClientConfig): void => {\n  if (config['~experimental_resource']) {\n    throw new Error(`\\`${service}\\` does not support resource-based operations`)\n  }\n}\n", "import type {Any} from '../types'\n\nexport function once(fn: Any) {\n  let didCall = false\n  let returnValue: Any\n  return (...args: Any[]) => {\n    if (didCall) {\n      return returnValue\n    }\n    returnValue = fn(...args)\n    didCall = true\n    return returnValue\n  }\n}\n", "import {generateHelpUrl} from './generateHelpUrl'\nimport {type Any} from './types'\nimport {once} from './util/once'\n\nconst createWarningPrinter = (message: string[]) =>\n  // eslint-disable-next-line no-console\n  once((...args: Any[]) => console.warn(message.join(' '), ...args))\n\nexport const printCdnAndWithCredentialsWarning = createWarningPrinter([\n  `Because you set \\`withCredentials\\` to true, we will override your \\`useCdn\\``,\n  `setting to be false since (cookie-based) credentials are never set on the CDN`,\n])\n\nexport const printCdnWarning = createWarningPrinter([\n  `Since you haven't set a value for \\`useCdn\\`, we will deliver content using our`,\n  `global, edge-cached API-CDN. If you wish to have content delivered faster, set`,\n  `\\`useCdn: false\\` to use the Live API. Note: You may incur higher costs using the live API.`,\n])\n\nexport const printCdnPreviewDraftsWarning = createWarningPrinter([\n  `The Sanity client is configured with the \\`perspective\\` set to \\`drafts\\` or \\`previewDrafts\\`, which doesn't support the API-CDN.`,\n  `The Live API will be used instead. Set \\`useCdn: false\\` in your configuration to hide this warning.`,\n])\n\nexport const printPreviewDraftsDeprecationWarning = createWarningPrinter([\n  `The \\`previewDrafts\\` perspective has been renamed to  \\`drafts\\` and will be removed in a future API version`,\n])\n\nexport const printBrowserTokenWarning = createWarningPrinter([\n  'You have configured Sanity client to use a token in the browser. This may cause unintentional security issues.',\n  `See ${generateHelpUrl(\n    'js-client-browser-token',\n  )} for more information and how to hide this warning.`,\n])\n\nexport const printCredentialedTokenWarning = createWarningPrinter([\n  'You have configured Sanity client to use a token, but also provided `withCredentials: true`.',\n  'This is no longer supported - only token will be used - remove `withCredentials: true`.',\n])\n\nexport const printNoApiVersionSpecifiedWarning = createWarningPrinter([\n  'Using the Sanity client without specifying an API version is deprecated.',\n  `See ${generateHelpUrl('js-client-api-version')}`,\n])\n\nexport const printNoDefaultExport = createWarningPrinter([\n  'The default export of @sanity/client has been deprecated. Use the named export `createClient` instead.',\n])\n", "import {generateHelpUrl} from './generateHelpUrl'\nimport type {ClientConfig, ClientPerspective, InitializedClientConfig} from './types'\nimport * as validate from './validators'\nimport * as warnings from './warnings'\n\nconst defaultCdnHost = 'apicdn.sanity.io'\nexport const defaultConfig = {\n  apiHost: 'https://api.sanity.io',\n  apiVersion: '1',\n  useProjectHostname: true,\n  stega: {enabled: false},\n} satisfies ClientConfig\n\nconst LOCALHOSTS = ['localhost', '127.0.0.1', '0.0.0.0']\nconst isLocal = (host: string) => LOCALHOSTS.indexOf(host) !== -1\n\nfunction validateApiVersion(apiVersion: string) {\n  if (apiVersion === '1' || apiVersion === 'X') {\n    return\n  }\n\n  const apiDate = new Date(apiVersion)\n  const apiVersionValid =\n    /^\\d{4}-\\d{2}-\\d{2}$/.test(apiVersion) && apiDate instanceof Date && apiDate.getTime() > 0\n\n  if (!apiVersionValid) {\n    throw new Error('Invalid API version string, expected `1` or date in format `YYYY-MM-DD`')\n  }\n}\n\n/**\n * @internal - it may have breaking changes in any release\n */\nexport function validateApiPerspective(\n  perspective: unknown,\n): asserts perspective is ClientPerspective {\n  if (Array.isArray(perspective) && perspective.length > 1 && perspective.includes('raw')) {\n    throw new TypeError(\n      `Invalid API perspective value: \"raw\". The raw-perspective can not be combined with other perspectives`,\n    )\n  }\n}\n\nexport const initConfig = (\n  config: Partial<ClientConfig>,\n  prevConfig: Partial<ClientConfig>,\n): InitializedClientConfig => {\n  const specifiedConfig = {\n    ...prevConfig,\n    ...config,\n    stega: {\n      ...(typeof prevConfig.stega === 'boolean'\n        ? {enabled: prevConfig.stega}\n        : prevConfig.stega || defaultConfig.stega),\n      ...(typeof config.stega === 'boolean' ? {enabled: config.stega} : config.stega || {}),\n    },\n  }\n  if (!specifiedConfig.apiVersion) {\n    warnings.printNoApiVersionSpecifiedWarning()\n  }\n\n  const newConfig = {\n    ...defaultConfig,\n    ...specifiedConfig,\n  } as InitializedClientConfig\n  const projectBased = newConfig.useProjectHostname && !newConfig['~experimental_resource']\n\n  if (typeof Promise === 'undefined') {\n    const helpUrl = generateHelpUrl('js-client-promise-polyfill')\n    throw new Error(`No native Promise-implementation found, polyfill needed - see ${helpUrl}`)\n  }\n\n  if (projectBased && !newConfig.projectId) {\n    throw new Error('Configuration must contain `projectId`')\n  }\n\n  if (newConfig['~experimental_resource']) {\n    validate.resourceConfig(newConfig)\n  }\n\n  if (typeof newConfig.perspective !== 'undefined') {\n    validateApiPerspective(newConfig.perspective)\n  }\n\n  if ('encodeSourceMap' in newConfig) {\n    throw new Error(\n      `It looks like you're using options meant for '@sanity/preview-kit/client'. 'encodeSourceMap' is not supported in '@sanity/client'. Did you mean 'stega.enabled'?`,\n    )\n  }\n  if ('encodeSourceMapAtPath' in newConfig) {\n    throw new Error(\n      `It looks like you're using options meant for '@sanity/preview-kit/client'. 'encodeSourceMapAtPath' is not supported in '@sanity/client'. Did you mean 'stega.filter'?`,\n    )\n  }\n  if (typeof newConfig.stega.enabled !== 'boolean') {\n    throw new Error(`stega.enabled must be a boolean, received ${newConfig.stega.enabled}`)\n  }\n  if (newConfig.stega.enabled && newConfig.stega.studioUrl === undefined) {\n    throw new Error(`stega.studioUrl must be defined when stega.enabled is true`)\n  }\n  if (\n    newConfig.stega.enabled &&\n    typeof newConfig.stega.studioUrl !== 'string' &&\n    typeof newConfig.stega.studioUrl !== 'function'\n  ) {\n    throw new Error(\n      `stega.studioUrl must be a string or a function, received ${newConfig.stega.studioUrl}`,\n    )\n  }\n\n  const isBrowser = typeof window !== 'undefined' && window.location && window.location.hostname\n  const isLocalhost = isBrowser && isLocal(window.location.hostname)\n\n  const hasToken = Boolean(newConfig.token)\n  if (newConfig.withCredentials && hasToken) {\n    warnings.printCredentialedTokenWarning()\n    newConfig.withCredentials = false\n  }\n\n  if (isBrowser && isLocalhost && hasToken && newConfig.ignoreBrowserTokenWarning !== true) {\n    warnings.printBrowserTokenWarning()\n  } else if (typeof newConfig.useCdn === 'undefined') {\n    warnings.printCdnWarning()\n  }\n\n  if (projectBased) {\n    validate.projectId(newConfig.projectId!)\n  }\n\n  if (newConfig.dataset) {\n    validate.dataset(newConfig.dataset)\n  }\n\n  if ('requestTagPrefix' in newConfig) {\n    // Allow setting and unsetting request tag prefix\n    newConfig.requestTagPrefix = newConfig.requestTagPrefix\n      ? validate.requestTag(newConfig.requestTagPrefix).replace(/\\.+$/, '')\n      : undefined\n  }\n\n  newConfig.apiVersion = `${newConfig.apiVersion}`.replace(/^v/, '')\n  newConfig.isDefaultApi = newConfig.apiHost === defaultConfig.apiHost\n\n  if (newConfig.useCdn === true && newConfig.withCredentials) {\n    warnings.printCdnAndWithCredentialsWarning()\n  }\n\n  // If `useCdn` is undefined, we treat it as `true`\n  newConfig.useCdn = newConfig.useCdn !== false && !newConfig.withCredentials\n\n  validateApiVersion(newConfig.apiVersion)\n\n  const hostParts = newConfig.apiHost.split('://', 2)\n  const protocol = hostParts[0]\n  const host = hostParts[1]\n  const cdnHost = newConfig.isDefaultApi ? defaultCdnHost : host\n\n  if (projectBased) {\n    newConfig.url = `${protocol}://${newConfig.projectId}.${host}/v${newConfig.apiVersion}`\n    newConfig.cdnUrl = `${protocol}://${newConfig.projectId}.${cdnHost}/v${newConfig.apiVersion}`\n  } else {\n    newConfig.url = `${newConfig.apiHost}/v${newConfig.apiVersion}`\n    newConfig.cdnUrl = newConfig.url\n  }\n\n  return newConfig\n}\n"], "names": ["warnings.printNoApiVersionSpecifiedWarning", "validate.resourceConfig", "warnings.printCredentialedTokenWarning", "warnings.printBrowserTokenWarning", "warnings.printCdnWarning", "validate.projectId", "validate.dataset", "validate.requestTag", "warnings.printCdnAndWithCredentialsWarning"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA,MAAM,WAAW;AAEV,SAAS,gBAAgB,IAAA,EAAc;IAC5C,OAAO,WAAW;AACpB;ACFA,MAAM,oBAAoB;IAAC;IAAS,MAAM;CAAA,EACpC,yBAAyB;IAAC;IAAU;IAAS,SAAS;CAAA,EAE/C,UAAU,CAAC,SAAiB;IACnC,IAAA,CAAC,qDAAqD,IAAA,CAAK,IAAI,GACjE,MAAM,IAAI,MACR;AAGN,GAEa,YAAY,CAAC,OAAe;IACnC,IAAA,CAAC,gBAAgB,IAAA,CAAK,EAAE,GACpB,MAAA,IAAI,MAAM,uDAAuD;AAE3E,GAEa,oBAAoB,CAAC,SAAiB;IAC7C,IAAA,kBAAkB,OAAA,CAAQ,IAAI,MAAM,CAAA,GAChC,MAAA,IAAI,MAAM,CAAA,oBAAA,EAAuB,IAAI,CAAA,iBAAA,EAAoB,kBAAkB,IAAA,CAAK,IAAI,CAAC,EAAE;AAEjG,GAEa,iBAAiB,CAAC,IAAY,QAAa;IACtD,IAAI,QAAQ,QAAQ,OAAO,OAAQ,YAAY,MAAM,OAAA,CAAQ,GAAG,GAC9D,MAAM,IAAI,MAAM,GAAG,EAAE,CAAA,gCAAA,CAAkC;AAE3D,GAEa,qBAAqB,CAAC,IAAY,OAAe;IACxD,IAAA,OAAO,MAAO,YAAY,CAAC,iCAAiC,IAAA,CAAK,EAAE,KAAK,GAAG,QAAA,CAAS,IAAI,GAC1F,MAAM,IAAI,MAAM,GAAG,EAAE,CAAA,KAAA,EAAQ,EAAE,CAAA,4BAAA,CAA8B;AAEjE,GAEa,oBAAoB,CAAC,IAAY,QAA6B;IACzE,IAAI,CAAC,IAAI,GAAA,EACP,MAAM,IAAI,MAAM,GAAG,EAAE,CAAA,6DAAA,CAA+D;IAGnE,mBAAA,IAAI,IAAI,GAAG;AAChC,GAEa,uBAAuB,CAAC,IAAY,SAAiB;IAChE,IAAI,OAAO,QAAS,UAClB,MAAM,IAAI,MAAM,CAAA,EAAA,EAAK,EAAE,CAAA,QAAA,EAAW,IAAI,CAAA,+BAAA,CAAiC;AAE3E,GAEa,sBAAsB,CAAC,IAAY,QAA6B;IAC3E,IAAI,CAAC,IAAI,KAAA,EACP,MAAM,IAAI,MAAM,CAAA,EAAA,EAAK,EAAE,CAAA,oEAAA,CAAsE;IAG1E,qBAAA,IAAI,IAAI,KAAK;AACpC,GAEa,yBAAyB,CAAC,gBAAwB,aAAiC;IAC1F,IAAA,SAAS,GAAA,IAAO,SAAS,GAAA,KAAQ,gBACnC,MAAM,IAAI,MACR,CAAA,4BAAA,EAA+B,SAAS,GAAG,CAAA,+CAAA,EAAkD,cAAc,CAAA,GAAA,CAAA;AAGjH,GAEa,iBAAiB,CAAC,IAAY,UAAkB,UAAiB;IAC5E,MAAM,YAAY;IAClB,IAAI,uBAAuB,OAAA,CAAQ,EAAE,MAAM,CAAA,GAAI;QACvC,MAAA,QAAQ,uBAAuB,GAAA,CAAI,CAAC,MAAQ,CAAA,CAAA,EAAI,GAAG,CAAA,CAAA,CAAG,EAAE,IAAA,CAAK,IAAI;QACvE,MAAM,IAAI,MAAM,GAAG,SAAS,CAAA,yCAAA,EAA4C,KAAK,EAAE;IAAA;IAGjF,IAAI,OAAO,YAAa,UACtB,MAAM,IAAI,MAAM,GAAG,SAAS,CAAA,mDAAA,CAAqD;IAG/E,IAAA,CAAC,MAAM,OAAA,CAAQ,KAAK,GACtB,MAAM,IAAI,MAAM,GAAG,SAAS,CAAA,iDAAA,CAAmD;AAEnF,GAEa,aAAa,CAAC,WAA4C;IACrE,IAAI,CAAC,OAAO,OAAA,EACJ,MAAA,IAAI,MAAM,+CAA+C;IAGjE,OAAO,OAAO,OAAA,IAAW;AAC3B,GAEa,aAAa,CAAC,QAAgB;IACzC,IAAI,OAAO,OAAQ,YAAY,CAAC,uBAAuB,IAAA,CAAK,GAAG,GAC7D,MAAM,IAAI,MACR;IAIG,OAAA;AACT,GAEa,iBAAiB,CAAC,WAA0C;IACnE,IAAA,CAAC,MAAA,CAAO,wBAAwB,CAAA,EAC5B,MAAA,IAAI,MAAM,yDAAyD;IAE3E,MAAM,EAAC,IAAA,EAAM,EAAA,EAAA,GAAM,MAAA,CAAO,wBAAwB,CAAA;IAElD,OAAQ,MAAM;QACZ,KAAK;YAAW;gBAEd,IADiB,GAAG,KAAA,CAAM,GAAG,EAChB,MAAA,KAAW,GAChB,MAAA,IAAI,MAAM,6DAA6D;gBAE/E;YAAA;QAEF,KAAK;QACL,KAAK;QACL,KAAK;YACH;QAEF;YAEE,MAAM,IAAI,MAAM,CAAA,2BAAA,EAA8B,KAAK,QAAA,CAAU,CAAA,EAAE;IAAA;AAErE,GAEa,gBAAgB,CAAC,SAAiB,WAA0C;IACvF,IAAI,MAAA,CAAO,wBAAwB,CAAA,EACjC,MAAM,IAAI,MAAM,CAAA,EAAA,EAAK,OAAO,CAAA,6CAAA,CAA+C;AAE/E;AChIO,SAAS,KAAK,EAAA,EAAS;IAC5B,IAAI,UAAU,CAAA,GACV;IACG,OAAA,CAAA,GAAI,OAAA,CACL,WAAA,CAGJ,cAAc,GAAG,GAAG,IAAI,GACxB,UAAU,CAAA,CAAA,GACH,WAAA;AAEX;ACTA,MAAM,uBAAuB,CAAC,UAAA,sCAAA;IAE5B,KAAK,CAAA,GAAI,OAAgB,QAAQ,IAAA,CAAK,QAAQ,IAAA,CAAK,GAAG,GAAG,GAAG,IAAI,CAAC,GAEtD,oCAAoC,qBAAqB;IACpE;IACA;CACD,GAEY,kBAAkB,qBAAqB;IAClD;IACA;IACA;CACD,GAEY,+BAA+B,qBAAqB;IAC/D;IACA;CACD,GAEY,uCAAuC,qBAAqB;IACvE;CACD,GAEY,2BAA2B,qBAAqB;IAC3D;IACA,CAAA,IAAA,EAAO,gBACL,2BACD,mDAAA,CAAA;CACF,GAEY,gCAAgC,qBAAqB;IAChE;IACA;CACD,GAEY,oCAAoC,qBAAqB;IACpE;IACA,CAAA,IAAA,EAAO,gBAAgB,uBAAuB,CAAC,EAAA;CAChD,GAEY,uBAAuB,qBAAqB;IACvD;CACD,GC1CK,iBAAiB,oBACV,gBAAgB;IAC3B,SAAS;IACT,YAAY;IACZ,oBAAoB,CAAA;IACpB,OAAO;QAAC,SAAS,CAAA;IAAK;AACxB,GAEM,aAAa;IAAC;IAAa;IAAa,SAAS;CAAA,EACjD,UAAU,CAAC,OAAiB,WAAW,OAAA,CAAQ,IAAI,MAAM,CAAA;AAE/D,SAAS,mBAAmB,UAAA,EAAoB;IAC1C,IAAA,eAAe,OAAO,eAAe,KACvC;IAGI,MAAA,UAAU,IAAI,KAAK,UAAU;IAI/B,IAAA,CAAA,CAFF,sBAAsB,IAAA,CAAK,UAAU,KAAK,mBAAmB,QAAQ,QAAQ,OAAA,CAAY,IAAA,CAAA,GAGnF,MAAA,IAAI,MAAM,yEAAyE;AAE7F;AAKO,SAAS,uBACd,WAAA,EAC0C;IACtC,IAAA,MAAM,OAAA,CAAQ,WAAW,KAAK,YAAY,MAAA,GAAS,KAAK,YAAY,QAAA,CAAS,KAAK,GACpF,MAAM,IAAI,UACR;AAGN;AAEa,MAAA,aAAa,CACxB,QACA,eAC4B;IAC5B,MAAM,kBAAkB;QACtB,GAAG,UAAA;QACH,GAAG,MAAA;QACH,OAAO;YACL,GAAI,OAAO,WAAW,KAAA,IAAU,YAC5B;gBAAC,SAAS,WAAW,KAAA;YAAA,IACrB,WAAW,KAAA,IAAS,cAAc,KAAA;YACtC,GAAI,OAAO,OAAO,KAAA,IAAU,YAAY;gBAAC,SAAS,OAAO,KAAA;YAAK,IAAI,OAAO,KAAA,IAAS,CAAA,CAAA;QAAC;IAEvF;IACK,gBAAgB,UAAA,IACnBA,kCAA2C;IAG7C,MAAM,YAAY;QAChB,GAAG,aAAA;QACH,GAAG,eAAA;IAAA,GAEC,eAAe,UAAU,kBAAA,IAAsB,CAAC,SAAA,CAAU,wBAAwB,CAAA;IAEpF,IAAA,OAAO,UAAY,KAAa;QAC5B,MAAA,UAAU,gBAAgB,4BAA4B;QAC5D,MAAM,IAAI,MAAM,CAAA,8DAAA,EAAiE,OAAO,EAAE;IAAA;IAGxF,IAAA,gBAAgB,CAAC,UAAU,SAAA,EACvB,MAAA,IAAI,MAAM,wCAAwC;IAW1D,IARI,SAAA,CAAU,wBAAwB,CAAA,IACpCC,eAAwB,SAAS,GAG/B,OAAO,UAAU,WAAA,GAAgB,OACnC,uBAAuB,UAAU,WAAW,GAG1C,qBAAqB,WACvB,MAAM,IAAI,MACR;IAGJ,IAAI,2BAA2B,WAC7B,MAAM,IAAI,MACR;IAGA,IAAA,OAAO,UAAU,KAAA,CAAM,OAAA,IAAY,WACrC,MAAM,IAAI,MAAM,CAAA,0CAAA,EAA6C,UAAU,KAAA,CAAM,OAAO,EAAE;IAExF,IAAI,UAAU,KAAA,CAAM,OAAA,IAAW,UAAU,KAAA,CAAM,SAAA,KAAc,KAAA,GACrD,MAAA,IAAI,MAAM,4DAA4D;IAG5E,IAAA,UAAU,KAAA,CAAM,OAAA,IAChB,OAAO,UAAU,KAAA,CAAM,SAAA,IAAc,YACrC,OAAO,UAAU,KAAA,CAAM,SAAA,IAAc,YAErC,MAAM,IAAI,MACR,CAAA,yDAAA,EAA4D,UAAU,KAAA,CAAM,SAAS,EAAA;IAIzF,MAAM,YAAY,OAAO,SAAW,OAAe,OAAO,QAAA,IAAY,OAAO,QAAA,CAAS,QAAA,EAChF,cAAc,aAAa,QAAQ,OAAO,QAAA,CAAS,QAAQ,GAE3D,WAAW,CAAA,CAAQ,UAAU,KAAA;IAC/B,UAAU,eAAA,IAAmB,YAAA,CAC/BC,8BAAuC,GACvC,UAAU,eAAA,GAAkB,CAAA,CAAA,GAG1B,aAAa,eAAe,YAAY,UAAU,yBAAA,KAA8B,CAAA,IAClFC,6BACS,OAAO,UAAU,MAAA,GAAW,OACrCC,mBAGE,gBACFC,UAAmB,UAAU,SAAU,GAGrC,UAAU,OAAA,IACZC,QAAiB,UAAU,OAAO,GAGhC,sBAAsB,aAAA,CAExB,UAAU,gBAAA,GAAmB,UAAU,gBAAA,GACnCC,WAAoB,UAAU,gBAAgB,EAAE,OAAA,CAAQ,QAAQ,EAAE,IAClE,KAAA,CAAA,GAGN,UAAU,UAAA,GAAa,GAAG,UAAU,UAAU,EAAA,CAAG,OAAA,CAAQ,MAAM,EAAE,GACjE,UAAU,YAAA,GAAe,UAAU,OAAA,KAAY,cAAc,OAAA,EAEzD,UAAU,MAAA,KAAW,CAAA,KAAQ,UAAU,eAAA,IACzCC,kCAA2C,GAI7C,UAAU,MAAA,GAAS,UAAU,MAAA,KAAW,CAAA,KAAS,CAAC,UAAU,eAAA,EAE5D,mBAAmB,UAAU,UAAU;IAEvC,MAAM,YAAY,UAAU,OAAA,CAAQ,KAAA,CAAM,OAAO,CAAC,GAC5C,WAAW,SAAA,CAAU,CAAC,CAAA,EACtB,OAAO,SAAA,CAAU,CAAC,CAAA,EAClB,UAAU,UAAU,YAAA,GAAe,iBAAiB;IAE1D,OAAI,eAAA,CACF,UAAU,GAAA,GAAM,GAAG,QAAQ,CAAA,GAAA,EAAM,UAAU,SAAS,CAAA,CAAA,EAAI,IAAI,CAAA,EAAA,EAAK,UAAU,UAAU,EAAA,EACrF,UAAU,MAAA,GAAS,GAAG,QAAQ,CAAA,GAAA,EAAM,UAAU,SAAS,CAAA,CAAA,EAAI,OAAO,CAAA,EAAA,EAAK,UAAU,UAAU,EAAA,IAAA,CAE3F,UAAU,GAAA,GAAM,GAAG,UAAU,OAAO,CAAA,EAAA,EAAK,UAAU,UAAU,EAAA,EAC7D,UAAU,MAAA,GAAS,UAAU,GAAA,GAGxB;AACT", "ignoreList": [0, 1, 2, 3, 4], "debugId": null}}, {"offset": {"line": 593, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/util/codeFrame.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/http/errors.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/http/request.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/data/eventsource.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/util/getSelection.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/data/patch.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/data/transaction.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/http/requestOptions.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/data/encodeQueryString.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/data/dataMethods.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/agent/actions/generate.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/agent/actions/patch.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/agent/actions/prompt.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/agent/actions/transform.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/agent/actions/translate.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/agent/actions/AgentActionsClient.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/assets/AssetsClient.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/util/defaults.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/util/pick.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/data/eventsourcePolyfill.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/data/reconnectOnConnectionFailure.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/data/listen.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/util/shareReplayLatest.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/data/live.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/datasets/DatasetsClient.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/projects/ProjectsClient.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/util/createVersionId.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/releases/createRelease.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/releases/ReleasesClient.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/users/UsersClient.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/SanityClient.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/defineCreateClient.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/defineDeprecatedCreateClient.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/http/nodeMiddleware.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/index.ts"], "sourcesContent": ["/**\n * Inlined, modified version of the `codeFrameColumns` function from `@babel/code-frame`.\n * MIT-licensed - https://github.com/babel/babel/blob/main/LICENSE\n * Copyright (c) 2014-present <PERSON> and other contributors.\n */\ntype Location = {\n  column: number\n  line: number\n}\n\ntype NodeLocation = {\n  start: Location\n  end?: Location\n}\n\ntype GroqLocation = {\n  start: number\n  end?: number\n}\n\n/**\n * RegExp to test for newlines.\n */\n\nconst NEWLINE = /\\r\\n|[\\n\\r\\u2028\\u2029]/\n\n/**\n * Extract what lines should be marked and highlighted.\n */\n\ntype MarkerLines = Record<number, true | [number, number]>\n\n/**\n * Highlight a code frame with the given location and message.\n *\n * @param query - The query to be highlighted.\n * @param location - The location of the error in the code/query.\n * @param message - Message to be displayed inline (if possible) next to the highlighted\n * location in the code. If it can't be positioned inline, it will be placed above the\n * code frame.\n * @returns The highlighted code frame.\n */\nexport function codeFrame(query: string, location: GroqLocation, message?: string): string {\n  const lines = query.split(NEWLINE)\n  const loc = {\n    start: columnToLine(location.start, lines),\n    end: location.end ? columnToLine(location.end, lines) : undefined,\n  }\n\n  const {start, end, markerLines} = getMarkerLines(loc, lines)\n\n  const numberMaxWidth = `${end}`.length\n\n  return query\n    .split(NEWLINE, end)\n    .slice(start, end)\n    .map((line, index) => {\n      const number = start + 1 + index\n      const paddedNumber = ` ${number}`.slice(-numberMaxWidth)\n      const gutter = ` ${paddedNumber} |`\n      const hasMarker = markerLines[number]\n      const lastMarkerLine = !markerLines[number + 1]\n      if (!hasMarker) {\n        return ` ${gutter}${line.length > 0 ? ` ${line}` : ''}`\n      }\n\n      let markerLine = ''\n      if (Array.isArray(hasMarker)) {\n        const markerSpacing = line.slice(0, Math.max(hasMarker[0] - 1, 0)).replace(/[^\\t]/g, ' ')\n        const numberOfMarkers = hasMarker[1] || 1\n\n        markerLine = [\n          '\\n ',\n          gutter.replace(/\\d/g, ' '),\n          ' ',\n          markerSpacing,\n          '^'.repeat(numberOfMarkers),\n        ].join('')\n\n        if (lastMarkerLine && message) {\n          markerLine += ' ' + message\n        }\n      }\n      return ['>', gutter, line.length > 0 ? ` ${line}` : '', markerLine].join('')\n    })\n    .join('\\n')\n}\n\nfunction getMarkerLines(\n  loc: NodeLocation,\n  source: Array<string>,\n): {\n  start: number\n  end: number\n  markerLines: MarkerLines\n} {\n  const startLoc: Location = {...loc.start}\n  const endLoc: Location = {...startLoc, ...loc.end}\n  const linesAbove = 2\n  const linesBelow = 3\n  const startLine = startLoc.line ?? -1\n  const startColumn = startLoc.column ?? 0\n  const endLine = endLoc.line\n  const endColumn = endLoc.column\n\n  let start = Math.max(startLine - (linesAbove + 1), 0)\n  let end = Math.min(source.length, endLine + linesBelow)\n\n  if (startLine === -1) {\n    start = 0\n  }\n\n  if (endLine === -1) {\n    end = source.length\n  }\n\n  const lineDiff = endLine - startLine\n  const markerLines: MarkerLines = {}\n\n  if (lineDiff) {\n    for (let i = 0; i <= lineDiff; i++) {\n      const lineNumber = i + startLine\n\n      if (!startColumn) {\n        markerLines[lineNumber] = true\n      } else if (i === 0) {\n        const sourceLength = source[lineNumber - 1].length\n\n        markerLines[lineNumber] = [startColumn, sourceLength - startColumn + 1]\n      } else if (i === lineDiff) {\n        markerLines[lineNumber] = [0, endColumn]\n      } else {\n        const sourceLength = source[lineNumber - i].length\n\n        markerLines[lineNumber] = [0, sourceLength]\n      }\n    }\n  } else {\n    if (startColumn === endColumn) {\n      if (startColumn) {\n        markerLines[startLine] = [startColumn, 0]\n      } else {\n        markerLines[startLine] = true\n      }\n    } else {\n      markerLines[startLine] = [startColumn, endColumn - startColumn]\n    }\n  }\n\n  return {start, end, markerLines}\n}\n\nfunction columnToLine(column: number, lines: string[]): Location {\n  let offset = 0\n\n  for (let i = 0; i < lines.length; i++) {\n    const lineLength = lines[i].length + 1 // assume '\\n' after each line\n\n    if (offset + lineLength > column) {\n      return {\n        line: i + 1, // 1-based line\n        column: column - offset, // 0-based column\n      }\n    }\n\n    offset += lineLength\n  }\n\n  // Fallback: beyond last line\n  return {\n    line: lines.length,\n    column: lines[lines.length - 1]?.length ?? 0,\n  }\n}\n", "import type {HttpContext} from 'get-it'\n\nimport type {ActionError, Any, ErrorProps, MutationError, QueryParseError} from '../types'\nimport {codeFrame} from '../util/codeFrame'\nimport {isRecord} from '../util/isRecord'\n\nconst MAX_ITEMS_IN_ERROR_MESSAGE = 5\n\n/** @public */\nexport class ClientError extends Error {\n  response: ErrorProps['response']\n  statusCode: ErrorProps['statusCode'] = 400\n  responseBody: ErrorProps['responseBody']\n  details: ErrorProps['details']\n\n  constructor(res: Any, context?: HttpContext) {\n    const props = extractErrorProps(res, context)\n    super(props.message)\n    Object.assign(this, props)\n  }\n}\n\n/** @public */\nexport class ServerError extends Error {\n  response: ErrorProps['response']\n  statusCode: ErrorProps['statusCode'] = 500\n  responseBody: ErrorProps['responseBody']\n  details: ErrorProps['details']\n\n  constructor(res: Any) {\n    const props = extractErrorProps(res)\n    super(props.message)\n    Object.assign(this, props)\n  }\n}\n\nfunction extractErrorProps(res: Any, context?: HttpContext): ErrorProps {\n  const body = res.body\n  const props = {\n    response: res,\n    statusCode: res.statusCode,\n    responseBody: stringifyBody(body, res),\n    message: '',\n    details: undefined as Any,\n  }\n\n  // Fall back early if we didn't get a JSON object returned as expected\n  if (!isRecord(body)) {\n    props.message = httpErrorMessage(res, body)\n    return props\n  }\n\n  const error = body.error\n\n  // API/Boom style errors ({statusCode, error, message})\n  if (typeof error === 'string' && typeof body.message === 'string') {\n    props.message = `${error} - ${body.message}`\n    return props\n  }\n\n  // Content Lake errors with a `error` prop being an object\n  if (typeof error !== 'object' || error === null) {\n    if (typeof error === 'string') {\n      props.message = error\n    } else if (typeof body.message === 'string') {\n      props.message = body.message\n    } else {\n      props.message = httpErrorMessage(res, body)\n    }\n    return props\n  }\n\n  // Mutation errors (specifically)\n  if (isMutationError(error) || isActionError(error)) {\n    const allItems = error.items || []\n    const items = allItems\n      .slice(0, MAX_ITEMS_IN_ERROR_MESSAGE)\n      .map((item) => item.error?.description)\n      .filter(Boolean)\n    let itemsStr = items.length ? `:\\n- ${items.join('\\n- ')}` : ''\n    if (allItems.length > MAX_ITEMS_IN_ERROR_MESSAGE) {\n      itemsStr += `\\n...and ${allItems.length - MAX_ITEMS_IN_ERROR_MESSAGE} more`\n    }\n    props.message = `${error.description}${itemsStr}`\n    props.details = body.error\n    return props\n  }\n\n  // Query parse errors\n  if (isQueryParseError(error)) {\n    const tag = context?.options?.query?.tag\n    props.message = formatQueryParseError(error, tag)\n    props.details = body.error\n    return props\n  }\n\n  if ('description' in error && typeof error.description === 'string') {\n    // Query/database errors ({error: {description, other, arb, props}})\n    props.message = error.description\n    props.details = error\n    return props\n  }\n\n  // Other, more arbitrary errors\n  props.message = httpErrorMessage(res, body)\n  return props\n}\n\nfunction isMutationError(error: object): error is MutationError {\n  return (\n    'type' in error &&\n    error.type === 'mutationError' &&\n    'description' in error &&\n    typeof error.description === 'string'\n  )\n}\n\nfunction isActionError(error: object): error is ActionError {\n  return (\n    'type' in error &&\n    error.type === 'actionError' &&\n    'description' in error &&\n    typeof error.description === 'string'\n  )\n}\n\n/** @internal */\nexport function isQueryParseError(error: object): error is QueryParseError {\n  return (\n    isRecord(error) &&\n    error.type === 'queryParseError' &&\n    typeof error.query === 'string' &&\n    typeof error.start === 'number' &&\n    typeof error.end === 'number'\n  )\n}\n\n/**\n * Formats a GROQ query parse error into a human-readable string.\n *\n * @param error - The error object containing details about the parse error.\n * @param tag - An optional tag to include in the error message.\n * @returns A formatted error message string.\n * @public\n */\nexport function formatQueryParseError(error: QueryParseError, tag?: string | null) {\n  const {query, start, end, description} = error\n\n  if (!query || typeof start === 'undefined') {\n    return `GROQ query parse error: ${description}`\n  }\n\n  const withTag = tag ? `\\n\\nTag: ${tag}` : ''\n  const framed = codeFrame(query, {start, end}, description)\n\n  return `GROQ query parse error:\\n${framed}${withTag}`\n}\n\nfunction httpErrorMessage(res: Any, body: unknown) {\n  const details = typeof body === 'string' ? ` (${sliceWithEllipsis(body, 100)})` : ''\n  const statusMessage = res.statusMessage ? ` ${res.statusMessage}` : ''\n  return `${res.method}-request to ${res.url} resulted in HTTP ${res.statusCode}${statusMessage}${details}`\n}\n\nfunction stringifyBody(body: Any, res: Any) {\n  const contentType = (res.headers['content-type'] || '').toLowerCase()\n  const isJson = contentType.indexOf('application/json') !== -1\n  return isJson ? JSON.stringify(body, null, 2) : body\n}\n\nfunction sliceWithEllipsis(str: string, max: number) {\n  return str.length > max ? `${str.slice(0, max)}…` : str\n}\n\n/** @public */\nexport class CorsOriginError extends Error {\n  projectId: string\n  addOriginUrl?: URL\n\n  constructor({projectId}: {projectId: string}) {\n    super('CorsOriginError')\n    this.name = 'CorsOriginError'\n    this.projectId = projectId\n\n    const url = new URL(`https://sanity.io/manage/project/${projectId}/api`)\n    if (typeof location !== 'undefined') {\n      const {origin} = location\n      url.searchParams.set('cors', 'add')\n      url.searchParams.set('origin', origin)\n      this.addOriginUrl = url\n      this.message = `The current origin is not allowed to connect to the Live Content API. Add it here: ${url}`\n    } else {\n      this.message = `The current origin is not allowed to connect to the Live Content API. Change your configuration here: ${url}`\n    }\n  }\n}\n", "import {getIt, type HttpContext, type Middlewares, type Requester} from 'get-it'\nimport {jsonRequest, jsonResponse, observable, progress, retry} from 'get-it/middleware'\nimport {Observable} from 'rxjs'\n\nimport type {Any} from '../types'\nimport {ClientError, ServerError} from './errors'\n\nconst httpError = {\n  onResponse: (res: Any, context: HttpContext) => {\n    if (res.statusCode >= 500) {\n      throw new ServerError(res)\n    } else if (res.statusCode >= 400) {\n      throw new ClientError(res, context)\n    }\n\n    return res\n  },\n}\n\nfunction printWarnings() {\n  const seen: Record<string, boolean> = {}\n  return {\n    onResponse: (res: Any) => {\n      const warn = res.headers['x-sanity-warning']\n      const warnings = Array.isArray(warn) ? warn : [warn]\n      for (const msg of warnings) {\n        if (!msg || seen[msg]) continue\n        seen[msg] = true\n        console.warn(msg) // eslint-disable-line no-console\n      }\n      return res\n    },\n  }\n}\n\n/** @internal */\nexport function defineHttpRequest(envMiddleware: Middlewares): Requester {\n  return getIt([\n    retry({shouldRetry}),\n    ...envMiddleware,\n    printWarnings(),\n    jsonRequest(),\n    jsonResponse(),\n    progress(),\n    httpError,\n    observable({implementation: Observable}),\n  ])\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction shouldRetry(err: any, attempt: number, options: any) {\n  // Allow opting out of retries\n  if (options.maxRetries === 0) return false\n\n  // By default `retry.shouldRetry` doesn't retry on server errors so we add our own logic.\n\n  const isSafe = options.method === 'GET' || options.method === 'HEAD'\n  const uri = options.uri || options.url\n  const isQuery = uri.startsWith('/data/query')\n  const isRetriableResponse =\n    err.response &&\n    (err.response.statusCode === 429 ||\n      err.response.statusCode === 502 ||\n      err.response.statusCode === 503)\n\n  // We retry the following errors:\n  // - 429 means that the request was rate limited. It's a bit difficult\n  //   to know exactly how long it makes sense to wait and/or how many\n  //   attempts we should retry, but the backoff should alleviate the\n  //   additional load.\n  // - 502/503 can occur when certain components struggle to talk to their\n  //   upstream dependencies. This is most likely a temporary problem\n  //   and retrying makes sense.\n\n  if ((isSafe || isQuery) && isRetriableResponse) return true\n\n  return retry.shouldRetry(err, attempt, options)\n}\n", "import {defer, isObservable, mergeMap, Observable, of} from 'rxjs'\n\nimport {formatQueryParseError, isQueryParseError} from '../http/errors'\nimport {type Any} from '../types'\n\n/**\n * @public\n * Thrown if the EventSource connection could not be established.\n * Note that ConnectionFailedErrors are rare, and disconnects will normally be handled by the EventSource instance itself and emitted as `reconnect` events.\n */\nexport class ConnectionFailedError extends Error {\n  readonly name = 'ConnectionFailedError'\n}\n\n/**\n * The listener has been told to explicitly disconnect.\n *  This is a rare situation, but may occur if the API knows reconnect attempts will fail,\n *  eg in the case of a deleted dataset, a blocked project or similar events.\n * @public\n */\nexport class DisconnectError extends Error {\n  readonly name = 'DisconnectError'\n  readonly reason?: string\n  constructor(message: string, reason?: string, options: ErrorOptions = {}) {\n    super(message, options)\n    this.reason = reason\n  }\n}\n\n/**\n * @public\n * The server sent a `channelError` message. Usually indicative of a bad or malformed request\n */\nexport class ChannelError extends Error {\n  readonly name = 'ChannelError'\n  readonly data?: unknown\n  constructor(message: string, data: unknown) {\n    super(message)\n    this.data = data\n  }\n}\n\n/**\n * @public\n * The server sent an `error`-event to tell the client that an unexpected error has happened.\n */\nexport class MessageError extends Error {\n  readonly name = 'MessageError'\n  readonly data?: unknown\n  constructor(message: string, data: unknown, options: ErrorOptions = {}) {\n    super(message, options)\n    this.data = data\n  }\n}\n\n/**\n * @public\n * An error occurred while parsing the message sent by the server as JSON. Should normally not happen.\n */\nexport class MessageParseError extends Error {\n  readonly name = 'MessageParseError'\n}\n\n/**\n * @public\n */\nexport interface ServerSentEvent<Name extends string> {\n  type: Name\n  id?: string\n  data?: unknown\n}\n\n// Always listen for these events, no matter what\nconst REQUIRED_EVENTS = ['channelError', 'disconnect']\n\n/**\n * @internal\n */\nexport type EventSourceEvent<Name extends string> = ServerSentEvent<Name>\n\n/**\n * @internal\n */\nexport type EventSourceInstance = InstanceType<typeof globalThis.EventSource>\n\n/**\n * Sanity API specific EventSource handler shared between the listen and live APIs\n *\n * Since the `EventSource` API is not provided by all environments, this function enables custom initialization of the EventSource instance\n * for runtimes that requires polyfilling or custom setup logic (e.g. custom HTTP headers)\n * via the passed `initEventSource` function which must return an EventSource instance.\n *\n * Possible errors to be thrown on the returned observable are:\n * - {@link MessageError}\n * - {@link MessageParseError}\n * - {@link ChannelError}\n * - {@link DisconnectError}\n * - {@link ConnectionFailedError}\n *\n * @param initEventSource - A function that returns an EventSource instance or an Observable that resolves to an EventSource instance\n * @param events - an array of named events from the API to listen for.\n *\n * @internal\n */\nexport function connectEventSource<EventName extends string>(\n  initEventSource: () => EventSourceInstance | Observable<EventSourceInstance>,\n  events: EventName[],\n) {\n  return defer(() => {\n    const es = initEventSource()\n    return isObservable(es) ? es : of(es)\n  }).pipe(mergeMap((es) => connectWithESInstance(es, events))) as Observable<\n    ServerSentEvent<EventName>\n  >\n}\n\n/**\n * Provides an observable from the passed EventSource instance, subscribing to the passed list of names of events types to listen for\n * Handles connection logic, adding/removing event listeners, payload parsing, error propagation, etc.\n *\n * @param es - The EventSource instance\n * @param events - List of event names to listen for\n */\nfunction connectWithESInstance<EventTypeName extends string>(\n  es: EventSourceInstance,\n  events: EventTypeName[],\n) {\n  return new Observable<EventSourceEvent<EventTypeName>>((observer) => {\n    const emitOpen = (events as string[]).includes('open')\n    const emitReconnect = (events as string[]).includes('reconnect')\n\n    // EventSource will emit a regular Event if it fails to connect, however the API may also emit an `error` MessageEvent\n    // So we need to handle both cases\n    function onError(evt: MessageEvent | Event) {\n      // If the event has a `data` property, then it`s a MessageEvent emitted by the API and we should forward the error\n      if ('data' in evt) {\n        const [parseError, event] = parseEvent(evt as MessageEvent)\n        observer.error(\n          parseError\n            ? new MessageParseError('Unable to parse EventSource error message', {cause: event})\n            : new MessageError((event?.data as {message: string}).message, event),\n        )\n        return\n      }\n\n      // We should never be in a disconnected state. By default, EventSource will reconnect\n      // automatically, but in some cases (like when a laptop lid is closed), it will trigger onError\n      // if it can't reconnect.\n      // see https://html.spec.whatwg.org/multipage/server-sent-events.html#sse-processing-model\n      if (es.readyState === es.CLOSED) {\n        // In these cases we'll signal to consumers (via the error path) that a retry/reconnect is needed.\n        observer.error(new ConnectionFailedError('EventSource connection failed'))\n      } else if (emitReconnect) {\n        observer.next({type: 'reconnect' as EventTypeName})\n      }\n    }\n\n    function onOpen() {\n      // The open event of the EventSource API is fired when a connection with an event source is opened.\n      observer.next({type: 'open' as EventTypeName})\n    }\n\n    function onMessage(message: MessageEvent) {\n      const [parseError, event] = parseEvent(message)\n      if (parseError) {\n        observer.error(\n          new MessageParseError('Unable to parse EventSource message', {cause: parseError}),\n        )\n        return\n      }\n      if (message.type === 'channelError') {\n        // An error occurred. This is different from a network-level error (which will be emitted as 'error').\n        // Possible causes are things such as malformed filters, non-existant datasets\n        // or similar.\n        const tag = new URL(es.url).searchParams.get('tag')\n        observer.error(new ChannelError(extractErrorMessage(event?.data, tag), event.data))\n        return\n      }\n      if (message.type === 'disconnect') {\n        // The listener has been told to explicitly disconnect and not reconnect.\n        // This is a rare situation, but may occur if the API knows reconnect attempts will fail,\n        // eg in the case of a deleted dataset, a blocked project or similar events.\n        observer.error(\n          new DisconnectError(\n            `Server disconnected client: ${\n              (event.data as {reason?: string})?.reason || 'unknown error'\n            }`,\n          ),\n        )\n        return\n      }\n      observer.next({\n        type: message.type as EventTypeName,\n        id: message.lastEventId,\n        ...(event.data ? {data: event.data} : {}),\n      })\n    }\n\n    es.addEventListener('error', onError)\n\n    if (emitOpen) {\n      es.addEventListener('open', onOpen)\n    }\n\n    // Make sure we have a unique list of events types to avoid listening multiple times,\n    const cleanedEvents = [...new Set([...REQUIRED_EVENTS, ...events])]\n      // filter out events that are handled separately\n      .filter((type) => type !== 'error' && type !== 'open' && type !== 'reconnect')\n\n    cleanedEvents.forEach((type: string) => es.addEventListener(type, onMessage))\n\n    return () => {\n      es.removeEventListener('error', onError)\n      if (emitOpen) {\n        es.removeEventListener('open', onOpen)\n      }\n      cleanedEvents.forEach((type: string) => es.removeEventListener(type, onMessage))\n      es.close()\n    }\n  })\n}\n\nfunction parseEvent(\n  message: MessageEvent,\n): [null, {type: string; id: string; data?: unknown}] | [Error, null] {\n  try {\n    const data = typeof message.data === 'string' && JSON.parse(message.data)\n    return [\n      null,\n      {\n        type: message.type,\n        id: message.lastEventId,\n        ...(isEmptyObject(data) ? {} : {data}),\n      },\n    ]\n  } catch (err) {\n    return [err as Error, null]\n  }\n}\n\nfunction extractErrorMessage(err: Any, tag?: string | null) {\n  const error = err.error\n\n  if (!error) {\n    return err.message || 'Unknown listener error'\n  }\n\n  if (isQueryParseError(error)) {\n    return formatQueryParseError(error, tag)\n  }\n\n  if (error.description) {\n    return error.description\n  }\n\n  return typeof error === 'string' ? error : JSON.stringify(error, null, 2)\n}\n\nfunction isEmptyObject(data: object) {\n  for (const _ in data) {\n    return false\n  }\n  return true\n}\n", "import type {MutationSelection} from '../types'\n\nexport function getSelection(sel: unknown): MutationSelection {\n  if (typeof sel === 'string') {\n    return {id: sel}\n  }\n\n  if (Array.isArray(sel)) {\n    return {query: '*[_id in $ids]', params: {ids: sel}}\n  }\n\n  if (typeof sel === 'object' && sel !== null && 'query' in sel && typeof sel.query === 'string') {\n    return 'params' in sel && typeof sel.params === 'object' && sel.params !== null\n      ? {query: sel.query, params: sel.params}\n      : {query: sel.query}\n  }\n\n  const selectionOpts = [\n    '* Document ID (<docId>)',\n    '* Array of document IDs',\n    '* Object containing `query`',\n  ].join('\\n')\n\n  throw new Error(`Unknown selection - must be one of:\\n\\n${selectionOpts}`)\n}\n", "import {type Observable} from 'rxjs'\n\nimport type {ObservableSanityClient, SanityClient} from '../SanityClient'\nimport type {\n  AllDocumentIdsMutationOptions,\n  AllDocumentsMutationOptions,\n  Any,\n  AttributeSet,\n  BaseMutationOptions,\n  FirstDocumentIdMutationOptions,\n  FirstDocumentMutationOptions,\n  MultipleMutationResult,\n  PatchMutationOperation,\n  PatchOperations,\n  PatchSelection,\n  SanityDocument,\n  SingleMutationResult,\n} from '../types'\nimport {getSelection} from '../util/getSelection'\nimport {validateInsert, validateObject} from '../validators'\n\n/** @internal */\nexport class BasePatch {\n  protected selection: PatchSelection\n  protected operations: PatchOperations\n  constructor(selection: PatchSelection, operations: PatchOperations = {}) {\n    this.selection = selection\n    this.operations = operations\n  }\n\n  /**\n   * Sets the given attributes to the document. Does NOT merge objects.\n   * The operation is added to the current patch, ready to be commited by `commit()`\n   *\n   * @param attrs - Attributes to set. To set a deep attribute, use JSONMatch, eg: \\{\"nested.prop\": \"value\"\\}\n   */\n  set(attrs: AttributeSet): this {\n    return this._assign('set', attrs)\n  }\n\n  /**\n   * Sets the given attributes to the document if they are not currently set. Does NOT merge objects.\n   * The operation is added to the current patch, ready to be commited by `commit()`\n   *\n   * @param attrs - Attributes to set. To set a deep attribute, use JSONMatch, eg: \\{\"nested.prop\": \"value\"\\}\n   */\n  setIfMissing(attrs: AttributeSet): this {\n    return this._assign('setIfMissing', attrs)\n  }\n\n  /**\n   * Performs a \"diff-match-patch\" operation on the string attributes provided.\n   * The operation is added to the current patch, ready to be commited by `commit()`\n   *\n   * @param attrs - Attributes to perform operation on. To set a deep attribute, use JSONMatch, eg: \\{\"nested.prop\": \"dmp\"\\}\n   */\n  diffMatchPatch(attrs: AttributeSet): this {\n    validateObject('diffMatchPatch', attrs)\n    return this._assign('diffMatchPatch', attrs)\n  }\n\n  /**\n   * Unsets the attribute paths provided.\n   * The operation is added to the current patch, ready to be commited by `commit()`\n   *\n   * @param attrs - Attribute paths to unset.\n   */\n  unset(attrs: string[]): this {\n    if (!Array.isArray(attrs)) {\n      throw new Error('unset(attrs) takes an array of attributes to unset, non-array given')\n    }\n\n    this.operations = Object.assign({}, this.operations, {unset: attrs})\n    return this\n  }\n\n  /**\n   * Increment a numeric value. Each entry in the argument is either an attribute or a JSON path. The value may be a positive or negative integer or floating-point value. The operation will fail if target value is not a numeric value, or doesn't exist.\n   *\n   * @param attrs - Object of attribute paths to increment, values representing the number to increment by.\n   */\n  inc(attrs: {[key: string]: number}): this {\n    return this._assign('inc', attrs)\n  }\n\n  /**\n   * Decrement a numeric value. Each entry in the argument is either an attribute or a JSON path. The value may be a positive or negative integer or floating-point value. The operation will fail if target value is not a numeric value, or doesn't exist.\n   *\n   * @param attrs - Object of attribute paths to decrement, values representing the number to decrement by.\n   */\n  dec(attrs: {[key: string]: number}): this {\n    return this._assign('dec', attrs)\n  }\n\n  /**\n   * Provides methods for modifying arrays, by inserting, appending and replacing elements via a JSONPath expression.\n   *\n   * @param at - Location to insert at, relative to the given selector, or 'replace' the matched path\n   * @param selector - JSONPath expression, eg `comments[-1]` or `blocks[_key==\"abc123\"]`\n   * @param items - Array of items to insert/replace\n   */\n  insert(at: 'before' | 'after' | 'replace', selector: string, items: Any[]): this {\n    validateInsert(at, selector, items)\n    return this._assign('insert', {[at]: selector, items})\n  }\n\n  /**\n   * Append the given items to the array at the given JSONPath\n   *\n   * @param selector - Attribute/path to append to, eg `comments` or `person.hobbies`\n   * @param items - Array of items to append to the array\n   */\n  append(selector: string, items: Any[]): this {\n    return this.insert('after', `${selector}[-1]`, items)\n  }\n\n  /**\n   * Prepend the given items to the array at the given JSONPath\n   *\n   * @param selector - Attribute/path to prepend to, eg `comments` or `person.hobbies`\n   * @param items - Array of items to prepend to the array\n   */\n  prepend(selector: string, items: Any[]): this {\n    return this.insert('before', `${selector}[0]`, items)\n  }\n\n  /**\n   * Change the contents of an array by removing existing elements and/or adding new elements.\n   *\n   * @param selector - Attribute or JSONPath expression for array\n   * @param start - Index at which to start changing the array (with origin 0). If greater than the length of the array, actual starting index will be set to the length of the array. If negative, will begin that many elements from the end of the array (with origin -1) and will be set to 0 if absolute value is greater than the length of the array.x\n   * @param deleteCount - An integer indicating the number of old array elements to remove.\n   * @param items - The elements to add to the array, beginning at the start index. If you don't specify any elements, splice() will only remove elements from the array.\n   */\n  splice(selector: string, start: number, deleteCount?: number, items?: Any[]): this {\n    // Negative indexes doesn't mean the same in Sanity as they do in JS;\n    // -1 means \"actually at the end of the array\", which allows inserting\n    // at the end of the array without knowing its length. We therefore have\n    // to substract negative indexes by one to match JS. If you want Sanity-\n    // behaviour, just use `insert('replace', selector, items)` directly\n    const delAll = typeof deleteCount === 'undefined' || deleteCount === -1\n    const startIndex = start < 0 ? start - 1 : start\n    const delCount = delAll ? -1 : Math.max(0, start + deleteCount)\n    const delRange = startIndex < 0 && delCount >= 0 ? '' : delCount\n    const rangeSelector = `${selector}[${startIndex}:${delRange}]`\n    return this.insert('replace', rangeSelector, items || [])\n  }\n\n  /**\n   * Adds a revision clause, preventing the document from being patched if the `_rev` property does not match the given value\n   *\n   * @param rev - Revision to lock the patch to\n   */\n  ifRevisionId(rev: string): this {\n    this.operations.ifRevisionID = rev\n    return this\n  }\n\n  /**\n   * Return a plain JSON representation of the patch\n   */\n  serialize(): PatchMutationOperation {\n    return {...getSelection(this.selection), ...this.operations}\n  }\n\n  /**\n   * Return a plain JSON representation of the patch\n   */\n  toJSON(): PatchMutationOperation {\n    return this.serialize()\n  }\n\n  /**\n   * Clears the patch of all operations\n   */\n  reset(): this {\n    this.operations = {}\n    return this\n  }\n\n  protected _assign(op: keyof PatchOperations, props: Any, merge = true): this {\n    validateObject(op, props)\n    this.operations = Object.assign({}, this.operations, {\n      [op]: Object.assign({}, (merge && this.operations[op]) || {}, props),\n    })\n    return this\n  }\n\n  protected _set(op: keyof PatchOperations, props: Any): this {\n    return this._assign(op, props, false)\n  }\n}\n\n/** @public */\nexport class ObservablePatch extends BasePatch {\n  #client?: ObservableSanityClient\n\n  constructor(\n    selection: PatchSelection,\n    operations?: PatchOperations,\n    client?: ObservableSanityClient,\n  ) {\n    super(selection, operations)\n    this.#client = client\n  }\n\n  /**\n   * Clones the patch\n   */\n  clone(): ObservablePatch {\n    return new ObservablePatch(this.selection, {...this.operations}, this.#client)\n  }\n\n  /**\n   * Commit the patch, returning an observable that produces the first patched document\n   *\n   * @param options - Options for the mutation operation\n   */\n  commit<R extends Record<string, Any> = Record<string, Any>>(\n    options: FirstDocumentMutationOptions,\n  ): Observable<SanityDocument<R>>\n  /**\n   * Commit the patch, returning an observable that produces an array of the mutated documents\n   *\n   * @param options - Options for the mutation operation\n   */\n  commit<R extends Record<string, Any> = Record<string, Any>>(\n    options: AllDocumentsMutationOptions,\n  ): Observable<SanityDocument<R>[]>\n  /**\n   * Commit the patch, returning an observable that produces a mutation result object\n   *\n   * @param options - Options for the mutation operation\n   */\n  commit(options: FirstDocumentIdMutationOptions): Observable<SingleMutationResult>\n  /**\n   * Commit the patch, returning an observable that produces a mutation result object\n   *\n   * @param options - Options for the mutation operation\n   */\n  commit(options: AllDocumentIdsMutationOptions): Observable<MultipleMutationResult>\n  /**\n   * Commit the patch, returning an observable that produces the first patched document\n   *\n   * @param options - Options for the mutation operation\n   */\n  commit<R extends Record<string, Any> = Record<string, Any>>(\n    options?: BaseMutationOptions,\n  ): Observable<SanityDocument<R>>\n  commit<R extends Record<string, Any> = Record<string, Any>>(\n    options?:\n      | FirstDocumentMutationOptions\n      | AllDocumentsMutationOptions\n      | FirstDocumentIdMutationOptions\n      | AllDocumentIdsMutationOptions\n      | BaseMutationOptions,\n  ): Observable<\n    SanityDocument<R> | SanityDocument<R>[] | SingleMutationResult | MultipleMutationResult\n  > {\n    if (!this.#client) {\n      throw new Error(\n        'No `client` passed to patch, either provide one or pass the ' +\n          'patch to a clients `mutate()` method',\n      )\n    }\n\n    const returnFirst = typeof this.selection === 'string'\n    const opts = Object.assign({returnFirst, returnDocuments: true}, options)\n    return this.#client.mutate<R>({patch: this.serialize()} as Any, opts)\n  }\n}\n\n/** @public */\nexport class Patch extends BasePatch {\n  #client?: SanityClient\n  constructor(selection: PatchSelection, operations?: PatchOperations, client?: SanityClient) {\n    super(selection, operations)\n    this.#client = client\n  }\n\n  /**\n   * Clones the patch\n   */\n  clone(): Patch {\n    return new Patch(this.selection, {...this.operations}, this.#client)\n  }\n\n  /**\n   * Commit the patch, returning a promise that resolves to the first patched document\n   *\n   * @param options - Options for the mutation operation\n   */\n  commit<R extends Record<string, Any> = Record<string, Any>>(\n    options: FirstDocumentMutationOptions,\n  ): Promise<SanityDocument<R>>\n  /**\n   * Commit the patch, returning a promise that resolves to an array of the mutated documents\n   *\n   * @param options - Options for the mutation operation\n   */\n  commit<R extends Record<string, Any> = Record<string, Any>>(\n    options: AllDocumentsMutationOptions,\n  ): Promise<SanityDocument<R>[]>\n  /**\n   * Commit the patch, returning a promise that resolves to a mutation result object\n   *\n   * @param options - Options for the mutation operation\n   */\n  commit(options: FirstDocumentIdMutationOptions): Promise<SingleMutationResult>\n  /**\n   * Commit the patch, returning a promise that resolves to a mutation result object\n   *\n   * @param options - Options for the mutation operation\n   */\n  commit(options: AllDocumentIdsMutationOptions): Promise<MultipleMutationResult>\n  /**\n   * Commit the patch, returning a promise that resolves to the first patched document\n   *\n   * @param options - Options for the mutation operation\n   */\n  commit<R extends Record<string, Any> = Record<string, Any>>(\n    options?: BaseMutationOptions,\n  ): Promise<SanityDocument<R>>\n  commit<R extends Record<string, Any> = Record<string, Any>>(\n    options?:\n      | FirstDocumentMutationOptions\n      | AllDocumentsMutationOptions\n      | FirstDocumentIdMutationOptions\n      | AllDocumentIdsMutationOptions\n      | BaseMutationOptions,\n  ): Promise<\n    SanityDocument<R> | SanityDocument<R>[] | SingleMutationResult | MultipleMutationResult\n  > {\n    if (!this.#client) {\n      throw new Error(\n        'No `client` passed to patch, either provide one or pass the ' +\n          'patch to a clients `mutate()` method',\n      )\n    }\n\n    const returnFirst = typeof this.selection === 'string'\n    const opts = Object.assign({returnFirst, returnDocuments: true}, options)\n    return this.#client.mutate<R>({patch: this.serialize()} as Any, opts)\n  }\n}\n", "import type {Observable} from 'rxjs'\n\nimport type {ObservableSanityClient, SanityClient} from '../SanityClient'\nimport type {\n  Any,\n  BaseMutationOptions,\n  IdentifiedSanityDocumentStub,\n  MultipleMutationResult,\n  Mutation,\n  MutationSelection,\n  PatchOperations,\n  SanityDocument,\n  SanityDocumentStub,\n  SingleMutationResult,\n  TransactionAllDocumentIdsMutationOptions,\n  TransactionAllDocumentsMutationOptions,\n  TransactionFirstDocumentIdMutationOptions,\n  TransactionFirstDocumentMutationOptions,\n} from '../types'\nimport * as validators from '../validators'\nimport {ObservablePatch, Patch} from './patch'\n\n/** @public */\nexport type PatchBuilder = (patch: Patch) => Patch\n/** @public */\nexport type ObservablePatchBuilder = (patch: ObservablePatch) => ObservablePatch\n\nconst defaultMutateOptions = {returnDocuments: false}\n\n/** @internal */\nexport class BaseTransaction {\n  protected operations: Mutation[]\n  protected trxId?: string\n  constructor(operations: Mutation[] = [], transactionId?: string) {\n    this.operations = operations\n    this.trxId = transactionId\n  }\n  /**\n   * Creates a new Sanity document. If `_id` is provided and already exists, the mutation will fail. If no `_id` is given, one will automatically be generated by the database.\n   * The operation is added to the current transaction, ready to be commited by `commit()`\n   *\n   * @param doc - Document to create. Requires a `_type` property.\n   */\n  create<R extends Record<string, Any> = Record<string, Any>>(doc: SanityDocumentStub<R>): this {\n    validators.validateObject('create', doc)\n    return this._add({create: doc})\n  }\n\n  /**\n   * Creates a new Sanity document. If a document with the same `_id` already exists, the create operation will be ignored.\n   * The operation is added to the current transaction, ready to be commited by `commit()`\n   *\n   * @param doc - Document to create if it does not already exist. Requires `_id` and `_type` properties.\n   */\n  createIfNotExists<R extends Record<string, Any> = Record<string, Any>>(\n    doc: IdentifiedSanityDocumentStub<R>,\n  ): this {\n    const op = 'createIfNotExists'\n    validators.validateObject(op, doc)\n    validators.requireDocumentId(op, doc)\n    return this._add({[op]: doc})\n  }\n\n  /**\n   * Creates a new Sanity document, or replaces an existing one if the same `_id` is already used.\n   * The operation is added to the current transaction, ready to be commited by `commit()`\n   *\n   * @param doc - Document to create or replace. Requires `_id` and `_type` properties.\n   */\n  createOrReplace<R extends Record<string, Any> = Record<string, Any>>(\n    doc: IdentifiedSanityDocumentStub<R>,\n  ): this {\n    const op = 'createOrReplace'\n    validators.validateObject(op, doc)\n    validators.requireDocumentId(op, doc)\n    return this._add({[op]: doc})\n  }\n\n  /**\n   * Deletes the document with the given document ID\n   * The operation is added to the current transaction, ready to be commited by `commit()`\n   *\n   * @param documentId - Document ID to delete\n   */\n  delete(documentId: string): this {\n    validators.validateDocumentId('delete', documentId)\n    return this._add({delete: {id: documentId}})\n  }\n\n  /**\n   * Gets the current transaction ID, if any\n   */\n  transactionId(): string | undefined\n  /**\n   * Set the ID of this transaction.\n   *\n   * @param id - Transaction ID\n   */\n  transactionId(id: string): this\n  transactionId(id?: string): this | string | undefined {\n    if (!id) {\n      return this.trxId\n    }\n\n    this.trxId = id\n    return this\n  }\n\n  /**\n   * Return a plain JSON representation of the transaction\n   */\n  serialize(): Mutation[] {\n    return [...this.operations]\n  }\n\n  /**\n   * Return a plain JSON representation of the transaction\n   */\n  toJSON(): Mutation[] {\n    return this.serialize()\n  }\n\n  /**\n   * Clears the transaction of all operations\n   */\n  reset(): this {\n    this.operations = []\n    return this\n  }\n\n  protected _add(mut: Mutation): this {\n    this.operations.push(mut)\n    return this\n  }\n}\n\n/** @public */\nexport class Transaction extends BaseTransaction {\n  #client?: SanityClient\n  constructor(operations?: Mutation[], client?: SanityClient, transactionId?: string) {\n    super(operations, transactionId)\n    this.#client = client\n  }\n\n  /**\n   * Clones the transaction\n   */\n  clone(): Transaction {\n    return new Transaction([...this.operations], this.#client, this.trxId)\n  }\n\n  /**\n   * Commit the transaction, returning a promise that resolves to the first mutated document\n   *\n   * @param options - Options for the mutation operation\n   */\n  commit<R extends Record<string, Any>>(\n    options: TransactionFirstDocumentMutationOptions,\n  ): Promise<SanityDocument<R>>\n  /**\n   * Commit the transaction, returning a promise that resolves to an array of the mutated documents\n   *\n   * @param options - Options for the mutation operation\n   */\n  commit<R extends Record<string, Any>>(\n    options: TransactionAllDocumentsMutationOptions,\n  ): Promise<SanityDocument<R>[]>\n  /**\n   * Commit the transaction, returning a promise that resolves to a mutation result object\n   *\n   * @param options - Options for the mutation operation\n   */\n  commit(options: TransactionFirstDocumentIdMutationOptions): Promise<SingleMutationResult>\n  /**\n   * Commit the transaction, returning a promise that resolves to a mutation result object\n   *\n   * @param options - Options for the mutation operation\n   */\n  commit(options: TransactionAllDocumentIdsMutationOptions): Promise<MultipleMutationResult>\n  /**\n   * Commit the transaction, returning a promise that resolves to a mutation result object\n   *\n   * @param options - Options for the mutation operation\n   */\n  commit(options?: BaseMutationOptions): Promise<MultipleMutationResult>\n  commit<R extends Record<string, Any> = Record<string, Any>>(\n    options?:\n      | TransactionFirstDocumentMutationOptions\n      | TransactionAllDocumentsMutationOptions\n      | TransactionFirstDocumentIdMutationOptions\n      | TransactionAllDocumentIdsMutationOptions\n      | BaseMutationOptions,\n  ): Promise<\n    SanityDocument<R> | SanityDocument<R>[] | SingleMutationResult | MultipleMutationResult\n  > {\n    if (!this.#client) {\n      throw new Error(\n        'No `client` passed to transaction, either provide one or pass the ' +\n          'transaction to a clients `mutate()` method',\n      )\n    }\n\n    return this.#client.mutate<R>(\n      this.serialize() as Any,\n      Object.assign({transactionId: this.trxId}, defaultMutateOptions, options || {}),\n    )\n  }\n\n  /**\n   * Performs a patch on the given document ID. Can either be a builder function or an object of patch operations.\n   * The operation is added to the current transaction, ready to be commited by `commit()`\n   *\n   * @param documentId - Document ID to perform the patch operation on\n   * @param patchOps - Operations to perform, or a builder function\n   */\n  patch(documentId: string, patchOps?: PatchBuilder | PatchOperations): this\n  /**\n   * Performs a patch on the given selection. Can either be a builder function or an object of patch operations.\n   *\n   * @param selection - An object with `query` and optional `params`, defining which document(s) to patch\n   * @param patchOps - Operations to perform, or a builder function\n   */\n  patch(patch: MutationSelection, patchOps?: PatchBuilder | PatchOperations): this\n  /**\n   * Adds the given patch instance to the transaction.\n   * The operation is added to the current transaction, ready to be commited by `commit()`\n   *\n   * @param patch - Patch to execute\n   */\n  patch(patch: Patch): this\n  patch(\n    patchOrDocumentId: Patch | MutationSelection | string,\n    patchOps?: PatchBuilder | PatchOperations,\n  ): this {\n    const isBuilder = typeof patchOps === 'function'\n    const isPatch = typeof patchOrDocumentId !== 'string' && patchOrDocumentId instanceof Patch\n    const isMutationSelection =\n      typeof patchOrDocumentId === 'object' &&\n      ('query' in patchOrDocumentId || 'id' in patchOrDocumentId)\n\n    // transaction.patch(client.patch('documentId').inc({visits: 1}))\n    if (isPatch) {\n      return this._add({patch: patchOrDocumentId.serialize()})\n    }\n\n    // patch => patch.inc({visits: 1}).set({foo: 'bar'})\n    if (isBuilder) {\n      const patch = patchOps(new Patch(patchOrDocumentId, {}, this.#client))\n      if (!(patch instanceof Patch)) {\n        throw new Error('function passed to `patch()` must return the patch')\n      }\n\n      return this._add({patch: patch.serialize()})\n    }\n\n    /*\n     * transaction.patch(\n     *   {query: \"*[_type == 'person' && points >= $threshold]\", params: { threshold: 100 }},\n     *   {dec: { points: 100 }, inc: { bonuses: 1 }}\n     * )\n     */\n    if (isMutationSelection) {\n      const patch = new Patch(patchOrDocumentId, patchOps || {}, this.#client)\n      return this._add({patch: patch.serialize()})\n    }\n\n    return this._add({patch: {id: patchOrDocumentId, ...patchOps}})\n  }\n}\n\n/** @public */\nexport class ObservableTransaction extends BaseTransaction {\n  #client?: ObservableSanityClient\n  constructor(operations?: Mutation[], client?: ObservableSanityClient, transactionId?: string) {\n    super(operations, transactionId)\n    this.#client = client\n  }\n\n  /**\n   * Clones the transaction\n   */\n  clone(): ObservableTransaction {\n    return new ObservableTransaction([...this.operations], this.#client, this.trxId)\n  }\n\n  /**\n   * Commit the transaction, returning an observable that produces the first mutated document\n   *\n   * @param options - Options for the mutation operation\n   */\n  commit<R extends Record<string, Any>>(\n    options: TransactionFirstDocumentMutationOptions,\n  ): Observable<SanityDocument<R>>\n  /**\n   * Commit the transaction, returning an observable that produces an array of the mutated documents\n   *\n   * @param options - Options for the mutation operation\n   */\n  commit<R extends Record<string, Any>>(\n    options: TransactionAllDocumentsMutationOptions,\n  ): Observable<SanityDocument<R>[]>\n  /**\n   * Commit the transaction, returning an observable that produces a mutation result object\n   *\n   * @param options - Options for the mutation operation\n   */\n  commit(options: TransactionFirstDocumentIdMutationOptions): Observable<SingleMutationResult>\n  /**\n   * Commit the transaction, returning an observable that produces a mutation result object\n   *\n   * @param options - Options for the mutation operation\n   */\n  commit(options: TransactionAllDocumentIdsMutationOptions): Observable<MultipleMutationResult>\n  /**\n   * Commit the transaction, returning an observable that produces a mutation result object\n   *\n   * @param options - Options for the mutation operation\n   */\n  commit(options?: BaseMutationOptions): Observable<MultipleMutationResult>\n  commit<R extends Record<string, Any> = Record<string, Any>>(\n    options?:\n      | TransactionFirstDocumentMutationOptions\n      | TransactionAllDocumentsMutationOptions\n      | TransactionFirstDocumentIdMutationOptions\n      | TransactionAllDocumentIdsMutationOptions\n      | BaseMutationOptions,\n  ): Observable<\n    SanityDocument<R> | SanityDocument<R>[] | SingleMutationResult | MultipleMutationResult\n  > {\n    if (!this.#client) {\n      throw new Error(\n        'No `client` passed to transaction, either provide one or pass the ' +\n          'transaction to a clients `mutate()` method',\n      )\n    }\n\n    return this.#client.mutate<R>(\n      this.serialize() as Any,\n      Object.assign({transactionId: this.trxId}, defaultMutateOptions, options || {}),\n    )\n  }\n\n  /**\n   * Performs a patch on the given document ID. Can either be a builder function or an object of patch operations.\n   * The operation is added to the current transaction, ready to be commited by `commit()`\n   *\n   * @param documentId - Document ID to perform the patch operation on\n   * @param patchOps - Operations to perform, or a builder function\n   */\n  patch(documentId: string, patchOps?: ObservablePatchBuilder | PatchOperations): this\n  /**\n   * Adds the given patch instance to the transaction.\n   * The operation is added to the current transaction, ready to be commited by `commit()`\n   *\n   * @param patch - ObservablePatch to execute\n   */\n  patch(patch: ObservablePatch): this\n  patch(\n    patchOrDocumentId: ObservablePatch | string,\n    patchOps?: ObservablePatchBuilder | PatchOperations,\n  ): this {\n    const isBuilder = typeof patchOps === 'function'\n    const isPatch =\n      typeof patchOrDocumentId !== 'string' && patchOrDocumentId instanceof ObservablePatch\n\n    // transaction.patch(client.patch('documentId').inc({visits: 1}))\n    if (isPatch) {\n      return this._add({patch: patchOrDocumentId.serialize()})\n    }\n\n    // patch => patch.inc({visits: 1}).set({foo: 'bar'})\n    if (isBuilder) {\n      const patch = patchOps(new ObservablePatch(patchOrDocumentId, {}, this.#client))\n      if (!(patch instanceof ObservablePatch)) {\n        throw new Error('function passed to `patch()` must return the patch')\n      }\n\n      return this._add({patch: patch.serialize()})\n    }\n\n    return this._add({patch: {id: patchOrDocumentId, ...patchOps}})\n  }\n}\n", "import type {RequestOptions} from 'get-it'\n\nimport type {Any} from '../types'\n\nconst projectHeader = 'X-Sanity-Project-ID'\n\nexport function requestOptions(config: Any, overrides: Any = {}): Omit<RequestOptions, 'url'> {\n  const headers: Any = {}\n\n  if (config.headers) {\n    Object.assign(headers, config.headers)\n  }\n\n  const token = overrides.token || config.token\n  if (token) {\n    headers.Authorization = `Bearer ${token}`\n  }\n\n  if (!overrides.useGlobalApi && !config.useProjectHostname && config.projectId) {\n    headers[projectHeader] = config.projectId\n  }\n\n  const withCredentials = Boolean(\n    typeof overrides.withCredentials === 'undefined'\n      ? config.withCredentials\n      : overrides.withCredentials,\n  )\n\n  const timeout = typeof overrides.timeout === 'undefined' ? config.timeout : overrides.timeout\n  return Object.assign({}, overrides, {\n    headers: Object.assign({}, headers, overrides.headers || {}),\n    timeout: typeof timeout === 'undefined' ? 5 * 60 * 1000 : timeout,\n    proxy: overrides.proxy || config.proxy,\n    json: true,\n    withCredentials,\n    fetch:\n      typeof overrides.fetch === 'object' && typeof config.fetch === 'object'\n        ? {...config.fetch, ...overrides.fetch}\n        : overrides.fetch || config.fetch,\n  })\n}\n", "import type {Any, ListenParams, QueryParams} from '../types'\n\nexport const encodeQueryString = ({\n  query,\n  params = {},\n  options = {},\n}: {\n  query: string\n  params?: ListenParams | QueryParams\n  options?: Any\n}) => {\n  const searchParams = new URLSearchParams()\n  // We generally want tag at the start of the query string\n  const {tag, includeMutations, returnQuery, ...opts} = options\n  // We're using `append` instead of `set` to support React Native: https://github.com/facebook/react-native/blob/1982c4722fcc51aa87e34cf562672ee4aff540f1/packages/react-native/Libraries/Blob/URL.js#L86-L88\n  if (tag) searchParams.append('tag', tag)\n  searchParams.append('query', query)\n\n  // Iterate params, the keys are prefixed with `$` and their values JSON stringified\n  for (const [key, value] of Object.entries(params)) {\n    if (value !== undefined) searchParams.append(`$${key}`, JSON.stringify(value))\n  }\n  // Options are passed as-is\n  for (const [key, value] of Object.entries(opts)) {\n    // Skip falsy values\n    if (value) searchParams.append(key, `${value}`)\n  }\n\n  // `returnQuery` is default `true`, so needs an explicit `false` handling\n  if (returnQuery === false) searchParams.append('returnQuery', 'false')\n\n  // `includeMutations` is default `true`, so needs an explicit `false` handling\n  if (includeMutations === false) searchParams.append('includeMutations', 'false')\n\n  return `?${searchParams}`\n}\n", "import {getVersionFromId, getVersionId, isDraftId} from '@sanity/client/csm'\nimport {from, type MonoTypeOperatorFunction, Observable} from 'rxjs'\nimport {combineLatestWith, filter, map} from 'rxjs/operators'\n\nimport {validateApiPerspective} from '../config'\nimport {requestOptions} from '../http/requestOptions'\nimport type {ObservableSanityClient, SanityClient} from '../SanityClient'\nimport {stegaClean} from '../stega/stegaClean'\nimport type {\n  Action,\n  AllDocumentIdsMutationOptions,\n  AllDocumentsMutationOptions,\n  Any,\n  BaseActionOptions,\n  BaseMutationOptions,\n  CreateVersionAction,\n  DiscardVersionAction,\n  FirstDocumentIdMutationOptions,\n  FirstDocumentMutationOptions,\n  HttpRequest,\n  HttpRequestEvent,\n  IdentifiedSanityDocumentStub,\n  InitializedClientConfig,\n  InitializedStegaConfig,\n  MultipleActionResult,\n  MultipleMutationResult,\n  Mutation,\n  MutationSelection,\n  QueryOptions,\n  RawQueryResponse,\n  ReplaceVersionAction,\n  RequestObservableOptions,\n  RequestOptions,\n  SanityDocument,\n  SingleActionResult,\n  SingleMutationResult,\n  UnpublishVersionAction,\n} from '../types'\nimport {getSelection} from '../util/getSelection'\nimport * as validate from '../validators'\nimport * as validators from '../validators'\nimport {printCdnPreviewDraftsWarning, printPreviewDraftsDeprecationWarning} from '../warnings'\nimport {encodeQueryString} from './encodeQueryString'\nimport {ObservablePatch, Patch} from './patch'\nimport {ObservableTransaction, Transaction} from './transaction'\n\ntype Client = SanityClient | ObservableSanityClient\n\nconst excludeFalsey = (param: Any, defValue: Any) => {\n  const value = typeof param === 'undefined' ? defValue : param\n  return param === false ? undefined : value\n}\n\nconst getMutationQuery = (options: BaseMutationOptions = {}) => {\n  return {\n    dryRun: options.dryRun,\n    returnIds: true,\n    returnDocuments: excludeFalsey(options.returnDocuments, true),\n    visibility: options.visibility || 'sync',\n    autoGenerateArrayKeys: options.autoGenerateArrayKeys,\n    skipCrossDatasetReferenceValidation: options.skipCrossDatasetReferenceValidation,\n  }\n}\n\nconst isResponse = (event: Any) => event.type === 'response'\nconst getBody = (event: Any) => event.body\n\nconst indexBy = (docs: Any[], attr: Any) =>\n  docs.reduce((indexed, doc) => {\n    indexed[attr(doc)] = doc\n    return indexed\n  }, Object.create(null))\n\nconst getQuerySizeLimit = 11264\n\n/** @internal */\nexport function _fetch<R, Q>(\n  client: Client,\n  httpRequest: HttpRequest,\n  _stega: InitializedStegaConfig,\n  query: string,\n  _params: Q = {} as Q,\n  options: QueryOptions = {},\n): Observable<RawQueryResponse<R> | R> {\n  const stega =\n    'stega' in options\n      ? {\n          ...(_stega || {}),\n          ...(typeof options.stega === 'boolean' ? {enabled: options.stega} : options.stega || {}),\n        }\n      : _stega\n  const params = stega.enabled ? stegaClean(_params) : _params\n  const mapResponse =\n    options.filterResponse === false ? (res: Any) => res : (res: Any) => res.result\n\n  const {cache, next, ...opts} = {\n    // Opt out of setting a `signal` on an internal `fetch` if one isn't provided.\n    // This is necessary in React Server Components to avoid opting out of Request Memoization.\n    useAbortSignal: typeof options.signal !== 'undefined',\n    // Set `resultSourceMap' when stega is enabled, as it's required for encoding.\n    resultSourceMap: stega.enabled ? 'withKeyArraySelector' : options.resultSourceMap,\n    ...options,\n    // Default to not returning the query, unless `filterResponse` is `false`,\n    // or `returnQuery` is explicitly set. `true` is the default in Content Lake, so skip if truthy\n    returnQuery: options.filterResponse === false && options.returnQuery !== false,\n  }\n  const reqOpts =\n    typeof cache !== 'undefined' || typeof next !== 'undefined'\n      ? {...opts, fetch: {cache, next}}\n      : opts\n\n  const $request = _dataRequest(client, httpRequest, 'query', {query, params}, reqOpts)\n  return stega.enabled\n    ? $request.pipe(\n        combineLatestWith(\n          from(\n            import('../stega/stegaEncodeSourceMap').then(\n              ({stegaEncodeSourceMap}) => stegaEncodeSourceMap,\n            ),\n          ),\n        ),\n        map(\n          ([res, stegaEncodeSourceMap]: [\n            Any,\n            (typeof import('../stega/stegaEncodeSourceMap'))['stegaEncodeSourceMap'],\n          ]) => {\n            const result = stegaEncodeSourceMap(res.result, res.resultSourceMap, stega)\n            return mapResponse({...res, result})\n          },\n        ),\n      )\n    : $request.pipe(map(mapResponse))\n}\n\n/** @internal */\nexport function _getDocument<R extends Record<string, Any>>(\n  client: Client,\n  httpRequest: HttpRequest,\n  id: string,\n  opts: {signal?: AbortSignal; tag?: string; releaseId?: string} = {},\n): Observable<SanityDocument<R> | undefined> {\n  const getDocId = () => {\n    if (!opts.releaseId) {\n      return id\n    }\n\n    const versionId = getVersionFromId(id)\n    if (!versionId) {\n      if (isDraftId(id)) {\n        throw new Error(\n          `The document ID (\\`${id}\\`) is a draft, but \\`options.releaseId\\` is set as \\`${opts.releaseId}\\``,\n        )\n      }\n\n      return getVersionId(id, opts.releaseId)\n    }\n\n    if (versionId !== opts.releaseId) {\n      throw new Error(\n        `The document ID (\\`${id}\\`) is already a version of \\`${versionId}\\` release, but this does not match the provided \\`options.releaseId\\` (\\`${opts.releaseId}\\`)`,\n      )\n    }\n\n    return id\n  }\n  const docId = getDocId()\n\n  const options = {\n    uri: _getDataUrl(client, 'doc', docId),\n    json: true,\n    tag: opts.tag,\n    signal: opts.signal,\n  }\n  return _requestObservable<SanityDocument<R> | undefined>(client, httpRequest, options).pipe(\n    filter(isResponse),\n    map((event) => event.body.documents && event.body.documents[0]),\n  )\n}\n\n/** @internal */\nexport function _getDocuments<R extends Record<string, Any>>(\n  client: Client,\n  httpRequest: HttpRequest,\n  ids: string[],\n  opts: {signal?: AbortSignal; tag?: string} = {},\n): Observable<(SanityDocument<R> | null)[]> {\n  const options = {\n    uri: _getDataUrl(client, 'doc', ids.join(',')),\n    json: true,\n    tag: opts.tag,\n    signal: opts.signal,\n  }\n  return _requestObservable<(SanityDocument<R> | null)[]>(client, httpRequest, options).pipe(\n    filter(isResponse),\n    map((event: Any) => {\n      const indexed = indexBy(event.body.documents || [], (doc: Any) => doc._id)\n      return ids.map((id) => indexed[id] || null)\n    }),\n  )\n}\n\n/** @internal */\nexport function _getReleaseDocuments<R extends Record<string, Any>>(\n  client: ObservableSanityClient | SanityClient,\n  httpRequest: HttpRequest,\n  releaseId: string,\n  opts: BaseMutationOptions = {},\n): Observable<RawQueryResponse<SanityDocument<R>[]>> {\n  return _dataRequest(\n    client,\n    httpRequest,\n    'query',\n    {\n      query: '*[sanity::partOfRelease($releaseId)]',\n      params: {\n        releaseId,\n      },\n    },\n    opts,\n  )\n}\n\n/** @internal */\nexport function _createIfNotExists<R extends Record<string, Any>>(\n  client: Client,\n  httpRequest: HttpRequest,\n  doc: IdentifiedSanityDocumentStub<R>,\n  options?:\n    | AllDocumentIdsMutationOptions\n    | AllDocumentsMutationOptions\n    | BaseMutationOptions\n    | FirstDocumentIdMutationOptions\n    | FirstDocumentMutationOptions,\n): Observable<\n  SanityDocument<R> | SanityDocument<R>[] | SingleMutationResult | MultipleMutationResult\n> {\n  validators.requireDocumentId('createIfNotExists', doc)\n  return _create<R>(client, httpRequest, doc, 'createIfNotExists', options)\n}\n\n/** @internal */\nexport function _createOrReplace<R extends Record<string, Any>>(\n  client: Client,\n  httpRequest: HttpRequest,\n  doc: IdentifiedSanityDocumentStub<R>,\n  options?:\n    | AllDocumentIdsMutationOptions\n    | AllDocumentsMutationOptions\n    | BaseMutationOptions\n    | FirstDocumentIdMutationOptions\n    | FirstDocumentMutationOptions,\n): Observable<\n  SanityDocument<R> | SanityDocument<R>[] | SingleMutationResult | MultipleMutationResult\n> {\n  validators.requireDocumentId('createOrReplace', doc)\n  return _create<R>(client, httpRequest, doc, 'createOrReplace', options)\n}\n\n/** @internal */\nexport function _createVersion<R extends Record<string, Any>>(\n  client: ObservableSanityClient | SanityClient,\n  httpRequest: HttpRequest,\n  doc: IdentifiedSanityDocumentStub<R>,\n  publishedId: string,\n  options?: BaseActionOptions,\n): Observable<SingleActionResult> {\n  validators.requireDocumentId('createVersion', doc)\n  validators.requireDocumentType('createVersion', doc)\n\n  const createVersionAction: CreateVersionAction = {\n    actionType: 'sanity.action.document.version.create',\n    publishedId,\n    document: doc,\n  }\n\n  return _action(client, httpRequest, createVersionAction, options)\n}\n\n/** @internal */\nexport function _delete<R extends Record<string, Any>>(\n  client: Client,\n  httpRequest: HttpRequest,\n  selection: string | MutationSelection,\n  options?:\n    | AllDocumentIdsMutationOptions\n    | AllDocumentsMutationOptions\n    | BaseMutationOptions\n    | FirstDocumentIdMutationOptions\n    | FirstDocumentMutationOptions,\n): Observable<\n  SanityDocument<R> | SanityDocument<R>[] | SingleMutationResult | MultipleMutationResult\n> {\n  return _dataRequest(\n    client,\n    httpRequest,\n    'mutate',\n    {mutations: [{delete: getSelection(selection)}]},\n    options,\n  )\n}\n\n/** @internal */\nexport function _discardVersion(\n  client: ObservableSanityClient | SanityClient,\n  httpRequest: HttpRequest,\n  versionId: string,\n  purge: boolean = false,\n  options?: BaseActionOptions,\n): Observable<SingleActionResult> {\n  const discardVersionAction: DiscardVersionAction = {\n    actionType: 'sanity.action.document.version.discard',\n    versionId,\n    purge,\n  }\n\n  return _action(client, httpRequest, discardVersionAction, options)\n}\n\n/** @internal */\nexport function _replaceVersion<R extends Record<string, Any>>(\n  client: ObservableSanityClient | SanityClient,\n  httpRequest: HttpRequest,\n  doc: IdentifiedSanityDocumentStub<R>,\n  options?: BaseActionOptions,\n): Observable<SingleActionResult> {\n  validators.requireDocumentId('replaceVersion', doc)\n  validators.requireDocumentType('replaceVersion', doc)\n\n  const replaceVersionAction: ReplaceVersionAction = {\n    actionType: 'sanity.action.document.version.replace',\n    document: doc,\n  }\n\n  return _action(client, httpRequest, replaceVersionAction, options)\n}\n\n/** @internal */\nexport function _unpublishVersion(\n  client: ObservableSanityClient | SanityClient,\n  httpRequest: HttpRequest,\n  versionId: string,\n  publishedId: string,\n  options?: BaseActionOptions,\n): Observable<SingleActionResult> {\n  const unpublishVersionAction: UnpublishVersionAction = {\n    actionType: 'sanity.action.document.version.unpublish',\n    versionId,\n    publishedId,\n  }\n\n  return _action(client, httpRequest, unpublishVersionAction, options)\n}\n\n/** @internal */\nexport function _mutate<R extends Record<string, Any>>(\n  client: Client,\n  httpRequest: HttpRequest,\n  mutations: Mutation<R>[] | Patch | ObservablePatch | Transaction | ObservableTransaction,\n  options?:\n    | AllDocumentIdsMutationOptions\n    | AllDocumentsMutationOptions\n    | BaseMutationOptions\n    | FirstDocumentIdMutationOptions\n    | FirstDocumentMutationOptions,\n): Observable<\n  SanityDocument<R> | SanityDocument<R>[] | SingleMutationResult | MultipleMutationResult\n> {\n  let mut: Mutation | Mutation[]\n  if (mutations instanceof Patch || mutations instanceof ObservablePatch) {\n    mut = {patch: mutations.serialize()}\n  } else if (mutations instanceof Transaction || mutations instanceof ObservableTransaction) {\n    mut = mutations.serialize()\n  } else {\n    mut = mutations\n  }\n\n  const muts = Array.isArray(mut) ? mut : [mut]\n  const transactionId = (options && options.transactionId) || undefined\n  return _dataRequest(client, httpRequest, 'mutate', {mutations: muts, transactionId}, options)\n}\n\n/**\n * @internal\n */\nexport function _action(\n  client: Client,\n  httpRequest: HttpRequest,\n  actions: Action | Action[],\n  options?: BaseActionOptions,\n): Observable<SingleActionResult | MultipleActionResult> {\n  const acts = Array.isArray(actions) ? actions : [actions]\n  const transactionId = (options && options.transactionId) || undefined\n  const skipCrossDatasetReferenceValidation =\n    (options && options.skipCrossDatasetReferenceValidation) || undefined\n  const dryRun = (options && options.dryRun) || undefined\n\n  return _dataRequest(\n    client,\n    httpRequest,\n    'actions',\n    {actions: acts, transactionId, skipCrossDatasetReferenceValidation, dryRun},\n    options,\n  )\n}\n\n/**\n * @internal\n */\nexport function _dataRequest(\n  client: Client,\n  httpRequest: HttpRequest,\n  endpoint: string,\n  body: Any,\n  options: Any = {},\n): Any {\n  const isMutation = endpoint === 'mutate'\n  const isAction = endpoint === 'actions'\n  const isQuery = endpoint === 'query'\n\n  // Check if the query string is within a configured threshold,\n  // in which case we can use GET. Otherwise, use POST.\n  const strQuery = isMutation || isAction ? '' : encodeQueryString(body)\n  const useGet = !isMutation && !isAction && strQuery.length < getQuerySizeLimit\n  const stringQuery = useGet ? strQuery : ''\n  const returnFirst = options.returnFirst\n  const {timeout, token, tag, headers, returnQuery, lastLiveEventId, cacheMode} = options\n\n  const uri = _getDataUrl(client, endpoint, stringQuery)\n\n  const reqOptions = {\n    method: useGet ? 'GET' : 'POST',\n    uri: uri,\n    json: true,\n    body: useGet ? undefined : body,\n    query: isMutation && getMutationQuery(options),\n    timeout,\n    headers,\n    token,\n    tag,\n    returnQuery,\n    perspective: options.perspective,\n    resultSourceMap: options.resultSourceMap,\n    lastLiveEventId: Array.isArray(lastLiveEventId) ? lastLiveEventId[0] : lastLiveEventId,\n    cacheMode: cacheMode,\n    canUseCdn: isQuery,\n    signal: options.signal,\n    fetch: options.fetch,\n    useAbortSignal: options.useAbortSignal,\n    useCdn: options.useCdn,\n  }\n\n  return _requestObservable(client, httpRequest, reqOptions).pipe(\n    filter(isResponse),\n    map(getBody),\n    map((res) => {\n      if (!isMutation) {\n        return res\n      }\n\n      // Should we return documents?\n      const results = res.results || []\n      if (options.returnDocuments) {\n        return returnFirst\n          ? results[0] && results[0].document\n          : results.map((mut: Any) => mut.document)\n      }\n\n      // Return a reduced subset\n      const key = returnFirst ? 'documentId' : 'documentIds'\n      const ids = returnFirst ? results[0] && results[0].id : results.map((mut: Any) => mut.id)\n      return {\n        transactionId: res.transactionId,\n        results: results,\n        [key]: ids,\n      }\n    }),\n  )\n}\n\n/**\n * @internal\n */\nexport function _create<R extends Record<string, Any>>(\n  client: Client,\n  httpRequest: HttpRequest,\n  doc: Any,\n  op: Any,\n  options: Any = {},\n): Observable<\n  SanityDocument<R> | SanityDocument<R>[] | SingleMutationResult | MultipleMutationResult\n> {\n  const mutation = {[op]: doc}\n  const opts = Object.assign({returnFirst: true, returnDocuments: true}, options)\n  return _dataRequest(client, httpRequest, 'mutate', {mutations: [mutation]}, opts)\n}\n\nconst hasDataConfig = (client: Client) =>\n  (client.config().dataset !== undefined && client.config().projectId !== undefined) ||\n  client.config()['~experimental_resource'] !== undefined\n\nconst isQuery = (client: Client, uri: string) =>\n  hasDataConfig(client) && uri.startsWith(_getDataUrl(client, 'query'))\n\nconst isMutate = (client: Client, uri: string) =>\n  hasDataConfig(client) && uri.startsWith(_getDataUrl(client, 'mutate'))\n\nconst isDoc = (client: Client, uri: string) =>\n  hasDataConfig(client) && uri.startsWith(_getDataUrl(client, 'doc', ''))\n\nconst isListener = (client: Client, uri: string) =>\n  hasDataConfig(client) && uri.startsWith(_getDataUrl(client, 'listen'))\n\nconst isHistory = (client: Client, uri: string) =>\n  hasDataConfig(client) && uri.startsWith(_getDataUrl(client, 'history', ''))\n\nconst isData = (client: Client, uri: string) =>\n  uri.startsWith('/data/') ||\n  isQuery(client, uri) ||\n  isMutate(client, uri) ||\n  isDoc(client, uri) ||\n  isListener(client, uri) ||\n  isHistory(client, uri)\n\n/**\n * @internal\n */\nexport function _requestObservable<R>(\n  client: Client,\n  httpRequest: HttpRequest,\n  options: RequestObservableOptions,\n): Observable<HttpRequestEvent<R>> {\n  const uri = options.url || (options.uri as string)\n  const config = client.config()\n\n  // If the `canUseCdn`-option is not set we detect it automatically based on the method + URL.\n  // Only the /data endpoint is currently available through API-CDN.\n  const canUseCdn =\n    typeof options.canUseCdn === 'undefined'\n      ? ['GET', 'HEAD'].indexOf(options.method || 'GET') >= 0 && isData(client, uri)\n      : options.canUseCdn\n\n  let useCdn = (options.useCdn ?? config.useCdn) && canUseCdn\n\n  const tag =\n    options.tag && config.requestTagPrefix\n      ? [config.requestTagPrefix, options.tag].join('.')\n      : options.tag || config.requestTagPrefix\n\n  if (tag && options.tag !== null) {\n    options.query = {tag: validate.requestTag(tag), ...options.query}\n  }\n\n  // GROQ query-only parameters\n  if (['GET', 'HEAD', 'POST'].indexOf(options.method || 'GET') >= 0 && isQuery(client, uri)) {\n    const resultSourceMap = options.resultSourceMap ?? config.resultSourceMap\n    if (resultSourceMap !== undefined && resultSourceMap !== false) {\n      options.query = {resultSourceMap, ...options.query}\n    }\n    const perspectiveOption = options.perspective || config.perspective\n    if (typeof perspectiveOption !== 'undefined') {\n      if (perspectiveOption === 'previewDrafts') {\n        printPreviewDraftsDeprecationWarning()\n      }\n      validateApiPerspective(perspectiveOption)\n      options.query = {\n        perspective: Array.isArray(perspectiveOption)\n          ? perspectiveOption.join(',')\n          : perspectiveOption,\n        ...options.query,\n      }\n      // If the perspective is set to `drafts` or multiple perspectives we can't use the CDN, the API will throw\n      if (\n        ((Array.isArray(perspectiveOption) && perspectiveOption.length > 0) ||\n          // previewDrafts was renamed to drafts, but keep for backwards compat\n          perspectiveOption === 'previewDrafts' ||\n          perspectiveOption === 'drafts') &&\n        useCdn\n      ) {\n        useCdn = false\n        printCdnPreviewDraftsWarning()\n      }\n    }\n\n    if (options.lastLiveEventId) {\n      options.query = {...options.query, lastLiveEventId: options.lastLiveEventId}\n    }\n\n    if (options.returnQuery === false) {\n      options.query = {returnQuery: 'false', ...options.query}\n    }\n\n    if (useCdn && options.cacheMode == 'noStale') {\n      options.query = {cacheMode: 'noStale', ...options.query}\n    }\n  }\n\n  const reqOptions = requestOptions(\n    config,\n    Object.assign({}, options, {\n      url: _getUrl(client, uri, useCdn),\n    }),\n  ) as RequestOptions\n\n  const request = new Observable<HttpRequestEvent<R>>((subscriber) =>\n    httpRequest(reqOptions, config.requester!).subscribe(subscriber),\n  )\n\n  return options.signal ? request.pipe(_withAbortSignal(options.signal)) : request\n}\n\n/**\n * @internal\n */\nexport function _request<R>(client: Client, httpRequest: HttpRequest, options: Any): Observable<R> {\n  const observable = _requestObservable<R>(client, httpRequest, options).pipe(\n    filter((event: Any) => event.type === 'response'),\n    map((event: Any) => event.body),\n  )\n\n  return observable\n}\n\n/**\n * @internal\n */\nexport function _getDataUrl(client: Client, operation: string, path?: string): string {\n  const config = client.config()\n  if (config['~experimental_resource']) {\n    validators.resourceConfig(config)\n    const resourceBase = resourceDataBase(config)\n    const uri = path !== undefined ? `${operation}/${path}` : operation\n    return `${resourceBase}/${uri}`.replace(/\\/($|\\?)/, '$1')\n  }\n  const catalog = validators.hasDataset(config)\n  const baseUri = `/${operation}/${catalog}`\n  const uri = path !== undefined ? `${baseUri}/${path}` : baseUri\n  return `/data${uri}`.replace(/\\/($|\\?)/, '$1')\n}\n\n/**\n * @internal\n */\nexport function _getUrl(client: Client, uri: string, canUseCdn = false): string {\n  const {url, cdnUrl} = client.config()\n  const base = canUseCdn ? cdnUrl : url\n  return `${base}/${uri.replace(/^\\//, '')}`\n}\n\n/**\n * @internal\n */\nfunction _withAbortSignal<T>(signal: AbortSignal): MonoTypeOperatorFunction<T> {\n  return (input) => {\n    return new Observable((observer) => {\n      const abort = () => observer.error(_createAbortError(signal))\n\n      if (signal && signal.aborted) {\n        abort()\n        return\n      }\n      const subscription = input.subscribe(observer)\n      signal.addEventListener('abort', abort)\n      return () => {\n        signal.removeEventListener('abort', abort)\n        subscription.unsubscribe()\n      }\n    })\n  }\n}\n// DOMException is supported on most modern browsers and Node.js 18+.\n// @see https://developer.mozilla.org/en-US/docs/Web/API/DOMException#browser_compatibility\nconst isDomExceptionSupported = Boolean(globalThis.DOMException)\n\n/**\n * @internal\n * @param signal - The abort signal to use.\n * Original source copied from https://github.com/sindresorhus/ky/blob/740732c78aad97e9aec199e9871bdbf0ae29b805/source/errors/DOMException.ts\n * TODO: When targeting Node.js 18, use `signal.throwIfAborted()` (https://developer.mozilla.org/en-US/docs/Web/API/AbortSignal/throwIfAborted)\n */\nfunction _createAbortError(signal?: AbortSignal) {\n  /*\n  NOTE: Use DomException with AbortError name as specified in MDN docs (https://developer.mozilla.org/en-US/docs/Web/API/AbortController/abort)\n  > When abort() is called, the fetch() promise rejects with an Error of type DOMException, with name AbortError.\n  */\n  if (isDomExceptionSupported) {\n    return new DOMException(signal?.reason ?? 'The operation was aborted.', 'AbortError')\n  }\n\n  // DOMException not supported. Fall back to use of error and override name.\n  const error = new Error(signal?.reason ?? 'The operation was aborted.')\n  error.name = 'AbortError'\n\n  return error\n}\n\nconst resourceDataBase = (config: InitializedClientConfig): string => {\n  if (!config['~experimental_resource']) {\n    throw new Error('`resource` must be provided to perform resource queries')\n  }\n  const {type, id} = config['~experimental_resource']\n\n  switch (type) {\n    case 'dataset': {\n      const segments = id.split('.')\n      if (segments.length !== 2) {\n        throw new Error('Dataset ID must be in the format \"project.dataset\"')\n      }\n      return `/projects/${segments[0]}/datasets/${segments[1]}`\n    }\n    case 'canvas': {\n      return `/canvases/${id}`\n    }\n    case 'media-library': {\n      return `/media-libraries/${id}`\n    }\n    case 'dashboard': {\n      return `/dashboards/${id}`\n    }\n    default:\n      // @ts-expect-error - handle all supported resource types\n      throw new Error(`Unsupported resource type: ${type.toString()}`)\n  }\n}\n", "import {type Observable} from 'rxjs'\n\nimport {_request} from '../../data/dataMethods'\nimport type {ObservableSanityClient, SanityClient} from '../../SanityClient'\nimport type {AgentActionParams, Any, HttpRequest, IdentifiedSanityDocumentStub} from '../../types'\nimport {hasDataset} from '../../validators'\nimport type {\n  AgentActionAsync,\n  AgentActionPathSegment,\n  AgentActionRequestBase,\n  AgentActionSync,\n  AgentActionTarget,\n  AgentActionTargetInclude,\n} from './commonTypes'\n\n/**  @beta */\nexport type GenerateOperation = 'set' | 'append' | 'mixed'\n\n/**  @beta */\nexport interface GenerateRequestBase extends AgentActionRequestBase {\n  /** schemaId as reported by sanity deploy / sanity schema store */\n  schemaId: string\n  /**\n   * Instruct the LLM how it should generate content. Be as specific and detailed as needed.\n   *\n   * The LLM only has access to information in the instruction, plus the target schema.\n   *\n   * String template with support for $variable from `instructionParams`.\n   * */\n  instruction: string\n  /**\n   * param values for the string template, keys are the variable name, ie if the template has \"$variable\", one key must be \"variable\"\n   *\n   * ### Examples\n   *\n   * #### Constant\n   *\n   * ##### Shorthand\n   * ```ts\n   * client.agent.action.generate({\n   *   schemaId,\n   *   documentId,\n   *   instruction: 'Give the following topic:\\n $topic \\n ---\\nGenerate the full article.',\n   *   instructionParams: {\n   *     topic: 'Grapefruit'\n   *   },\n   * })\n   * ```\n   * ##### Object-form\n   *\n   * ```ts\n   * client.agent.action.generate({\n   *   schemaId,\n   *   documentId,\n   *   instruction: 'Give the following topic:\\n $topic \\n ---\\nGenerate the full article.',\n   *   instructionParams: {\n   *     topic: {\n   *       type: 'constant',\n   *       value: 'Grapefruit'\n   *     },\n   *   },\n   * })\n   * ```\n   * #### Field\n   * ```ts\n   * client.agent.action.generate({\n   *   schemaId,\n   *   documentId,\n   *   instruction: 'Give the following field value:\\n $pte \\n ---\\nGenerate keywords.',\n   *   instructionParams: {\n   *     pte: {\n   *       type: 'field',\n   *       path: ['pteField'],\n   *     },\n   *   },\n   *   target: {path: 'keywords' }\n   * })\n   * ```\n   * #### Document\n   * ```ts\n   * client.agent.action.generate({\n   *   schemaId,\n   *   documentId,\n   *   instruction: 'Give the following document value:\\n $document \\n ---\\nGenerate keywords.',\n   *   instructionParams: {\n   *     document: {\n   *       type: 'document',\n   *     },\n   *   },\n   *   target: {path: 'keywords' }\n   * })\n   * ```\n   *\n   * #### GROQ\n   * ```ts\n   * client.agent.action.generate({\n   *   schemaId,\n   *   documentId,\n   *   instruction: 'Give the following list of titles:\\n $list \\n ---\\nGenerate a similar title.',\n   *   instructionParams: {\n   *     list: {\n   *       type: 'groq',\n   *       query: '* [_type==$type].title',\n   *       params: {type: 'article'}\n   *     },\n   *   },\n   *   target: {path: 'title' }\n   * })\n   * ```\n   * */\n  instructionParams?: AgentActionParams\n\n  /**\n   * Target defines which parts of the document will be affected by the instruction.\n   * It can be an array, so multiple parts of the document can be separately configured in detail.\n   *\n   * Omitting target implies that the document itself is the root.\n   *\n   * Notes:\n   * - instruction can only affect fields up to `maxPathDepth`\n   * - when multiple targets are provided, they will be coalesced into a single target sharing a common target root.\n   * It is therefor an error to provide conflicting include/exclude across targets (ie, include title in one, and exclude it in another)\n   *\n   * ## Generating images\n   *\n   * Generate will generate images the same was as AI Assist, for images that have been configured using\n   * [AI Assist schema options](https://github.com/sanity-io/assist/tree/main/plugin#image-generation).\n   *\n   * To generate images _without_ changing the schema, directly target an image asset path.\n   *\n   * For example, all the following will generate an image into the provided asset:\n   * * `target: {path: ['image', 'asset'] }`\n   * * `target: {path: 'image', include: ['asset'] }`\n   *\n   * Image generation can be combined with regular content targets:\n   * * `target: [{path: ['image', 'asset'] }, {include: ['title', 'description']}]`\n   *\n   * Since Generate happens in a single LLM pass, the image will be contextually related to other generated content.\n   * @see AgentActionRequestBase#conditionalPaths\n   */\n  target?: GenerateTarget | GenerateTarget[]\n}\n\n/**  @beta */\nexport interface GenerateTargetInclude extends AgentActionTargetInclude {\n  /**\n   * Sets the operation for this path, and all its children.\n   * This overrides any operation set parents or the root target.\n   * @see #GenerateTarget.operation\n   * @see #include\n   */\n  operation?: GenerateOperation\n\n  /**\n   * By default, all children up to `target.maxPathDepth` are included.\n   *\n   * When `include` is specified, only segments explicitly listed will be included.\n   *\n   * Fields or array items not on the include list, are implicitly excluded.\n   */\n  include?: (AgentActionPathSegment | GenerateTargetInclude)[]\n}\n\n/**  @beta */\nexport interface GenerateTarget extends AgentActionTarget {\n  /**\n   * Sets the default operation for all paths in the target.\n   * Generate runs in `'mixed'` operation mode by default:\n   * Changes are set in all non-array fields, and append to all array fields.\n   *\n   * ### Operation types\n   * - `'set'` – an *overwriting* operation, and replaces the full field value.\n   * - `'append'`:\n   *    – array fields: appends new items to the end of the array,\n   *    - string fields: '\"existing content\" \"new content\"'\n   *    - text fields: '\"existing content\"\\\\n\"new content\"'\n   *    - number fields: existing + new\n   *    - other field types not mentioned will set instead (dates, url)\n   * - `'mixed'` – (default) sets non-array fields, and appends to array fields\n   *\n   * The default operation can be overridden on a per-path basis using `include`.\n   *\n   * Nested fields inherit the operation specified by their parent and falls back to the\n   * top level target operation if not otherwise specified.\n   *\n   * Use `include` to change the `operation` of individual fields or items.\n   *\n   * #### Appending in the middle of arrays\n   * `target: {path: ['array'], operation: 'append'}` will append the output of the instruction to the end of the array.\n   *\n   * To insert in the middle of the array, use `target: {path: ['array', {_key: 'appendAfterKey'}], operation: 'append'}`.\n   * Here, the output of the instruction will be appended after the array item with key `'appendAfterKey'`.\n   *\n   * @see #AgentActionTargetInclude.operation\n   * @see #include\n   * @see #AgentActionTargetInclude.include\n   * @see #AgentActionSchema.forcePublishedWrite\n   */\n  operation?: GenerateOperation\n\n  /**\n   * By default, all children up to `target.maxPathDepth` are included.\n   *\n   * When `include` is specified, only segments explicitly listed will be included.\n   *\n   * Fields or array items not on the include list, are implicitly excluded.\n   */\n  include?: (AgentActionPathSegment | GenerateTargetInclude)[]\n}\n\n/**  @beta */\nexport type GenerateTargetDocument<T extends Record<string, Any> = Record<string, Any>> =\n  | {\n      operation: 'edit'\n      /**\n       * @see #AgentActionSchema.forcePublishedWrite\n       */\n      _id: string\n    }\n  | {\n      operation: 'create'\n      /**\n       * @see #AgentActionSchema.forcePublishedWrite\n       */\n      _id?: string\n      _type: string\n      initialValues?: T\n    }\n  | {\n      operation: 'createIfNotExists'\n      /**\n       * @see #AgentActionSchema.forcePublishedWrite\n       */\n      _id: string\n      _type: string\n      initialValues?: T\n    }\n  | {\n      operation: 'createOrReplace'\n      /**\n       * @see #AgentActionSchema.forcePublishedWrite\n       */\n      _id: string\n      _type: string\n      initialValues?: T\n    }\n\n/**\n * Instruction for an existing document.\n * @beta\n */\ninterface GenerateExistingDocumentRequest {\n  /**\n   * @see #AgentActionSchema.forcePublishedWrite\n   */\n  documentId: string\n  targetDocument?: never\n}\n\n/**\n * Instruction to create a new document\n * @beta\n */\ninterface GenerateTargetDocumentRequest<T extends Record<string, Any> = Record<string, Any>> {\n  /**\n   * @see #AgentActionSchema.forcePublishedWrite\n   */\n  targetDocument: GenerateTargetDocument<T>\n  documentId?: never\n}\n\n/** @beta */\nexport type GenerateSyncInstruction<T extends Record<string, Any> = Record<string, Any>> = (\n  | GenerateExistingDocumentRequest\n  | GenerateTargetDocumentRequest<T>\n) &\n  GenerateRequestBase &\n  AgentActionSync\n\n/** @beta */\nexport type GenerateAsyncInstruction<T extends Record<string, Any> = Record<string, Any>> = (\n  | GenerateExistingDocumentRequest\n  | GenerateTargetDocumentRequest<T>\n) &\n  GenerateRequestBase &\n  AgentActionAsync\n\n/** @beta */\nexport type GenerateInstruction<T extends Record<string, Any> = Record<string, Any>> =\n  | GenerateSyncInstruction<T>\n  | GenerateAsyncInstruction<T>\n\nexport function _generate<DocumentShape extends Record<string, Any>>(\n  client: SanityClient | ObservableSanityClient,\n  httpRequest: HttpRequest,\n  request: GenerateInstruction<DocumentShape>,\n): Observable<\n  (typeof request)['async'] extends true\n    ? {_id: string}\n    : IdentifiedSanityDocumentStub & DocumentShape\n> {\n  const dataset = hasDataset(client.config())\n  return _request(client, httpRequest, {\n    method: 'POST',\n    uri: `/agent/action/generate/${dataset}`,\n    body: request,\n  })\n}\n", "import {type Observable} from 'rxjs'\n\nimport {_request} from '../../data/dataMethods'\nimport type {ObservableSanityClient, SanityClient} from '../../SanityClient'\nimport type {\n  AgentActionPath,\n  AgentActionPathSegment,\n  Any,\n  GenerateTargetDocument,\n  HttpRequest,\n  IdentifiedSanityDocumentStub,\n} from '../../types'\nimport {hasDataset} from '../../validators'\nimport type {AgentActionAsync, AgentActionSchema, AgentActionSync} from './commonTypes'\n\n/**  @beta */\nexport type PatchOperation = 'set' | 'append' | 'mixed' | 'unset'\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\ntype AnyNonNullable = Exclude<any, null | undefined>\n\n/**  @beta */\nexport interface PatchRequestBase extends AgentActionSchema {\n  /**\n   * Target defines which parts of the document will be affected by the instruction.\n   * It can be an array, so multiple parts of the document can be separately configured in detail.\n   *\n   * Omitting target implies that the document itself is the root.\n   *\n   * Notes:\n   * - instruction can only affect fields up to `maxPathDepth`\n   * - when multiple targets are provided, they will be coalesced into a single target sharing a common target root.\n   * It is therefore an error to provide conflicting include/exclude across targets (ie, include title in one, and exclude it in another)\n   *\n   * @see AgentActionRequestBase#conditionalPaths\n   */\n  target: PatchTarget | PatchTarget[]\n}\n\n/**  @beta */\nexport type PatchTarget = {\n  /**\n   * Determines how the target path will be patched.\n   *\n   * ### Operation types\n   * - `'set'` – an *overwriting* operation: sets the full field value for primitive targets, and merges the provided value with existing values for objects\n   * - `'append'`:\n   *    – array fields: appends new items to the end of the array,\n   *    - string fields: '\"existing content\" \"new content\"'\n   *    - text fields: '\"existing content\"\\\\n\"new content\"'\n   *    - number fields: existing + new\n   *    - other field types not mentioned will set instead (dates, url)\n   * - `'mixed'` –  sets non-array fields, and appends to array fields\n   * - `'unset'` – removes whatever value is on the target path\n   *\n   * All operations except unset requires a `value`.\n   *\n   * #### Appending in the middle of arrays\n   * To append to an array, use the 'append' operation, and provide an array value with one or more array items.\n   *\n   * `target: {path: ['array'], operation: 'append', value: [{_type: 'item' _key: 'a'}]}` will append the items in the value to the existing array.\n   *\n   * To insert in the middle of the array, use `target: {path: ['array', {_key: 'appendAfterKey'}], operation: 'append', value: [{_type: 'item' _key: 'a'}]}`.\n   * Here, `{_type: 'item' _key: 'a'}` will be appended after the array item with key `'appendAfterKey'`\n   *\n   * It is optional to provide a _key for inserted array items; if one isn't provided, it will be generated.\n   */\n  operation: PatchOperation\n\n  path: AgentActionPathSegment | AgentActionPath\n} & (\n  | {operation: 'unset'; value?: never}\n  | {operation: Exclude<PatchOperation, 'unset'>; value: AnyNonNullable}\n)\n\n/**\n * Patches an existing document\n * @beta\n */\ninterface PatchExistingDocumentRequest {\n  /**\n   * @see #AgentActionSchema.forcePublishedWrite\n   */\n  documentId: string\n  targetDocument?: never\n}\n\n/**\n * Create a new document, then patch it\n * @beta\n */\ninterface PatchTargetDocumentRequest<T extends Record<string, Any> = Record<string, Any>> {\n  /**\n   * @see #AgentActionSchema.forcePublishedWrite\n   */\n  targetDocument: GenerateTargetDocument<T>\n  documentId?: never\n}\n\n/** @beta */\nexport type PatchDocumentSync<T extends Record<string, Any> = Record<string, Any>> = (\n  | PatchExistingDocumentRequest\n  | PatchTargetDocumentRequest<T>\n) &\n  PatchRequestBase &\n  AgentActionSync\n\n/** @beta */\nexport type PatchDocumentAsync<T extends Record<string, Any> = Record<string, Any>> = (\n  | PatchExistingDocumentRequest\n  | PatchTargetDocumentRequest<T>\n) &\n  PatchRequestBase &\n  AgentActionAsync\n\n/** @beta */\nexport type PatchDocument<T extends Record<string, Any> = Record<string, Any>> =\n  | PatchDocumentSync<T>\n  | PatchDocumentAsync<T>\n\nexport function _patch<DocumentShape extends Record<string, Any>>(\n  client: SanityClient | ObservableSanityClient,\n  httpRequest: HttpRequest,\n  request: PatchDocument<DocumentShape>,\n): Observable<\n  (typeof request)['async'] extends true\n    ? {_id: string}\n    : IdentifiedSanityDocumentStub & DocumentShape\n> {\n  const dataset = hasDataset(client.config())\n  return _request(client, httpRequest, {\n    method: 'POST',\n    uri: `/agent/action/patch/${dataset}`,\n    body: request,\n  })\n}\n", "import {type Observable} from 'rxjs'\n\nimport {_request} from '../../data/dataMethods'\nimport type {ObservableSanityClient, SanityClient} from '../../SanityClient'\nimport type {AgentActionParams, Any, HttpRequest} from '../../types'\nimport {hasDataset} from '../../validators'\n\n/**  @beta */\nexport interface PromptRequestBase {\n  /**\n   * Instruct the LLM how it should generate content. Be as specific and detailed as needed.\n   *\n   * The LLM only has access to information in the instruction, plus the target schema.\n   *\n   * String template with support for $variable from `instructionParams`.\n   * */\n  instruction: string\n  /**\n   * param values for the string template, keys are the variable name, ie if the template has \"$variable\", one key must be \"variable\"\n   *\n   * ### Examples\n   *\n   * #### Constant\n   *\n   * ##### Shorthand\n   * ```ts\n   * client.agent.action.prompt({\n   *   instruction: 'Give the following topic:\\n $topic \\n ---\\nReturns some facts about it',\n   *   instructionParams: {\n   *     topic: 'Grapefruit'\n   *   },\n   * })\n   * ```\n   * ##### Object-form\n   *\n   * ```ts\n   * client.agent.action.prompt({\n   *   instruction: 'Give the following topic:\\n $topic \\n ---\\nReturns some facts about it.',\n   *   instructionParams: {\n   *     topic: {\n   *       type: 'constant',\n   *       value: 'Grapefruit'\n   *     },\n   *   },\n   * })\n   * ```\n   * #### Field\n   * ```ts\n   * client.agent.action.prompt({\n   *   instruction: 'Give the following field value:\\n $pte \\n ---\\nGenerate keywords.',\n   *   instructionParams: {\n   *     pte: {\n   *       type: 'field',\n   *       path: ['pteField'],\n   *       documentId: 'someSanityDocId'\n   *     },\n   *   },\n   * })\n   * ```\n   * #### Document\n   * ```ts\n   * client.agent.action.prompt({\n   *   json: true,\n   *   instruction: 'Given the following document:$document\\nCreate a JSON string[] array with keywords describing it.',\n   *   instructionParams: {\n   *     document: {\n   *       type: 'document',\n   *       documentId: 'someSanityDocId'\n   *     },\n   *   },\n   * })\n   * ```\n   *\n   * #### GROQ\n   * ```ts\n   * client.agent.action.prompt({\n   *   instruction: 'Return the best title amongst these: $titles.',\n   *   instructionParams: {\n   *     titles: {\n   *       type: 'groq',\n   *       query: '* [_type==$type].title',\n   *       params: {type: 'article'}\n   *     },\n   *   },\n   * })\n   * ```\n   * */\n  instructionParams?: AgentActionParams<{docIdRequired: true}>\n\n  /**\n   * Controls how much variance the instructions will run with.\n   *\n   * Value must be in the range [0, 1] (inclusive).\n   *\n   * Default: 0.3\n   */\n  temperature?: number\n}\n\n/**\n * @beta\n */\n// need the unused generic here to allow for optional callsite casting\n// eslint-disable-next-line unused-imports/no-unused-vars\ninterface PromptJsonResponse<T extends Record<string, Any> = Record<string, Any>> {\n  /**\n   *\n   * When true, the response body will be json according to the instruction.\n   * When false, the response is the raw text response to the instruction.\n   *\n   * Note: In addition to setting this to true,  `instruction` MUST include the word 'JSON', or 'json' for this to work.\n   */\n  format: 'json'\n}\n\ninterface PromptTextResponse {\n  /**\n   *\n   * When true, the response body will be json according to the instruction.\n   * When false, the response is the raw text response to the instruction.\n   *\n   * Note: In addition to setting this to true,  `instruction` MUST include the word 'JSON', or 'json' for this to work.\n   */\n  format?: 'text'\n}\n\n/** @beta */\nexport type PromptRequest<T extends Record<string, Any> = Record<string, Any>> = (\n  | PromptTextResponse\n  | PromptJsonResponse<T>\n) &\n  PromptRequestBase\n\nexport function _prompt<const DocumentShape extends Record<string, Any>>(\n  client: SanityClient | ObservableSanityClient,\n  httpRequest: HttpRequest,\n  request: PromptRequest<DocumentShape>,\n): Observable<(typeof request)['format'] extends 'json' ? DocumentShape : string> {\n  const dataset = hasDataset(client.config())\n  return _request(client, httpRequest, {\n    method: 'POST',\n    uri: `/agent/action/prompt/${dataset}`,\n    body: request,\n  })\n}\n", "import {type Observable} from 'rxjs'\n\nimport {_request} from '../../data/dataMethods'\nimport type {ObservableSanityClient, SanityClient} from '../../SanityClient'\nimport type {\n  AgentActionParams,\n  AgentActionPath,\n  Any,\n  HttpRequest,\n  IdentifiedSanityDocumentStub,\n} from '../../types'\nimport {hasDataset} from '../../validators'\nimport type {\n  AgentActionAsync,\n  AgentActionPathSegment,\n  AgentActionRequestBase,\n  AgentActionSync,\n  AgentActionTarget,\n  AgentActionTargetInclude,\n} from './commonTypes'\n\n/** @beta */\nexport interface TransformRequestBase extends AgentActionRequestBase {\n  /** schemaId as reported by sanity deploy / sanity schema store */\n  schemaId: string\n\n  /**\n   * The source document the transformation will use as input.\n   *\n   * @see #AgentActionSchema.forcePublishedWrite\n   */\n  documentId: string\n\n  /**\n   * The source document's content is first copied to the target,\n   * then it is transformed according to the instruction.\n   *\n   * When omitted, the source document (documentId) is also the target document.\n   *\n   *  @see #AgentActionSchema.forcePublishedWrite\n   */\n  targetDocument?: TransformTargetDocument\n\n  /**\n   * Instruct the LLM how to transform the input to th output.\n   *\n   * String template with support for $variable from `instructionParams`.\n   *\n   * Capped to 2000 characters, after variables has been injected.\n   * */\n  instruction: string\n  /**\n   *\n   * param values for the string template, keys are the variable name, ie if the template has \"$variable\", one key must be \"variable\"\n   *\n   * ### Examples\n   *\n   * #### Constant\n   *\n   * ##### Shorthand\n   * ```ts\n   * client.agent.action.generate({\n   *   schemaId,\n   *   documentId,\n   *   instruction: 'Give the following topic:\\n $topic \\n ---\\nGenerate the full article.',\n   *   instructionParams: {\n   *     topic: 'Grapefruit'\n   *   },\n   * })\n   * ```\n   * ##### Object-form\n   *\n   * ```ts\n   * client.agent.action.transform({\n   *   schemaId,\n   *   documentId,\n   *   instruction: 'Give the following topic:\\n $topic \\n ---\\nGenerate the full article.',\n   *   instructionParams: {\n   *     topic: {\n   *       type: 'constant',\n   *       value: 'Grapefruit'\n   *     },\n   *   },\n   * })\n   * ```\n   * #### Field\n   * ```ts\n   * client.agent.action.transform({\n   *   schemaId,\n   *   documentId,\n   *   instruction: 'Give the following field value:\\n $pte \\n ---\\nGenerate keywords.',\n   *   instructionParams: {\n   *     pte: {\n   *       type: 'field',\n   *       path: ['pteField'],\n   *     },\n   *   },\n   *   target: {path: 'keywords' }\n   * })\n   * ```\n   * #### Document\n   * ```ts\n   * client.agent.action.transform({\n   *   schemaId,\n   *   documentId,\n   *   instruction: 'Give the following document value:\\n $document \\n ---\\nGenerate keywords.',\n   *   instructionParams: {\n   *     document: {\n   *       type: 'document',\n   *     },\n   *   },\n   *   target: {path: 'keywords' }\n   * })\n   * ```\n   *\n   * #### GROQ\n   * ```ts\n   * client.agent.action.transform({\n   *   schemaId,\n   *   documentId,\n   *   instruction: 'Give the following list of titles:\\n $list \\n ---\\nGenerate a similar title.',\n   *   instructionParams: {\n   *     list: {\n   *       type: 'groq',\n   *       query: '* [_type==$type].title',\n   *       params: {type: 'article'}\n   *     },\n   *   },\n   *   target: {path: 'title'}\n   * })\n   * ```\n   * */\n  instructionParams?: AgentActionParams\n\n  /**\n   * Target defines which parts of the document will be affected by the instruction.\n   * It can be an array, so multiple parts of the document can be separately configured in detail.\n   *\n   * Omitting target implies that the document itself is the root.\n   *\n   * Notes:\n   * - instruction can only affect fields up to `maxPathDepth`\n   * - when multiple targets are provided, they will be coalesced into a single target sharing a common target root.\n   * It is therefor an error to provide conflicting include/exclude across targets (ie, include title in one, and exclude it in another)\n   *\n   * Default max depth for transform: 12\n   *\n   * ## Transforming images\n   *\n   * To transform an existing image, directly target an image asset path.\n   *\n   * For example, all the following will transform the image into the provided asset:\n   * * `target: {path: ['image', 'asset'] }`\n   * * `target: {path: 'image', include: ['asset'] }`\n   *\n   * Image transform can be combined with regular content targets:\n   * * `target: [{path: ['image', 'asset'] }, {include: ['title', 'description']}]`\n   *\n   * Image transform can have per-path instructions, just like any other target paths:\n   * * `target: [{path: ['image', 'asset'], instruction: 'Make the sky blue' }`\n   *\n   * @see AgentActionRequestBase#conditionalPaths\n   */\n  target?: TransformTarget | TransformTarget[]\n}\n\n/**\n * @see #AgentActionSchema.forcePublishedWrite\n *\n * @beta\n */\nexport type TransformTargetDocument =\n  | {operation: 'edit'; _id: string}\n  | {operation: 'create'; _id?: string}\n  | {operation: 'createIfNotExists'; _id: string}\n  | {operation: 'createOrReplace'; _id: string}\n\n/**\n *\n * @see #TransformOperation\n * @beta\n */\nexport type ImageDescriptionOperation = {\n  type: 'image-description'\n  /**\n   * When omitted, parent image value will be inferred from the arget path.\n   *\n   * When specified, the `sourcePath` should be a path to an image (or image asset) field:\n   * - `['image']`\n   * - `['wrapper', 'mainImage']`\n   * - `['heroImage', 'asset'] // the asset segment is optional, but supported`\n   */\n  sourcePath?: AgentActionPath\n} & (\n  | {\n      /**\n       * When omitted, parent image value will be inferred from the target path.\n       *\n       * When specified, the `sourcePath` should be a path to an image (or image asset) field:\n       * - `['image']`\n       * - `['wrapper', 'mainImage']`\n       * - `['heroImage', 'asset'] // the asset segment is optional, but supported`\n       *\n       * Incompatible with `imageUrl`\n       *\n       */\n      sourcePath?: AgentActionPath\n      imageUrl?: never\n    }\n  | {\n      /**\n       * When specified, the image source to be described will be fetched from the URL.\n       *\n       * Incompatible with `sourcePath`\n       */\n      imageUrl?: `https://${string}`\n      sourcePath?: never\n    }\n)\n\n/**\n *\n * ## `set` by default\n * By default, Transform will change the value of every target field in place using a set operation.\n *\n * ## Image description\n *\n * ### Targeting image fields\n * Images can be transformed to a textual description by targeting a `string`, `text` or Portable Text field (`array` with `block`)\n * with `operation: {type: 'image-description'}`.\n *\n * Custom instructions for image description targets will be used to generate the description.\n *\n * Such targets must be a descendant field of an image object.\n *\n * For example:\n * - `target: {path: ['image', 'description'], operation: {type: 'image-description'} }`\n * - `target: {path: ['array', {_key: 'abc'}, 'alt'], operation: {type: 'image-description'} } //assuming the item in the array on the key-ed path is an image`\n * - `target: {path: ['image'], include: ['portableTextField'], operation: {type: 'image-description'}, instruction: 'Use formatting and headings to describe the image in great detail' }`\n *\n * ### Targeting non-image fields\n * If the target image description lives outside an image object, use the `sourcePath` option to specify the path to the image field.\n * `sourcePath` must be an image or image asset field.\n *\n * For example:\n * - `target: {path: ['description'], operation: operation: {type: 'image-description', sourcePath: ['image', 'asset'] }`\n * - `target: {path: ['wrapper', 'title'], operation: {type: 'image-description', sourcePath: ['array', {_key: 'abc'}, 'image'] }`\n * - `target: {path: ['wrapper'], include: ['portableTextField'], operation: {type: 'image-description', sourcePath: ['image', 'asset'] }, instruction: 'Use formatting and headings to describe the image in great detail' }`\n *\n * ### Targeting images outside the document (URL)\n * If the source image is available on a https URL outside the target document, it is possible to get a description for it using `imageUrl`.\n *\n * Example:\n * - `target: {path: ['description'], operation: operation: {type: 'image-description', imageUrL: 'https://www.sanity.io/static/images/favicons/android-icon-192x192.png?v=2' }`\n * @beta\n */\nexport type TransformOperation = 'set' | ImageDescriptionOperation\n\n/**\n * @see #TransformOperation\n * @beta\n * */\nexport interface TransformTargetInclude extends AgentActionTargetInclude {\n  /**\n   * Specifies a tailored instruction of this target.\n   *\n   * String template with support for $variable from `instructionParams`.  */\n  instruction?: string\n\n  /**\n   * By default, all children up to `target.maxPathDepth` are included.\n   *\n   * When `include` is specified, only segments explicitly listed will be included.\n   *\n   * Fields or array items not on the include list, are implicitly excluded.\n   */\n  include?: (AgentActionPathSegment | TransformTargetInclude)[]\n\n  /**\n   * Default: `set`\n   * @see #TransformOperation\n   */\n  operation?: TransformOperation\n}\n\n/**\n * @see #TransformOperation\n * @beta\n * */\nexport interface TransformTarget extends AgentActionTarget {\n  /**\n   * Specifies a tailored instruction of this target.\n   *\n   * String template with support for $variable from `instructionParams`.\n   * */\n  instruction?: string\n\n  /**\n   * By default, all children up to `target.maxPathDepth` are included.\n   *\n   * When `include` is specified, only segments explicitly listed will be included.\n   *\n   * Fields or array items not on the include list, are implicitly excluded.\n   */\n  include?: (AgentActionPathSegment | TransformTargetInclude)[]\n\n  /**\n   * Default: `set`\n   * @see #TransformOperation\n   */\n  operation?: TransformOperation\n}\n\n/** @beta */\n// need the generics to hold optional call-site response generics\n// eslint-disable-next-line unused-imports/no-unused-vars\nexport type TransformDocumentSync<T extends Record<string, Any> = Record<string, Any>> =\n  TransformRequestBase & AgentActionSync\n\n/** @beta */\nexport type TransformDocumentAsync = TransformRequestBase & AgentActionAsync\n\n/** @beta */\nexport type TransformDocument<T extends Record<string, Any> = Record<string, Any>> =\n  | TransformDocumentSync<T>\n  | TransformDocumentAsync\n\nexport function _transform<DocumentShape extends Record<string, Any>>(\n  client: SanityClient | ObservableSanityClient,\n  httpRequest: HttpRequest,\n  request: TransformDocument<DocumentShape>,\n): Observable<\n  (typeof request)['async'] extends true\n    ? {_id: string}\n    : IdentifiedSanityDocumentStub & DocumentShape\n> {\n  const dataset = hasDataset(client.config())\n  return _request(client, httpRequest, {\n    method: 'POST',\n    uri: `/agent/action/transform/${dataset}`,\n    body: request,\n  })\n}\n", "import {type Observable} from 'rxjs'\n\nimport {_request} from '../../data/dataMethods'\nimport type {ObservableSanityClient, SanityClient} from '../../SanityClient'\nimport type {\n  AgentActionParams,\n  AgentActionPathSegment,\n  AgentActionTarget,\n  Any,\n  HttpRequest,\n  IdentifiedSanityDocumentStub,\n} from '../../types'\nimport {hasDataset} from '../../validators'\nimport type {\n  Agent<PERSON>ction<PERSON><PERSON>,\n  AgentActionPath,\n  AgentActionRequestBase,\n  AgentActionSync,\n  AgentActionTargetInclude,\n} from './commonTypes'\nimport type {TransformTargetDocument} from './transform'\n\n/**  @beta */\nexport interface TranslateRequestBase extends AgentActionRequestBase {\n  /** schemaId as reported by sanity deploy / sanity schema store */\n  schemaId: string\n\n  /**\n   * The source document the transformation will use as input.\n   * @see #AgentActionSchema.forcePublishedWrite\n   */\n  documentId: string\n\n  /**\n   * The target document will first get content copied over from the source,\n   * then it is translated according to the instruction.\n   *\n   * When omitted, the source document (documentId) is also the target document.\n   *\n   * @see #AgentActionSchema.forcePublishedWrite\n   */\n  targetDocument?: TransformTargetDocument\n\n  /**\n   * While optional, it is recommended\n   */\n  fromLanguage?: TranslateLanguage\n  toLanguage: TranslateLanguage\n\n  /**\n   * `styleGuide` can be used to tailor how the translation should be preformed.\n   *\n   * String template using $variable from styleGuideParams.\n   *\n   * Capped to 2000 characters, after variables has been injected.\n   *\n   * @see #protectedPhrases\n   */\n  styleGuide?: string\n  /** param values for the string template, keys are the variable name, ie if the template has \"$variable\", one key must be \"variable\" */\n  styleGuideParams?: AgentActionParams\n\n  /**\n   * When the input string contains any phrase from `protectedPhrases`, the LLM will be instructed not\n   * to translate them.\n   *\n   * It is recommended to use `protectedPhrases` instead of `styleGuide` for deny-list words and phrases,\n   * since it keeps token cost low, resulting in faster responses, and limits how much information the LLM\n   * has to process, since only phrases that are actually in the input string will be included in the final prompt.\n   */\n  protectedPhrases?: string[]\n\n  /**\n   * When specified, the `toLanguage.id` will be stored in the specified path in the target document.\n   *\n   * The file _can_ be hidden: true (unlike other fields in the target, which will be ignored)\n   */\n  languageFieldPath?: AgentActionPathSegment | AgentActionPath\n\n  /**\n   * Target defines which parts of the document will be affected by the instruction.\n   * It can be an array, so multiple parts of the document can be separately configured in detail.\n   *\n   * Omitting target implies that the document itself is the root.\n   *\n   * Notes:\n   * - instruction can only affect fields up to `maxPathDepth`\n   * - when multiple targets are provided, they will be coalesced into a single target sharing a common target root.\n   * It is therefor an error to provide conflicting include/exclude across targets (ie, include title in one, and exclude it in another)\n   *\n   * @see AgentActionRequestBase#conditionalPaths\n   */\n  target?: TranslateTarget | TranslateTarget[]\n}\n\n/**  @beta */\nexport interface TranslateLanguage {\n  /**\n   * Language code\n   */\n  id: string\n\n  /**\n   * While optional, it is recommended to provide a language title\n   */\n  title?: string\n}\n\n/**  @beta */\nexport interface TranslateTargetInclude extends AgentActionTargetInclude {\n  /** String template using $variable from styleGuideParams.  */\n  styleGuide?: string\n\n  /**\n   * By default, all children up to `target.maxPathDepth` are included.\n   *\n   * When `include` is specified, only segments explicitly listed will be included.\n   *\n   * Fields or array items not on the include list, are implicitly excluded.\n   */\n  include?: (AgentActionPathSegment | TranslateTargetInclude)[]\n}\n\n/**  @beta */\nexport interface TranslateTarget extends AgentActionTarget {\n  /** String template using $variable from styleGuideParams.  */\n  styleGuide?: string\n\n  /**\n   * By default, all children up to `target.maxPathDepth` are included.\n   *\n   * When `include` is specified, only segments explicitly listed will be included.\n   *\n   * Fields or array items not on the include list, are implicitly excluded.\n   */\n  include?: (AgentActionPathSegment | TranslateTargetInclude)[]\n}\n\n/** @beta */\n// need the generics to hold optional call-site response generics\n// eslint-disable-next-line unused-imports/no-unused-vars\nexport type TranslateDocumentSync<T extends Record<string, Any> = Record<string, Any>> =\n  TranslateRequestBase & AgentActionSync\n\n/** @beta */\nexport type TranslateDocumentAsync = TranslateRequestBase & AgentActionAsync\n\n/** @beta */\nexport type TranslateDocument<T extends Record<string, Any> = Record<string, Any>> =\n  | TranslateDocumentSync<T>\n  | TranslateDocumentAsync\n\nexport function _translate<DocumentShape extends Record<string, Any>>(\n  client: SanityClient | ObservableSanityClient,\n  httpRequest: HttpRequest,\n  request: TranslateDocument<DocumentShape>,\n): Observable<\n  (typeof request)['async'] extends true\n    ? {_id: string}\n    : IdentifiedSanityDocumentStub & DocumentShape\n> {\n  const dataset = hasDataset(client.config())\n  return _request(client, httpRequest, {\n    method: 'POST',\n    uri: `/agent/action/translate/${dataset}`,\n    body: request,\n  })\n}\n", "import {lastValueFrom, type Observable} from 'rxjs'\n\nimport type {ObservableSanityClient, SanityClient} from '../../SanityClient'\nimport type {Any, HttpRequest, IdentifiedSanityDocumentStub} from '../../types'\nimport {_generate, type GenerateInstruction} from './generate'\nimport {_patch, type PatchDocument} from './patch'\nimport {_prompt, type PromptRequest} from './prompt'\nimport {_transform, type TransformDocument} from './transform'\nimport {_translate, type TranslateDocument} from './translate'\n\n/** @public */\nexport class ObservableAgentsActionClient {\n  #client: ObservableSanityClient\n  #httpRequest: HttpRequest\n  constructor(client: ObservableSanityClient, httpRequest: HttpRequest) {\n    this.#client = client\n    this.#httpRequest = httpRequest\n  }\n\n  /**\n   * Run an instruction to generate content in a target document.\n   * @param request - instruction request\n   */\n  generate<DocumentShape extends Record<string, Any>>(\n    request: GenerateInstruction<DocumentShape>,\n  ): Observable<\n    (typeof request)['async'] extends true\n      ? {_id: string}\n      : IdentifiedSanityDocumentStub & DocumentShape\n  > {\n    return _generate(this.#client, this.#httpRequest, request)\n  }\n\n  /**\n   * Transform a target document based on a source.\n   * @param request - translation request\n   */\n  transform<DocumentShape extends Record<string, Any>>(\n    request: TransformDocument<DocumentShape>,\n  ): Observable<\n    (typeof request)['async'] extends true\n      ? {_id: string}\n      : IdentifiedSanityDocumentStub & DocumentShape\n  > {\n    return _transform(this.#client, this.#httpRequest, request)\n  }\n\n  /**\n   * Translate a target document based on a source.\n   * @param request - translation request\n   */\n  translate<DocumentShape extends Record<string, Any>>(\n    request: TranslateDocument<DocumentShape>,\n  ): Observable<\n    (typeof request)['async'] extends true\n      ? {_id: string}\n      : IdentifiedSanityDocumentStub & DocumentShape\n  > {\n    return _translate(this.#client, this.#httpRequest, request)\n  }\n}\n\n/** @public */\nexport class AgentActionsClient {\n  #client: SanityClient\n  #httpRequest: HttpRequest\n  constructor(client: SanityClient, httpRequest: HttpRequest) {\n    this.#client = client\n    this.#httpRequest = httpRequest\n  }\n\n  /**\n   * Run an instruction to generate content in a target document.\n   * @param request - instruction request\n   */\n  generate<DocumentShape extends Record<string, Any>>(\n    request: GenerateInstruction<DocumentShape>,\n  ): Promise<\n    (typeof request)['async'] extends true\n      ? {_id: string}\n      : IdentifiedSanityDocumentStub & DocumentShape\n  > {\n    return lastValueFrom(_generate(this.#client, this.#httpRequest, request))\n  }\n\n  /**\n   * Transform a target document based on a source.\n   * @param request - translation request\n   */\n  transform<DocumentShape extends Record<string, Any>>(\n    request: TransformDocument<DocumentShape>,\n  ): Promise<\n    (typeof request)['async'] extends true\n      ? {_id: string}\n      : IdentifiedSanityDocumentStub & DocumentShape\n  > {\n    return lastValueFrom(_transform(this.#client, this.#httpRequest, request))\n  }\n\n  /**\n   * Translate a target document based on a source.\n   * @param request - translation request\n   */\n  translate<DocumentShape extends Record<string, Any>>(\n    request: TranslateDocument<DocumentShape>,\n  ): Promise<\n    (typeof request)['async'] extends true\n      ? {_id: string}\n      : IdentifiedSanityDocumentStub & DocumentShape\n  > {\n    return lastValueFrom(_translate(this.#client, this.#httpRequest, request))\n  }\n\n  /**\n   * Run a raw instruction and return the result either as text or json\n   * @param request - prompt request\n   */\n  prompt<const DocumentShape extends Record<string, Any>>(\n    request: PromptRequest<DocumentShape>,\n  ): Promise<(typeof request)['format'] extends 'json' ? DocumentShape : string> {\n    return lastValueFrom(_prompt(this.#client, this.#httpRequest, request))\n  }\n\n  /**\n   * Patch a document using a schema aware API.\n   * Does not use an LLM, but uses the schema to ensure paths and values matches the schema.\n   * @param request - instruction request\n   */\n  patch<DocumentShape extends Record<string, Any>>(\n    request: PatchDocument<DocumentShape>,\n  ): Promise<\n    (typeof request)['async'] extends true\n      ? {_id: string}\n      : IdentifiedSanityDocumentStub & DocumentShape\n  > {\n    return lastValueFrom(_patch(this.#client, this.#httpRequest, request))\n  }\n}\n", "import {lastValue<PERSON>rom, type Observable} from 'rxjs'\nimport {filter, map} from 'rxjs/operators'\n\nimport {_requestObservable} from '../data/dataMethods'\nimport type {ObservableSanityClient, SanityClient} from '../SanityClient'\nimport type {\n  Any,\n  HttpRequest,\n  HttpRequestEvent,\n  InitializedClientConfig,\n  ResponseEvent,\n  SanityAssetDocument,\n  SanityImageAssetDocument,\n  UploadBody,\n  UploadClientConfig,\n} from '../types'\nimport * as validators from '../validators'\n\n/** @internal */\nexport class ObservableAssetsClient {\n  #client: ObservableSanityClient\n  #httpRequest: HttpRequest\n  constructor(client: ObservableSanityClient, httpRequest: HttpRequest) {\n    this.#client = client\n    this.#httpRequest = httpRequest\n  }\n\n  /**\n   * Uploads a file asset to the configured dataset\n   *\n   * @param assetType - Asset type (file)\n   * @param body - Asset content - can be a browser File instance, a Blob, a Node.js Buffer instance or a Node.js ReadableStream.\n   * @param options - Options to use for the upload\n   */\n  upload(\n    assetType: 'file',\n    body: UploadBody,\n    options?: UploadClientConfig,\n  ): Observable<HttpRequestEvent<{document: SanityAssetDocument}>>\n\n  /**\n   * Uploads an image asset to the configured dataset\n   *\n   * @param assetType - Asset type (image)\n   * @param body - Asset content - can be a browser File instance, a Blob, a Node.js Buffer instance or a Node.js ReadableStream.\n   * @param options - Options to use for the upload\n   */\n  upload(\n    assetType: 'image',\n    body: UploadBody,\n    options?: UploadClientConfig,\n  ): Observable<HttpRequestEvent<{document: SanityImageAssetDocument}>>\n  /**\n   * Uploads a file or an image asset to the configured dataset\n   *\n   * @param assetType - Asset type (file/image)\n   * @param body - Asset content - can be a browser File instance, a Blob, a Node.js Buffer instance or a Node.js ReadableStream.\n   * @param options - Options to use for the upload\n   */\n  upload(\n    assetType: 'file' | 'image',\n    body: UploadBody,\n    options?: UploadClientConfig,\n  ): Observable<HttpRequestEvent<{document: SanityAssetDocument | SanityImageAssetDocument}>>\n  upload(\n    assetType: 'file' | 'image',\n    body: UploadBody,\n    options?: UploadClientConfig,\n  ): Observable<HttpRequestEvent<{document: SanityAssetDocument | SanityImageAssetDocument}>> {\n    return _upload(this.#client, this.#httpRequest, assetType, body, options)\n  }\n}\n\n/** @internal */\nexport class AssetsClient {\n  #client: SanityClient\n  #httpRequest: HttpRequest\n  constructor(client: SanityClient, httpRequest: HttpRequest) {\n    this.#client = client\n    this.#httpRequest = httpRequest\n  }\n\n  /**\n   * Uploads a file asset to the configured dataset\n   *\n   * @param assetType - Asset type (file)\n   * @param body - Asset content - can be a browser File instance, a Blob, a Node.js Buffer instance or a Node.js ReadableStream.\n   * @param options - Options to use for the upload\n   */\n  upload(\n    assetType: 'file',\n    body: UploadBody,\n    options?: UploadClientConfig,\n  ): Promise<SanityAssetDocument>\n  /**\n   * Uploads an image asset to the configured dataset\n   *\n   * @param assetType - Asset type (image)\n   * @param body - Asset content - can be a browser File instance, a Blob, a Node.js Buffer instance or a Node.js ReadableStream.\n   * @param options - Options to use for the upload\n   */\n  upload(\n    assetType: 'image',\n    body: UploadBody,\n    options?: UploadClientConfig,\n  ): Promise<SanityImageAssetDocument>\n  /**\n   * Uploads a file or an image asset to the configured dataset\n   *\n   * @param assetType - Asset type (file/image)\n   * @param body - Asset content - can be a browser File instance, a Blob, a Node.js Buffer instance or a Node.js ReadableStream.\n   * @param options - Options to use for the upload\n   */\n  upload(\n    assetType: 'file' | 'image',\n    body: UploadBody,\n    options?: UploadClientConfig,\n  ): Promise<SanityAssetDocument | SanityImageAssetDocument>\n  upload(\n    assetType: 'file' | 'image',\n    body: UploadBody,\n    options?: UploadClientConfig,\n  ): Promise<SanityAssetDocument | SanityImageAssetDocument> {\n    const observable = _upload(this.#client, this.#httpRequest, assetType, body, options)\n    return lastValueFrom(\n      observable.pipe(\n        filter((event: Any) => event.type === 'response'),\n        map(\n          (event) =>\n            (event as ResponseEvent<{document: SanityAssetDocument | SanityImageAssetDocument}>)\n              .body.document,\n        ),\n      ),\n    )\n  }\n}\n\nfunction _upload(\n  client: SanityClient | ObservableSanityClient,\n  httpRequest: HttpRequest,\n  assetType: 'image' | 'file',\n  body: UploadBody,\n  opts: UploadClientConfig = {},\n): Observable<HttpRequestEvent<{document: SanityAssetDocument | SanityImageAssetDocument}>> {\n  validators.validateAssetType(assetType)\n\n  // If an empty array is given, explicitly set `none` to override API defaults\n  let meta = opts.extract || undefined\n  if (meta && !meta.length) {\n    meta = ['none']\n  }\n\n  const config = client.config()\n  const options = optionsFromFile(opts, body)\n  const {tag, label, title, description, creditLine, filename, source} = options\n  const query: Any = {\n    label,\n    title,\n    description,\n    filename,\n    meta,\n    creditLine,\n  }\n  if (source) {\n    query.sourceId = source.id\n    query.sourceName = source.name\n    query.sourceUrl = source.url\n  }\n\n  return _requestObservable(client, httpRequest, {\n    tag,\n    method: 'POST',\n    timeout: options.timeout || 0,\n    uri: buildAssetUploadUrl(config, assetType),\n    headers: options.contentType ? {'Content-Type': options.contentType} : {},\n    query,\n    body,\n  })\n}\n\nfunction buildAssetUploadUrl(config: InitializedClientConfig, assetType: 'image' | 'file'): string {\n  const assetTypeEndpoint = assetType === 'image' ? 'images' : 'files'\n\n  if (config['~experimental_resource']) {\n    const {type, id} = config['~experimental_resource']\n    switch (type) {\n      case 'dataset': {\n        throw new Error(\n          'Assets are not supported for dataset resources, yet. Configure the client with `{projectId: <projectId>, dataset: <datasetId>}` instead.',\n        )\n      }\n      case 'canvas': {\n        return `/canvases/${id}/assets/${assetTypeEndpoint}`\n      }\n      case 'media-library': {\n        return `/media-libraries/${id}/upload`\n      }\n      case 'dashboard': {\n        return `/dashboards/${id}/assets/${assetTypeEndpoint}`\n      }\n      default:\n        // @ts-expect-error - handle all supported resource types\n        throw new Error(`Unsupported resource type: ${type.toString()}`)\n    }\n  }\n\n  const dataset = validators.hasDataset(config)\n  return `assets/${assetTypeEndpoint}/${dataset}`\n}\n\nfunction optionsFromFile(opts: Record<string, Any>, file: Any) {\n  if (typeof File === 'undefined' || !(file instanceof File)) {\n    return opts\n  }\n\n  return Object.assign(\n    {\n      filename: opts.preserveFilename === false ? undefined : file.name,\n      contentType: file.type,\n    },\n    opts,\n  )\n}\n", "import type {Any} from '../types'\n\nexport default (obj: Any, defaults: Any) =>\n  Object.keys(defaults)\n    .concat(Object.keys(obj))\n    .reduce((target, prop) => {\n      target[prop] = typeof obj[prop] === 'undefined' ? defaults[prop] : obj[prop]\n\n      return target\n    }, {} as Any)\n", "import {type Any} from '../types'\n\nexport const pick = (obj: Any, props: Any) =>\n  props.reduce((selection: Any, prop: Any) => {\n    if (typeof obj[prop] === 'undefined') {\n      return selection\n    }\n\n    selection[prop] = obj[prop]\n    return selection\n  }, {})\n", "import {defer, shareReplay} from 'rxjs'\nimport {map} from 'rxjs/operators'\n\nexport const eventSourcePolyfill = defer(() => import('@sanity/eventsource')).pipe(\n  map(({default: EventSource}) => EventSource as unknown as typeof globalThis.EventSource),\n  shareReplay(1),\n)\n", "import {\n  catchError,\n  concat,\n  mergeMap,\n  Observable,\n  of,\n  type OperatorFunction,\n  throwError,\n  timer,\n} from 'rxjs'\n\nimport {ConnectionFailedError} from './eventsource'\n\n/**\n * Note: connection failure is not the same as network disconnect which may happen more frequent.\n * The EventSource instance will automatically reconnect in case of a network disconnect, however,\n * in some rare cases a ConnectionFailed Error will be thrown and this operator explicitly retries these\n */\nexport function reconnectOnConnectionFailure<T>(): OperatorFunction<T, T | {type: 'reconnect'}> {\n  return function (source: Observable<T>) {\n    return source.pipe(\n      catchError((err, caught) => {\n        if (err instanceof ConnectionFailedError) {\n          return concat(of({type: 'reconnect' as const}), timer(1000).pipe(mergeMap(() => caught)))\n        }\n        return throwError(() => err)\n      }),\n    )\n  }\n}\n", "import {Observable, of, throwError} from 'rxjs'\nimport {filter, map} from 'rxjs/operators'\n\nimport type {ObservableSanityClient, SanityClient} from '../SanityClient'\nimport {\n  type Any,\n  type ListenEvent,\n  type ListenOptions,\n  type ListenParams,\n  type MutationEvent,\n} from '../types'\nimport defaults from '../util/defaults'\nimport {pick} from '../util/pick'\nimport {_getDataUrl} from './dataMethods'\nimport {encodeQueryString} from './encodeQueryString'\nimport {connectEventSource} from './eventsource'\nimport {eventSourcePolyfill} from './eventsourcePolyfill'\nimport {reconnectOnConnectionFailure} from './reconnectOnConnectionFailure'\n\n// Limit is 16K for a _request_, eg including headers. Have to account for an\n// unknown range of headers, but an average EventSource request from Chrome seems\n// to have around 700 bytes of cruft, so let us account for 1.2K to be \"safe\"\nconst MAX_URL_LENGTH = 16000 - 1200\n\nconst possibleOptions = [\n  'includePreviousRevision',\n  'includeResult',\n  'includeMutations',\n  'includeAllVersions',\n  'visibility',\n  'effectFormat',\n  'tag',\n]\n\nconst defaultOptions = {\n  includeResult: true,\n}\n\n/**\n * Set up a listener that will be notified when mutations occur on documents matching the provided query/filter.\n *\n * @param query - GROQ-filter to listen to changes for\n * @param params - Optional query parameters\n * @param options - Optional listener options\n * @public\n */\nexport function _listen<R extends Record<string, Any> = Record<string, Any>>(\n  this: SanityClient | ObservableSanityClient,\n  query: string,\n  params?: ListenParams,\n): Observable<MutationEvent<R>>\n/**\n * Set up a listener that will be notified when mutations occur on documents matching the provided query/filter.\n *\n * @param query - GROQ-filter to listen to changes for\n * @param params - Optional query parameters\n * @param options - Optional listener options\n * @public\n */\nexport function _listen<R extends Record<string, Any> = Record<string, Any>>(\n  this: SanityClient | ObservableSanityClient,\n  query: string,\n  params?: ListenParams,\n  options?: ListenOptions,\n): Observable<ListenEvent<R>>\n/** @public */\nexport function _listen<R extends Record<string, Any> = Record<string, Any>>(\n  this: SanityClient | ObservableSanityClient,\n  query: string,\n  params?: ListenParams,\n  opts: ListenOptions = {},\n): Observable<MutationEvent<R> | ListenEvent<R>> {\n  const {url, token, withCredentials, requestTagPrefix, headers: configHeaders} = this.config()\n  const tag = opts.tag && requestTagPrefix ? [requestTagPrefix, opts.tag].join('.') : opts.tag\n  const options = {...defaults(opts, defaultOptions), tag}\n  const listenOpts = pick(options, possibleOptions)\n  const qs = encodeQueryString({query, params, options: {tag, ...listenOpts}})\n\n  const uri = `${url}${_getDataUrl(this, 'listen', qs)}`\n  if (uri.length > MAX_URL_LENGTH) {\n    return throwError(() => new Error('Query too large for listener'))\n  }\n\n  const listenFor = options.events ? options.events : ['mutation']\n\n  const esOptions: EventSourceInit & {headers?: Record<string, string>} = {}\n  if (withCredentials) {\n    esOptions.withCredentials = true\n  }\n\n  if (token || configHeaders) {\n    esOptions.headers = {}\n\n    if (token) {\n      esOptions.headers.Authorization = `Bearer ${token}`\n    }\n\n    if (configHeaders) {\n      Object.assign(esOptions.headers, configHeaders)\n    }\n  }\n\n  const initEventSource = () =>\n    // use polyfill if there is no global EventSource or if we need to set headers\n    (typeof EventSource === 'undefined' || esOptions.headers\n      ? eventSourcePolyfill\n      : of(EventSource)\n    ).pipe(map((EventSource) => new EventSource(uri, esOptions)))\n\n  return connectEventSource(initEventSource, listenFor).pipe(\n    reconnectOnConnectionFailure(),\n    filter((event) => listenFor.includes(event.type)),\n    map(\n      (event) =>\n        ({\n          type: event.type,\n          ...('data' in event ? (event.data as object) : {}),\n        }) as MutationEvent<R> | ListenEvent<R>,\n    ),\n  )\n}\n", "import {\n  finalize,\n  merge,\n  type MonoTypeOperatorFunction,\n  Observable,\n  share,\n  type ShareConfig,\n  tap,\n} from 'rxjs'\n\nexport type ShareReplayLatestConfig<T> = ShareConfig<T> & {predicate: (value: T) => boolean}\n\n/**\n * A variant of share that takes a predicate function to determine which value to replay to new subscribers\n * @param predicate - Predicate function to determine which value to replay\n */\nexport function shareReplayLatest<T>(predicate: (value: T) => boolean): MonoTypeOperatorFunction<T>\n\n/**\n * A variant of share that takes a predicate function to determine which value to replay to new subscribers\n * @param config - ShareConfig with additional predicate function\n */\nexport function shareReplayLatest<T>(\n  config: ShareReplayLatestConfig<T>,\n): MonoTypeOperatorFunction<T>\n\n/**\n * A variant of share that takes a predicate function to determine which value to replay to new subscribers\n * @param configOrPredicate - Predicate function to determine which value to replay\n * @param config - Optional ShareConfig\n */\nexport function shareReplayLatest<T>(\n  configOrPredicate: ShareReplayLatestConfig<T> | ShareReplayLatestConfig<T>['predicate'],\n  config?: ShareConfig<T>,\n) {\n  return _shareReplayLatest(\n    typeof configOrPredicate === 'function'\n      ? {predicate: configOrPredicate, ...config}\n      : configOrPredicate,\n  )\n}\nfunction _shareReplayLatest<T>(config: ShareReplayLatestConfig<T>): MonoTypeOperatorFunction<T> {\n  return (source: Observable<T>) => {\n    let latest: T | undefined\n    let emitted = false\n\n    // eslint-disable-next-line unused-imports/no-unused-vars\n    const {predicate, ...shareConfig} = config\n\n    const wrapped = source.pipe(\n      tap((value) => {\n        if (config.predicate(value)) {\n          emitted = true\n          latest = value\n        }\n      }),\n      finalize(() => {\n        emitted = false\n        latest = undefined\n      }),\n      share(shareConfig),\n    )\n    const emitLatest = new Observable<T>((subscriber) => {\n      if (emitted) {\n        subscriber.next(\n          // this cast is safe because of the emitted check which asserts that we got T from the source\n          latest as T,\n        )\n      }\n      subscriber.complete()\n    })\n    return merge(wrapped, emitLatest)\n  }\n}\n", "import {catchError, concat, EMPTY, mergeMap, Observable, of} from 'rxjs'\nimport {finalize, map} from 'rxjs/operators'\n\nimport {CorsOriginError} from '../http/errors'\nimport type {ObservableSanityClient, SanityClient} from '../SanityClient'\nimport type {\n  LiveEvent,\n  LiveEventGoAway,\n  LiveEventMessage,\n  LiveEventReconnect,\n  LiveEventRestart,\n  LiveEventWelcome,\n  SyncTag,\n} from '../types'\nimport {shareReplayLatest} from '../util/shareReplayLatest'\nimport * as validate from '../validators'\nimport {_getDataUrl} from './dataMethods'\nimport {connectEventSource} from './eventsource'\nimport {eventSourcePolyfill} from './eventsourcePolyfill'\nimport {reconnectOnConnectionFailure} from './reconnectOnConnectionFailure'\n\nconst requiredApiVersion = '2021-03-25'\n\n/**\n * @public\n */\nexport class LiveClient {\n  #client: SanityClient | ObservableSanityClient\n  constructor(client: SanityClient | ObservableSanityClient) {\n    this.#client = client\n  }\n\n  /**\n   * Requires `apiVersion` to be `2021-03-25` or later.\n   */\n  events({\n    includeDrafts = false,\n    tag: _tag,\n  }: {\n    includeDrafts?: boolean\n    /**\n     * Optional request tag for the listener. Use to identify the request in logs.\n     *\n     * @defaultValue `undefined`\n     */\n    tag?: string\n  } = {}): Observable<LiveEvent> {\n    validate.resourceGuard('live', this.#client.config())\n    const {\n      projectId,\n      apiVersion: _apiVersion,\n      token,\n      withCredentials,\n      requestTagPrefix,\n      headers: configHeaders,\n    } = this.#client.config()\n    const apiVersion = _apiVersion.replace(/^v/, '')\n    if (apiVersion !== 'X' && apiVersion < requiredApiVersion) {\n      throw new Error(\n        `The live events API requires API version ${requiredApiVersion} or later. ` +\n          `The current API version is ${apiVersion}. ` +\n          `Please update your API version to use this feature.`,\n      )\n    }\n    if (includeDrafts && !token && !withCredentials) {\n      throw new Error(\n        `The live events API requires a token or withCredentials when 'includeDrafts: true'. Please update your client configuration. The token should have the lowest possible access role.`,\n      )\n    }\n    const path = _getDataUrl(this.#client, 'live/events')\n    const url = new URL(this.#client.getUrl(path, false))\n    const tag = _tag && requestTagPrefix ? [requestTagPrefix, _tag].join('.') : _tag\n    if (tag) {\n      url.searchParams.set('tag', tag)\n    }\n    if (includeDrafts) {\n      url.searchParams.set('includeDrafts', 'true')\n    }\n    const esOptions: EventSourceInit & {headers?: Record<string, string>} = {}\n    if (includeDrafts && withCredentials) {\n      esOptions.withCredentials = true\n    }\n\n    if ((includeDrafts && token) || configHeaders) {\n      esOptions.headers = {}\n\n      if (includeDrafts && token) {\n        esOptions.headers.Authorization = `Bearer ${token}`\n      }\n\n      if (configHeaders) {\n        Object.assign(esOptions.headers, configHeaders)\n      }\n    }\n\n    const key = `${url.href}::${JSON.stringify(esOptions)}`\n    const existing = eventsCache.get(key)\n\n    if (existing) {\n      return existing\n    }\n\n    const initEventSource = () =>\n      // use polyfill if there is no global EventSource or if we need to set headers\n      (typeof EventSource === 'undefined' || esOptions.headers\n        ? eventSourcePolyfill\n        : of(EventSource)\n      ).pipe(map((EventSource) => new EventSource(url.href, esOptions)))\n\n    const events = connectEventSource(initEventSource, [\n      'message',\n      'restart',\n      'welcome',\n      'reconnect',\n      'goaway',\n    ]).pipe(\n      reconnectOnConnectionFailure(),\n      map((event) => {\n        if (event.type === 'message') {\n          const {data, ...rest} = event\n          // Splat data properties from the eventsource message onto the returned event\n          return {...rest, tags: (data as {tags: SyncTag[]}).tags} as LiveEventMessage\n        }\n        return event as LiveEventRestart | LiveEventReconnect | LiveEventWelcome | LiveEventGoAway\n      }),\n    )\n\n    // Detect if CORS is allowed, the way the CORS is checked supports preflight caching, so when the EventSource boots up it knows it sees the preflight was already made and we're good to go\n    const checkCors = fetchObservable(url, {\n      method: 'OPTIONS',\n      mode: 'cors',\n      credentials: esOptions.withCredentials ? 'include' : 'omit',\n      headers: esOptions.headers,\n    }).pipe(\n      mergeMap(() => EMPTY),\n      catchError(() => {\n        // If the request fails, then we assume it was due to CORS, and we rethrow a special error that allows special handling in userland\n        throw new CorsOriginError({projectId: projectId!})\n      }),\n    )\n    const observable = concat(checkCors, events).pipe(\n      finalize(() => eventsCache.delete(key)),\n      shareReplayLatest({\n        predicate: (event) => event.type === 'welcome',\n      }),\n    )\n    eventsCache.set(key, observable)\n    return observable\n  }\n}\n\nfunction fetchObservable(url: URL, init: RequestInit) {\n  return new Observable((observer) => {\n    const controller = new AbortController()\n    const signal = controller.signal\n    fetch(url, {...init, signal: controller.signal}).then(\n      (response) => {\n        observer.next(response)\n        observer.complete()\n      },\n      (err) => {\n        if (!signal.aborted) {\n          observer.error(err)\n        }\n      },\n    )\n    return () => controller.abort()\n  })\n}\n\nconst eventsCache = new Map<string, Observable<LiveEvent>>()\n", "import {lastValue<PERSON>rom, type Observable} from 'rxjs'\n\nimport {_request} from '../data/dataMethods'\nimport type {ObservableSanityClient, SanityClient} from '../SanityClient'\nimport type {DatasetAclMode, DatasetResponse, DatasetsResponse, HttpRequest} from '../types'\nimport * as validate from '../validators'\n\n/** @internal */\nexport class ObservableDatasetsClient {\n  #client: ObservableSanityClient\n  #httpRequest: HttpRequest\n  constructor(client: ObservableSanityClient, httpRequest: HttpRequest) {\n    this.#client = client\n    this.#httpRequest = httpRequest\n  }\n\n  /**\n   * Create a new dataset with the given name\n   *\n   * @param name - Name of the dataset to create\n   * @param options - Options for the dataset\n   */\n  create(name: string, options?: {aclMode?: DatasetAclMode}): Observable<DatasetResponse> {\n    return _modify<DatasetResponse>(this.#client, this.#httpRequest, 'PUT', name, options)\n  }\n\n  /**\n   * Edit a dataset with the given name\n   *\n   * @param name - Name of the dataset to edit\n   * @param options - New options for the dataset\n   */\n  edit(name: string, options?: {aclMode?: DatasetAclMode}): Observable<DatasetResponse> {\n    return _modify<DatasetResponse>(this.#client, this.#httpRequest, 'PATCH', name, options)\n  }\n\n  /**\n   * Delete a dataset with the given name\n   *\n   * @param name - Name of the dataset to delete\n   */\n  delete(name: string): Observable<{deleted: true}> {\n    return _modify<{deleted: true}>(this.#client, this.#httpRequest, 'DELETE', name)\n  }\n\n  /**\n   * Fetch a list of datasets for the configured project\n   */\n  list(): Observable<DatasetsResponse> {\n    return _request<DatasetsResponse>(this.#client, this.#httpRequest, {\n      uri: '/datasets',\n      tag: null,\n    })\n  }\n}\n\n/** @internal */\nexport class DatasetsClient {\n  #client: SanityClient\n  #httpRequest: HttpRequest\n  constructor(client: SanityClient, httpRequest: HttpRequest) {\n    this.#client = client\n    this.#httpRequest = httpRequest\n  }\n\n  /**\n   * Create a new dataset with the given name\n   *\n   * @param name - Name of the dataset to create\n   * @param options - Options for the dataset\n   */\n  create(name: string, options?: {aclMode?: DatasetAclMode}): Promise<DatasetResponse> {\n    validate.resourceGuard('dataset', this.#client.config())\n    return lastValueFrom(\n      _modify<DatasetResponse>(this.#client, this.#httpRequest, 'PUT', name, options),\n    )\n  }\n\n  /**\n   * Edit a dataset with the given name\n   *\n   * @param name - Name of the dataset to edit\n   * @param options - New options for the dataset\n   */\n  edit(name: string, options?: {aclMode?: DatasetAclMode}): Promise<DatasetResponse> {\n    validate.resourceGuard('dataset', this.#client.config())\n    return lastValueFrom(\n      _modify<DatasetResponse>(this.#client, this.#httpRequest, 'PATCH', name, options),\n    )\n  }\n\n  /**\n   * Delete a dataset with the given name\n   *\n   * @param name - Name of the dataset to delete\n   */\n  delete(name: string): Promise<{deleted: true}> {\n    validate.resourceGuard('dataset', this.#client.config())\n    return lastValueFrom(_modify<{deleted: true}>(this.#client, this.#httpRequest, 'DELETE', name))\n  }\n\n  /**\n   * Fetch a list of datasets for the configured project\n   */\n  list(): Promise<DatasetsResponse> {\n    validate.resourceGuard('dataset', this.#client.config())\n    return lastValueFrom(\n      _request<DatasetsResponse>(this.#client, this.#httpRequest, {uri: '/datasets', tag: null}),\n    )\n  }\n}\n\nfunction _modify<R = unknown>(\n  client: SanityClient | ObservableSanityClient,\n  httpRequest: HttpRequest,\n  method: 'DELETE' | 'PATCH' | 'PUT',\n  name: string,\n  options?: {aclMode?: DatasetAclMode},\n) {\n  validate.resourceGuard('dataset', client.config())\n  validate.dataset(name)\n  return _request<R>(client, httpRequest, {\n    method,\n    uri: `/datasets/${name}`,\n    body: options,\n    tag: null,\n  })\n}\n", "import {lastValue<PERSON>rom, type Observable} from 'rxjs'\n\nimport {_request} from '../data/dataMethods'\nimport type {ObservableSanityClient, SanityClient} from '../SanityClient'\nimport type {HttpRequest, SanityProject} from '../types'\nimport * as validate from '../validators'\n\n/** @internal */\nexport class ObservableProjectsClient {\n  #client: ObservableSanityClient\n  #httpRequest: HttpRequest\n  constructor(client: ObservableSanityClient, httpRequest: HttpRequest) {\n    this.#client = client\n    this.#httpRequest = httpRequest\n  }\n\n  /**\n   * Fetch a list of projects the authenticated user has access to.\n   *\n   * @param options - Options for the list request\n   *   - `includeMembers` - Whether to include members in the response (default: true)\n   */\n  list(options?: {includeMembers?: true}): Observable<SanityProject[]>\n  list(options?: {includeMembers?: false}): Observable<Omit<SanityProject, 'members'>[]>\n  list(options?: {\n    includeMembers?: boolean\n  }): Observable<SanityProject[] | Omit<SanityProject, 'members'>[]> {\n    validate.resourceGuard('projects', this.#client.config())\n    const uri = options?.includeMembers === false ? '/projects?includeMembers=false' : '/projects'\n    return _request<SanityProject[]>(this.#client, this.#httpRequest, {uri})\n  }\n\n  /**\n   * Fetch a project by project ID\n   *\n   * @param projectId - ID of the project to fetch\n   */\n  getById(projectId: string): Observable<SanityProject> {\n    validate.resourceGuard('projects', this.#client.config())\n    return _request<SanityProject>(this.#client, this.#httpRequest, {uri: `/projects/${projectId}`})\n  }\n}\n\n/** @internal */\nexport class ProjectsClient {\n  #client: SanityClient\n  #httpRequest: HttpRequest\n  constructor(client: SanityClient, httpRequest: HttpRequest) {\n    this.#client = client\n    this.#httpRequest = httpRequest\n  }\n\n  /**\n   * Fetch a list of projects the authenticated user has access to.\n   *\n   * @param options - Options for the list request\n   *   - `includeMembers` - Whether to include members in the response (default: true)\n   */\n  list(options?: {includeMembers?: true}): Promise<SanityProject[]>\n  list(options?: {includeMembers?: false}): Promise<Omit<SanityProject, 'members'>[]>\n  list(options?: {includeMembers?: boolean}): Promise<SanityProject[]> {\n    validate.resourceGuard('projects', this.#client.config())\n    const uri = options?.includeMembers === false ? '/projects?includeMembers=false' : '/projects'\n    return lastValueFrom(_request<SanityProject[]>(this.#client, this.#httpRequest, {uri}))\n  }\n\n  /**\n   * Fetch a project by project ID\n   *\n   * @param projectId - ID of the project to fetch\n   */\n  getById(projectId: string): Promise<SanityProject> {\n    validate.resourceGuard('projects', this.#client.config())\n    return lastValueFrom(\n      _request<SanityProject>(this.#client, this.#httpRequest, {uri: `/projects/${projectId}`}),\n    )\n  }\n}\n", "import {\n  getDraftId,\n  getVersionFromId,\n  getVersionId,\n  isDraftId,\n  isVersionId,\n} from '@sanity/client/csm'\nimport {customAlphabet} from 'nanoid'\n\nimport type {IdentifiedSanityDocumentStub, SanityDocumentStub} from '../types'\nimport {validateVersionIdMatch} from '../validators'\n\n/**\n * @internal\n *\n * ~24 years (or 7.54e+8 seconds) needed, in order to have a 1% probability of at least one collision if 10 ID's are generated every hour.\n */\nexport const generateReleaseId = customAlphabet(\n  'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789',\n  8,\n)\n\n/** @internal */\nexport const getDocumentVersionId = (publishedId: string, releaseId?: string) =>\n  releaseId ? getVersionId(publishedId, releaseId) : getDraftId(publishedId)\n\n/** @internal */\nexport function deriveDocumentVersionId(\n  op: string,\n  {\n    releaseId,\n    publishedId,\n    document,\n  }: {\n    releaseId?: string\n    publishedId?: string\n    document: SanityDocumentStub | IdentifiedSanityDocumentStub\n  },\n): string {\n  if (publishedId && document._id) {\n    const versionId = getDocumentVersionId(publishedId, releaseId)\n    validateVersionIdMatch(versionId, document)\n    return versionId\n  }\n\n  if (document._id) {\n    const isDraft = isDraftId(document._id)\n    const isVersion = isVersionId(document._id)\n\n    if (!isDraft && !isVersion) {\n      throw new Error(\n        `\\`${op}()\\` requires a document with an \\`_id\\` that is a version or draft ID`,\n      )\n    }\n\n    if (releaseId) {\n      if (isDraft) {\n        throw new Error(\n          `\\`${op}()\\` was called with a document ID (\\`${document._id}\\`) that is a draft ID, but a release ID (\\`${releaseId}\\`) was also provided.`,\n        )\n      }\n\n      const builtVersionId = getVersionFromId(document._id)\n      if (builtVersionId !== releaseId) {\n        throw new Error(\n          `\\`${op}()\\` was called with a document ID (\\`${document._id}\\`) that is a version ID, but the release ID (\\`${releaseId}\\`) does not match the document's version ID (\\`${builtVersionId}\\`).`,\n        )\n      }\n    }\n\n    return document._id\n  }\n\n  if (publishedId) {\n    return getDocumentVersionId(publishedId, releaseId)\n  }\n\n  throw new Error(`\\`${op}()\\` requires either a publishedId or a document with an \\`_id\\``)\n}\n", "import type {BaseActionOptions, CreateReleaseAction, ReleaseDocument} from '@sanity/client'\n\nimport {generateReleaseId} from '../util/createVersionId'\n\ninterface ReleaseOrOptions extends BaseActionOptions {\n  releaseId?: string\n  metadata?: Partial<ReleaseDocument['metadata']>\n}\n\ninterface CompleteCreateReleaseAction extends CreateReleaseAction {\n  metadata: ReleaseDocument['metadata']\n}\n\nconst getArgs = (\n  releaseOrOptions?: ReleaseOrOptions,\n  maybeOptions?: BaseActionOptions,\n): [string, Partial<ReleaseDocument['metadata']>, BaseActionOptions | undefined] => {\n  const isReleaseInput =\n    typeof releaseOrOptions === 'object' &&\n    releaseOrOptions !== null &&\n    ('releaseId' in releaseOrOptions || 'metadata' in releaseOrOptions)\n\n  if (isReleaseInput) {\n    const {releaseId = generateReleaseId(), metadata = {}} = releaseOrOptions\n    return [releaseId, metadata, maybeOptions]\n  }\n\n  return [generateReleaseId(), {}, releaseOrOptions as BaseActionOptions]\n}\n\n/** @internal */\nexport const createRelease = (\n  releaseOrOptions?: ReleaseOrOptions,\n  maybeOptions?: BaseActionOptions,\n): {\n  action: CompleteCreateReleaseAction\n  options?: BaseActionOptions\n} => {\n  const [releaseId, metadata, options] = getArgs(releaseOrOptions, maybeOptions)\n\n  const finalMetadata: ReleaseDocument['metadata'] = {\n    ...metadata,\n    releaseType: metadata.releaseType || 'undecided',\n  }\n\n  const createAction: CompleteCreateReleaseAction = {\n    actionType: 'sanity.action.release.create',\n    releaseId,\n    metadata: finalMetadata,\n  }\n\n  return {action: createAction, options}\n}\n", "import {lastValueFrom, map, Observable} from 'rxjs'\n\nimport {_action, _getDocument, _getReleaseDocuments} from '../data/dataMethods'\nimport type {ObservableSanityClient, SanityClient} from '../SanityClient'\nimport type {\n  ArchiveReleaseAction,\n  BaseActionOptions,\n  BaseMutationOptions,\n  DeleteReleaseAction,\n  EditReleaseAction,\n  HttpRequest,\n  PatchOperations,\n  PublishReleaseAction,\n  RawQueryResponse,\n  ReleaseDocument,\n  SanityDocument,\n  ScheduleReleaseAction,\n  SingleActionResult,\n  UnarchiveReleaseAction,\n  UnscheduleReleaseAction,\n} from '../types'\nimport {createRelease} from './createRelease'\n\n/** @public */\nexport class ObservableReleasesClient {\n  #client: ObservableSanityClient\n  #httpRequest: HttpRequest\n  constructor(client: ObservableSanityClient, httpRequest: HttpRequest) {\n    this.#client = client\n    this.#httpRequest = httpRequest\n  }\n\n  /**\n   * @public\n   *\n   * Retrieve a release by id.\n   *\n   * @category Releases\n   *\n   * @param params - Release action parameters:\n   *   - `releaseId` - The id of the release to retrieve.\n   * @param options - Additional query options including abort signal and query tag.\n   * @returns An observable that resolves to the release document {@link ReleaseDocument}.\n   *\n   * @example Retrieving a release by id\n   * ```ts\n   * client.observable.releases.get({releaseId: 'my-release'}).pipe(\n   *   tap((release) => console.log(release)),\n   *   // {\n   *   //   _id: '_.releases.my-release',\n   *   //   name: 'my-release'\n   *   //   _type: 'system.release',\n   *   //   metadata: {releaseType: 'asap'},\n   *   //   _createdAt: '2021-01-01T00:00:00.000Z',\n   *   //   ...\n   *   // }\n   * ).subscribe()\n   * ```\n   */\n  get(\n    {releaseId}: {releaseId: string},\n    options?: {signal?: AbortSignal; tag?: string},\n  ): Observable<ReleaseDocument | undefined> {\n    return _getDocument<ReleaseDocument>(\n      this.#client,\n      this.#httpRequest,\n      `_.releases.${releaseId}`,\n      options,\n    )\n  }\n\n  /**\n   * @public\n   *\n   * Creates a new release under the given id, with metadata.\n   *\n   * @remarks\n   * * If no releaseId is provided, a release id will be generated.\n   * * If no metadata is provided, then an `undecided` releaseType will be used.\n   *\n   * @category Releases\n   *\n   * @param params - Release action parameters:\n   *   - `releaseId` - The id of the release to create.\n   *   - `metadata` - The metadata to associate with the release {@link ReleaseDocument}.\n   * @param options - Additional action options.\n   * @returns An observable that resolves to the `transactionId` and the release id and metadata.\n   *\n   * @example Creating a release with a custom id and metadata\n   * ```ts\n   * const releaseId = 'my-release'\n   * const metadata: ReleaseDocument['metadata'] = {\n   *   releaseType: 'asap',\n   * }\n   *\n   * client.observable.releases.create({releaseId, metadata}).pipe(\n   *   tap(({transactionId, releaseId, metadata}) => console.log(transactionId, releaseId, metadata)),\n   *   // {\n   *   //   transactionId: 'transaction-id',\n   *   //   releaseId: 'my-release',\n   *   //   metadata: {releaseType: 'asap'},\n   *   // }\n   * ).subscribe()\n   * ```\n   *\n   * @example Creating a release with generated id and metadata\n   * ```ts\n   * client.observable.releases.create().pipe(\n   *   tap(({metadata}) => console.log(metadata)),\n   *   // {\n   *   //   metadata: {releaseType: 'undecided'},\n   *   // }\n   * ).subscribe()\n   * ```\n   *\n   * @example Creating a release using a custom transaction id\n   * ```ts\n   * client.observable.releases.create({transactionId: 'my-transaction-id'}).pipe(\n   *   tap(({transactionId, metadata}) => console.log(transactionId, metadata)),\n   *   // {\n   *   //   transactionId: 'my-transaction-id',\n   *   //   metadata: {releaseType: 'undecided'},\n   *   // }\n   * ).subscribe()\n   * ```\n   */\n  create(\n    options: BaseActionOptions,\n  ): Observable<SingleActionResult & {releaseId: string; metadata: ReleaseDocument['metadata']}>\n  create(\n    release: {releaseId?: string; metadata?: Partial<ReleaseDocument['metadata']>},\n    options?: BaseActionOptions,\n  ): Observable<SingleActionResult & {releaseId: string; metadata: ReleaseDocument['metadata']}>\n  create(\n    releaseOrOptions?:\n      | {releaseId?: string; metadata?: Partial<ReleaseDocument['metadata']>}\n      | BaseActionOptions,\n    maybeOptions?: BaseActionOptions,\n  ): Observable<SingleActionResult & {releaseId: string; metadata: ReleaseDocument['metadata']}> {\n    const {action, options} = createRelease(releaseOrOptions, maybeOptions)\n    const {releaseId, metadata} = action\n\n    return _action(this.#client, this.#httpRequest, action, options).pipe(\n      map((actionResult) => ({\n        ...actionResult,\n        releaseId,\n        metadata,\n      })),\n    )\n  }\n\n  /**\n   * @public\n   *\n   * Edits an existing release, updating the metadata.\n   *\n   * @category Releases\n   *\n   * @param params - Release action parameters:\n   *   - `releaseId` - The id of the release to edit.\n   *   - `patch` - The patch operation to apply on the release metadata {@link PatchMutationOperation}.\n   * @param options - Additional action options.\n   * @returns An observable that resolves to the `transactionId`.\n   */\n  edit(\n    {releaseId, patch}: {releaseId: string; patch: PatchOperations},\n    options?: BaseActionOptions,\n  ): Observable<SingleActionResult> {\n    const editAction: EditReleaseAction = {\n      actionType: 'sanity.action.release.edit',\n      releaseId,\n      patch,\n    }\n\n    return _action(this.#client, this.#httpRequest, editAction, options)\n  }\n\n  /**\n   * @public\n   *\n   * Publishes all documents in a release at once. For larger releases the effect of the publish\n   * will be visible immediately when querying but the removal of the `versions.<releasesId>.*`\n   * documents and creation of the corresponding published documents with the new content may\n   * take some time.\n   *\n   * During this period both the source and target documents are locked and cannot be\n   * modified through any other means.\n   *\n   * @category Releases\n   *\n   * @param params - Release action parameters:\n   *   - `releaseId` - The id of the release to publish.\n   * @param options - Additional action options.\n   * @returns An observable that resolves to the `transactionId`.\n   */\n  publish(\n    {releaseId}: {releaseId: string},\n    options?: BaseActionOptions,\n  ): Observable<SingleActionResult> {\n    const publishAction: PublishReleaseAction = {\n      actionType: 'sanity.action.release.publish',\n      releaseId,\n    }\n\n    return _action(this.#client, this.#httpRequest, publishAction, options)\n  }\n\n  /**\n   * @public\n   *\n   * An archive action removes an active release. The documents that comprise the release\n   * are deleted and therefore no longer queryable.\n   *\n   * While the documents remain in retention the last version can still be accessed using document history endpoint.\n   *\n   * @category Releases\n   *\n   * @param params - Release action parameters:\n   *   - `releaseId` - The id of the release to archive.\n   * @param options - Additional action options.\n   * @returns An observable that resolves to the `transactionId`.\n   */\n  archive(\n    {releaseId}: {releaseId: string},\n    options?: BaseActionOptions,\n  ): Observable<SingleActionResult> {\n    const archiveAction: ArchiveReleaseAction = {\n      actionType: 'sanity.action.release.archive',\n      releaseId,\n    }\n\n    return _action(this.#client, this.#httpRequest, archiveAction, options)\n  }\n\n  /**\n   * @public\n   *\n   * An unarchive action restores an archived release and all documents\n   * with the content they had just prior to archiving.\n   *\n   * @category Releases\n   *\n   * @param params - Release action parameters:\n   *   - `releaseId` - The id of the release to unarchive.\n   * @param options - Additional action options.\n   * @returns An observable that resolves to the `transactionId`.\n   */\n  unarchive(\n    {releaseId}: {releaseId: string},\n    options?: BaseActionOptions,\n  ): Observable<SingleActionResult> {\n    const unarchiveAction: UnarchiveReleaseAction = {\n      actionType: 'sanity.action.release.unarchive',\n      releaseId,\n    }\n\n    return _action(this.#client, this.#httpRequest, unarchiveAction, options)\n  }\n\n  /**\n   * @public\n   *\n   * A schedule action queues a release for publishing at the given future time.\n   * The release is locked such that no documents in the release can be modified and\n   * no documents that it references can be deleted as this would make the publish fail.\n   * At the given time, the same logic as for the publish action is triggered.\n   *\n   * @category Releases\n   *\n   * @param params - Release action parameters:\n   *   - `releaseId` - The id of the release to schedule.\n   *   - `publishAt` - The serialised date and time to publish the release. If the `publishAt` is in the past, the release will be published immediately.\n   * @param options - Additional action options.\n   * @returns An observable that resolves to the `transactionId`.\n   */\n  schedule(\n    {releaseId, publishAt}: {releaseId: string; publishAt: string},\n    options?: BaseActionOptions,\n  ): Observable<SingleActionResult> {\n    const scheduleAction: ScheduleReleaseAction = {\n      actionType: 'sanity.action.release.schedule',\n      releaseId,\n      publishAt,\n    }\n\n    return _action(this.#client, this.#httpRequest, scheduleAction, options)\n  }\n\n  /**\n   * @public\n   *\n   * An unschedule action stops a release from being published.\n   * The documents in the release are considered unlocked and can be edited again.\n   * This may fail if another release is scheduled to be published after this one and\n   * has a reference to a document created by this one.\n   *\n   * @category Releases\n   *\n   * @param params - Release action parameters:\n   *   - `releaseId` - The id of the release to unschedule.\n   * @param options - Additional action options.\n   * @returns An observable that resolves to the `transactionId`.\n   */\n  unschedule(\n    {releaseId}: {releaseId: string},\n    options?: BaseActionOptions,\n  ): Observable<SingleActionResult> {\n    const unscheduleAction: UnscheduleReleaseAction = {\n      actionType: 'sanity.action.release.unschedule',\n      releaseId,\n    }\n\n    return _action(this.#client, this.#httpRequest, unscheduleAction, options)\n  }\n\n  /**\n   * @public\n   *\n   * A delete action removes a published or archived release.\n   * The backing system document will be removed from the dataset.\n   *\n   * @category Releases\n   *\n   * @param params - Release action parameters:\n   *   - `releaseId` - The id of the release to delete.\n   * @param options - Additional action options.\n   * @returns An observable that resolves to the `transactionId`.\n   */\n  delete(\n    {releaseId}: {releaseId: string},\n    options?: BaseActionOptions,\n  ): Observable<SingleActionResult> {\n    const deleteAction: DeleteReleaseAction = {\n      actionType: 'sanity.action.release.delete',\n      releaseId,\n    }\n\n    return _action(this.#client, this.#httpRequest, deleteAction, options)\n  }\n\n  /**\n   * @public\n   *\n   * Fetch the documents in a release by release id.\n   *\n   * @category Releases\n   *\n   * @param params - Release action parameters:\n   *   - `releaseId` - The id of the release to fetch documents for.\n   * @param options - Additional mutation options {@link BaseMutationOptions}.\n   * @returns An observable that resolves to the documents in the release.\n   */\n  fetchDocuments(\n    {releaseId}: {releaseId: string},\n    options?: BaseMutationOptions,\n  ): Observable<RawQueryResponse<SanityDocument[]>> {\n    return _getReleaseDocuments(this.#client, this.#httpRequest, releaseId, options)\n  }\n}\n\n/** @public */\nexport class ReleasesClient {\n  #client: SanityClient\n  #httpRequest: HttpRequest\n  constructor(client: SanityClient, httpRequest: HttpRequest) {\n    this.#client = client\n    this.#httpRequest = httpRequest\n  }\n\n  /**\n   * @public\n   *\n   * Retrieve a release by id.\n   *\n   * @category Releases\n   *\n   * @param params - Release action parameters:\n   *   - `releaseId` - The id of the release to retrieve.\n   * @param options - Additional query options including abort signal and query tag.\n   * @returns A promise that resolves to the release document {@link ReleaseDocument}.\n   *\n   * @example Retrieving a release by id\n   * ```ts\n   * const release = await client.releases.get({releaseId: 'my-release'})\n   * console.log(release)\n   * // {\n   * //   _id: '_.releases.my-release',\n   * //   name: 'my-release'\n   * //   _type: 'system.release',\n   * //   metadata: {releaseType: 'asap'},\n   * //   _createdAt: '2021-01-01T00:00:00.000Z',\n   * //   ...\n   * // }\n   * ```\n   */\n  get(\n    {releaseId}: {releaseId: string},\n    options?: {signal?: AbortSignal; tag?: string},\n  ): Promise<ReleaseDocument | undefined> {\n    return lastValueFrom(\n      _getDocument<ReleaseDocument>(\n        this.#client,\n        this.#httpRequest,\n        `_.releases.${releaseId}`,\n        options,\n      ),\n    )\n  }\n\n  /**\n   * @public\n   *\n   * Creates a new release under the given id, with metadata.\n   *\n   * @remarks\n   * * If no releaseId is provided, a release id will be generated.\n   * * If no metadata is provided, then an `undecided` releaseType will be used.\n   *\n   * @category Releases\n   *\n   * @param params - Release action parameters:\n   *   - `releaseId` - The id of the release to create.\n   *   - `metadata` - The metadata to associate with the release {@link ReleaseDocument}.\n   * @param options - Additional action options.\n   * @returns A promise that resolves to the `transactionId` and the release id and metadata.\n   *\n   * @example Creating a release with a custom id and metadata\n   * ```ts\n   * const releaseId = 'my-release'\n   * const releaseMetadata: ReleaseDocument['metadata'] = {\n   *   releaseType: 'asap',\n   * }\n   *\n   * const result =\n   *   await client.releases.create({releaseId, metadata: releaseMetadata})\n   * console.log(result)\n   * // {\n   * //   transactionId: 'transaction-id',\n   * //   releaseId: 'my-release',\n   * //   metadata: {releaseType: 'asap'},\n   * // }\n   * ```\n   *\n   * @example Creating a release with generated id and metadata\n   * ```ts\n   * const {metadata} = await client.releases.create()\n   * console.log(metadata.releaseType) // 'undecided'\n   * ```\n   *\n   * @example Creating a release with a custom transaction id\n   * ```ts\n   * const {transactionId, metadata} = await client.releases.create({transactionId: 'my-transaction-id'})\n   * console.log(metadata.releaseType) // 'undecided'\n   * console.log(transactionId) // 'my-transaction-id'\n   * ```\n   */\n  async create(\n    options: BaseActionOptions,\n  ): Promise<SingleActionResult & {releaseId: string; metadata: ReleaseDocument['metadata']}>\n  async create(\n    release: {releaseId?: string; metadata?: Partial<ReleaseDocument['metadata']>},\n    options?: BaseActionOptions,\n  ): Promise<SingleActionResult & {releaseId: string; metadata: ReleaseDocument['metadata']}>\n  async create(\n    releaseOrOptions?:\n      | {releaseId?: string; metadata?: Partial<ReleaseDocument['metadata']>}\n      | BaseActionOptions,\n    maybeOptions?: BaseActionOptions,\n  ): Promise<SingleActionResult & {releaseId: string; metadata: ReleaseDocument['metadata']}> {\n    const {action, options} = createRelease(releaseOrOptions, maybeOptions)\n    const {releaseId, metadata} = action\n\n    const actionResult = await lastValueFrom(\n      _action(this.#client, this.#httpRequest, action, options),\n    )\n\n    return {...actionResult, releaseId, metadata}\n  }\n\n  /**\n   * @public\n   *\n   * Edits an existing release, updating the metadata.\n   *\n   * @category Releases\n   *\n   * @param params - Release action parameters:\n   *   - `releaseId` - The id of the release to edit.\n   *   - `patch` - The patch operation to apply on the release metadata {@link PatchMutationOperation}.\n   * @param options - Additional action options.\n   * @returns A promise that resolves to the `transactionId`.\n   */\n  edit(\n    {releaseId, patch}: {releaseId: string; patch: PatchOperations},\n    options?: BaseActionOptions,\n  ): Promise<SingleActionResult> {\n    const editAction: EditReleaseAction = {\n      actionType: 'sanity.action.release.edit',\n      releaseId,\n      patch,\n    }\n\n    return lastValueFrom(_action(this.#client, this.#httpRequest, editAction, options))\n  }\n\n  /**\n   * @public\n   *\n   * Publishes all documents in a release at once. For larger releases the effect of the publish\n   * will be visible immediately when querying but the removal of the `versions.<releasesId>.*`\n   * documents and creation of the corresponding published documents with the new content may\n   * take some time.\n   *\n   * During this period both the source and target documents are locked and cannot be\n   * modified through any other means.\n   *\n   * @category Releases\n   *\n   * @param params - Release action parameters:\n   *   - `releaseId` - The id of the release to publish.\n   * @param options - Additional action options.\n   * @returns A promise that resolves to the `transactionId`.\n   */\n  publish(\n    {releaseId}: {releaseId: string},\n    options?: BaseActionOptions,\n  ): Promise<SingleActionResult> {\n    const publishAction: PublishReleaseAction = {\n      actionType: 'sanity.action.release.publish',\n      releaseId,\n    }\n\n    return lastValueFrom(_action(this.#client, this.#httpRequest, publishAction, options))\n  }\n\n  /**\n   * @public\n   *\n   * An archive action removes an active release. The documents that comprise the release\n   * are deleted and therefore no longer queryable.\n   *\n   * While the documents remain in retention the last version can still be accessed using document history endpoint.\n   *\n   * @category Releases\n   *\n   * @param params - Release action parameters:\n   *   - `releaseId` - The id of the release to archive.\n   * @param options - Additional action options.\n   * @returns A promise that resolves to the `transactionId`.\n   */\n  archive(\n    {releaseId}: {releaseId: string},\n    options?: BaseActionOptions,\n  ): Promise<SingleActionResult> {\n    const archiveAction: ArchiveReleaseAction = {\n      actionType: 'sanity.action.release.archive',\n      releaseId,\n    }\n\n    return lastValueFrom(_action(this.#client, this.#httpRequest, archiveAction, options))\n  }\n\n  /**\n   * @public\n   *\n   * An unarchive action restores an archived release and all documents\n   * with the content they had just prior to archiving.\n   *\n   * @category Releases\n   *\n   * @param params - Release action parameters:\n   *   - `releaseId` - The id of the release to unarchive.\n   * @param options - Additional action options.\n   * @returns A promise that resolves to the `transactionId`.\n   */\n  unarchive(\n    {releaseId}: {releaseId: string},\n    options?: BaseActionOptions,\n  ): Promise<SingleActionResult> {\n    const unarchiveAction: UnarchiveReleaseAction = {\n      actionType: 'sanity.action.release.unarchive',\n      releaseId,\n    }\n\n    return lastValueFrom(_action(this.#client, this.#httpRequest, unarchiveAction, options))\n  }\n\n  /**\n   * @public\n   *\n   * A schedule action queues a release for publishing at the given future time.\n   * The release is locked such that no documents in the release can be modified and\n   * no documents that it references can be deleted as this would make the publish fail.\n   * At the given time, the same logic as for the publish action is triggered.\n   *\n   * @category Releases\n   *\n   * @param params - Release action parameters:\n   *   - `releaseId` - The id of the release to schedule.\n   *   - `publishAt` - The serialised date and time to publish the release. If the `publishAt` is in the past, the release will be published immediately.\n   * @param options - Additional action options.\n   * @returns A promise that resolves to the `transactionId`.\n   */\n  schedule(\n    {releaseId, publishAt}: {releaseId: string; publishAt: string},\n    options?: BaseActionOptions,\n  ): Promise<SingleActionResult> {\n    const scheduleAction: ScheduleReleaseAction = {\n      actionType: 'sanity.action.release.schedule',\n      releaseId,\n      publishAt,\n    }\n\n    return lastValueFrom(_action(this.#client, this.#httpRequest, scheduleAction, options))\n  }\n\n  /**\n   * @public\n   *\n   * An unschedule action stops a release from being published.\n   * The documents in the release are considered unlocked and can be edited again.\n   * This may fail if another release is scheduled to be published after this one and\n   * has a reference to a document created by this one.\n   *\n   * @category Releases\n   *\n   * @param params - Release action parameters:\n   *   - `releaseId` - The id of the release to unschedule.\n   * @param options - Additional action options.\n   * @returns A promise that resolves to the `transactionId`.\n   */\n  unschedule(\n    {releaseId}: {releaseId: string},\n    options?: BaseActionOptions,\n  ): Promise<SingleActionResult> {\n    const unscheduleAction: UnscheduleReleaseAction = {\n      actionType: 'sanity.action.release.unschedule',\n      releaseId,\n    }\n\n    return lastValueFrom(_action(this.#client, this.#httpRequest, unscheduleAction, options))\n  }\n\n  /**\n   * @public\n   *\n   * A delete action removes a published or archived release.\n   * The backing system document will be removed from the dataset.\n   *\n   * @category Releases\n   *\n   * @param params - Release action parameters:\n   *   - `releaseId` - The id of the release to delete.\n   * @param options - Additional action options.\n   * @returns A promise that resolves to the `transactionId`.\n   */\n  delete(\n    {releaseId}: {releaseId: string},\n    options?: BaseActionOptions,\n  ): Promise<SingleActionResult> {\n    const deleteAction: DeleteReleaseAction = {\n      actionType: 'sanity.action.release.delete',\n      releaseId,\n    }\n\n    return lastValueFrom(_action(this.#client, this.#httpRequest, deleteAction, options))\n  }\n\n  /**\n   * @public\n   *\n   * Fetch the documents in a release by release id.\n   *\n   * @category Releases\n   *\n   * @param params - Release action parameters:\n   *   - `releaseId` - The id of the release to fetch documents for.\n   * @param options - Additional mutation options {@link BaseMutationOptions}.\n   * @returns A promise that resolves to the documents in the release.\n   */\n  fetchDocuments(\n    {releaseId}: {releaseId: string},\n    options?: BaseMutationOptions,\n  ): Promise<RawQueryResponse<SanityDocument[]>> {\n    return lastValueFrom(_getReleaseDocuments(this.#client, this.#httpRequest, releaseId, options))\n  }\n}\n", "import {lastValueFrom, type Observable} from 'rxjs'\n\nimport {_request} from '../data/dataMethods'\nimport type {ObservableSanityClient, SanityClient} from '../SanityClient'\nimport type {CurrentSanityUser, HttpRequest, SanityUser} from '../types'\n\n/** @public */\nexport class ObservableUsersClient {\n  #client: ObservableSanityClient\n  #httpRequest: HttpRequest\n  constructor(client: ObservableSanityClient, httpRequest: HttpRequest) {\n    this.#client = client\n    this.#httpRequest = httpRequest\n  }\n\n  /**\n   * Fetch a user by user ID\n   *\n   * @param id - User ID of the user to fetch. If `me` is provided, a minimal response including the users role is returned.\n   */\n  getById<T extends 'me' | string>(\n    id: T,\n  ): Observable<T extends 'me' ? CurrentSanityUser : SanityUser> {\n    return _request<T extends 'me' ? CurrentSanityUser : SanityUser>(\n      this.#client,\n      this.#httpRequest,\n      {uri: `/users/${id}`},\n    )\n  }\n}\n\n/** @public */\nexport class UsersClient {\n  #client: SanityClient\n  #httpRequest: HttpRequest\n  constructor(client: SanityClient, httpRequest: HttpRequest) {\n    this.#client = client\n    this.#httpRequest = httpRequest\n  }\n\n  /**\n   * Fetch a user by user ID\n   *\n   * @param id - User ID of the user to fetch. If `me` is provided, a minimal response including the users role is returned.\n   */\n  getById<T extends 'me' | string>(\n    id: T,\n  ): Promise<T extends 'me' ? CurrentSanityUser : SanityUser> {\n    return lastValueFrom(\n      _request<T extends 'me' ? CurrentSanityUser : SanityUser>(this.#client, this.#httpRequest, {\n        uri: `/users/${id}`,\n      }),\n    )\n  }\n}\n", "import {getPublishedId, getVersionId} from '@sanity/client/csm'\nimport {firstValue<PERSON>rom, lastValueFrom, Observable} from 'rxjs'\n\nimport {AgentActionsClient, ObservableAgentsActionClient} from './agent/actions/AgentActionsClient'\nimport {AssetsClient, ObservableAssetsClient} from './assets/AssetsClient'\nimport {defaultConfig, initConfig} from './config'\nimport * as dataMethods from './data/dataMethods'\nimport {_listen} from './data/listen'\nimport {LiveClient} from './data/live'\nimport {ObservablePatch, Patch} from './data/patch'\nimport {ObservableTransaction, Transaction} from './data/transaction'\nimport {DatasetsClient, ObservableDatasetsClient} from './datasets/DatasetsClient'\nimport {ObservableProjectsClient, ProjectsClient} from './projects/ProjectsClient'\nimport {ObservableReleasesClient, ReleasesClient} from './releases/ReleasesClient'\nimport type {\n  Action,\n  AllDocumentIdsMutationOptions,\n  AllDocumentsMutationOptions,\n  Any,\n  BaseActionOptions,\n  BaseMutationOptions,\n  ClientConfig,\n  ClientReturn,\n  FilteredResponseQueryOptions,\n  FirstDocumentIdMutationOptions,\n  FirstDocumentMutationOptions,\n  HttpRequest,\n  IdentifiedSanityDocumentStub,\n  InitializedClientConfig,\n  MultipleActionResult,\n  MultipleMutationResult,\n  Mutation,\n  MutationSelection,\n  PatchOperations,\n  PatchSelection,\n  QueryOptions,\n  QueryParams,\n  QueryWithoutParams,\n  RawQuerylessQueryResponse,\n  RawQueryResponse,\n  RawRequestOptions,\n  SanityDocument,\n  SanityDocumentStub,\n  SingleActionResult,\n  SingleMutationResult,\n  UnfilteredResponseQueryOptions,\n  UnfilteredResponseWithoutQuery,\n} from './types'\nimport {ObservableUsersClient, UsersClient} from './users/UsersClient'\nimport {deriveDocumentVersionId, getDocumentVersionId} from './util/createVersionId'\n\nexport type {\n  _listen,\n  AssetsClient,\n  DatasetsClient,\n  LiveClient,\n  ObservableAssetsClient,\n  ObservableDatasetsClient,\n  ObservableProjectsClient,\n  ObservableUsersClient,\n  ProjectsClient,\n  UsersClient,\n}\n\n/** @public */\nexport class ObservableSanityClient {\n  assets: ObservableAssetsClient\n  datasets: ObservableDatasetsClient\n  live: LiveClient\n  projects: ObservableProjectsClient\n  users: ObservableUsersClient\n  agent: {\n    action: ObservableAgentsActionClient\n  }\n  releases: ObservableReleasesClient\n\n  /**\n   * Private properties\n   */\n  #clientConfig: InitializedClientConfig\n  #httpRequest: HttpRequest\n\n  /**\n   * Instance properties\n   */\n  listen = _listen\n\n  constructor(httpRequest: HttpRequest, config: ClientConfig = defaultConfig) {\n    this.config(config)\n\n    this.#httpRequest = httpRequest\n\n    this.assets = new ObservableAssetsClient(this, this.#httpRequest)\n    this.datasets = new ObservableDatasetsClient(this, this.#httpRequest)\n    this.live = new LiveClient(this)\n    this.projects = new ObservableProjectsClient(this, this.#httpRequest)\n    this.users = new ObservableUsersClient(this, this.#httpRequest)\n    this.agent = {\n      action: new ObservableAgentsActionClient(this, this.#httpRequest),\n    }\n    this.releases = new ObservableReleasesClient(this, this.#httpRequest)\n  }\n\n  /**\n   * Clone the client - returns a new instance\n   */\n  clone(): ObservableSanityClient {\n    return new ObservableSanityClient(this.#httpRequest, this.config())\n  }\n\n  /**\n   * Returns the current client configuration\n   */\n  config(): InitializedClientConfig\n  /**\n   * Reconfigure the client. Note that this _mutates_ the current client.\n   */\n  config(newConfig?: Partial<ClientConfig>): this\n  config(newConfig?: Partial<ClientConfig>): ClientConfig | this {\n    if (newConfig === undefined) {\n      return {...this.#clientConfig}\n    }\n\n    if (this.#clientConfig && this.#clientConfig.allowReconfigure === false) {\n      throw new Error(\n        'Existing client instance cannot be reconfigured - use `withConfig(newConfig)` to return a new client',\n      )\n    }\n\n    this.#clientConfig = initConfig(newConfig, this.#clientConfig || {})\n    return this\n  }\n\n  /**\n   * Clone the client with a new (partial) configuration.\n   *\n   * @param newConfig - New client configuration properties, shallowly merged with existing configuration\n   */\n  withConfig(newConfig?: Partial<ClientConfig>): ObservableSanityClient {\n    const thisConfig = this.config()\n    return new ObservableSanityClient(this.#httpRequest, {\n      ...thisConfig,\n      ...newConfig,\n      stega: {\n        ...(thisConfig.stega || {}),\n        ...(typeof newConfig?.stega === 'boolean'\n          ? {enabled: newConfig.stega}\n          : newConfig?.stega || {}),\n      },\n    })\n  }\n\n  /**\n   * Perform a GROQ-query against the configured dataset.\n   *\n   * @param query - GROQ-query to perform\n   */\n  fetch<\n    R = Any,\n    Q extends QueryWithoutParams = QueryWithoutParams,\n    const G extends string = string,\n  >(query: G, params?: Q | QueryWithoutParams): Observable<ClientReturn<G, R>>\n  /**\n   * Perform a GROQ-query against the configured dataset.\n   *\n   * @param query - GROQ-query to perform\n   * @param params - Optional query parameters\n   * @param options - Optional request options\n   */\n  fetch<\n    R = Any,\n    Q extends QueryWithoutParams | QueryParams = QueryParams,\n    const G extends string = string,\n  >(\n    query: G,\n    params: Q extends QueryWithoutParams ? QueryWithoutParams : Q,\n    options?: FilteredResponseQueryOptions,\n  ): Observable<ClientReturn<G, R>>\n  /**\n   * Perform a GROQ-query against the configured dataset.\n   *\n   * @param query - GROQ-query to perform\n   * @param params - Optional query parameters\n   * @param options - Request options\n   */\n  fetch<\n    R = Any,\n    Q extends QueryWithoutParams | QueryParams = QueryParams,\n    const G extends string = string,\n  >(\n    query: string,\n    params: Q extends QueryWithoutParams ? QueryWithoutParams : Q,\n    options: UnfilteredResponseQueryOptions,\n  ): Observable<RawQueryResponse<ClientReturn<G, R>>>\n  /**\n   * Perform a GROQ-query against the configured dataset.\n   *\n   * @param query - GROQ-query to perform\n   * @param params - Optional query parameters\n   * @param options - Request options\n   */\n  fetch<\n    R = Any,\n    Q extends QueryWithoutParams | QueryParams = QueryParams,\n    const G extends string = string,\n  >(\n    query: G,\n    params: Q extends QueryWithoutParams ? QueryWithoutParams : Q,\n    options: UnfilteredResponseWithoutQuery,\n  ): Observable<RawQuerylessQueryResponse<ClientReturn<G, R>>>\n  fetch<R, Q, const G extends string>(\n    query: G,\n    params?: Q,\n    options?: QueryOptions,\n  ): Observable<RawQueryResponse<R> | R> {\n    return dataMethods._fetch<R, Q>(\n      this,\n      this.#httpRequest,\n      this.#clientConfig.stega,\n      query,\n      params,\n      options,\n    )\n  }\n\n  /**\n   * Fetch a single document with the given ID.\n   *\n   * @param id - Document ID to fetch\n   * @param options - Request options\n   */\n  getDocument<R extends Record<string, Any> = Record<string, Any>>(\n    id: string,\n    options?: {signal?: AbortSignal; tag?: string; releaseId?: string},\n  ): Observable<SanityDocument<R> | undefined> {\n    return dataMethods._getDocument<R>(this, this.#httpRequest, id, options)\n  }\n\n  /**\n   * Fetch multiple documents in one request.\n   * Should be used sparingly - performing a query is usually a better option.\n   * The order/position of documents is preserved based on the original array of IDs.\n   * If any of the documents are missing, they will be replaced by a `null` entry in the returned array\n   *\n   * @param ids - Document IDs to fetch\n   * @param options - Request options\n   */\n  getDocuments<R extends Record<string, Any> = Record<string, Any>>(\n    ids: string[],\n    options?: {tag?: string},\n  ): Observable<(SanityDocument<R> | null)[]> {\n    return dataMethods._getDocuments<R>(this, this.#httpRequest, ids, options)\n  }\n\n  /**\n   * Create a document. Requires a `_type` property. If no `_id` is provided, it will be generated by the database.\n   * Returns an observable that resolves to the created document.\n   *\n   * @param document - Document to create\n   * @param options - Mutation options\n   */\n  create<R extends Record<string, Any> = Record<string, Any>>(\n    document: SanityDocumentStub<R>,\n    options: FirstDocumentMutationOptions,\n  ): Observable<SanityDocument<R>>\n  /**\n   * Create a document. Requires a `_type` property. If no `_id` is provided, it will be generated by the database.\n   * Returns an observable that resolves to an array containing the created document.\n   *\n   * @param document - Document to create\n   * @param options - Mutation options\n   */\n  create<R extends Record<string, Any> = Record<string, Any>>(\n    document: SanityDocumentStub<R>,\n    options: AllDocumentsMutationOptions,\n  ): Observable<SanityDocument<R>[]>\n  /**\n   * Create a document. Requires a `_type` property. If no `_id` is provided, it will be generated by the database.\n   * Returns an observable that resolves to a mutation result object containing the ID of the created document.\n   *\n   * @param document - Document to create\n   * @param options - Mutation options\n   */\n  create<R extends Record<string, Any> = Record<string, Any>>(\n    document: SanityDocumentStub<R>,\n    options: FirstDocumentIdMutationOptions,\n  ): Observable<SingleMutationResult>\n  /**\n   * Create a document. Requires a `_type` property. If no `_id` is provided, it will be generated by the database.\n   * Returns an observable that resolves to a mutation result object containing the ID of the created document.\n   *\n   * @param document - Document to create\n   * @param options - Mutation options\n   */\n  create<R extends Record<string, Any> = Record<string, Any>>(\n    document: SanityDocumentStub<R>,\n    options: AllDocumentIdsMutationOptions,\n  ): Observable<MultipleMutationResult>\n  /**\n   * Create a document. Requires a `_type` property. If no `_id` is provided, it will be generated by the database.\n   * Returns an observable that resolves to the created document.\n   *\n   * @param document - Document to create\n   * @param options - Mutation options\n   */\n  create<R extends Record<string, Any> = Record<string, Any>>(\n    document: SanityDocumentStub<R>,\n    options?: BaseMutationOptions,\n  ): Observable<SanityDocument<R>>\n  create<R extends Record<string, Any> = Record<string, Any>>(\n    document: SanityDocumentStub<R>,\n    options?:\n      | AllDocumentIdsMutationOptions\n      | AllDocumentsMutationOptions\n      | BaseMutationOptions\n      | FirstDocumentIdMutationOptions\n      | FirstDocumentMutationOptions,\n  ): Observable<\n    SanityDocument<R> | SanityDocument<R>[] | SingleMutationResult | MultipleMutationResult\n  > {\n    return dataMethods._create<R>(this, this.#httpRequest, document, 'create', options)\n  }\n\n  /**\n   * Create a document if no document with the same ID already exists.\n   * Returns an observable that resolves to the created document.\n   *\n   * @param document - Document to create\n   * @param options - Mutation options\n   */\n  createIfNotExists<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options: FirstDocumentMutationOptions,\n  ): Observable<SanityDocument<R>>\n  /**\n   * Create a document if no document with the same ID already exists.\n   * Returns an observable that resolves to an array containing the created document.\n   *\n   * @param document - Document to create\n   * @param options - Mutation options\n   */\n  createIfNotExists<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options: AllDocumentsMutationOptions,\n  ): Observable<SanityDocument<R>[]>\n  /**\n   * Create a document if no document with the same ID already exists.\n   * Returns an observable that resolves to a mutation result object containing the ID of the created document.\n   *\n   * @param document - Document to create\n   * @param options - Mutation options\n   */\n  createIfNotExists<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options: FirstDocumentIdMutationOptions,\n  ): Observable<SingleMutationResult>\n  /**\n   * Create a document if no document with the same ID already exists.\n   * Returns an observable that resolves to a mutation result object containing the ID of the created document.\n   *\n   * @param document - Document to create\n   * @param options - Mutation options\n   */\n  createIfNotExists<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options: AllDocumentIdsMutationOptions,\n  ): Observable<MultipleMutationResult>\n  /**\n   * Create a document if no document with the same ID already exists.\n   * Returns an observable that resolves to the created document.\n   *\n   * @param document - Document to create\n   * @param options - Mutation options\n   */\n  createIfNotExists<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options?: BaseMutationOptions,\n  ): Observable<SanityDocument<R>>\n  createIfNotExists<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options?:\n      | AllDocumentIdsMutationOptions\n      | AllDocumentsMutationOptions\n      | BaseMutationOptions\n      | FirstDocumentIdMutationOptions\n      | FirstDocumentMutationOptions,\n  ): Observable<\n    SanityDocument<R> | SanityDocument<R>[] | SingleMutationResult | MultipleMutationResult\n  > {\n    return dataMethods._createIfNotExists<R>(this, this.#httpRequest, document, options)\n  }\n\n  /**\n   * Create a document if it does not exist, or replace a document with the same document ID\n   * Returns an observable that resolves to the created document.\n   *\n   * @param document - Document to either create or replace\n   * @param options - Mutation options\n   */\n  createOrReplace<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options: FirstDocumentMutationOptions,\n  ): Observable<SanityDocument<R>>\n  /**\n   * Create a document if it does not exist, or replace a document with the same document ID\n   * Returns an observable that resolves to an array containing the created document.\n   *\n   * @param document - Document to either create or replace\n   * @param options - Mutation options\n   */\n  createOrReplace<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options: AllDocumentsMutationOptions,\n  ): Observable<SanityDocument<R>[]>\n  /**\n   * Create a document if it does not exist, or replace a document with the same document ID\n   * Returns an observable that resolves to a mutation result object containing the ID of the created document.\n   *\n   * @param document - Document to either create or replace\n   * @param options - Mutation options\n   */\n  createOrReplace<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options: FirstDocumentIdMutationOptions,\n  ): Observable<SingleMutationResult>\n  /**\n   * Create a document if it does not exist, or replace a document with the same document ID\n   * Returns an observable that resolves to a mutation result object containing the created document ID.\n   *\n   * @param document - Document to either create or replace\n   * @param options - Mutation options\n   */\n  createOrReplace<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options: AllDocumentIdsMutationOptions,\n  ): Observable<MultipleMutationResult>\n  /**\n   * Create a document if it does not exist, or replace a document with the same document ID\n   * Returns an observable that resolves to the created document.\n   *\n   * @param document - Document to either create or replace\n   * @param options - Mutation options\n   */\n  createOrReplace<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options?: BaseMutationOptions,\n  ): Observable<SanityDocument<R>>\n  createOrReplace<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options?:\n      | AllDocumentIdsMutationOptions\n      | AllDocumentsMutationOptions\n      | BaseMutationOptions\n      | FirstDocumentIdMutationOptions\n      | FirstDocumentMutationOptions,\n  ): Observable<\n    SanityDocument<R> | SanityDocument<R>[] | SingleMutationResult | MultipleMutationResult\n  > {\n    return dataMethods._createOrReplace<R>(this, this.#httpRequest, document, options)\n  }\n\n  /**\n   * @public\n   *\n   * Creates a new version of a published document.\n   *\n   * @remarks\n   * * Requires a document with a `_type` property.\n   * * Creating a version with no `releaseId` will create a new draft version of the published document.\n   * * If the `document._id` is defined, it should be a draft or release version ID that matches the version ID generated from `publishedId` and `releaseId`.\n   * * If the `document._id` is not defined, it will be generated from `publishedId` and `releaseId`.\n   * * To create a version of an unpublished document, use the `client.create` method.\n   *\n   * @category Versions\n   *\n   * @param params - Version action parameters:\n   *   - `document` - The document to create as a new version (must include `_type`).\n   *   - `publishedId` - The ID of the published document being versioned.\n   *   - `releaseId` - The ID of the release to create the version for.\n   * @param options - Additional action options.\n   * @returns an observable that resolves to the `transactionId`.\n   *\n   * @example Creating a new version of a published document with a generated version ID\n   * ```ts\n   * client.observable.createVersion({\n   *   // The document does not need to include an `_id` property since it will be generated from `publishedId` and `releaseId`\n   *   document: {_type: 'myDocument', title: 'My Document'},\n   *   publishedId: 'myDocument',\n   *   releaseId: 'myRelease',\n   * })\n   *\n   * // The following document will be created:\n   * // {\n   * //   _id: 'versions.myRelease.myDocument',\n   * //   _type: 'myDocument',\n   * //   title: 'My Document',\n   * // }\n   * ```\n   *\n   * @example Creating a new version of a published document with a specified version ID\n   * ```ts\n   * client.observable.createVersion({\n   *   document: {_type: 'myDocument', _id: 'versions.myRelease.myDocument', title: 'My Document'},\n   *   // `publishedId` and `releaseId` are not required since `document._id` has been specified\n   * })\n   *\n   * // The following document will be created:\n   * // {\n   * //   _id: 'versions.myRelease.myDocument',\n   * //   _type: 'myDocument',\n   * //   title: 'My Document',\n   * // }\n   * ```\n   *\n   * @example Creating a new draft version of a published document\n   * ```ts\n   * client.observable.createVersion({\n   *   document: {_type: 'myDocument', title: 'My Document'},\n   *   publishedId: 'myDocument',\n   * })\n   *\n   * // The following document will be created:\n   * // {\n   * //   _id: 'drafts.myDocument',\n   * //   _type: 'myDocument',\n   * //   title: 'My Document',\n   * // }\n   * ```\n   */\n  createVersion<R extends Record<string, Any>>(\n    args: {\n      document: SanityDocumentStub<R>\n      publishedId: string\n      releaseId?: string\n    },\n    options?: BaseActionOptions,\n  ): Observable<SingleActionResult | MultipleActionResult>\n  createVersion<R extends Record<string, Any>>(\n    args: {\n      document: IdentifiedSanityDocumentStub<R>\n      publishedId?: string\n      releaseId?: string\n    },\n    options?: BaseActionOptions,\n  ): Observable<SingleActionResult | MultipleActionResult>\n  createVersion<R extends Record<string, Any>>(\n    {\n      document,\n      publishedId,\n      releaseId,\n    }: {\n      document: SanityDocumentStub<R> | IdentifiedSanityDocumentStub<R>\n      publishedId?: string\n      releaseId?: string\n    },\n    options?: BaseActionOptions,\n  ): Observable<SingleActionResult | MultipleActionResult> {\n    const documentVersionId = deriveDocumentVersionId('createVersion', {\n      document,\n      publishedId,\n      releaseId,\n    })\n\n    const documentVersion = {...document, _id: documentVersionId}\n    const versionPublishedId = publishedId || getPublishedId(document._id)\n\n    return dataMethods._createVersion<R>(\n      this,\n      this.#httpRequest,\n      documentVersion,\n      versionPublishedId,\n      options,\n    )\n  }\n\n  /**\n   * Deletes a document with the given document ID.\n   * Returns an observable that resolves to the deleted document.\n   *\n   * @param id - Document ID to delete\n   * @param options - Options for the mutation\n   */\n  delete<R extends Record<string, Any> = Record<string, Any>>(\n    id: string,\n    options: FirstDocumentMutationOptions,\n  ): Observable<SanityDocument<R>>\n  /**\n   * Deletes a document with the given document ID.\n   * Returns an observable that resolves to an array containing the deleted document.\n   *\n   * @param id - Document ID to delete\n   * @param options - Options for the mutation\n   */\n  delete<R extends Record<string, Any> = Record<string, Any>>(\n    id: string,\n    options: AllDocumentsMutationOptions,\n  ): Observable<SanityDocument<R>[]>\n  /**\n   * Deletes a document with the given document ID.\n   * Returns an observable that resolves to a mutation result object containing the deleted document ID.\n   *\n   * @param id - Document ID to delete\n   * @param options - Options for the mutation\n   */\n  delete(id: string, options: FirstDocumentIdMutationOptions): Observable<SingleMutationResult>\n  /**\n   * Deletes a document with the given document ID.\n   * Returns an observable that resolves to a mutation result object containing the deleted document ID.\n   *\n   * @param id - Document ID to delete\n   * @param options - Options for the mutation\n   */\n  delete(id: string, options: AllDocumentIdsMutationOptions): Observable<MultipleMutationResult>\n  /**\n   * Deletes a document with the given document ID.\n   * Returns an observable that resolves to the deleted document.\n   *\n   * @param id - Document ID to delete\n   * @param options - Options for the mutation\n   */\n  delete<R extends Record<string, Any> = Record<string, Any>>(\n    id: string,\n    options?: BaseMutationOptions,\n  ): Observable<SanityDocument<R>>\n  /**\n   * Deletes one or more documents matching the given query or document ID.\n   * Returns an observable that resolves to first deleted document.\n   *\n   * @param selection - An object with either an `id` or `query` key defining what to delete\n   * @param options - Options for the mutation\n   */\n  delete<R extends Record<string, Any> = Record<string, Any>>(\n    selection: MutationSelection,\n    options: FirstDocumentMutationOptions,\n  ): Observable<SanityDocument<R>>\n  /**\n   * Deletes one or more documents matching the given query or document ID.\n   * Returns an observable that resolves to an array containing the deleted documents.\n   *\n   * @param selection - An object with either an `id` or `query` key defining what to delete\n   * @param options - Options for the mutation\n   */\n  delete<R extends Record<string, Any> = Record<string, Any>>(\n    selection: MutationSelection,\n    options: AllDocumentsMutationOptions,\n  ): Observable<SanityDocument<R>[]>\n  /**\n   * Deletes one or more documents matching the given query or document ID.\n   * Returns an observable that resolves to a mutation result object containing the ID of the first deleted document.\n   *\n   * @param selection - An object with either an `id` or `query` key defining what to delete\n   * @param options - Options for the mutation\n   */\n  delete(\n    selection: MutationSelection,\n    options: FirstDocumentIdMutationOptions,\n  ): Observable<SingleMutationResult>\n  /**\n   * Deletes one or more documents matching the given query or document ID.\n   * Returns an observable that resolves to a mutation result object containing the document IDs that were deleted.\n   *\n   * @param selection - An object with either an `id` or `query` key defining what to delete\n   * @param options - Options for the mutation\n   */\n  delete(\n    selection: MutationSelection,\n    options: AllDocumentIdsMutationOptions,\n  ): Observable<MultipleMutationResult>\n  /**\n   * Deletes one or more documents matching the given query or document ID.\n   * Returns an observable that resolves to first deleted document.\n   *\n   * @param selection - An object with either an `id` or `query` key defining what to delete\n   * @param options - Options for the mutation\n   */\n  delete<R extends Record<string, Any> = Record<string, Any>>(\n    selection: MutationSelection,\n    options?: BaseMutationOptions,\n  ): Observable<SanityDocument<R>>\n  delete<R extends Record<string, Any> = Record<string, Any>>(\n    selection: string | MutationSelection,\n    options?:\n      | AllDocumentIdsMutationOptions\n      | AllDocumentsMutationOptions\n      | BaseMutationOptions\n      | FirstDocumentIdMutationOptions\n      | FirstDocumentMutationOptions,\n  ): Observable<\n    SanityDocument<R> | SanityDocument<R>[] | SingleMutationResult | MultipleMutationResult\n  > {\n    return dataMethods._delete<R>(this, this.#httpRequest, selection, options)\n  }\n\n  /**\n   * @public\n   *\n   * Deletes the draft or release version of a document.\n   *\n   * @remarks\n   * * Discarding a version with no `releaseId` will discard the draft version of the published document.\n   * * If the draft or release version does not exist, any error will throw.\n   *\n   * @param params - Version action parameters:\n   *   - `releaseId` - The ID of the release to discard the document from.\n   *   - `publishedId` - The published ID of the document to discard.\n   * @param purge - if `true` the document history is also discarded.\n   * @param options - Additional action options.\n   * @returns an observable that resolves to the `transactionId`.\n   *\n   * @example Discarding a release version of a document\n   * ```ts\n   * client.observable.discardVersion({publishedId: 'myDocument', releaseId: 'myRelease'})\n   * // The document with the ID `versions.myRelease.myDocument` will be discarded.\n   * ```\n   *\n   * @example Discarding a draft version of a document\n   * ```ts\n   * client.observable.discardVersion({publishedId: 'myDocument'})\n   * // The document with the ID `drafts.myDocument` will be discarded.\n   * ```\n   */\n  discardVersion(\n    {releaseId, publishedId}: {releaseId?: string; publishedId: string},\n    purge?: boolean,\n    options?: BaseActionOptions,\n  ): Observable<SingleActionResult | MultipleActionResult> {\n    const documentVersionId = getDocumentVersionId(publishedId, releaseId)\n\n    return dataMethods._discardVersion(this, this.#httpRequest, documentVersionId, purge, options)\n  }\n\n  /**\n   * @public\n   *\n   * Replaces an existing version document.\n   *\n   * @remarks\n   * * Requires a document with a `_type` property.\n   * * If the `document._id` is defined, it should be a draft or release version ID that matches the version ID generated from `publishedId` and `releaseId`.\n   * * If the `document._id` is not defined, it will be generated from `publishedId` and `releaseId`.\n   * * Replacing a version with no `releaseId` will replace the draft version of the published document.\n   * * At least one of the **version** or **published** documents must exist.\n   *\n   * @param params - Version action parameters:\n   *   - `document` - The new document to replace the version with.\n   *   - `releaseId` - The ID of the release where the document version is replaced.\n   *   - `publishedId` - The ID of the published document to replace.\n   * @param options - Additional action options.\n   * @returns an observable that resolves to the `transactionId`.\n   *\n   * @example Replacing a release version of a published document with a generated version ID\n   * ```ts\n   * client.observable.replaceVersion({\n   *   document: {_type: 'myDocument', title: 'My Document'},\n   *   publishedId: 'myDocument',\n   *   releaseId: 'myRelease',\n   * })\n   *\n   * // The following document will be patched:\n   * // {\n   * //   _id: 'versions.myRelease.myDocument',\n   * //   _type: 'myDocument',\n   * //   title: 'My Document',\n   * // }\n   * ```\n   *\n   * @example Replacing a release version of a published document with a specified version ID\n   * ```ts\n   * client.observable.replaceVersion({\n   *   document: {_type: 'myDocument', _id: 'versions.myRelease.myDocument', title: 'My Document'},\n   *   // `publishedId` and `releaseId` are not required since `document._id` has been specified\n   * })\n   *\n   * // The following document will be patched:\n   * // {\n   * //   _id: 'versions.myRelease.myDocument',\n   * //   _type: 'myDocument',\n   * //   title: 'My Document',\n   * // }\n   * ```\n   *\n   * @example Replacing a draft version of a published document\n   * ```ts\n   * client.observable.replaceVersion({\n   *   document: {_type: 'myDocument', title: 'My Document'},\n   *   publishedId: 'myDocument',\n   * })\n   *\n   * // The following document will be patched:\n   * // {\n   * //   _id: 'drafts.myDocument',\n   * //   _type: 'myDocument',\n   * //   title: 'My Document',\n   * // }\n   * ```\n   */\n  replaceVersion<R extends Record<string, Any>>(\n    args: {\n      document: SanityDocumentStub<R>\n      publishedId: string\n      releaseId?: string\n    },\n    options?: BaseActionOptions,\n  ): Observable<SingleActionResult | MultipleActionResult>\n  replaceVersion<R extends Record<string, Any>>(\n    args: {\n      document: IdentifiedSanityDocumentStub<R>\n      publishedId?: string\n      releaseId?: string\n    },\n    options?: BaseActionOptions,\n  ): Observable<SingleActionResult | MultipleActionResult>\n  replaceVersion<R extends Record<string, Any>>(\n    {\n      document,\n      publishedId,\n      releaseId,\n    }: {\n      document: SanityDocumentStub<R> | IdentifiedSanityDocumentStub<R>\n      publishedId?: string\n      releaseId?: string\n    },\n    options?: BaseActionOptions,\n  ): Observable<SingleActionResult | MultipleActionResult> {\n    const documentVersionId = deriveDocumentVersionId('replaceVersion', {\n      document,\n      publishedId,\n      releaseId,\n    })\n\n    const documentVersion = {...document, _id: documentVersionId}\n\n    return dataMethods._replaceVersion<R>(this, this.#httpRequest, documentVersion, options)\n  }\n\n  /**\n   * @public\n   *\n   * Used to indicate when a document within a release should be unpublished when\n   * the release is run.\n   *\n   * @remarks\n   * * If the published document does not exist, an error will be thrown.\n   *\n   * @param params - Version action parameters:\n   *   - `releaseId` - The ID of the release to unpublish the document from.\n   *   - `publishedId` - The published ID of the document to unpublish.\n   * @param options - Additional action options.\n   * @returns an observable that resolves to the `transactionId`.\n   *\n   * @example Unpublishing a release version of a published document\n   * ```ts\n   * client.observable.unpublishVersion({publishedId: 'myDocument', releaseId: 'myRelease'})\n   * // The document with the ID `versions.myRelease.myDocument` will be unpublished. when `myRelease` is run.\n   * ```\n   */\n  unpublishVersion(\n    {releaseId, publishedId}: {releaseId: string; publishedId: string},\n    options?: BaseActionOptions,\n  ): Observable<SingleActionResult | MultipleActionResult> {\n    const versionId = getVersionId(publishedId, releaseId)\n\n    return dataMethods._unpublishVersion(this, this.#httpRequest, versionId, publishedId, options)\n  }\n\n  /**\n   * Perform mutation operations against the configured dataset\n   * Returns an observable that resolves to the first mutated document.\n   *\n   * @param operations - Mutation operations to execute\n   * @param options - Mutation options\n   */\n  mutate<R extends Record<string, Any> = Record<string, Any>>(\n    operations: Mutation<R>[] | ObservablePatch | ObservableTransaction,\n    options: FirstDocumentMutationOptions,\n  ): Observable<SanityDocument<R>>\n  /**\n   * Perform mutation operations against the configured dataset.\n   * Returns an observable that resolves to an array of the mutated documents.\n   *\n   * @param operations - Mutation operations to execute\n   * @param options - Mutation options\n   */\n  mutate<R extends Record<string, Any> = Record<string, Any>>(\n    operations: Mutation<R>[] | ObservablePatch | ObservableTransaction,\n    options: AllDocumentsMutationOptions,\n  ): Observable<SanityDocument<R>[]>\n  /**\n   * Perform mutation operations against the configured dataset\n   * Returns an observable that resolves to a mutation result object containing the document ID of the first mutated document.\n   *\n   * @param operations - Mutation operations to execute\n   * @param options - Mutation options\n   */\n  mutate<R extends Record<string, Any> = Record<string, Any>>(\n    operations: Mutation<R>[] | ObservablePatch | ObservableTransaction,\n    options: FirstDocumentIdMutationOptions,\n  ): Observable<SingleMutationResult>\n  /**\n   * Perform mutation operations against the configured dataset\n   * Returns an observable that resolves to a mutation result object containing the mutated document IDs.\n   *\n   * @param operations - Mutation operations to execute\n   * @param options - Mutation options\n   */\n  mutate<R extends Record<string, Any> = Record<string, Any>>(\n    operations: Mutation<R>[] | ObservablePatch | ObservableTransaction,\n    options: AllDocumentIdsMutationOptions,\n  ): Observable<MultipleMutationResult>\n  /**\n   * Perform mutation operations against the configured dataset\n   * Returns an observable that resolves to the first mutated document.\n   *\n   * @param operations - Mutation operations to execute\n   * @param options - Mutation options\n   */\n  mutate<R extends Record<string, Any> = Record<string, Any>>(\n    operations: Mutation<R>[] | ObservablePatch | ObservableTransaction,\n    options?: BaseMutationOptions,\n  ): Observable<SanityDocument<R>>\n  mutate<R extends Record<string, Any> = Record<string, Any>>(\n    operations: Mutation<R>[] | ObservablePatch | ObservableTransaction,\n    options?:\n      | FirstDocumentMutationOptions\n      | AllDocumentsMutationOptions\n      | FirstDocumentIdMutationOptions\n      | AllDocumentIdsMutationOptions\n      | BaseMutationOptions,\n  ): Observable<\n    SanityDocument<R> | SanityDocument<R>[] | SingleMutationResult | MultipleMutationResult\n  > {\n    return dataMethods._mutate<R>(this, this.#httpRequest, operations, options)\n  }\n\n  /**\n   * Create a new buildable patch of operations to perform\n   *\n   * @param documentId - Document ID to patch\n   * @param operations - Optional object of patch operations to initialize the patch instance with\n   * @returns Patch instance - call `.commit()` to perform the operations defined\n   */\n  patch(documentId: string, operations?: PatchOperations): ObservablePatch\n\n  /**\n   * Create a new buildable patch of operations to perform\n   *\n   * @param documentIds - Array of document IDs to patch\n   * @param operations - Optional object of patch operations to initialize the patch instance with\n   * @returns Patch instance - call `.commit()` to perform the operations defined\n   */\n  patch(documentIds: string[], operations?: PatchOperations): ObservablePatch\n\n  /**\n   * Create a new buildable patch of operations to perform\n   *\n   * @param selection - An object with `query` and optional `params`, defining which document(s) to patch\n   * @param operations - Optional object of patch operations to initialize the patch instance with\n   * @returns Patch instance - call `.commit()` to perform the operations defined\n   */\n  patch(selection: MutationSelection, operations?: PatchOperations): ObservablePatch\n\n  /**\n   * Create a new buildable patch of operations to perform\n   *\n   * @param selection - Document ID, an array of document IDs, or an object with `query` and optional `params`, defining which document(s) to patch\n   * @param operations - Optional object of patch operations to initialize the patch instance with\n   * @returns Patch instance - call `.commit()` to perform the operations defined\n   */\n  patch(selection: PatchSelection, operations?: PatchOperations): ObservablePatch {\n    return new ObservablePatch(selection, operations, this)\n  }\n\n  /**\n   * Create a new transaction of mutations\n   *\n   * @param operations - Optional array of mutation operations to initialize the transaction instance with\n   */\n  transaction<R extends Record<string, Any> = Record<string, Any>>(\n    operations?: Mutation<R>[],\n  ): ObservableTransaction {\n    return new ObservableTransaction(operations, this)\n  }\n\n  /**\n   * Perform action operations against the configured dataset\n   *\n   * @param operations - Action operation(s) to execute\n   * @param options - Action options\n   */\n  action(\n    operations: Action | Action[],\n    options?: BaseActionOptions,\n  ): Observable<SingleActionResult | MultipleActionResult> {\n    return dataMethods._action(this, this.#httpRequest, operations, options)\n  }\n\n  /**\n   * Perform an HTTP request against the Sanity API\n   *\n   * @param options - Request options\n   */\n  request<R = Any>(options: RawRequestOptions): Observable<R> {\n    return dataMethods._request(this, this.#httpRequest, options)\n  }\n\n  /**\n   * Get a Sanity API URL for the URI provided\n   *\n   * @param uri - URI/path to build URL for\n   * @param canUseCdn - Whether or not to allow using the API CDN for this route\n   */\n  getUrl(uri: string, canUseCdn?: boolean): string {\n    return dataMethods._getUrl(this, uri, canUseCdn)\n  }\n\n  /**\n   * Get a Sanity API URL for the data operation and path provided\n   *\n   * @param operation - Data operation (eg `query`, `mutate`, `listen` or similar)\n   * @param path - Path to append after the operation\n   */\n  getDataUrl(operation: string, path?: string): string {\n    return dataMethods._getDataUrl(this, operation, path)\n  }\n}\n\n/** @public */\nexport class SanityClient {\n  assets: AssetsClient\n  datasets: DatasetsClient\n  live: LiveClient\n  projects: ProjectsClient\n  users: UsersClient\n  agent: {\n    action: AgentActionsClient\n  }\n  releases: ReleasesClient\n\n  /**\n   * Observable version of the Sanity client, with the same configuration as the promise-based one\n   */\n  observable: ObservableSanityClient\n\n  /**\n   * Private properties\n   */\n  #clientConfig: InitializedClientConfig\n  #httpRequest: HttpRequest\n\n  /**\n   * Instance properties\n   */\n  listen = _listen\n\n  constructor(httpRequest: HttpRequest, config: ClientConfig = defaultConfig) {\n    this.config(config)\n\n    this.#httpRequest = httpRequest\n\n    this.assets = new AssetsClient(this, this.#httpRequest)\n    this.datasets = new DatasetsClient(this, this.#httpRequest)\n    this.live = new LiveClient(this)\n    this.projects = new ProjectsClient(this, this.#httpRequest)\n    this.users = new UsersClient(this, this.#httpRequest)\n    this.agent = {\n      action: new AgentActionsClient(this, this.#httpRequest),\n    }\n    this.releases = new ReleasesClient(this, this.#httpRequest)\n\n    this.observable = new ObservableSanityClient(httpRequest, config)\n  }\n\n  /**\n   * Clone the client - returns a new instance\n   */\n  clone(): SanityClient {\n    return new SanityClient(this.#httpRequest, this.config())\n  }\n\n  /**\n   * Returns the current client configuration\n   */\n  config(): InitializedClientConfig\n  /**\n   * Reconfigure the client. Note that this _mutates_ the current client.\n   */\n  config(newConfig?: Partial<ClientConfig>): this\n  config(newConfig?: Partial<ClientConfig>): ClientConfig | this {\n    if (newConfig === undefined) {\n      return {...this.#clientConfig}\n    }\n\n    if (this.#clientConfig && this.#clientConfig.allowReconfigure === false) {\n      throw new Error(\n        'Existing client instance cannot be reconfigured - use `withConfig(newConfig)` to return a new client',\n      )\n    }\n\n    if (this.observable) {\n      this.observable.config(newConfig)\n    }\n\n    this.#clientConfig = initConfig(newConfig, this.#clientConfig || {})\n    return this\n  }\n\n  /**\n   * Clone the client with a new (partial) configuration.\n   *\n   * @param newConfig - New client configuration properties, shallowly merged with existing configuration\n   */\n  withConfig(newConfig?: Partial<ClientConfig>): SanityClient {\n    const thisConfig = this.config()\n    return new SanityClient(this.#httpRequest, {\n      ...thisConfig,\n      ...newConfig,\n      stega: {\n        ...(thisConfig.stega || {}),\n        ...(typeof newConfig?.stega === 'boolean'\n          ? {enabled: newConfig.stega}\n          : newConfig?.stega || {}),\n      },\n    })\n  }\n\n  /**\n   * Perform a GROQ-query against the configured dataset.\n   *\n   * @param query - GROQ-query to perform\n   */\n  fetch<\n    R = Any,\n    Q extends QueryWithoutParams = QueryWithoutParams,\n    const G extends string = string,\n  >(query: G, params?: Q | QueryWithoutParams): Promise<ClientReturn<G, R>>\n  /**\n   * Perform a GROQ-query against the configured dataset.\n   *\n   * @param query - GROQ-query to perform\n   * @param params - Optional query parameters\n   * @param options - Optional request options\n   */\n  fetch<\n    R = Any,\n    Q extends QueryWithoutParams | QueryParams = QueryParams,\n    const G extends string = string,\n  >(\n    query: G,\n    params: Q extends QueryWithoutParams ? QueryWithoutParams : Q,\n    options?: FilteredResponseQueryOptions,\n  ): Promise<ClientReturn<G, R>>\n  /**\n   * Perform a GROQ-query against the configured dataset.\n   *\n   * @param query - GROQ-query to perform\n   * @param params - Optional query parameters\n   * @param options - Request options\n   */\n  fetch<\n    R = Any,\n    Q extends QueryWithoutParams | QueryParams = QueryParams,\n    const G extends string = string,\n  >(\n    query: G,\n    params: Q extends QueryWithoutParams ? QueryWithoutParams : Q,\n    options: UnfilteredResponseQueryOptions,\n  ): Promise<RawQueryResponse<ClientReturn<G, R>>>\n  /**\n   * Perform a GROQ-query against the configured dataset.\n   *\n   * @param query - GROQ-query to perform\n   * @param params - Optional query parameters\n   * @param options - Request options\n   */\n  fetch<\n    R = Any,\n    Q extends QueryWithoutParams | QueryParams = QueryParams,\n    const G extends string = string,\n  >(\n    query: G,\n    params: Q extends QueryWithoutParams ? QueryWithoutParams : Q,\n    options: UnfilteredResponseWithoutQuery,\n  ): Promise<RawQuerylessQueryResponse<ClientReturn<G, R>>>\n  fetch<R, Q, const G extends string>(\n    query: G,\n    params?: Q,\n    options?: QueryOptions,\n  ): Promise<RawQueryResponse<ClientReturn<G, R>> | ClientReturn<G, R>> {\n    return lastValueFrom(\n      dataMethods._fetch<ClientReturn<G, R>, Q>(\n        this,\n        this.#httpRequest,\n        this.#clientConfig.stega,\n        query,\n        params,\n        options,\n      ),\n    )\n  }\n\n  /**\n   * Fetch a single document with the given ID.\n   *\n   * @param id - Document ID to fetch\n   * @param options - Request options\n   */\n  getDocument<R extends Record<string, Any> = Record<string, Any>>(\n    id: string,\n    options?: {signal?: AbortSignal; tag?: string; releaseId?: string},\n  ): Promise<SanityDocument<R> | undefined> {\n    return lastValueFrom(dataMethods._getDocument<R>(this, this.#httpRequest, id, options))\n  }\n\n  /**\n   * Fetch multiple documents in one request.\n   * Should be used sparingly - performing a query is usually a better option.\n   * The order/position of documents is preserved based on the original array of IDs.\n   * If any of the documents are missing, they will be replaced by a `null` entry in the returned array\n   *\n   * @param ids - Document IDs to fetch\n   * @param options - Request options\n   */\n  getDocuments<R extends Record<string, Any> = Record<string, Any>>(\n    ids: string[],\n    options?: {signal?: AbortSignal; tag?: string},\n  ): Promise<(SanityDocument<R> | null)[]> {\n    return lastValueFrom(dataMethods._getDocuments<R>(this, this.#httpRequest, ids, options))\n  }\n\n  /**\n   * Create a document. Requires a `_type` property. If no `_id` is provided, it will be generated by the database.\n   * Returns a promise that resolves to the created document.\n   *\n   * @param document - Document to create\n   * @param options - Mutation options\n   */\n  create<R extends Record<string, Any> = Record<string, Any>>(\n    document: SanityDocumentStub<R>,\n    options: FirstDocumentMutationOptions,\n  ): Promise<SanityDocument<R>>\n  /**\n   * Create a document. Requires a `_type` property. If no `_id` is provided, it will be generated by the database.\n   * Returns a promise that resolves to an array containing the created document.\n   *\n   * @param document - Document to create\n   * @param options - Mutation options\n   */\n  create<R extends Record<string, Any> = Record<string, Any>>(\n    document: SanityDocumentStub<R>,\n    options: AllDocumentsMutationOptions,\n  ): Promise<SanityDocument<R>[]>\n  /**\n   * Create a document. Requires a `_type` property. If no `_id` is provided, it will be generated by the database.\n   * Returns a promise that resolves to a mutation result object containing the ID of the created document.\n   *\n   * @param document - Document to create\n   * @param options - Mutation options\n   */\n  create<R extends Record<string, Any> = Record<string, Any>>(\n    document: SanityDocumentStub<R>,\n    options: FirstDocumentIdMutationOptions,\n  ): Promise<SingleMutationResult>\n  /**\n   * Create a document. Requires a `_type` property. If no `_id` is provided, it will be generated by the database.\n   * Returns a promise that resolves to a mutation result object containing the ID of the created document.\n   *\n   * @param document - Document to create\n   * @param options - Mutation options\n   */\n  create<R extends Record<string, Any> = Record<string, Any>>(\n    document: SanityDocumentStub<R>,\n    options: AllDocumentIdsMutationOptions,\n  ): Promise<MultipleMutationResult>\n  /**\n   * Create a document. Requires a `_type` property. If no `_id` is provided, it will be generated by the database.\n   * Returns a promise that resolves to the created document.\n   *\n   * @param document - Document to create\n   * @param options - Mutation options\n   */\n  create<R extends Record<string, Any> = Record<string, Any>>(\n    document: SanityDocumentStub<R>,\n    options?: BaseMutationOptions,\n  ): Promise<SanityDocument<R>>\n  create<R extends Record<string, Any> = Record<string, Any>>(\n    document: SanityDocumentStub<R>,\n    options?:\n      | AllDocumentIdsMutationOptions\n      | AllDocumentsMutationOptions\n      | BaseMutationOptions\n      | FirstDocumentIdMutationOptions\n      | FirstDocumentMutationOptions,\n  ): Promise<\n    SanityDocument<R> | SanityDocument<R>[] | SingleMutationResult | MultipleMutationResult\n  > {\n    return lastValueFrom(\n      dataMethods._create<R>(this, this.#httpRequest, document, 'create', options),\n    )\n  }\n\n  /**\n   * Create a document if no document with the same ID already exists.\n   * Returns a promise that resolves to the created document.\n   *\n   * @param document - Document to create\n   * @param options - Mutation options\n   */\n  createIfNotExists<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options: FirstDocumentMutationOptions,\n  ): Promise<SanityDocument<R>>\n  /**\n   * Create a document if no document with the same ID already exists.\n   * Returns a promise that resolves to an array containing the created document.\n   *\n   * @param document - Document to create\n   * @param options - Mutation options\n   */\n  createIfNotExists<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options: AllDocumentsMutationOptions,\n  ): Promise<SanityDocument<R>[]>\n  /**\n   * Create a document if no document with the same ID already exists.\n   * Returns a promise that resolves to a mutation result object containing the ID of the created document.\n   *\n   * @param document - Document to create\n   * @param options - Mutation options\n   */\n  createIfNotExists<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options: FirstDocumentIdMutationOptions,\n  ): Promise<SingleMutationResult>\n  /**\n   * Create a document if no document with the same ID already exists.\n   * Returns a promise that resolves to a mutation result object containing the ID of the created document.\n   *\n   * @param document - Document to create\n   * @param options - Mutation options\n   */\n  createIfNotExists<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options: AllDocumentIdsMutationOptions,\n  ): Promise<MultipleMutationResult>\n  /**\n   * Create a document if no document with the same ID already exists.\n   * Returns a promise that resolves to the created document.\n   *\n   * @param document - Document to create\n   * @param options - Mutation options\n   */\n  createIfNotExists<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options?: BaseMutationOptions,\n  ): Promise<SanityDocument<R>>\n  createIfNotExists<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options?:\n      | AllDocumentIdsMutationOptions\n      | AllDocumentsMutationOptions\n      | BaseMutationOptions\n      | FirstDocumentIdMutationOptions\n      | FirstDocumentMutationOptions,\n  ): Promise<\n    SanityDocument<R> | SanityDocument<R>[] | SingleMutationResult | MultipleMutationResult\n  > {\n    return lastValueFrom(\n      dataMethods._createIfNotExists<R>(this, this.#httpRequest, document, options),\n    )\n  }\n\n  /**\n   * Create a document if it does not exist, or replace a document with the same document ID\n   * Returns a promise that resolves to the created document.\n   *\n   * @param document - Document to either create or replace\n   * @param options - Mutation options\n   */\n  createOrReplace<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options: FirstDocumentMutationOptions,\n  ): Promise<SanityDocument<R>>\n  /**\n   * Create a document if it does not exist, or replace a document with the same document ID\n   * Returns a promise that resolves to an array containing the created document.\n   *\n   * @param document - Document to either create or replace\n   * @param options - Mutation options\n   */\n  createOrReplace<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options: AllDocumentsMutationOptions,\n  ): Promise<SanityDocument<R>[]>\n  /**\n   * Create a document if it does not exist, or replace a document with the same document ID\n   * Returns a promise that resolves to a mutation result object containing the ID of the created document.\n   *\n   * @param document - Document to either create or replace\n   * @param options - Mutation options\n   */\n  createOrReplace<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options: FirstDocumentIdMutationOptions,\n  ): Promise<SingleMutationResult>\n  /**\n   * Create a document if it does not exist, or replace a document with the same document ID\n   * Returns a promise that resolves to a mutation result object containing the created document ID.\n   *\n   * @param document - Document to either create or replace\n   * @param options - Mutation options\n   */\n  createOrReplace<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options: AllDocumentIdsMutationOptions,\n  ): Promise<MultipleMutationResult>\n  /**\n   * Create a document if it does not exist, or replace a document with the same document ID\n   * Returns a promise that resolves to the created document.\n   *\n   * @param document - Document to either create or replace\n   * @param options - Mutation options\n   */\n  createOrReplace<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options?: BaseMutationOptions,\n  ): Promise<SanityDocument<R>>\n  createOrReplace<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options?:\n      | AllDocumentIdsMutationOptions\n      | AllDocumentsMutationOptions\n      | BaseMutationOptions\n      | FirstDocumentIdMutationOptions\n      | FirstDocumentMutationOptions,\n  ): Promise<\n    SanityDocument<R> | SanityDocument<R>[] | SingleMutationResult | MultipleMutationResult\n  > {\n    return lastValueFrom(\n      dataMethods._createOrReplace<R>(this, this.#httpRequest, document, options),\n    )\n  }\n\n  /**\n   * @public\n   *\n   * Creates a new version of a published document.\n   *\n   * @remarks\n   * * Requires a document with a `_type` property.\n   * * Creating a version with no `releaseId` will create a new draft version of the published document.\n   * * If the `document._id` is defined, it should be a draft or release version ID that matches the version ID generated from `publishedId` and `releaseId`.\n   * * If the `document._id` is not defined, it will be generated from `publishedId` and `releaseId`.\n   * * To create a version of an unpublished document, use the `client.create` method.\n   *\n   * @category Versions\n   *\n   * @param params - Version action parameters:\n   *   - `document` - The document to create as a new version (must include `_type`).\n   *   - `publishedId` - The ID of the published document being versioned.\n   *   - `releaseId` - The ID of the release to create the version for.\n   * @param options - Additional action options.\n   * @returns A promise that resolves to the `transactionId`.\n   *\n   * @example Creating a new version of a published document with a generated version ID\n   * ```ts\n   * const transactionId = await client.createVersion({\n   *   // The document does not need to include an `_id` property since it will be generated from `publishedId` and `releaseId`\n   *   document: {_type: 'myDocument', title: 'My Document'},\n   *   publishedId: 'myDocument',\n   *   releaseId: 'myRelease',\n   * })\n   *\n   * // The following document will be created:\n   * // {\n   * //   _id: 'versions.myRelease.myDocument',\n   * //   _type: 'myDocument',\n   * //   title: 'My Document',\n   * // }\n   * ```\n   *\n   * @example Creating a new version of a published document with a specified version ID\n   * ```ts\n   * const transactionId = await client.createVersion({\n   *   document: {_type: 'myDocument', _id: 'versions.myRelease.myDocument', title: 'My Document'},\n   *   // `publishedId` and `releaseId` are not required since `document._id` has been specified\n   * })\n   *\n   * // The following document will be created:\n   * // {\n   * //   _id: 'versions.myRelease.myDocument',\n   * //   _type: 'myDocument',\n   * //   title: 'My Document',\n   * // }\n   * ```\n   *\n   * @example Creating a new draft version of a published document\n   * ```ts\n   * const transactionId = await client.createVersion({\n   *   document: {_type: 'myDocument', title: 'My Document'},\n   *   publishedId: 'myDocument',\n   * })\n   *\n   * // The following document will be created:\n   * // {\n   * //   _id: 'drafts.myDocument',\n   * //   _type: 'myDocument',\n   * //   title: 'My Document',\n   * // }\n   * ```\n   */\n  createVersion<R extends Record<string, Any>>(\n    args: {\n      document: SanityDocumentStub<R>\n      publishedId: string\n      releaseId?: string\n    },\n    options?: BaseActionOptions,\n  ): Promise<SingleActionResult | MultipleActionResult>\n  createVersion<R extends Record<string, Any>>(\n    args: {\n      document: IdentifiedSanityDocumentStub<R>\n      publishedId?: string\n      releaseId?: string\n    },\n    options?: BaseActionOptions,\n  ): Promise<SingleActionResult | MultipleActionResult>\n  createVersion<R extends Record<string, Any>>(\n    {\n      document,\n      publishedId,\n      releaseId,\n    }: {\n      document: SanityDocumentStub<R> | IdentifiedSanityDocumentStub<R>\n      publishedId?: string\n      releaseId?: string\n    },\n    options?: BaseActionOptions,\n  ): Promise<SingleActionResult | MultipleActionResult> {\n    const documentVersionId = deriveDocumentVersionId('createVersion', {\n      document,\n      publishedId,\n      releaseId,\n    })\n\n    const documentVersion = {...document, _id: documentVersionId}\n    const versionPublishedId = publishedId || getPublishedId(document._id)\n\n    return firstValueFrom(\n      dataMethods._createVersion<R>(\n        this,\n        this.#httpRequest,\n        documentVersion,\n        versionPublishedId,\n        options,\n      ),\n    )\n  }\n\n  /**\n   * Deletes a document with the given document ID.\n   * Returns a promise that resolves to the deleted document.\n   *\n   * @param id - Document ID to delete\n   * @param options - Options for the mutation\n   */\n  delete<R extends Record<string, Any> = Record<string, Any>>(\n    id: string,\n    options: FirstDocumentMutationOptions,\n  ): Promise<SanityDocument<R>>\n  /**\n   * Deletes a document with the given document ID.\n   * Returns a promise that resolves to an array containing the deleted document.\n   *\n   * @param id - Document ID to delete\n   * @param options - Options for the mutation\n   */\n  delete<R extends Record<string, Any> = Record<string, Any>>(\n    id: string,\n    options: AllDocumentsMutationOptions,\n  ): Promise<SanityDocument<R>[]>\n  /**\n   * Deletes a document with the given document ID.\n   * Returns a promise that resolves to a mutation result object containing the deleted document ID.\n   *\n   * @param id - Document ID to delete\n   * @param options - Options for the mutation\n   */\n  delete(id: string, options: FirstDocumentIdMutationOptions): Promise<SingleMutationResult>\n  /**\n   * Deletes a document with the given document ID.\n   * Returns a promise that resolves to a mutation result object containing the deleted document ID.\n   *\n   * @param id - Document ID to delete\n   * @param options - Options for the mutation\n   */\n  delete(id: string, options: AllDocumentIdsMutationOptions): Promise<MultipleMutationResult>\n  /**\n   * Deletes a document with the given document ID.\n   * Returns a promise that resolves to the deleted document.\n   *\n   * @param id - Document ID to delete\n   * @param options - Options for the mutation\n   */\n  delete<R extends Record<string, Any> = Record<string, Any>>(\n    id: string,\n    options?: BaseMutationOptions,\n  ): Promise<SanityDocument<R>>\n  /**\n   * Deletes one or more documents matching the given query or document ID.\n   * Returns a promise that resolves to first deleted document.\n   *\n   * @param selection - An object with either an `id` or `query` key defining what to delete\n   * @param options - Options for the mutation\n   */\n  delete<R extends Record<string, Any> = Record<string, Any>>(\n    selection: MutationSelection,\n    options: FirstDocumentMutationOptions,\n  ): Promise<SanityDocument<R>>\n  /**\n   * Deletes one or more documents matching the given query or document ID.\n   * Returns a promise that resolves to an array containing the deleted documents.\n   *\n   * @param selection - An object with either an `id` or `query` key defining what to delete\n   * @param options - Options for the mutation\n   */\n  delete<R extends Record<string, Any> = Record<string, Any>>(\n    selection: MutationSelection,\n    options: AllDocumentsMutationOptions,\n  ): Promise<SanityDocument<R>[]>\n  /**\n   * Deletes one or more documents matching the given query or document ID.\n   * Returns a promise that resolves to a mutation result object containing the ID of the first deleted document.\n   *\n   * @param selection - An object with either an `id` or `query` key defining what to delete\n   * @param options - Options for the mutation\n   */\n  delete(\n    selection: MutationSelection,\n    options: FirstDocumentIdMutationOptions,\n  ): Promise<SingleMutationResult>\n  /**\n   * Deletes one or more documents matching the given query or document ID.\n   * Returns a promise that resolves to a mutation result object containing the document IDs that were deleted.\n   *\n   * @param selection - An object with either an `id` or `query` key defining what to delete\n   * @param options - Options for the mutation\n   */\n  delete(\n    selection: MutationSelection,\n    options: AllDocumentIdsMutationOptions,\n  ): Promise<MultipleMutationResult>\n  /**\n   * Deletes one or more documents matching the given query or document ID.\n   * Returns a promise that resolves to first deleted document.\n   *\n   * @param selection - An object with either an `id` or `query` key defining what to delete\n   * @param options - Options for the mutation\n   */\n  delete<R extends Record<string, Any> = Record<string, Any>>(\n    selection: MutationSelection,\n    options?: BaseMutationOptions,\n  ): Promise<SanityDocument<R>>\n  delete<R extends Record<string, Any> = Record<string, Any>>(\n    selection: string | MutationSelection,\n    options?:\n      | AllDocumentIdsMutationOptions\n      | AllDocumentsMutationOptions\n      | BaseMutationOptions\n      | FirstDocumentIdMutationOptions\n      | FirstDocumentMutationOptions,\n  ): Promise<\n    SanityDocument<R> | SanityDocument<R>[] | SingleMutationResult | MultipleMutationResult\n  > {\n    return lastValueFrom(dataMethods._delete<R>(this, this.#httpRequest, selection, options))\n  }\n\n  /**\n   * @public\n   *\n   * Deletes the draft or release version of a document.\n   *\n   * @remarks\n   * * Discarding a version with no `releaseId` will discard the draft version of the published document.\n   * * If the draft or release version does not exist, any error will throw.\n   *\n   * @param params - Version action parameters:\n   *   - `releaseId` - The ID of the release to discard the document from.\n   *   - `publishedId` - The published ID of the document to discard.\n   * @param purge - if `true` the document history is also discarded.\n   * @param options - Additional action options.\n   * @returns a promise that resolves to the `transactionId`.\n   *\n   * @example Discarding a release version of a document\n   * ```ts\n   * client.discardVersion({publishedId: 'myDocument', releaseId: 'myRelease'})\n   * // The document with the ID `versions.myRelease.myDocument` will be discarded.\n   * ```\n   *\n   * @example Discarding a draft version of a document\n   * ```ts\n   * client.discardVersion({publishedId: 'myDocument'})\n   * // The document with the ID `drafts.myDocument` will be discarded.\n   * ```\n   */\n  discardVersion(\n    {releaseId, publishedId}: {releaseId?: string; publishedId: string},\n    purge?: boolean,\n    options?: BaseActionOptions,\n  ): Promise<SingleActionResult | MultipleActionResult> {\n    const documentVersionId = getDocumentVersionId(publishedId, releaseId)\n\n    return lastValueFrom(\n      dataMethods._discardVersion(this, this.#httpRequest, documentVersionId, purge, options),\n    )\n  }\n\n  /**\n   * @public\n   *\n   * Replaces an existing version document.\n   *\n   * @remarks\n   * * Requires a document with a `_type` property.\n   * * If the `document._id` is defined, it should be a draft or release version ID that matches the version ID generated from `publishedId` and `releaseId`.\n   * * If the `document._id` is not defined, it will be generated from `publishedId` and `releaseId`.\n   * * Replacing a version with no `releaseId` will replace the draft version of the published document.\n   * * At least one of the **version** or **published** documents must exist.\n   *\n   * @param params - Version action parameters:\n   *   - `document` - The new document to replace the version with.\n   *   - `releaseId` - The ID of the release where the document version is replaced.\n   *   - `publishedId` - The ID of the published document to replace.\n   * @param options - Additional action options.\n   * @returns a promise that resolves to the `transactionId`.\n   *\n   * @example Replacing a release version of a published document with a generated version ID\n   * ```ts\n   * await client.replaceVersion({\n   *   document: {_type: 'myDocument', title: 'My Document'},\n   *   publishedId: 'myDocument',\n   *   releaseId: 'myRelease',\n   * })\n   *\n   * // The following document will be patched:\n   * // {\n   * //   _id: 'versions.myRelease.myDocument',\n   * //   _type: 'myDocument',\n   * //   title: 'My Document',\n   * // }\n   * ```\n   *\n   * @example Replacing a release version of a published document with a specified version ID\n   * ```ts\n   * await client.replaceVersion({\n   *   document: {_type: 'myDocument', _id: 'versions.myRelease.myDocument', title: 'My Document'},\n   *   // `publishedId` and `releaseId` are not required since `document._id` has been specified\n   * })\n   *\n   * // The following document will be patched:\n   * // {\n   * //   _id: 'versions.myRelease.myDocument',\n   * //   _type: 'myDocument',\n   * //   title: 'My Document',\n   * // }\n   * ```\n   *\n   * @example Replacing a draft version of a published document\n   * ```ts\n   * await client.replaceVersion({\n   *   document: {_type: 'myDocument', title: 'My Document'},\n   *   publishedId: 'myDocument',\n   * })\n   *\n   * // The following document will be patched:\n   * // {\n   * //   _id: 'drafts.myDocument',\n   * //   _type: 'myDocument',\n   * //   title: 'My Document',\n   * // }\n   * ```\n   */\n\n  replaceVersion<R extends Record<string, Any>>(\n    args: {\n      document: SanityDocumentStub<R>\n      publishedId: string\n      releaseId?: string\n    },\n    options?: BaseActionOptions,\n  ): Promise<SingleActionResult | MultipleActionResult>\n  replaceVersion<R extends Record<string, Any>>(\n    args: {\n      document: IdentifiedSanityDocumentStub<R>\n      publishedId?: string\n      releaseId?: string\n    },\n    options?: BaseActionOptions,\n  ): Promise<SingleActionResult | MultipleActionResult>\n  replaceVersion<R extends Record<string, Any>>(\n    {\n      document,\n      publishedId,\n      releaseId,\n    }: {\n      document: SanityDocumentStub<R> | IdentifiedSanityDocumentStub<R>\n      publishedId?: string\n      releaseId?: string\n    },\n    options?: BaseActionOptions,\n  ): Promise<SingleActionResult | MultipleActionResult> {\n    const documentVersionId = deriveDocumentVersionId('replaceVersion', {\n      document,\n      publishedId,\n      releaseId,\n    })\n\n    const documentVersion = {...document, _id: documentVersionId}\n\n    return firstValueFrom(\n      dataMethods._replaceVersion<R>(this, this.#httpRequest, documentVersion, options),\n    )\n  }\n\n  /**\n   * @public\n   *\n   * Used to indicate when a document within a release should be unpublished when\n   * the release is run.\n   *\n   * @remarks\n   * * If the published document does not exist, an error will be thrown.\n   *\n   * @param params - Version action parameters:\n   *   - `releaseId` - The ID of the release to unpublish the document from.\n   *   - `publishedId` - The published ID of the document to unpublish.\n   * @param options - Additional action options.\n   * @returns a promise that resolves to the `transactionId`.\n   *\n   * @example Unpublishing a release version of a published document\n   * ```ts\n   * await client.unpublishVersion({publishedId: 'myDocument', releaseId: 'myRelease'})\n   * // The document with the ID `versions.myRelease.myDocument` will be unpublished. when `myRelease` is run.\n   * ```\n   */\n  unpublishVersion(\n    {releaseId, publishedId}: {releaseId: string; publishedId: string},\n    options?: BaseActionOptions,\n  ): Promise<SingleActionResult | MultipleActionResult> {\n    const versionId = getVersionId(publishedId, releaseId)\n\n    return lastValueFrom(\n      dataMethods._unpublishVersion(this, this.#httpRequest, versionId, publishedId, options),\n    )\n  }\n\n  /**\n   * Perform mutation operations against the configured dataset\n   * Returns a promise that resolves to the first mutated document.\n   *\n   * @param operations - Mutation operations to execute\n   * @param options - Mutation options\n   */\n  mutate<R extends Record<string, Any> = Record<string, Any>>(\n    operations: Mutation<R>[] | Patch | Transaction,\n    options: FirstDocumentMutationOptions,\n  ): Promise<SanityDocument<R>>\n  /**\n   * Perform mutation operations against the configured dataset.\n   * Returns a promise that resolves to an array of the mutated documents.\n   *\n   * @param operations - Mutation operations to execute\n   * @param options - Mutation options\n   */\n  mutate<R extends Record<string, Any> = Record<string, Any>>(\n    operations: Mutation<R>[] | Patch | Transaction,\n    options: AllDocumentsMutationOptions,\n  ): Promise<SanityDocument<R>[]>\n  /**\n   * Perform mutation operations against the configured dataset\n   * Returns a promise that resolves to a mutation result object containing the document ID of the first mutated document.\n   *\n   * @param operations - Mutation operations to execute\n   * @param options - Mutation options\n   */\n  mutate<R extends Record<string, Any> = Record<string, Any>>(\n    operations: Mutation<R>[] | Patch | Transaction,\n    options: FirstDocumentIdMutationOptions,\n  ): Promise<SingleMutationResult>\n  /**\n   * Perform mutation operations against the configured dataset\n   * Returns a promise that resolves to a mutation result object containing the mutated document IDs.\n   *\n   * @param operations - Mutation operations to execute\n   * @param options - Mutation options\n   */\n  mutate<R extends Record<string, Any> = Record<string, Any>>(\n    operations: Mutation<R>[] | Patch | Transaction,\n    options: AllDocumentIdsMutationOptions,\n  ): Promise<MultipleMutationResult>\n  /**\n   * Perform mutation operations against the configured dataset\n   * Returns a promise that resolves to the first mutated document.\n   *\n   * @param operations - Mutation operations to execute\n   * @param options - Mutation options\n   */\n  mutate<R extends Record<string, Any> = Record<string, Any>>(\n    operations: Mutation<R>[] | Patch | Transaction,\n    options?: BaseMutationOptions,\n  ): Promise<SanityDocument<R>>\n  mutate<R extends Record<string, Any> = Record<string, Any>>(\n    operations: Mutation<R>[] | Patch | Transaction,\n    options?:\n      | FirstDocumentMutationOptions\n      | AllDocumentsMutationOptions\n      | FirstDocumentIdMutationOptions\n      | AllDocumentIdsMutationOptions\n      | BaseMutationOptions,\n  ): Promise<\n    SanityDocument<R> | SanityDocument<R>[] | SingleMutationResult | MultipleMutationResult\n  > {\n    return lastValueFrom(dataMethods._mutate<R>(this, this.#httpRequest, operations, options))\n  }\n\n  /**\n   * Create a new buildable patch of operations to perform\n   *\n   * @param documentId - Document ID to patch\n   * @param operations - Optional object of patch operations to initialize the patch instance with\n   * @returns Patch instance - call `.commit()` to perform the operations defined\n   */\n  patch(documentId: string, operations?: PatchOperations): Patch\n\n  /**\n   * Create a new buildable patch of operations to perform\n   *\n   * @param documentIds - Array of document IDs to patch\n   * @param operations - Optional object of patch operations to initialize the patch instance with\n   * @returns Patch instance - call `.commit()` to perform the operations defined\n   */\n  patch(documentIds: string[], operations?: PatchOperations): Patch\n\n  /**\n   * Create a new buildable patch of operations to perform\n   *\n   * @param selection - An object with `query` and optional `params`, defining which document(s) to patch\n   * @param operations - Optional object of patch operations to initialize the patch instance with\n   * @returns Patch instance - call `.commit()` to perform the operations defined\n   */\n  patch(selection: MutationSelection, operations?: PatchOperations): Patch\n\n  /**\n   * Create a new buildable patch of operations to perform\n   *\n   * @param selection - Document ID, an array of document IDs, or an object with `query` and optional `params`, defining which document(s) to patch\n   * @param operations - Optional object of patch operations to initialize the patch instance with\n   * @returns Patch instance - call `.commit()` to perform the operations defined\n   */\n  patch(documentId: PatchSelection, operations?: PatchOperations): Patch {\n    return new Patch(documentId, operations, this)\n  }\n\n  /**\n   * Create a new transaction of mutations\n   *\n   * @param operations - Optional array of mutation operations to initialize the transaction instance with\n   */\n  transaction<R extends Record<string, Any> = Record<string, Any>>(\n    operations?: Mutation<R>[],\n  ): Transaction {\n    return new Transaction(operations, this)\n  }\n\n  /**\n   * Perform action operations against the configured dataset\n   * Returns a promise that resolves to the transaction result\n   *\n   * @param operations - Action operation(s) to execute\n   * @param options - Action options\n   */\n  action(\n    operations: Action | Action[],\n    options?: BaseActionOptions,\n  ): Promise<SingleActionResult | MultipleActionResult> {\n    return lastValueFrom(dataMethods._action(this, this.#httpRequest, operations, options))\n  }\n\n  /**\n   * Perform a request against the Sanity API\n   * NOTE: Only use this for Sanity API endpoints, not for your own APIs!\n   *\n   * @param options - Request options\n   * @returns Promise resolving to the response body\n   */\n  request<R = Any>(options: RawRequestOptions): Promise<R> {\n    return lastValueFrom(dataMethods._request<R>(this, this.#httpRequest, options))\n  }\n\n  /**\n   * Perform an HTTP request a `/data` sub-endpoint\n   * NOTE: Considered internal, thus marked as deprecated. Use `request` instead.\n   *\n   * @deprecated - Use `request()` or your own HTTP library instead\n   * @param endpoint - Endpoint to hit (mutate, query etc)\n   * @param body - Request body\n   * @param options - Request options\n   * @internal\n   */\n  dataRequest(endpoint: string, body: unknown, options?: BaseMutationOptions): Promise<Any> {\n    return lastValueFrom(dataMethods._dataRequest(this, this.#httpRequest, endpoint, body, options))\n  }\n\n  /**\n   * Get a Sanity API URL for the URI provided\n   *\n   * @param uri - URI/path to build URL for\n   * @param canUseCdn - Whether or not to allow using the API CDN for this route\n   */\n  getUrl(uri: string, canUseCdn?: boolean): string {\n    return dataMethods._getUrl(this, uri, canUseCdn)\n  }\n\n  /**\n   * Get a Sanity API URL for the data operation and path provided\n   *\n   * @param operation - Data operation (eg `query`, `mutate`, `listen` or similar)\n   * @param path - Path to append after the operation\n   */\n  getDataUrl(operation: string, path?: string): string {\n    return dataMethods._getDataUrl(this, operation, path)\n  }\n}\n", "import type {Middlewares} from 'get-it'\n\nimport {defineHttpRequest} from './http/request'\nimport type {Any, ClientConfig, HttpRequest} from './types'\n\nexport {validateApiPerspective} from './config'\nexport {\n  ChannelError,\n  connectEventSource,\n  ConnectionFailedError,\n  DisconnectError,\n  type EventSourceEvent,\n  type EventSourceInstance,\n  MessageError,\n  MessageParseError,\n  type ServerSentEvent,\n} from './data/eventsource'\nexport * from './data/patch'\nexport * from './data/transaction'\nexport {\n  ClientError,\n  CorsOriginError,\n  formatQueryParseError,\n  isQueryParseError,\n  ServerError,\n} from './http/errors'\nexport * from './SanityClient'\nexport * from './types'\n\n/** @alpha */\nexport {adapter as unstable__adapter, environment as unstable__environment} from 'get-it'\n\n/**\n * Create the `requester` and `createClient` exports, that have environment specific middleware for node and browsers\n * @internal\n */\nexport default function defineCreateClientExports<\n  SanityClientType,\n  ClientConfigType extends ClientConfig,\n>(\n  envMiddleware: Middlewares,\n  ClassConstructor: new (httpRequest: HttpRequest, config: ClientConfigType) => SanityClientType,\n) {\n  // Set the http client to use for requests, and its environment specific middleware\n  const defaultRequester = defineHttpRequest(envMiddleware)\n\n  const createClient = (config: ClientConfigType) => {\n    const clientRequester = defineHttpRequest(envMiddleware)\n    return new ClassConstructor(\n      (options, requester) =>\n        (requester || clientRequester)({\n          maxRedirects: 0,\n          maxRetries: config.maxRetries,\n          retryDelay: config.retryDelay,\n          ...options,\n        } as Any),\n      config,\n    )\n  }\n\n  return {requester: defaultRequester, createClient}\n}\n", "import {printNoDefaultExport} from './warnings'\n\n/* @internal */\nexport function defineDeprecatedCreateClient<SanityClientType, ClientConfigType>(\n  createClient: (config: ClientConfigType) => SanityClientType,\n) {\n  return function deprecatedCreateClient(config: ClientConfigType) {\n    printNoDefaultExport()\n    return createClient(config)\n  }\n}\n", "import {agent, debug, headers} from 'get-it/middleware'\n\nimport {name, version} from '../../package.json'\n\nconst middleware = [\n  debug({verbose: true, namespace: 'sanity:client'}),\n  headers({'User-Agent': `${name} ${version}`}),\n\n  // Enable keep-alive, and in addition limit the number of sockets that can be opened.\n  // This avoids opening too many connections to the server if someone tries to execute\n  // a bunch of requests in parallel. It's recommended to have a concurrency limit\n  // at a \"higher limit\" (i.e. you shouldn't actually execute hundreds of requests in parallel),\n  // and this is mainly to minimize the impact for the network and server.\n  //\n  // We're currently matching the same defaults as browsers:\n  // https://stackoverflow.com/questions/26003756/is-there-a-limit-practical-or-otherwise-to-the-number-of-web-sockets-a-page-op\n  agent({\n    keepAlive: true,\n    maxSockets: 30,\n    maxTotalSockets: 256,\n  }),\n]\n\nexport default middleware\n", "import defineCreateClientExports, {type ClientConfig, SanityClient} from './defineCreateClient'\nimport {defineDeprecatedCreateClient} from './defineDeprecatedCreateClient'\nimport envMiddleware from './http/nodeMiddleware'\n\nexport * from './defineCreateClient'\n\nconst exp = defineCreateClientExports<SanityClient, ClientConfig>(envMiddleware, SanityClient)\n\n/** @public */\nexport const requester = exp.requester\n\n/**\n * @remarks\n * As of API version `v2025-02-19`, the default perspective used by the client has changed from `raw` to `published`. {@link https://www.sanity.io/changelog/676aaa9d-2da6-44fb-abe5-580f28047c10|Changelog}\n * @public\n */\nexport const createClient = exp.createClient\n\n/**\n * @public\n * @deprecated Use the named export `createClient` instead of the `default` export\n */\nconst deprecatedCreateClient = defineDeprecatedCreateClient(createClient)\nexport default deprecatedCreateClient\n"], "names": ["location", "<PERSON><PERSON><PERSON><PERSON>", "merge", "validators.validateObject", "validators.requireDocumentId", "validators.validateDocumentId", "headers", "validators.requireDocumentType", "validate.requestTag", "validators.resourceConfig", "uri", "validators.hasDataset", "dataset", "observable", "validators.validateAssetType", "defaults", "EventSource", "validate.resourceGuard", "finalize", "name", "validate.dataset", "map", "dataMethods._fetch", "dataMethods._getDocument", "dataMethods._getDocuments", "dataMethods._create", "dataMethods._createIfNotExists", "dataMethods._createOrReplace", "dataMethods._createVersion", "dataMethods._delete", "dataMethods._discardVersion", "dataMethods._replaceVersion", "dataMethods._unpublishVersion", "dataMethods._mutate", "dataMethods._action", "dataMethods._request", "dataMethods._getUrl", "dataMethods._getDataUrl", "dataMethods._dataRequest", "requester", "createClient", "envMiddleware"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,MAAM,UAAU;AAkBA,SAAA,UAAU,KAAA,EAAeA,SAAAA,EAAwB,OAAA,EAA0B;IACzF,MAAM,QAAQ,MAAM,KAAA,CAAM,OAAO,GAC3B,MAAM;QACV,OAAO,aAAaA,UAAS,KAAA,EAAO,KAAK;QACzC,KAAKA,UAAS,GAAA,GAAM,aAAaA,UAAS,GAAA,EAAK,KAAK,IAAI,KAAA;IAGpD,GAAA,EAAC,KAAA,EAAO,GAAA,EAAK,WAAA,CAAW,CAAA,GAAI,eAAe,KAAK,KAAK,GAErD,iBAAiB,GAAG,GAAG,EAAA,CAAG,MAAA;IAEhC,OAAO,MACJ,KAAA,CAAM,SAAS,GAAG,EAClB,KAAA,CAAM,OAAO,GAAG,EAChB,GAAA,CAAI,CAAC,MAAM,UAAU;QACd,MAAA,SAAS,QAAQ,IAAI,OAErB,SAAS,CAAA,CAAA,EADM,CAAA,CAAA,EAAI,MAAM,EAAA,CAAG,KAAA,CAAM,CAAC,cAAc,CACxB,CAAA,EAAA,CAAA,EACzB,YAAY,WAAA,CAAY,MAAM,CAAA,EAC9B,iBAAiB,CAAC,WAAA,CAAY,SAAS,CAAC,CAAA;QAC9C,IAAI,CAAC,WACI,OAAA,CAAA,CAAA,EAAI,MAAM,GAAG,KAAK,MAAA,GAAS,IAAI,CAAA,CAAA,EAAI,IAAI,EAAA,GAAK,EAAE,EAAA;QAGvD,IAAI,aAAa;QACb,IAAA,MAAM,OAAA,CAAQ,SAAS,GAAG;YACtB,MAAA,gBAAgB,KAAK,KAAA,CAAM,GAAG,KAAK,GAAA,CAAI,SAAA,CAAU,CAAC,CAAA,GAAI,GAAG,CAAC,CAAC,EAAE,OAAA,CAAQ,UAAU,GAAG,GAClF,kBAAkB,SAAA,CAAU,CAAC,CAAA,IAAK;YAE3B,aAAA;gBACX,CAAA;CAAA,CAAA;gBACA,OAAO,OAAA,CAAQ,OAAO,GAAG;gBACzB;gBACA;gBACA,IAAI,MAAA,CAAO,eAAe;aAAA,CAC1B,IAAA,CAAK,EAAE,GAEL,kBAAkB,WAAA,CACpB,cAAc,MAAM,OAAA;QAAA;QAGxB,OAAO;YAAC;YAAK;YAAQ,KAAK,MAAA,GAAS,IAAI,CAAA,CAAA,EAAI,IAAI,EAAA,GAAK;YAAI,UAAU;SAAA,CAAE,IAAA,CAAK,EAAE;IAC5E,CAAA,EACA,IAAA,CAAK,CAAA;AAAA,CAAI;AACd;AAEA,SAAS,eACP,GAAA,EACA,MAAA,EAKA;IACA,MAAM,WAAqB;QAAC,GAAG,IAAI,KAAA;IAAK,GAClC,SAAmB;QAAC,GAAG,QAAA;QAAU,GAAG,IAAI,GAAA;IACxC,GAAA,aAAa,GACb,aAAa,GACb,YAAY,SAAS,IAAA,IAAQ,CAAA,GAC7B,cAAc,SAAS,MAAA,IAAU,GACjC,UAAU,OAAO,IAAA,EACjB,YAAY,OAAO,MAAA;IAEzB,IAAI,QAAQ,KAAK,GAAA,CAAI,YAAA,CAAa,aAAa,CAAA,GAAI,CAAC,GAChD,MAAM,KAAK,GAAA,CAAI,OAAO,MAAA,EAAQ,UAAU,UAAU;IAElD,cAAc,CAAA,KAAA,CAChB,QAAQ,CAAA,GAGN,YAAY,CAAA,KAAA,CACd,MAAM,OAAO,MAAA;IAGf,MAAM,WAAW,UAAU,WACrB,cAA2B,CAAC;IAE9B,IAAA,UACF,IAAA,IAAS,IAAI,GAAG,KAAK,UAAU,IAAK;QAClC,MAAM,aAAa,IAAI;QAEvB,IAAI,CAAC,aACH,WAAA,CAAY,UAAU,CAAA,GAAI,CAAA;aAAA,IACjB,MAAM,GAAG;YAClB,MAAM,eAAe,MAAA,CAAO,aAAa,CAAC,CAAA,CAAE,MAAA;YAE5C,WAAA,CAAY,UAAU,CAAA,GAAI;gBAAC;gBAAa,eAAe,cAAc,CAAC;aAAA;QAAA,OAAA,IAC7D,MAAM,UACf,WAAA,CAAY,UAAU,CAAA,GAAI;YAAC;YAAG,SAAS;SAAA;aAClC;YACL,MAAM,eAAe,MAAA,CAAO,aAAa,CAAC,CAAA,CAAE,MAAA;YAE5C,WAAA,CAAY,UAAU,CAAA,GAAI;gBAAC;gBAAG,YAAY;aAAA;QAAA;IAC5C;SAGE,gBAAgB,YACd,cACF,WAAA,CAAY,SAAS,CAAA,GAAI;QAAC;QAAa,CAAC;KAAA,GAExC,WAAA,CAAY,SAAS,CAAA,GAAI,CAAA,IAG3B,WAAA,CAAY,SAAS,CAAA,GAAI;QAAC;QAAa,YAAY,WAAW;KAAA;IAI3D,OAAA;QAAC;QAAO;QAAK;IAAW;AACjC;AAEA,SAAS,aAAa,MAAA,EAAgB,KAAA,EAA2B;IAC/D,IAAI,SAAS;IAEb,IAAA,IAAS,IAAI,GAAG,IAAI,MAAM,MAAA,EAAQ,IAAK;QACrC,MAAM,aAAa,KAAA,CAAM,CAAC,CAAA,CAAE,MAAA,GAAS;QAErC,IAAI,SAAS,aAAa,QACjB,OAAA;YACL,MAAM,IAAI;YAAA,eAAA;YACV,QAAQ,SAAS;QACnB;QAGQ,UAAA;IAAA;IAIL,OAAA;QACL,MAAM,MAAM,MAAA;QACZ,QAAQ,KAAA,CAAM,MAAM,MAAA,GAAS,CAAC,CAAA,EAAG,UAAU;IAC7C;AACF;ACvKA,MAAM,6BAA6B;AAG5B,MAAM,oBAAoB,MAAM;IACrC,SAAA;IACA,aAAuC,IAAA;IACvC,aAAA;IACA,QAAA;IAEA,YAAY,GAAA,EAAU,OAAA,CAAuB;QACrC,MAAA,QAAQ,kBAAkB,KAAK,OAAO;QAC5C,KAAA,CAAM,MAAM,OAAO,GACnB,OAAO,MAAA,CAAO,IAAA,EAAM,KAAK;IAAA;AAE7B;AAGO,MAAM,oBAAoB,MAAM;IACrC,SAAA;IACA,aAAuC,IAAA;IACvC,aAAA;IACA,QAAA;IAEA,YAAY,GAAA,CAAU;QACd,MAAA,QAAQ,kBAAkB,GAAG;QACnC,KAAA,CAAM,MAAM,OAAO,GACnB,OAAO,MAAA,CAAO,IAAA,EAAM,KAAK;IAAA;AAE7B;AAEA,SAAS,kBAAkB,GAAA,EAAU,OAAA,EAAmC;IAChE,MAAA,OAAO,IAAI,IAAA,EACX,QAAQ;QACZ,UAAU;QACV,YAAY,IAAI,UAAA;QAChB,cAAc,cAAc,MAAM,GAAG;QACrC,SAAS;QACT,SAAS,KAAA;IACX;IAGI,IAAA,6KAAC,WAAA,EAAS,IAAI,GAChB,OAAA,MAAM,OAAA,GAAU,iBAAiB,KAAK,IAAI,GACnC;IAGT,MAAM,QAAQ,KAAK,KAAA;IAGnB,IAAI,OAAO,SAAU,YAAY,OAAO,KAAK,OAAA,IAAY,UACvD,OAAA,MAAM,OAAA,GAAU,GAAG,KAAK,CAAA,GAAA,EAAM,KAAK,OAAO,EAAA,EACnC;IAIL,IAAA,OAAO,SAAU,YAAY,UAAU,MACzC,OAAI,OAAO,SAAU,WACnB,MAAM,OAAA,GAAU,QACP,OAAO,KAAK,OAAA,IAAY,WACjC,MAAM,OAAA,GAAU,KAAK,OAAA,GAErB,MAAM,OAAA,GAAU,iBAAiB,KAAK,IAAI,GAErC;IAIT,IAAI,gBAAgB,KAAK,KAAK,cAAc,KAAK,GAAG;QAClD,MAAM,WAAW,MAAM,KAAA,IAAS,CAC1B,CAAA,EAAA,QAAQ,SACX,KAAA,CAAM,GAAG,0BAA0B,EACnC,GAAA,CAAI,CAAC,OAAS,KAAK,KAAA,EAAO,WAAW,EACrC,MAAA,CAAO,OAAO;QACb,IAAA,WAAW,MAAM,MAAA,GAAS,CAAA;EAAA,EAAQ,MAAM,IAAA,CAAK,CAAA;EAAA,CAAM,CAAC,EAAA,GAAK;QACzD,OAAA,SAAS,MAAA,GAAS,8BAAA,CACpB,YAAY,CAAA;OAAA,EAAY,SAAS,MAAA,GAAS,0BAA0B,CAAA,KAAA,CAAA,GAEtE,MAAM,OAAA,GAAU,GAAG,MAAM,WAAW,GAAG,QAAQ,EAAA,EAC/C,MAAM,OAAA,GAAU,KAAK,KAAA,EACd;IAAA;IAIL,IAAA,kBAAkB,KAAK,GAAG;QACtB,MAAA,MAAM,SAAS,SAAS,OAAO;QAC/B,OAAA,MAAA,OAAA,GAAU,sBAAsB,OAAO,GAAG,GAChD,MAAM,OAAA,GAAU,KAAK,KAAA,EACd;IAAA;IAGT,OAAI,iBAAiB,SAAS,OAAO,MAAM,WAAA,IAAgB,WAAA,CAEzD,MAAM,OAAA,GAAU,MAAM,WAAA,EACtB,MAAM,OAAA,GAAU,OACT,KAAA,IAAA,CAIT,MAAM,OAAA,GAAU,iBAAiB,KAAK,IAAI,GACnC,KAAA;AACT;AAEA,SAAS,gBAAgB,KAAA,EAAuC;IAE5D,OAAA,UAAU,SACV,MAAM,IAAA,KAAS,mBACf,iBAAiB,SACjB,OAAO,MAAM,WAAA,IAAgB;AAEjC;AAEA,SAAS,cAAc,KAAA,EAAqC;IAExD,OAAA,UAAU,SACV,MAAM,IAAA,KAAS,iBACf,iBAAiB,SACjB,OAAO,MAAM,WAAA,IAAgB;AAEjC;AAGO,SAAS,kBAAkB,KAAA,EAAyC;IACzE,mLACE,WAAA,EAAS,KAAK,KACd,MAAM,IAAA,KAAS,qBACf,OAAO,MAAM,KAAA,IAAU,YACvB,OAAO,MAAM,KAAA,IAAU,YACvB,OAAO,MAAM,GAAA,IAAQ;AAEzB;AAUgB,SAAA,sBAAsB,KAAA,EAAwB,GAAA,EAAqB;IACjF,MAAM,EAAC,KAAA,EAAO,KAAA,EAAO,GAAA,EAAK,WAAA,CAAe,CAAA,GAAA;IAErC,IAAA,CAAC,SAAS,OAAO,QAAU,KAC7B,OAAO,CAAA,wBAAA,EAA2B,WAAW,EAAA;IAG/C,MAAM,UAAU,MAAM,CAAA;;KAAA,EAAY,GAAG,EAAA,GAAK;IAGnC,OAAA,CAAA;AAAA,EAFQ,UAAU,OAAO;QAAC;QAAO;IAAM,GAAA,WAAW,CAEhB,GAAG,OAAO,EAAA;AACrD;AAEA,SAAS,iBAAiB,GAAA,EAAU,IAAA,EAAe;IACjD,MAAM,UAAU,OAAO,QAAS,WAAW,CAAA,EAAA,EAAK,kBAAkB,MAAM,GAAG,CAAC,CAAA,CAAA,CAAA,GAAM,IAC5E,gBAAgB,IAAI,aAAA,GAAgB,CAAA,CAAA,EAAI,IAAI,aAAa,EAAA,GAAK;IACpE,OAAO,GAAG,IAAI,MAAM,CAAA,YAAA,EAAe,IAAI,GAAG,CAAA,kBAAA,EAAqB,IAAI,UAAU,GAAG,aAAa,GAAG,OAAO,EAAA;AACzG;AAEA,SAAS,cAAc,IAAA,EAAW,GAAA,EAAU;IAG1C,OAAA,CAFqB,IAAI,OAAA,CAAQ,cAAc,CAAA,IAAK,EAAA,EAAI,WAAA,GAC7B,OAAA,CAAQ,kBAAkB,MAAM,CAAA,IAC3C,KAAK,SAAA,CAAU,MAAM,MAAM,CAAC,IAAI;AAClD;AAEA,SAAS,kBAAkB,GAAA,EAAa,GAAA,EAAa;IAC5C,OAAA,IAAI,MAAA,GAAS,MAAM,GAAG,IAAI,KAAA,CAAM,GAAG,GAAG,CAAC,CAAA,MAAA,CAAA,GAAM;AACtD;AAGO,MAAM,wBAAwB,MAAM;IACzC,UAAA;IACA,aAAA;IAEA,YAAY,EAAC,SAAA,EAAA,CAAiC;QAC5C,KAAA,CAAM,iBAAiB,GACvB,IAAA,CAAK,IAAA,GAAO,mBACZ,IAAA,CAAK,SAAA,GAAY;QAEjB,MAAM,MAAM,IAAI,IAAI,CAAA,iCAAA,EAAoC,SAAS,CAAA,IAAA,CAAM;QACnE,IAAA,OAAO,WAAa,KAAa;YAC7B,MAAA,EAAC,MAAA,EAAA,GAAU;YACjB,IAAI,YAAA,CAAa,GAAA,CAAI,QAAQ,KAAK,GAClC,IAAI,YAAA,CAAa,GAAA,CAAI,UAAU,MAAM,GACrC,IAAA,CAAK,YAAA,GAAe,KACpB,IAAA,CAAK,OAAA,GAAU,CAAA,mFAAA,EAAsF,GAAG,EAAA;QAC1G,OACO,IAAA,CAAA,OAAA,GAAU,CAAA,sGAAA,EAAyG,GAAG,EAAA;IAAA;AAGjI;AC5LA,MAAM,YAAY;IAChB,YAAY,CAAC,KAAU,YAAyB;QAC9C,IAAI,IAAI,UAAA,IAAc,KACd,MAAA,IAAI,YAAY,GAAG;QACpB,IAAI,IAAI,UAAA,IAAc,KACrB,MAAA,IAAI,YAAY,KAAK,OAAO;QAG7B,OAAA;IAAA;AAEX;AAEA,SAAS,gBAAgB;IACvB,MAAM,OAAgC,CAAC;IAChC,OAAA;QACL,YAAY,CAAC,QAAa;YACxB,MAAM,OAAO,IAAI,OAAA,CAAQ,kBAAkB,CAAA,EACrC,WAAW,MAAM,OAAA,CAAQ,IAAI,IAAI,OAAO;gBAAC,IAAI;aAAA;YACnD,KAAA,MAAW,OAAO,SACZ,CAAC,OAAO,IAAA,CAAK,GAAG,CAAA,IAAA,CACpB,IAAA,CAAK,GAAG,CAAA,GAAI,CAAA,GACZ,QAAQ,IAAA,CAAK,GAAG,CAAA;YAEX,OAAA;QAAA;IAEX;AACF;AAGO,SAAS,kBAAkB,aAAA,EAAuC;IACvE,sKAAO,QAAA,EAAM;4KACX,QAAA,EAAM;YAAC;QAAA,CAAY;WAChB;QACH,cAAc;4KACd,cAAA,CAAY;4KACZ,eAAA,CAAa;YACb,2KAAA,CAAS;QACT;QACA,iLAAA,EAAW;YAAC,6JAAgB,aAAA;QAAW,CAAA;KACxC;AACH;AAGA,SAAS,YAAY,GAAA,EAAU,OAAA,EAAiB,OAAA,EAAc;IAExD,IAAA,QAAQ,UAAA,KAAe,EAAU,CAAA,OAAA,CAAA;IAIrC,MAAM,SAAS,QAAQ,MAAA,KAAW,SAAS,QAAQ,MAAA,KAAW,QAExDC,WAAAA,CADM,QAAQ,GAAA,IAAO,QAAQ,GAAA,EACf,UAAA,CAAW,aAAa,GACtC,sBACJ,IAAI,QAAA,IAAA,CACH,IAAI,QAAA,CAAS,UAAA,KAAe,OAC3B,IAAI,QAAA,CAAS,UAAA,KAAe,OAC5B,IAAI,QAAA,CAAS,UAAA,KAAe,GAAA;IAW3B,OAAA,CAAA,UAAUA,QAAAA,KAAY,sBAA4B,CAAA,oKAEhD,QAAA,CAAM,WAAA,CAAY,KAAK,SAAS,OAAO;AAChD;ACnEO,MAAM,8BAA8B,MAAM;IACtC,OAAO,wBAAA;AAClB;AAQO,MAAM,wBAAwB,MAAM;IAChC,OAAO,kBAAA;IACP,OAAA;IACT,YAAY,OAAA,EAAiB,MAAA,EAAiB,UAAwB,CAAA,CAAA,CAAI;QACxE,KAAA,CAAM,SAAS,OAAO,GACtB,IAAA,CAAK,MAAA,GAAS;IAAA;AAElB;AAMO,MAAM,qBAAqB,MAAM;IAC7B,OAAO,eAAA;IACP,KAAA;IACT,YAAY,OAAA,EAAiB,IAAA,CAAe;QACpC,KAAA,CAAA,OAAO,GACb,IAAA,CAAK,IAAA,GAAO;IAAA;AAEhB;AAMO,MAAM,qBAAqB,MAAM;IAC7B,OAAO,eAAA;IACP,KAAA;IACT,YAAY,OAAA,EAAiB,IAAA,EAAe,UAAwB,CAAA,CAAA,CAAI;QACtE,KAAA,CAAM,SAAS,OAAO,GACtB,IAAA,CAAK,IAAA,GAAO;IAAA;AAEhB;AAMO,MAAM,0BAA0B,MAAM;IAClC,OAAO,oBAAA;AAClB;AAYA,MAAM,kBAAkB;IAAC;IAAgB,YAAY;CAAA;AA+BrC,SAAA,mBACd,eAAA,EACA,MAAA,EACA;IACA,wJAAO,QAAA,EAAM,MAAM;QACjB,MAAM,KAAK,gBAAgB;QAC3B,OAAO,gKAAA,EAAa,EAAE,IAAI,sJAAK,KAAA,EAAG,EAAE;IAAA,CACrC,EAAE,IAAA,kJAAK,WAAA,EAAS,CAAC,KAAO,sBAAsB,IAAI,MAAM,CAAC,CAAC;AAG7D;AASA,SAAS,sBACP,EAAA,EACA,MAAA,EACA;IACO,OAAA,iJAAI,aAAA,CAA4C,CAAC,aAAa;QAC7D,MAAA,WAAY,OAAoB,QAAA,CAAS,MAAM,GAC/C,gBAAiB,OAAoB,QAAA,CAAS,WAAW;QAI/D,SAAS,QAAQ,GAAA,EAA2B;YAE1C,IAAI,UAAU,KAAK;gBACjB,MAAM,CAAC,YAAY,KAAK,CAAA,GAAI,WAAW,GAAmB;gBACjD,SAAA,KAAA,CACP,aACI,IAAI,kBAAkB,6CAA6C;oBAAC,OAAO;gBAAA,CAAM,IACjF,IAAI,aAAA,CAAc,OAAO,IAAA,EAA2B,OAAA,EAAS,KAAK;gBAExE;YAAA;YAOE,GAAG,UAAA,KAAe,GAAG,MAAA,GAEvB,SAAS,KAAA,CAAM,IAAI,sBAAsB,+BAA+B,CAAC,IAChE,iBACT,SAAS,IAAA,CAAK;gBAAC,MAAM;YAAA,CAA6B;QAAA;QAItD,SAAS,SAAS;YAEhB,SAAS,IAAA,CAAK;gBAAC,MAAM;YAAA,CAAwB;QAAA;QAG/C,SAAS,UAAU,OAAA,EAAuB;YACxC,MAAM,CAAC,YAAY,KAAK,CAAA,GAAI,WAAW,OAAO;YAC9C,IAAI,YAAY;gBACL,SAAA,KAAA,CACP,IAAI,kBAAkB,uCAAuC;oBAAC,OAAO;gBAAW,CAAA;gBAElF;YAAA;YAEE,IAAA,QAAQ,IAAA,KAAS,gBAAgB;gBAI7B,MAAA,MAAM,IAAI,IAAI,GAAG,GAAG,EAAE,YAAA,CAAa,GAAA,CAAI,KAAK;gBACzC,SAAA,KAAA,CAAM,IAAI,aAAa,oBAAoB,OAAO,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC;gBAClF;YAAA;YAEE,IAAA,QAAQ,IAAA,KAAS,cAAc;gBAIxB,SAAA,KAAA,CACP,IAAI,gBACF,CAAA,4BAAA,EACG,MAAM,IAAA,EAA4B,UAAU,eAC/C,EAAA;gBAGJ;YAAA;YAEF,SAAS,IAAA,CAAK;gBACZ,MAAM,QAAQ,IAAA;gBACd,IAAI,QAAQ,WAAA;gBACZ,GAAI,MAAM,IAAA,GAAO;oBAAC,MAAM,MAAM,IAAA;gBAAA,IAAQ,CAAA,CAAA;YAAC,CACxC;QAAA;QAGA,GAAA,gBAAA,CAAiB,SAAS,OAAO,GAEhC,YACF,GAAG,gBAAA,CAAiB,QAAQ,MAAM;QAI9B,MAAA,gBAAgB,CAAC;eAAG,aAAA,GAAA,IAAI,IAAI,CAAC;mBAAG,iBAAiB;mBAAG,MAAM;aAAC,CAAC;SAAA,CAE/D,MAAA,CAAO,CAAC,OAAS,SAAS,WAAW,SAAS,UAAU,SAAS,WAAW;QAEjE,OAAA,cAAA,OAAA,CAAQ,CAAC,OAAiB,GAAG,gBAAA,CAAiB,MAAM,SAAS,CAAC,GAErE,MAAM;YACR,GAAA,mBAAA,CAAoB,SAAS,OAAO,GACnC,YACF,GAAG,mBAAA,CAAoB,QAAQ,MAAM,GAEvC,cAAc,OAAA,CAAQ,CAAC,OAAiB,GAAG,mBAAA,CAAoB,MAAM,SAAS,CAAC,GAC/E,GAAG,KAAA,CAAM;QACX;IAAA,CACD;AACH;AAEA,SAAS,WACP,OAAA,EACoE;IAChE,IAAA;QACI,MAAA,OAAO,OAAO,QAAQ,IAAA,IAAS,YAAY,KAAK,KAAA,CAAM,QAAQ,IAAI;QACjE,OAAA;YACL;YACA;gBACE,MAAM,QAAQ,IAAA;gBACd,IAAI,QAAQ,WAAA;gBACZ,GAAI,cAAc,IAAI,IAAI,CAAA,IAAK;oBAAC;gBAAI,CAAA;YAAA;SAExC;IAAA,EAAA,OACO,KAAK;QACL,OAAA;YAAC;YAAc,IAAI;SAAA;IAAA;AAE9B;AAEA,SAAS,oBAAoB,GAAA,EAAU,GAAA,EAAqB;IAC1D,MAAM,QAAQ,IAAI,KAAA;IAEb,OAAA,QAID,kBAAkB,KAAK,IAClB,sBAAsB,OAAO,GAAG,IAGrC,MAAM,WAAA,GACD,MAAM,WAAA,GAGR,OAAO,SAAU,WAAW,QAAQ,KAAK,SAAA,CAAU,OAAO,MAAM,CAAC,IAX/D,IAAI,OAAA,IAAW;AAY1B;AAEA,SAAS,cAAc,IAAA,EAAc;IACnC,IAAA,MAAW,KAAK,KACP,OAAA,CAAA;IAEF,OAAA,CAAA;AACT;ACrQO,SAAS,aAAa,GAAA,EAAiC;IAC5D,IAAI,OAAO,OAAQ,UACV,OAAA;QAAC,IAAI;IAAG;IAGb,IAAA,MAAM,OAAA,CAAQ,GAAG,GACnB,OAAO;QAAC,OAAO;QAAkB,QAAQ;YAAC,KAAK;QAAA;IAAI;IAGjD,IAAA,OAAO,OAAQ,YAAY,QAAQ,QAAQ,WAAW,OAAO,OAAO,IAAI,KAAA,IAAU,UAC7E,OAAA,YAAY,OAAO,OAAO,IAAI,MAAA,IAAW,YAAY,IAAI,MAAA,KAAW,OACvE;QAAC,OAAO,IAAI,KAAA;QAAO,QAAQ,IAAI,MAAA;IAAA,IAC/B;QAAC,OAAO,IAAI,KAAA;IAAK;IAGvB,MAAM,gBAAgB;QACpB;QACA;QACA;KAAA,CACA,IAAA,CAAK,CAAA;AAAA,CAAI;IAEX,MAAM,IAAI,MAAM,CAAA;;AAAA,EAA0C,aAAa,EAAE;AAC3E;ACFO,MAAM,UAAU;IACX,UAAA;IACA,WAAA;IACV,YAAY,SAAA,EAA2B,aAA8B,CAAA,CAAA,CAAI;QAClE,IAAA,CAAA,SAAA,GAAY,WACjB,IAAA,CAAK,UAAA,GAAa;IAAA;IAAA;;;;;GAAA,GASpB,IAAI,KAAA,EAA2B;QACtB,OAAA,IAAA,CAAK,OAAA,CAAQ,OAAO,KAAK;IAAA;IAAA;;;;;GAAA,GASlC,aAAa,KAAA,EAA2B;QAC/B,OAAA,IAAA,CAAK,OAAA,CAAQ,gBAAgB,KAAK;IAAA;IAAA;;;;;GAAA,GAS3C,eAAe,KAAA,EAA2B;QACxC,iLAAA,iBAAA,EAAe,kBAAkB,KAAK,GAC/B,IAAA,CAAK,OAAA,CAAQ,kBAAkB,KAAK;IAAA;IAAA;;;;;GAAA,GAS7C,MAAM,KAAA,EAAuB;QACvB,IAAA,CAAC,MAAM,OAAA,CAAQ,KAAK,GAChB,MAAA,IAAI,MAAM,qEAAqE;QAGlF,OAAA,IAAA,CAAA,UAAA,GAAa,OAAO,MAAA,CAAO,CAAC,GAAG,IAAA,CAAK,UAAA,EAAY;YAAC,OAAO;QAAK,CAAC,GAC5D,IAAA;IAAA;IAAA;;;;GAAA,GAQT,IAAI,KAAA,EAAsC;QACjC,OAAA,IAAA,CAAK,OAAA,CAAQ,OAAO,KAAK;IAAA;IAAA;;;;GAAA,GAQlC,IAAI,KAAA,EAAsC;QACjC,OAAA,IAAA,CAAK,OAAA,CAAQ,OAAO,KAAK;IAAA;IAAA;;;;;;GAAA,GAUlC,OAAO,EAAA,EAAoC,QAAA,EAAkB,KAAA,EAAoB;QAC/E,iLAAA,iBAAA,EAAe,IAAI,UAAU,KAAK,GAC3B,IAAA,CAAK,OAAA,CAAQ,UAAU;YAAC,CAAC,EAAE,CAAA,EAAG;YAAU;QAAA,CAAM;IAAA;IAAA;;;;;GAAA,GASvD,OAAO,QAAA,EAAkB,KAAA,EAAoB;QAC3C,OAAO,IAAA,CAAK,MAAA,CAAO,SAAS,GAAG,QAAQ,CAAA,IAAA,CAAA,EAAQ,KAAK;IAAA;IAAA;;;;;GAAA,GAStD,QAAQ,QAAA,EAAkB,KAAA,EAAoB;QAC5C,OAAO,IAAA,CAAK,MAAA,CAAO,UAAU,GAAG,QAAQ,CAAA,GAAA,CAAA,EAAO,KAAK;IAAA;IAAA;;;;;;;GAAA,GAWtD,OAAO,QAAA,EAAkB,KAAA,EAAe,WAAA,EAAsB,KAAA,EAAqB;QAMjF,MAAM,SAAS,OAAO,cAAgB,OAAe,gBAAgB,CAAA,GAC/D,aAAa,QAAQ,IAAI,QAAQ,IAAI,OACrC,WAAW,SAAS,CAAA,IAAK,KAAK,GAAA,CAAI,GAAG,QAAQ,WAAW,GACxD,WAAW,aAAa,KAAK,YAAY,IAAI,KAAK,UAClD,gBAAgB,GAAG,QAAQ,CAAA,CAAA,EAAI,UAAU,CAAA,CAAA,EAAI,QAAQ,CAAA,CAAA,CAAA;QAC3D,OAAO,IAAA,CAAK,MAAA,CAAO,WAAW,eAAe,SAAS,CAAA,CAAE;IAAA;IAAA;;;;GAAA,GAQ1D,aAAa,GAAA,EAAmB;QACzB,OAAA,IAAA,CAAA,UAAA,CAAW,YAAA,GAAe,KACxB,IAAA;IAAA;IAAA;;GAAA,GAMT,YAAoC;QAC3B,OAAA;YAAC,GAAG,aAAa,IAAA,CAAK,SAAS,CAAA;YAAG,GAAG,IAAA,CAAK,UAAA;QAAU;IAAA;IAAA;;GAAA,GAM7D,SAAiC;QAC/B,OAAO,IAAA,CAAK,SAAA,CAAU;IAAA;IAAA;;GAAA,GAMxB,QAAc;QACP,OAAA,IAAA,CAAA,UAAA,GAAa,CAAA,GACX,IAAA;IAAA;IAGC,QAAQ,EAAA,EAA2B,KAAA,EAAYC,SAAQ,CAAA,CAAA,EAAY;QAC5D,iLAAA,iBAAA,EAAA,IAAI,KAAK,GACxB,IAAA,CAAK,UAAA,GAAa,OAAO,MAAA,CAAO,CAAA,GAAI,IAAA,CAAK,UAAA,EAAY;YACnD,CAAC,EAAE,CAAA,EAAG,OAAO,MAAA,CAAO,CAAA,GAAKA,UAAS,IAAA,CAAK,UAAA,CAAW,EAAE,CAAA,IAAM,CAAA,GAAI,KAAK;QACpE,CAAA,GACM,IAAA;IAAA;IAGC,KAAK,EAAA,EAA2B,KAAA,EAAkB;QAC1D,OAAO,IAAA,CAAK,OAAA,CAAQ,IAAI,OAAO,CAAA,CAAK;IAAA;AAExC;AAGO,MAAM,wBAAwB,UAAU;KAC7C,MAAA,CAAA;IAEA,YACE,SAAA,EACA,UAAA,EACA,MAAA,CACA;QACA,KAAA,CAAM,WAAW,UAAU,GAC3B,IAAA,EAAK,MAAA,GAAU;IAAA;IAAA;;GAAA,GAMjB,QAAyB;QAChB,OAAA,IAAI,gBAAgB,IAAA,CAAK,SAAA,EAAW;YAAC,GAAG,IAAA,CAAK,UAAA;QAAA,GAAa,IAAA,EAAK,MAAO;IAAA;IAuC/E,OACE,OAAA,EAQA;QACA,IAAI,CAAC,IAAA,EAAK,MAAA,EACR,MAAM,IAAI,MACR;QAKJ,MAAM,cAAc,OAAO,IAAA,CAAK,SAAA,IAAc,UACxC,OAAO,OAAO,MAAA,CAAO;YAAC;YAAa,iBAAiB,CAAA;QAAA,GAAO,OAAO;QACjE,OAAA,IAAA,EAAK,MAAA,CAAQ,MAAA,CAAU;YAAC,OAAO,IAAA,CAAK,SAAA;QAAW,GAAU,IAAI;IAAA;AAExE;AAGO,MAAM,cAAc,UAAU;KACnC,MAAA,CAAA;IACA,YAAY,SAAA,EAA2B,UAAA,EAA8B,MAAA,CAAuB;QAC1F,KAAA,CAAM,WAAW,UAAU,GAC3B,IAAA,CAAK,OAAA,GAAU;IAAA;IAAA;;GAAA,GAMjB,QAAe;QACN,OAAA,IAAI,MAAM,IAAA,CAAK,SAAA,EAAW;YAAC,GAAG,IAAA,CAAK,UAAA;QAAA,GAAa,IAAA,EAAK,MAAO;IAAA;IAuCrE,OACE,OAAA,EAQA;QACA,IAAI,CAAC,IAAA,EAAK,MAAA,EACR,MAAM,IAAI,MACR;QAKJ,MAAM,cAAc,OAAO,IAAA,CAAK,SAAA,IAAc,UACxC,OAAO,OAAO,MAAA,CAAO;YAAC;YAAa,iBAAiB,CAAA;QAAA,GAAO,OAAO;QACjE,OAAA,IAAA,EAAK,MAAA,CAAQ,MAAA,CAAU;YAAC,OAAO,IAAA,CAAK,SAAA;QAAW,GAAU,IAAI;IAAA;AAExE;AC7TA,MAAM,uBAAuB;IAAC,iBAAiB,CAAA;AAAK;AAG7C,MAAM,gBAAgB;IACjB,WAAA;IACA,MAAA;IACV,YAAY,aAAyB,CAAC,CAAA,EAAG,aAAA,CAAwB;QAC1D,IAAA,CAAA,UAAA,GAAa,YAClB,IAAA,CAAK,KAAA,GAAQ;IAAA;IAAA;;;;;GAAA,GAQf,OAA4D,GAAA,EAAkC;QACjF,OAAAC,2LAAAA,EAAe,UAAU,GAAG,GAChC,IAAA,CAAK,IAAA,CAAK;YAAC,QAAQ;QAAA,CAAI;IAAA;IAAA;;;;;GAAA,GAShC,kBACE,GAAA,EACM;QACN,MAAM,KAAK;QACX,WAAAA,uLAAAA,EAA0B,IAAI,GAAG,6KACjCC,oBAAAA,EAA6B,IAAI,GAAG,GAC7B,IAAA,CAAK,IAAA,CAAK;YAAC,CAAC,EAAE,CAAA,EAAG;QAAA,CAAI;IAAA;IAAA;;;;;GAAA,GAS9B,gBACE,GAAA,EACM;QACN,MAAM,KAAK;QACX,iLAAAD,iBAAAA,EAA0B,IAAI,GAAG,6KACjCC,oBAAAA,EAA6B,IAAI,GAAG,GAC7B,IAAA,CAAK,IAAA,CAAK;YAAC,CAAC,EAAE,CAAA,EAAG;QAAA,CAAI;IAAA;IAAA;;;;;GAAA,GAS9B,OAAO,UAAA,EAA0B;QAC/B,iLAAAC,qBAAAA,EAA8B,UAAU,UAAU,GAC3C,IAAA,CAAK,IAAA,CAAK;YAAC,QAAQ;gBAAC,IAAI;YAAU;QAAA,CAAE;IAAA;IAa7C,cAAc,EAAA,EAAwC;QACpD,OAAK,KAAA,CAIL,IAAA,CAAK,KAAA,GAAQ,IACN,IAAA,IAJE,IAAA,CAAK,KAAA;IAAA;IAAA;;GAAA,GAUhB,YAAwB;QACf,OAAA,CAAC;eAAG,IAAA,CAAK,UAAU;SAAA;IAAA;IAAA;;GAAA,GAM5B,SAAqB;QACnB,OAAO,IAAA,CAAK,SAAA,CAAU;IAAA;IAAA;;GAAA,GAMxB,QAAc;QACP,OAAA,IAAA,CAAA,UAAA,GAAa,CAAA,CAAA,EACX,IAAA;IAAA;IAGC,KAAK,GAAA,EAAqB;QAC7B,OAAA,IAAA,CAAA,UAAA,CAAW,IAAA,CAAK,GAAG,GACjB,IAAA;IAAA;AAEX;AAGO,MAAM,oBAAoB,gBAAgB;KAC/C,MAAA,CAAA;IACA,YAAY,UAAA,EAAyB,MAAA,EAAuB,aAAA,CAAwB;QAClF,KAAA,CAAM,YAAY,aAAa,GAC/B,IAAA,EAAK,MAAA,GAAU;IAAA;IAAA;;GAAA,GAMjB,QAAqB;QACZ,OAAA,IAAI,YAAY,CAAC;eAAG,IAAA,CAAK,UAAU;SAAA,EAAG,IAAA,EAAK,MAAA,EAAS,IAAA,CAAK,KAAK;IAAA;IAqCvE,OACE,OAAA,EAQA;QACA,IAAI,CAAC,IAAA,CAAK,OAAA,EACR,MAAM,IAAI,MACR;QAKJ,OAAO,IAAA,CAAK,OAAA,CAAQ,MAAA,CAClB,IAAA,CAAK,SAAA,CAAU,GACf,OAAO,MAAA,CAAO;YAAC,eAAe,IAAA,CAAK,KAAA;QAAA,GAAQ,sBAAsB,WAAW,CAAE,CAAA;IAChF;IAyBF,MACE,iBAAA,EACA,QAAA,EACM;QACN,MAAM,YAAY,OAAO,YAAa,YAChC,UAAU,OAAO,qBAAsB,YAAY,6BAA6B,OAChF,sBACJ,OAAO,qBAAsB,YAAA,CAC5B,WAAW,qBAAqB,QAAQ,iBAAA;QAGvC,IAAA,SACF,OAAO,IAAA,CAAK,IAAA,CAAK;YAAC,OAAO,kBAAkB,SAAA,CAAA;QAAA,CAAY;QAIzD,IAAI,WAAW;YACP,MAAA,QAAQ,SAAS,IAAI,MAAM,mBAAmB,CAAA,GAAI,IAAA,EAAK,MAAO,CAAC;YACrE,IAAI,CAAA,CAAE,iBAAiB,KAAA,GACf,MAAA,IAAI,MAAM,oDAAoD;YAGtE,OAAO,IAAA,CAAK,IAAA,CAAK;gBAAC,OAAO,MAAM,SAAA,CAAA;YAAA,CAAY;QAAA;QAS7C,IAAI,qBAAqB;YACjB,MAAA,QAAQ,IAAI,MAAM,mBAAmB,YAAY,CAAC,GAAG,IAAA,EAAK,MAAO;YACvE,OAAO,IAAA,CAAK,IAAA,CAAK;gBAAC,OAAO,MAAM,SAAA,CAAA;YAAA,CAAY;QAAA;QAGtC,OAAA,IAAA,CAAK,IAAA,CAAK;YAAC,OAAO;gBAAC,IAAI;gBAAmB,GAAG,QAAA;YAAQ;QAAA,CAAE;IAAA;AAElE;AAGO,MAAM,8BAA8B,gBAAgB;KACzD,MAAA,CAAA;IACA,YAAY,UAAA,EAAyB,MAAA,EAAiC,aAAA,CAAwB;QAC5F,KAAA,CAAM,YAAY,aAAa,GAC/B,IAAA,EAAK,MAAA,GAAU;IAAA;IAAA;;GAAA,GAMjB,QAA+B;QACtB,OAAA,IAAI,sBAAsB,CAAC;eAAG,IAAA,CAAK,UAAU;SAAA,EAAG,IAAA,EAAK,MAAA,EAAS,IAAA,CAAK,KAAK;IAAA;IAqCjF,OACE,OAAA,EAQA;QACA,IAAI,CAAC,IAAA,CAAK,OAAA,EACR,MAAM,IAAI,MACR;QAKJ,OAAO,IAAA,EAAK,MAAA,CAAQ,MAAA,CAClB,IAAA,CAAK,SAAA,CAAU,GACf,OAAO,MAAA,CAAO;YAAC,eAAe,IAAA,CAAK,KAAA;QAAA,GAAQ,sBAAsB,WAAW,CAAE,CAAA;IAChF;IAkBF,MACE,iBAAA,EACA,QAAA,EACM;QACA,MAAA,YAAY,OAAO,YAAa;QAEpC,IAAA,OAAO,qBAAsB,YAAY,6BAA6B,iBAItE,OAAO,IAAA,CAAK,IAAA,CAAK;YAAC,OAAO,kBAAkB,SAAA,CAAA;QAAA,CAAY;QAIzD,IAAI,WAAW;YACP,MAAA,QAAQ,SAAS,IAAI,gBAAgB,mBAAmB,CAAA,GAAI,IAAA,EAAK,MAAO,CAAC;YAC/E,IAAI,CAAA,CAAE,iBAAiB,eAAA,GACf,MAAA,IAAI,MAAM,oDAAoD;YAGtE,OAAO,IAAA,CAAK,IAAA,CAAK;gBAAC,OAAO,MAAM,SAAA,CAAA;YAAA,CAAY;QAAA;QAGtC,OAAA,IAAA,CAAK,IAAA,CAAK;YAAC,OAAO;gBAAC,IAAI;gBAAmB,GAAG,QAAA;YAAQ;QAAA,CAAE;IAAA;AAElE;AC1XA,MAAM,gBAAgB;AAEf,SAAS,eAAe,MAAA,EAAa,YAAiB,CAAA,CAAA,EAAiC;IAC5F,MAAMC,WAAe,CAAC;IAElB,OAAO,OAAA,IACT,OAAO,MAAA,CAAOA,UAAS,OAAO,OAAO;IAGjC,MAAA,QAAQ,UAAU,KAAA,IAAS,OAAO,KAAA;IACpC,SAAA,CACFA,SAAQ,aAAA,GAAgB,CAAA,OAAA,EAAU,KAAK,EAAA,GAGrC,CAAC,UAAU,YAAA,IAAgB,CAAC,OAAO,kBAAA,IAAsB,OAAO,SAAA,IAAA,CAClEA,QAAAA,CAAQ,aAAa,CAAA,GAAI,OAAO,SAAA;IAGlC,MAAM,kBAAkB,CACtB,CAAA,CAAA,OAAO,UAAU,eAAA,GAAoB,MACjC,OAAO,eAAA,GACP,UAAU,eAAA,GAGV,UAAU,OAAO,UAAU,OAAA,GAAY,MAAc,OAAO,OAAA,GAAU,UAAU,OAAA;IACtF,OAAO,OAAO,MAAA,CAAO,CAAC,GAAG,WAAW;QAClC,SAAS,OAAO,MAAA,CAAO,CAAA,GAAIA,UAAS,UAAU,OAAA,IAAW,CAAA,CAAE;QAC3D,SAAS,OAAO,UAAY,MAAc,IAAI,KAAK,MAAO;QAC1D,OAAO,UAAU,KAAA,IAAS,OAAO,KAAA;QACjC,MAAM,CAAA;QACN;QACA,OACE,OAAO,UAAU,KAAA,IAAU,YAAY,OAAO,OAAO,KAAA,IAAU,WAC3D;YAAC,GAAG,OAAO,KAAA;YAAO,GAAG,UAAU,KAAA;QAAA,IAC/B,UAAU,KAAA,IAAS,OAAO,KAAA;IAAA,CACjC;AACH;ACtCO,MAAM,oBAAoB,CAAC,EAChC,KAAA,EACA,SAAS,CAAC,CAAA,EACV,UAAU,CAAA,CAAA,EACZ,KAIM;IACE,MAAA,eAAe,IAAI,gBAAA,GAEnB,EAAC,GAAA,EAAK,gBAAA,EAAkB,WAAA,EAAa,GAAG,KAAA,CAAA,GAAQ;IAElD,OAAK,aAAa,MAAA,CAAO,OAAO,GAAG,GACvC,aAAa,MAAA,CAAO,SAAS,KAAK;IAGlC,KAAA,MAAW,CAAC,KAAK,KAAK,CAAA,IAAK,OAAO,OAAA,CAAQ,MAAM,EAC1C,UAAU,KAAA,KAAW,aAAa,MAAA,CAAO,CAAA,CAAA,EAAI,GAAG,EAAA,EAAI,KAAK,SAAA,CAAU,KAAK,CAAC;IAG/E,KAAA,MAAW,CAAC,KAAK,KAAK,CAAA,IAAK,OAAO,OAAA,CAAQ,IAAI,EAExC,SAAO,aAAa,MAAA,CAAO,KAAK,GAAG,KAAK,EAAE;IAIhD,OAAI,gBAAgB,CAAA,KAAO,aAAa,MAAA,CAAO,eAAe,OAAO,GAGjE,qBAAqB,CAAA,KAAO,aAAa,MAAA,CAAO,oBAAoB,OAAO,GAExE,CAAA,CAAA,EAAI,YAAY,EAAA;AACzB,GCaM,gBAAgB,CAAC,OAAY,WAE1B,UAAU,CAAA,IAAQ,KAAA,IADX,OAAO,QAAU,MAAc,WAAW,OAIpD,mBAAmB,CAAC,UAA+B,CAAA,CAAA,GAAA,CAChD;QACL,QAAQ,QAAQ,MAAA;QAChB,WAAW,CAAA;QACX,iBAAiB,cAAc,QAAQ,eAAA,EAAiB,CAAA,CAAI;QAC5D,YAAY,QAAQ,UAAA,IAAc;QAClC,uBAAuB,QAAQ,qBAAA;QAC/B,qCAAqC,QAAQ,mCAAA;IAC/C,CAAA,GAGI,aAAa,CAAC,QAAe,MAAM,IAAA,KAAS,YAC5C,UAAU,CAAC,QAAe,MAAM,IAAA,EAEhC,UAAU,CAAC,MAAa,OAC5B,KAAK,MAAA,CAAO,CAAC,SAAS,MAAA,CACpB,OAAA,CAAQ,KAAK,GAAG,CAAC,CAAA,GAAI,KACd,OAAA,GACC,aAAA,GAAA,OAAA,MAAA,CAAO,IAAI,CAAC,GAElB,oBAAoB;AAGV,SAAA,OACd,MAAA,EACA,WAAA,EACA,MAAA,EACA,KAAA,EACA,UAAa,CAAA,CAAA,EACb,UAAwB,CAAA,CAAA,EACa;IAC/B,MAAA,QACJ,WAAW,UACP;QACE,GAAI,UAAU,CAAC,CAAA;QACf,GAAI,OAAO,QAAQ,KAAA,IAAU,YAAY;YAAC,SAAS,QAAQ,KAAA;QAAK,IAAI,QAAQ,KAAA,IAAS,CAAA,CAAA;IACvF,IACA,QACA,SAAS,MAAM,OAAA,iLAAU,aAAA,EAAW,OAAO,IAAI,SAC/C,cACJ,QAAQ,cAAA,KAAmB,CAAA,IAAQ,CAAC,MAAa,MAAM,CAAC,MAAa,IAAI,MAAA,EAErE,EAAC,KAAA,EAAO,IAAA,EAAM,GAAG,MAAA,GAAQ;QAAA,8EAAA;QAAA,2FAAA;QAG7B,gBAAgB,OAAO,QAAQ,MAAA,GAAW;QAAA,8EAAA;QAE1C,iBAAiB,MAAM,OAAA,GAAU,yBAAyB,QAAQ,eAAA;QAClE,GAAG,OAAA;QAAA,0EAAA;QAAA,+FAAA;QAGH,aAAa,QAAQ,cAAA,KAAmB,CAAA,KAAS,QAAQ,WAAA,KAAgB,CAAA;IAC3E,GACM,UACJ,OAAO,QAAU,OAAe,OAAO,OAAS,MAC5C;QAAC,GAAG,IAAA;QAAM,OAAO;YAAC;YAAO;QAAK;IAAA,IAC9B,MAEA,WAAW,aAAa,QAAQ,aAAa,SAAS;QAAC;QAAO;IAAM,GAAG,OAAO;IAC7E,OAAA,MAAM,OAAA,GACT,SAAS,IAAA,+JACP,oBAAA,mJACE,OAAA,EACE,OAAO,sCAA+B,0HAAE,IAAA,CAAA,SAAA,CAAA,EAAA;QAAA,OAAA,EAAA,sBAAA;IAAA,CAAA,EAAA,IAAA,CACtC,CAAC,EAAC,oBAAA,CAAA,CAAA,GAA0B,uLAIlC,MAAA,EACE,CAAC,CAAC,KAAK,oBAAoB,CAAA,KAGrB;QACJ,MAAM,SAAS,qBAAqB,IAAI,MAAA,EAAQ,IAAI,eAAA,EAAiB,KAAK;QAC1E,OAAO,YAAY;YAAC,GAAG,GAAA;YAAK;QAAA,CAAO;IAAA,MAIzC,SAAS,IAAA,+JAAK,MAAA,EAAI,WAAW,CAAC;AACpC;AAGO,SAAS,aACd,MAAA,EACA,WAAA,EACA,EAAA,EACA,OAAiE,CAAA,CAAA,EACtB;IAyB3C,MAAM,QAAA,CAxBW,MAAM;QACrB,IAAI,CAAC,KAAK,SAAA,EACD,OAAA;QAGH,MAAA,+LAAY,mBAAA,EAAiB,EAAE;QACrC,IAAI,CAAC,WAAW;YACd,uLAAI,YAAA,EAAU,EAAE,GACd,MAAM,IAAI,MACR,CAAA,mBAAA,EAAsB,EAAE,CAAA,sDAAA,EAAyD,KAAK,SAAS,CAAA,EAAA,CAAA;YAI5F,OAAA,kMAAA,EAAa,IAAI,KAAK,SAAS;QAAA;QAGxC,IAAI,cAAc,KAAK,SAAA,EACrB,MAAM,IAAI,MACR,CAAA,mBAAA,EAAsB,EAAE,CAAA,8BAAA,EAAiC,SAAS,CAAA,0EAAA,EAA6E,KAAK,SAAS,CAAA,GAAA,CAAA;QAI1J,OAAA;IACT,CAAA,EAAA,GAGM,UAAU;QACd,KAAK,YAAY,QAAQ,OAAO,KAAK;QACrC,MAAM,CAAA;QACN,KAAK,KAAK,GAAA;QACV,QAAQ,KAAK,MAAA;IACf;IACA,OAAO,mBAAkD,QAAQ,aAAa,OAAO,EAAE,IAAA,+JACrF,SAAA,EAAO,UAAU,gKACjB,OAAA,EAAI,CAAC,QAAU,MAAM,IAAA,CAAK,SAAA,IAAa,MAAM,IAAA,CAAK,SAAA,CAAU,CAAC,CAAC;AAElE;AAGO,SAAS,cACd,MAAA,EACA,WAAA,EACA,GAAA,EACA,OAA6C,CAAA,CAAA,EACH;IAC1C,MAAM,UAAU;QACd,KAAK,YAAY,QAAQ,OAAO,IAAI,IAAA,CAAK,GAAG,CAAC;QAC7C,MAAM,CAAA;QACN,KAAK,KAAK,GAAA;QACV,QAAQ,KAAK,MAAA;IACf;IACA,OAAO,mBAAiD,QAAQ,aAAa,OAAO,EAAE,IAAA,+JACpF,SAAA,EAAO,UAAU,iKACjB,MAAA,EAAI,CAAC,UAAe;QACZ,MAAA,UAAU,QAAQ,MAAM,IAAA,CAAK,SAAA,IAAa,CAAA,CAAA,EAAI,CAAC,MAAa,IAAI,GAAG;QACzE,OAAO,IAAI,GAAA,CAAI,CAAC,KAAO,OAAA,CAAQ,EAAE,CAAA,IAAK,IAAI;IAC3C,CAAA;AAEL;AAGO,SAAS,qBACd,MAAA,EACA,WAAA,EACA,SAAA,EACA,OAA4B,CAAA,CAAA,EACuB;IAC5C,OAAA,aACL,QACA,aACA,SACA;QACE,OAAO;QACP,QAAQ;YACN;QAAA;IAEJ,GACA;AAEJ;AAGO,SAAS,mBACd,MAAA,EACA,WAAA,EACA,GAAA,EACA,OAAA,EAQA;IACW,iLAAAF,oBAAAA,EAAkB,qBAAqB,GAAG,GAC9C,QAAW,QAAQ,aAAa,KAAK,qBAAqB,OAAO;AAC1E;AAGO,SAAS,iBACd,MAAA,EACA,WAAA,EACA,GAAA,EACA,OAAA,EAQA;IACW,iLAAAA,oBAAAA,EAAkB,mBAAmB,GAAG,GAC5C,QAAW,QAAQ,aAAa,KAAK,mBAAmB,OAAO;AACxE;AAGO,SAAS,eACd,MAAA,EACA,WAAA,EACA,GAAA,EACA,WAAA,EACA,OAAA,EACgC;IACrB,iLAAAA,oBAAAA,EAAkB,iBAAiB,GAAG,OACjDG,4LAAAA,EAA+B,iBAAiB,GAAG,GAQ5C,QAAQ,QAAQ,aAN0B;QAC/C,YAAY;QACZ;QACA,UAAU;IAAA,GAG6C,OAAO;AAClE;AAGO,SAAS,QACd,MAAA,EACA,WAAA,EACA,SAAA,EACA,OAAA,EAQA;IACO,OAAA,aACL,QACA,aACA,UACA;QAAC,WAAW;YAAC;gBAAC,QAAQ,aAAa,SAAS;YAAC,CAAC;SAAA;IAAC,GAC/C;AAEJ;AAGO,SAAS,gBACd,MAAA,EACA,WAAA,EACA,SAAA,EACA,QAAiB,CAAA,CAAA,EACjB,OAAA,EACgC;IAOzB,OAAA,QAAQ,QAAQ,aAN4B;QACjD,YAAY;QACZ;QACA;IAAA,GAGwD,OAAO;AACnE;AAGO,SAAS,gBACd,MAAA,EACA,WAAA,EACA,GAAA,EACA,OAAA,EACgC;IACrB,gLAAAH,qBAAAA,EAAkB,kBAAkB,GAAG,6KAClDG,sBAAAA,EAA+B,kBAAkB,GAAG,GAO7C,QAAQ,QAAQ,aAL4B;QACjD,YAAY;QACZ,UAAU;IAAA,GAG8C,OAAO;AACnE;AAGO,SAAS,kBACd,MAAA,EACA,WAAA,EACA,SAAA,EACA,WAAA,EACA,OAAA,EACgC;IAOzB,OAAA,QAAQ,QAAQ,aANgC;QACrD,YAAY;QACZ;QACA;IAAA,GAG0D,OAAO;AACrE;AAGO,SAAS,QACd,MAAA,EACA,WAAA,EACA,SAAA,EACA,OAAA,EAQA;IACI,IAAA;IACA,qBAAqB,SAAS,qBAAqB,kBACrD,MAAM;QAAC,OAAO,UAAU,SAAA;IAAA,IACf,qBAAqB,eAAe,qBAAqB,wBAClE,MAAM,UAAU,SAAA,CAAA,IAEhB,MAAM;IAGR,MAAM,OAAO,MAAM,OAAA,CAAQ,GAAG,IAAI,MAAM;QAAC,GAAG;KAAA,EACtC,gBAAiB,WAAW,QAAQ,aAAA,IAAkB,KAAA;IACrD,OAAA,aAAa,QAAQ,aAAa,UAAU;QAAC,WAAW;QAAM;IAAa,GAAG,OAAO;AAC9F;AAKO,SAAS,QACd,MAAA,EACA,WAAA,EACA,OAAA,EACA,OAAA,EACuD;IACjD,MAAA,OAAO,MAAM,OAAA,CAAQ,OAAO,IAAI,UAAU;QAAC,OAAO;KAAA,EAClD,gBAAiB,WAAW,QAAQ,aAAA,IAAkB,KAAA,GACtD,sCACH,WAAW,QAAQ,mCAAA,IAAwC,KAAA,GACxD,SAAU,WAAW,QAAQ,MAAA,IAAW,KAAA;IAEvC,OAAA,aACL,QACA,aACA,WACA;QAAC,SAAS;QAAM;QAAe;QAAqC;IAAM,GAC1E;AAEJ;AAKO,SAAS,aACd,MAAA,EACA,WAAA,EACA,QAAA,EACA,IAAA,EACA,UAAe,CAAA,CAAA,EACV;IACC,MAAA,aAAa,aAAa,UAC1B,WAAW,aAAa,WACxBN,WAAU,aAAa,SAIvB,WAAW,cAAc,WAAW,KAAK,kBAAkB,IAAI,GAC/D,SAAS,CAAC,cAAc,CAAC,YAAY,SAAS,MAAA,GAAS,mBACvD,cAAc,SAAS,WAAW,IAClC,cAAc,QAAQ,WAAA,EACtB,EAAC,OAAA,EAAS,KAAA,EAAO,GAAA,EAAK,SAAAK,QAAAA,EAAS,WAAA,EAAa,eAAA,EAAiB,SAAA,EAAA,GAAa,SAE1E,MAAM,YAAY,QAAQ,UAAU,WAAW,GAE/C,aAAa;QACjB,QAAQ,SAAS,QAAQ;QACzB;QACA,MAAM,CAAA;QACN,MAAM,SAAS,KAAA,IAAY;QAC3B,OAAO,cAAc,iBAAiB,OAAO;QAC7C;QACA,SAAAA;QACA;QACA;QACA;QACA,aAAa,QAAQ,WAAA;QACrB,iBAAiB,QAAQ,eAAA;QACzB,iBAAiB,MAAM,OAAA,CAAQ,eAAe,IAAI,eAAA,CAAgB,CAAC,CAAA,GAAI;QACvE;QACA,WAAWL;QACX,QAAQ,QAAQ,MAAA;QAChB,OAAO,QAAQ,KAAA;QACf,gBAAgB,QAAQ,cAAA;QACxB,QAAQ,QAAQ,MAAA;IAClB;IAEA,OAAO,mBAAmB,QAAQ,aAAa,UAAU,EAAE,IAAA,CACzD,uKAAA,EAAO,UAAU,iKACjB,MAAA,EAAI,OAAO,iKACX,MAAA,EAAI,CAAC,QAAQ;QACX,IAAI,CAAC,YACI,OAAA;QAIH,MAAA,UAAU,IAAI,OAAA,IAAW,CAAC,CAAA;QAChC,IAAI,QAAQ,eAAA,EACV,OAAO,cACH,OAAA,CAAQ,CAAC,CAAA,IAAK,OAAA,CAAQ,CAAC,CAAA,CAAE,QAAA,GACzB,QAAQ,GAAA,CAAI,CAAC,MAAa,IAAI,QAAQ;QAI5C,MAAM,MAAM,cAAc,eAAe,eACnC,MAAM,cAAc,OAAA,CAAQ,CAAC,CAAA,IAAK,OAAA,CAAQ,CAAC,CAAA,CAAE,EAAA,GAAK,QAAQ,GAAA,CAAI,CAAC,MAAa,IAAI,EAAE;QACjF,OAAA;YACL,eAAe,IAAI,aAAA;YACnB;YACA,CAAC,GAAG,CAAA,EAAG;QACT;IACD,CAAA;AAEL;AAKO,SAAS,QACd,MAAA,EACA,WAAA,EACA,GAAA,EACA,EAAA,EACA,UAAe,CAAA,CAAA,EAGf;IACA,MAAM,WAAW;QAAC,CAAC,EAAE,CAAA,EAAG;IAAA,GAClB,OAAO,OAAO,MAAA,CAAO;QAAC,aAAa,CAAA;QAAM,iBAAiB,CAAA;IAAA,GAAO,OAAO;IACvE,OAAA,aAAa,QAAQ,aAAa,UAAU;QAAC,WAAW;YAAC,QAAQ;SAAA;IAAC,GAAG,IAAI;AAClF;AAEA,MAAM,gBAAgB,CAAC,SACpB,OAAO,MAAA,CAAA,EAAS,OAAA,KAAY,KAAA,KAAa,OAAO,MAAA,GAAS,SAAA,KAAc,KAAA,KACxE,OAAO,MAAA,CAAO,CAAA,CAAE,wBAAwB,CAAA,KAAM,KAAA,GAE1C,UAAU,CAAC,QAAgB,MAC/B,cAAc,MAAM,KAAK,IAAI,UAAA,CAAW,YAAY,QAAQ,OAAO,CAAC,GAEhE,WAAW,CAAC,QAAgB,MAChC,cAAc,MAAM,KAAK,IAAI,UAAA,CAAW,YAAY,QAAQ,QAAQ,CAAC,GAEjE,QAAQ,CAAC,QAAgB,MAC7B,cAAc,MAAM,KAAK,IAAI,UAAA,CAAW,YAAY,QAAQ,OAAO,EAAE,CAAC,GAElE,aAAa,CAAC,QAAgB,MAClC,cAAc,MAAM,KAAK,IAAI,UAAA,CAAW,YAAY,QAAQ,QAAQ,CAAC,GAEjE,YAAY,CAAC,QAAgB,MACjC,cAAc,MAAM,KAAK,IAAI,UAAA,CAAW,YAAY,QAAQ,WAAW,EAAE,CAAC,GAEtE,SAAS,CAAC,QAAgB,MAC9B,IAAI,UAAA,CAAW,QAAQ,KACvB,QAAQ,QAAQ,GAAG,KACnB,SAAS,QAAQ,GAAG,KACpB,MAAM,QAAQ,GAAG,KACjB,WAAW,QAAQ,GAAG,KACtB,UAAU,QAAQ,GAAG;AAKP,SAAA,mBACd,MAAA,EACA,WAAA,EACA,OAAA,EACiC;IACjC,MAAM,MAAM,QAAQ,GAAA,IAAQ,QAAQ,GAAA,EAC9B,SAAS,OAAO,MAAA,CAAA,GAIhB,YACJ,OAAO,QAAQ,SAAA,GAAc,MACzB;QAAC;QAAO,MAAM;KAAA,CAAE,OAAA,CAAQ,QAAQ,MAAA,IAAU,KAAK,KAAK,KAAK,OAAO,QAAQ,GAAG,IAC3E,QAAQ,SAAA;IAEd,IAAI,SAAA,CAAU,QAAQ,MAAA,IAAU,OAAO,MAAA,KAAW;IAElD,MAAM,MACJ,QAAQ,GAAA,IAAO,OAAO,gBAAA,GAClB;QAAC,OAAO,gBAAA;QAAkB,QAAQ,GAAG;KAAA,CAAE,IAAA,CAAK,GAAG,IAC/C,QAAQ,GAAA,IAAO,OAAO,gBAAA;IAO5B,IALI,OAAO,QAAQ,GAAA,KAAQ,QAAA,CACzB,QAAQ,KAAA,GAAQ;QAAC,+KAAKO,aAAAA,EAAoB,GAAG;QAAG,GAAG,QAAQ,KAAA;IAAA,CAAA,GAIzD;QAAC;QAAO;QAAQ,MAAM;KAAA,CAAE,OAAA,CAAQ,QAAQ,MAAA,IAAU,KAAK,KAAK,KAAK,QAAQ,QAAQ,GAAG,GAAG;QACnF,MAAA,kBAAkB,QAAQ,eAAA,IAAmB,OAAO,eAAA;QACtD,oBAAoB,KAAA,KAAa,oBAAoB,CAAA,KAAA,CACvD,QAAQ,KAAA,GAAQ;YAAC;YAAiB,GAAG,QAAQ,KAAA;QAAA,CAAA;QAEzC,MAAA,oBAAoB,QAAQ,WAAA,IAAe,OAAO,WAAA;QACpD,OAAO,oBAAsB,OAAA,CAC3B,sBAAsB,6LACxB,uCAAA,8KAEF,0BAAA,EAAuB,iBAAiB,GACxC,QAAQ,KAAA,GAAQ;YACd,aAAa,MAAM,OAAA,CAAQ,iBAAiB,IACxC,kBAAkB,IAAA,CAAK,GAAG,IAC1B;YACJ,GAAG,QAAQ,KAAA;QAAA,GAAA,CAIT,MAAM,OAAA,CAAQ,iBAAiB,KAAK,kBAAkB,MAAA,GAAS,KAAA,qEAAA;QAE/D,sBAAsB,mBACtB,sBAAsB,QAAA,KACxB,UAAA,CAEA,SAAS,CAAA,6KACT,+BAAA,CAIA,EAAA,CAAA,GAAA,QAAQ,eAAA,IAAA,CACV,QAAQ,KAAA,GAAQ;YAAC,GAAG,QAAQ,KAAA;YAAO,iBAAiB,QAAQ,eAAA;QAAA,CAAA,GAG1D,QAAQ,WAAA,KAAgB,CAAA,KAAA,CAC1B,QAAQ,KAAA,GAAQ;YAAC,aAAa;YAAS,GAAG,QAAQ,KAAA;QAAA,CAAA,GAGhD,UAAU,QAAQ,SAAA,IAAa,aAAA,CACjC,QAAQ,KAAA,GAAQ;YAAC,WAAW;YAAW,GAAG,QAAQ,KAAA;QAAA,CAAA;IAAK;IAI3D,MAAM,aAAa,eACjB,QACA,OAAO,MAAA,CAAO,CAAC,GAAG,SAAS;QACzB,KAAK,QAAQ,QAAQ,KAAK,MAAM;IACjC,CAAA,IAGG,UAAU,iJAAI,aAAA,CAAgC,CAAC,aACnD,YAAY,YAAY,OAAO,SAAU,EAAE,SAAA,CAAU,UAAU;IAG1D,OAAA,QAAQ,MAAA,GAAS,QAAQ,IAAA,CAAK,iBAAiB,QAAQ,MAAM,CAAC,IAAI;AAC3E;AAKgB,SAAA,SAAY,MAAA,EAAgB,WAAA,EAA0B,OAAA,EAA6B;IAMjG,OALmB,mBAAsB,QAAQ,aAAa,OAAO,EAAE,IAAA,+JACrE,SAAA,EAAO,CAAC,QAAe,MAAM,IAAA,KAAS,UAAU,IAChD,mKAAA,EAAI,CAAC,QAAe,MAAM,IAAI;AAIlC;AAKgB,SAAA,YAAY,MAAA,EAAgB,SAAA,EAAmB,IAAA,EAAuB;IAC9E,MAAA,SAAS,OAAO,MAAA,CAAO;IACzB,IAAA,MAAA,CAAO,wBAAwB,CAAA,EAAG;QACpCC,CAAAA,GAAAA,qKAAAA,CAAAA,iBAAAA,EAA0B,MAAM;QAC1B,MAAA,eAAe,iBAAiB,MAAM,GACtCC,OAAM,SAAS,KAAA,IAAY,GAAG,SAAS,CAAA,CAAA,EAAI,IAAI,EAAA,GAAK;QAC1D,OAAO,GAAG,YAAY,CAAA,CAAA,EAAIA,IAAG,EAAA,CAAG,OAAA,CAAQ,YAAY,IAAI;IAAA;IAEpD,MAAA,UAAUC,uLAAAA,EAAsB,MAAM,GACtC,UAAU,CAAA,CAAA,EAAI,SAAS,CAAA,CAAA,EAAI,OAAO,EAAA;IAExC,OAAO,CAAA,KAAA,EADK,SAAS,KAAA,IAAY,GAAG,OAAO,CAAA,CAAA,EAAI,IAAI,EAAA,GAAK,OACtC,EAAA,CAAG,OAAA,CAAQ,YAAY,IAAI;AAC/C;AAKO,SAAS,QAAQ,MAAA,EAAgB,GAAA,EAAa,YAAY,CAAA,CAAA,EAAe;IAC9E,MAAM,EAAC,GAAA,EAAK,MAAA,EAAA,GAAU,OAAO,MAAA,CAAO;IAE7B,OAAA,GADM,YAAY,SAAS,GACpB,CAAA,CAAA,EAAI,IAAI,OAAA,CAAQ,OAAO,EAAE,CAAC,EAAA;AAC1C;AAKA,SAAS,iBAAoB,MAAA,EAAkD;IAC7E,OAAO,CAAC,QACC,iJAAI,aAAA,CAAW,CAAC,aAAa;YAClC,MAAM,QAAQ,IAAM,SAAS,KAAA,CAAM,kBAAkB,MAAM,CAAC;YAExD,IAAA,UAAU,OAAO,OAAA,EAAS;gBACtB,MAAA;gBACN;YAAA;YAEI,MAAA,eAAe,MAAM,SAAA,CAAU,QAAQ;YAC7C,OAAA,OAAO,gBAAA,CAAiB,SAAS,KAAK,GAC/B,MAAM;gBACX,OAAO,mBAAA,CAAoB,SAAS,KAAK,GACzC,aAAa,WAAA,CAAY;YAC3B;QAAA,CACD;AAEL;AAGA,MAAM,0BAA0B,CAAA,CAAQ,WAAW,YAAA;AAQnD,SAAS,kBAAkB,MAAA,EAAsB;IAK3C,IAAA,yBACF,OAAO,IAAI,aAAa,QAAQ,UAAU,8BAA8B,YAAY;IAItF,MAAM,QAAQ,IAAI,MAAM,QAAQ,UAAU,4BAA4B;IACtE,OAAA,MAAM,IAAA,GAAO,cAEN;AACT;AAEA,MAAM,mBAAmB,CAAC,WAA4C;IAChE,IAAA,CAAC,MAAA,CAAO,wBAAwB,CAAA,EAC5B,MAAA,IAAI,MAAM,yDAAyD;IAE3E,MAAM,EAAC,IAAA,EAAM,EAAA,EAAA,GAAM,MAAA,CAAO,wBAAwB,CAAA;IAElD,OAAQ,MAAM;QACZ,KAAK;YAAW;gBACR,MAAA,WAAW,GAAG,KAAA,CAAM,GAAG;gBAC7B,IAAI,SAAS,MAAA,KAAW,GAChB,MAAA,IAAI,MAAM,oDAAoD;gBAEtE,OAAO,CAAA,UAAA,EAAa,QAAA,CAAS,CAAC,CAAC,CAAA,UAAA,EAAa,QAAA,CAAS,CAAC,CAAC,EAAA;YAAA;QAEzD,KAAK;YACH,OAAO,CAAA,UAAA,EAAa,EAAE,EAAA;QAExB,KAAK;YACH,OAAO,CAAA,iBAAA,EAAoB,EAAE,EAAA;QAE/B,KAAK;YACH,OAAO,CAAA,YAAA,EAAe,EAAE,EAAA;QAE1B;YAEE,MAAM,IAAI,MAAM,CAAA,2BAAA,EAA8B,KAAK,QAAA,CAAU,CAAA,EAAE;IAAA;AAErE;AC9agB,SAAA,UACd,MAAA,EACA,WAAA,EACA,OAAA,EAKA;IACA,MAAMC,WAAU,uLAAA,EAAW,OAAO,MAAA,CAAA,CAAQ;IACnC,OAAA,SAAS,QAAQ,aAAa;QACnC,QAAQ;QACR,KAAK,CAAA,uBAAA,EAA0BA,QAAO,EAAA;QACtC,MAAM;IAAA,CACP;AACH;AC3LgB,SAAA,OACd,MAAA,EACA,WAAA,EACA,OAAA,EAKA;IACA,MAAMA,qLAAU,aAAA,EAAW,OAAO,MAAA,CAAA,CAAQ;IACnC,OAAA,SAAS,QAAQ,aAAa;QACnC,QAAQ;QACR,KAAK,CAAA,oBAAA,EAAuBA,QAAO,EAAA;QACnC,MAAM;IAAA,CACP;AACH;ACFgB,SAAA,QACd,MAAA,EACA,WAAA,EACA,OAAA,EACgF;IAChF,MAAMA,qLAAU,aAAA,EAAW,OAAO,MAAA,CAAA,CAAQ;IACnC,OAAA,SAAS,QAAQ,aAAa;QACnC,QAAQ;QACR,KAAK,CAAA,qBAAA,EAAwBA,QAAO,EAAA;QACpC,MAAM;IAAA,CACP;AACH;ACuLgB,SAAA,WACd,MAAA,EACA,WAAA,EACA,OAAA,EAKA;IACA,MAAMA,oLAAU,cAAA,EAAW,OAAO,MAAA,CAAA,CAAQ;IACnC,OAAA,SAAS,QAAQ,aAAa;QACnC,QAAQ;QACR,KAAK,CAAA,wBAAA,EAA2BA,QAAO,EAAA;QACvC,MAAM;IAAA,CACP;AACH;AC9LgB,SAAA,WACd,MAAA,EACA,WAAA,EACA,OAAA,EAKA;IACA,MAAMA,qLAAU,aAAA,EAAW,OAAO,MAAA,CAAA,CAAQ;IACnC,OAAA,SAAS,QAAQ,aAAa;QACnC,QAAQ;QACR,KAAK,CAAA,wBAAA,EAA2BA,QAAO,EAAA;QACvC,MAAM;IAAA,CACP;AACH;AC5JO,MAAM,6BAA6B;KACxC,MAAA,CAAA;KACA,WAAA,CAAA;IACA,YAAY,MAAA,EAAgC,WAAA,CAA0B;QAC/D,IAAA,EAAA,MAAA,GAAU,QACf,IAAA,EAAK,WAAA,GAAe;IAAA;IAAA;;;GAAA,GAOtB,SACE,OAAA,EAKA;QACA,OAAO,UAAU,IAAA,CAAK,OAAA,EAAS,IAAA,CAAK,YAAA,EAAc,OAAO;IAAA;IAAA;;;GAAA,GAO3D,UACE,OAAA,EAKA;QACA,OAAO,WAAW,IAAA,EAAK,MAAA,EAAS,IAAA,EAAK,WAAA,EAAc,OAAO;IAAA;IAAA;;;GAAA,GAO5D,UACE,OAAA,EAKA;QACA,OAAO,WAAW,IAAA,EAAK,MAAA,EAAS,IAAA,EAAK,WAAA,EAAc,OAAO;IAAA;AAE9D;AAGO,MAAM,mBAAmB;IAC9B,OAAA,CAAA;KACA,WAAA,CAAA;IACA,YAAY,MAAA,EAAsB,WAAA,CAA0B;QACrD,IAAA,EAAA,MAAA,GAAU,QACf,IAAA,EAAK,WAAA,GAAe;IAAA;IAAA;;;GAAA,GAOtB,SACE,OAAA,EAKA;QACA,uJAAO,iBAAA,EAAc,UAAU,IAAA,EAAK,MAAA,EAAS,IAAA,EAAK,WAAA,EAAc,OAAO,CAAC;IAAA;IAAA;;;GAAA,GAO1E,UACE,OAAA,EAKA;QACA,wJAAO,gBAAA,EAAc,WAAW,IAAA,EAAK,MAAA,EAAS,IAAA,EAAK,WAAA,EAAc,OAAO,CAAC;IAAA;IAAA;;;GAAA,GAO3E,UACE,OAAA,EAKA;QACA,wJAAO,gBAAA,EAAc,WAAW,IAAA,EAAK,MAAA,EAAS,IAAA,CAAK,YAAA,EAAc,OAAO,CAAC;IAAA;IAAA;;;GAAA,GAO3E,OACE,OAAA,EAC6E;QAC7E,wJAAO,gBAAA,EAAc,QAAQ,IAAA,EAAK,MAAA,EAAS,IAAA,EAAK,WAAA,EAAc,OAAO,CAAC;IAAA;IAAA;;;;GAAA,GAQxE,MACE,OAAA,EAKA;QACA,WAAO,6JAAA,EAAc,OAAO,IAAA,EAAK,MAAA,EAAS,IAAA,EAAK,WAAA,EAAc,OAAO,CAAC;IAAA;AAEzE;ACtHO,MAAM,uBAAuB;KAClC,MAAA,CAAA;KACA,WAAA,CAAA;IACA,YAAY,MAAA,EAAgC,WAAA,CAA0B;QAC/D,IAAA,EAAA,MAAA,GAAU,QACf,IAAA,EAAK,WAAA,GAAe;IAAA;IAwCtB,OACE,SAAA,EACA,IAAA,EACA,OAAA,EAC0F;QAC1F,OAAO,QAAQ,IAAA,EAAK,MAAA,EAAS,IAAA,EAAK,WAAA,EAAc,WAAW,MAAM,OAAO;IAAA;AAE5E;AAGO,MAAM,aAAa;KACxB,MAAA,CAAA;IACA,YAAA,CAAA;IACA,YAAY,MAAA,EAAsB,WAAA,CAA0B;QACrD,IAAA,EAAA,MAAA,GAAU,QACf,IAAA,EAAK,WAAA,GAAe;IAAA;IAuCtB,OACE,SAAA,EACA,IAAA,EACA,OAAA,EACyD;QACnD,MAAAC,cAAa,QAAQ,IAAA,EAAK,MAAA,EAAS,IAAA,EAAK,WAAA,EAAc,WAAW,MAAM,OAAO;QAC7E,wJAAA,gBAAA,EACLA,YAAW,IAAA,+JACT,SAAA,EAAO,CAAC,QAAe,MAAM,IAAA,KAAS,UAAU,iKAChD,MAAA,EACE,CAAC,QACE,MACE,IAAA,CAAK,QAAA;IAGhB;AAEJ;AAEA,SAAS,QACP,MAAA,EACA,WAAA,EACA,SAAA,EACA,IAAA,EACA,OAA2B,CAAA,CAAA,EAC+D;IAC1FC,CAAAA,GAAAA,qKAAAA,CAAAA,oBAAAA,EAA6B,SAAS;IAGlC,IAAA,OAAO,KAAK,OAAA,IAAW,KAAA;IACvB,QAAQ,CAAC,KAAK,MAAA,IAAA,CAChB,OAAO;QAAC,MAAM;KAAA;IAGhB,MAAM,SAAS,OAAO,MAAA,IAChB,UAAU,gBAAgB,MAAM,IAAI,GACpC,EAAC,GAAA,EAAK,KAAA,EAAO,KAAA,EAAO,WAAA,EAAa,UAAA,EAAY,QAAA,EAAU,MAAA,CAAM,CAAA,GAAI,SACjE,QAAa;QACjB;QACA;QACA;QACA;QACA;QACA;IACF;IACA,OAAI,UAAA,CACF,MAAM,QAAA,GAAW,OAAO,EAAA,EACxB,MAAM,UAAA,GAAa,OAAO,IAAA,EAC1B,MAAM,SAAA,GAAY,OAAO,GAAA,GAGpB,mBAAmB,QAAQ,aAAa;QAC7C;QACA,QAAQ;QACR,SAAS,QAAQ,OAAA,IAAW;QAC5B,KAAK,oBAAoB,QAAQ,SAAS;QAC1C,SAAS,QAAQ,WAAA,GAAc;YAAC,gBAAgB,QAAQ,WAAA;QAAA,IAAe,CAAC;QACxE;QACA;IAAA,CACD;AACH;AAEA,SAAS,oBAAoB,MAAA,EAAiC,SAAA,EAAqC;IAC3F,MAAA,oBAAoB,cAAc,UAAU,WAAW;IAEzD,IAAA,MAAA,CAAO,wBAAwB,CAAA,EAAG;QACpC,MAAM,EAAC,IAAA,EAAM,EAAA,EAAA,GAAM,MAAA,CAAO,wBAAwB,CAAA;QAClD,OAAQ,MAAM;YACZ,KAAK;gBACH,MAAM,IAAI,MACR;YAGJ,KAAK;gBACI,OAAA,CAAA,UAAA,EAAa,EAAE,CAAA,QAAA,EAAW,iBAAiB,EAAA;YAEpD,KAAK;gBACH,OAAO,CAAA,iBAAA,EAAoB,EAAE,CAAA,OAAA,CAAA;YAE/B,KAAK;gBACI,OAAA,CAAA,YAAA,EAAe,EAAE,CAAA,QAAA,EAAW,iBAAiB,EAAA;YAEtD;gBAEE,MAAM,IAAI,MAAM,CAAA,2BAAA,EAA8B,KAAK,QAAA,CAAU,CAAA,EAAE;QAAA;IACnE;IAGI,MAAAF,qLAAUD,aAAAA,EAAsB,MAAM;IACrC,OAAA,CAAA,OAAA,EAAU,iBAAiB,CAAA,CAAA,EAAIC,QAAO,EAAA;AAC/C;AAEA,SAAS,gBAAgB,IAAA,EAA2B,IAAA,EAAW;IAC7D,OAAI,OAAO,OAAS,OAAe,CAAA,CAAE,gBAAgB,IAAA,IAC5C,OAGF,OAAO,MAAA,CACZ;QACE,UAAU,KAAK,gBAAA,KAAqB,CAAA,IAAQ,KAAA,IAAY,KAAK,IAAA;QAC7D,aAAa,KAAK,IAAA;IACpB,GACA;AAEJ;AC5NA,IAAe,WAAA,CAAC,KAAUG,YACxB,OAAO,IAAA,CAAKA,SAAQ,EACjB,MAAA,CAAO,OAAO,IAAA,CAAK,GAAG,CAAC,EACvB,MAAA,CAAO,CAAC,QAAQ,OAAA,CACf,MAAA,CAAO,IAAI,CAAA,GAAI,OAAO,GAAA,CAAI,IAAI,CAAA,GAAM,MAAcA,SAAAA,CAAS,IAAI,CAAA,GAAI,GAAA,CAAI,IAAI,CAAA,EAEpE,MAAA,GACN,CAAA,CAAS;ACPH,MAAA,OAAO,CAAC,KAAU,QAC7B,MAAM,MAAA,CAAO,CAAC,WAAgB,OAAA,CACxB,OAAO,GAAA,CAAI,IAAI,CAAA,GAAM,OAAA,CAIzB,SAAA,CAAU,IAAI,CAAA,GAAI,GAAA,CAAI,IAAI,CAAA,GACnB,SAAA,GACN,CAAA,CAAE,GCPM,uKAAsB,QAAA,EAAM,MAAM,OAAO,qBAAqB,CAAC,8GAAE,IAAA,+JAC5E,MAAA,EAAI,CAAC,EAAC,SAASC,YAAAA,CAAA,CAAA,GAAiBA,YAAuD,oJACvF,cAAA,EAAY,CAAC;ACaR,SAAS,+BAAgF;IAC9F,OAAO,SAAU,MAAA,EAAuB;QACtC,OAAO,OAAO,IAAA,kJACZ,aAAA,EAAW,CAAC,KAAK,SACX,eAAe,yKACV,SAAA,mJAAO,KAAA,EAAG;gBAAC,MAAM;YAAA,CAAqB,GAAG,yJAAA,EAAM,GAAI,EAAE,IAAA,kJAAK,WAAA,EAAS,IAAM,MAAM,CAAC,CAAC,qJAEnF,aAAA,EAAW,IAAM,GAAG,CAC5B;IAEL;AACF;ACPA,MAAM,iBAAiB,OAEjB,kBAAkB;IACtB;IACA;IACA;IACA;IACA;IACA;IACA;CACF,EAEM,iBAAiB;IACrB,eAAe,CAAA;AACjB;AA8BO,SAAS,QAEd,KAAA,EACA,MAAA,EACA,OAAsB,CAAA,CAAA,EACyB;IAC/C,MAAM,EAAC,GAAA,EAAK,KAAA,EAAO,eAAA,EAAiB,gBAAA,EAAkB,SAAS,aAAA,CAAa,CAAA,GAAI,IAAA,CAAK,MAAA,IAC/E,MAAM,KAAK,GAAA,IAAO,mBAAmB;QAAC;QAAkB,KAAK,GAAG;KAAA,CAAE,IAAA,CAAK,GAAG,IAAI,KAAK,GAAA,EACnF,UAAU;QAAC,GAAG,SAAS,MAAM,cAAc,CAAA;QAAG;IAAG,GACjD,aAAa,KAAK,SAAS,eAAe,GAC1C,KAAK,kBAAkB;QAAC;QAAO;QAAQ,SAAS;YAAC;YAAK,GAAG,UAAA;QAAU;IAAA,CAAE,GAErE,MAAM,GAAG,GAAG,GAAG,YAAY,IAAA,EAAM,UAAU,EAAE,CAAC,EAAA;IACpD,IAAI,IAAI,MAAA,GAAS,gBACf,wJAAO,aAAA,EAAW,IAAM,IAAI,MAAM,8BAA8B,CAAC;IAG7D,MAAA,YAAY,QAAQ,MAAA,GAAS,QAAQ,MAAA,GAAS;QAAC,UAAU;KAAA,EAEzD,YAAkE,CAAC;IACrE,OAAA,mBAAA,CACF,UAAU,eAAA,GAAkB,CAAA,CAAA,GAAA,CAG1B,SAAS,aAAA,KAAA,CACX,UAAU,OAAA,GAAU,CAAA,GAEhB,SAAA,CACF,UAAU,OAAA,CAAQ,aAAA,GAAgB,CAAA,OAAA,EAAU,KAAK,EAAA,GAG/C,iBACF,OAAO,MAAA,CAAO,UAAU,OAAA,EAAS,aAAa,CAAA,GAW3C,mBAPiB,IAAA,8EAAA;QAAA,CAErB,OAAO,cAAgB,OAAe,UAAU,OAAA,GAC7C,sBACA,sJAAA,EAAG,WAAW,CAAA,EAChB,IAAA,8JAAK,OAAA,EAAI,CAACA,eAAgB,IAAIA,aAAY,KAAK,SAAS,CAAC,CAAC,GAEnB,SAAS,EAAE,IAAA,CACpD,6BAA6B,iKAC7B,SAAA,EAAO,CAAC,QAAU,UAAU,QAAA,CAAS,MAAM,IAAI,CAAC,OAChD,gKAAA,EACE,CAAC,QAAA,CACE;YACC,MAAM,MAAM,IAAA;YACZ,GAAI,UAAU,QAAS,MAAM,IAAA,GAAkB,CAAA,CAAA;QACjD,CAAA;AAGR;ACzFgB,SAAA,kBACd,iBAAA,EACA,MAAA,EACA;IACO,OAAA,mBACL,OAAO,qBAAsB,aACzB;QAAC,WAAW;QAAmB,GAAG,MAAA;IAAA,IAClC;AAER;AACA,SAAS,mBAAsB,MAAA,EAAiE;IAC9F,OAAO,CAAC,WAA0B;QAChC,IAAI,QACA,UAAU,CAAA;QAGd,MAAM,EAAC,SAAA,EAAW,GAAG,aAAA,GAAe,QAE9B,UAAU,OAAO,IAAA,kJACrB,MAAA,EAAI,CAAC,UAAU;YACT,OAAO,SAAA,CAAU,KAAK,KAAA,CACxB,UAAU,CAAA,GACV,SAAS,KAAA;QAAA,CAEZ,oJACD,WAAA,EAAS,MAAM;YACb,UAAU,CAAA,GACV,SAAS,KAAA;QAAA,CACV,oJACD,QAAA,EAAM,WAAW,IAEb,aAAa,iJAAI,aAAA,CAAc,CAAC,eAAe;YAC/C,WACF,WAAW,IAAA,CAAA,6FAAA;YAET,SAGJ,WAAW,QAAA,CAAS;QAAA,CACrB;QACM,wJAAA,QAAA,EAAM,SAAS,UAAU;IAClC;AACF;ACpDA,MAAM,qBAAqB;AAKpB,MAAM,WAAW;KACtB,MAAA,CAAA;IACA,YAAY,MAAA,CAA+C;QACzD,IAAA,CAAK,OAAA,GAAU;IAAA;IAAA;;GAAA,GAMjB,OAAO,EACL,gBAAgB,CAAA,CAAA,EAChB,KAAK,IAAA,EACP,GAQI,CAAA,CAAA,EAA2B;QAC7BC,CAAAA,GAAAA,qKAAAA,CAAAA,gBAAAA,EAAuB,QAAQ,IAAA,EAAK,MAAA,CAAQ,MAAA,EAAQ;QAC9C,MAAA,EACJ,SAAA,EACA,YAAY,WAAA,EACZ,KAAA,EACA,eAAA,EACA,gBAAA,EACA,SAAS,aAAA,EAAA,GACP,IAAA,EAAK,MAAA,CAAQ,MAAA,IACX,aAAa,YAAY,OAAA,CAAQ,MAAM,EAAE;QAC3C,IAAA,eAAe,OAAO,aAAa,oBACrC,MAAM,IAAI,MACR,CAAA,yCAAA,EAA4C,kBAAkB,CAAA,sCAAA,EAC9B,UAAU,CAAA,qDAAA,CAAA;QAI1C,IAAA,iBAAiB,CAAC,SAAS,CAAC,iBAC9B,MAAM,IAAI,MACR;QAGE,MAAA,OAAO,YAAY,IAAA,EAAK,MAAA,EAAS,aAAa,GAC9C,MAAM,IAAI,IAAI,IAAA,EAAK,MAAA,CAAQ,MAAA,CAAO,MAAM,CAAA,CAAK,CAAC,GAC9C,MAAM,QAAQ,mBAAmB;YAAC;YAAkB,IAAI;SAAA,CAAE,IAAA,CAAK,GAAG,IAAI;QACxE,OACF,IAAI,YAAA,CAAa,GAAA,CAAI,OAAO,GAAG,GAE7B,iBACF,IAAI,YAAA,CAAa,GAAA,CAAI,iBAAiB,MAAM;QAE9C,MAAM,YAAkE,CAAC;QACrE,iBAAiB,mBAAA,CACnB,UAAU,eAAA,GAAkB,CAAA,CAAA,GAAA,CAGzB,iBAAiB,SAAU,aAAA,KAAA,CAC9B,UAAU,OAAA,GAAU,CAAA,GAEhB,iBAAiB,SAAA,CACnB,UAAU,OAAA,CAAQ,aAAA,GAAgB,CAAA,OAAA,EAAU,KAAK,EAAA,GAG/C,iBACF,OAAO,MAAA,CAAO,UAAU,OAAA,EAAS,aAAa,CAAA;QAIlD,MAAM,MAAM,GAAG,IAAI,IAAI,CAAA,EAAA,EAAK,KAAK,SAAA,CAAU,SAAS,CAAC,EAAA,EAC/C,WAAW,YAAY,GAAA,CAAI,GAAG;QAEhC,IAAA,UACK,OAAA;QAUT,MAAM,SAAS,mBAPS,IAAA,8EAAA;YAAA,CAErB,OAAO,cAAgB,OAAe,UAAU,OAAA,GAC7C,uKACA,KAAA,EAAG,WAAW,CAAA,EAChB,IAAA,CAAK,oKAAA,EAAI,CAACD,eAAgB,IAAIA,aAAY,IAAI,IAAA,EAAM,SAAS,CAAC,CAAC,GAEhB;YACjD;YACA;YACA;YACA;YACA;SACD,EAAE,IAAA,CACD,6BAA6B,iKAC7B,MAAA,EAAI,CAAC,UAAU;YACT,IAAA,MAAM,IAAA,KAAS,WAAW;gBAC5B,MAAM,EAAC,IAAA,EAAM,GAAG,KAAA,CAAA,GAAQ;gBAExB,OAAO;oBAAC,GAAG,IAAA;oBAAM,MAAO,KAA2B,IAAA;gBAAI;YAAA;YAElD,OAAA;QACR,CAAA,IAIG,YAAY,gBAAgB,KAAK;YACrC,QAAQ;YACR,MAAM;YACN,aAAa,UAAU,eAAA,GAAkB,YAAY;YACrD,SAAS,UAAU,OAAA;QACpB,CAAA,EAAE,IAAA,kJACD,WAAA,EAAS,iJAAM,QAAK,oJACpB,aAAA,EAAW,MAAM;YAEf,MAAM,IAAI,gBAAgB;gBAAC;YAAA,CAAsB;QAClD,CAAA,IAEGH,+JAAa,SAAA,EAAO,WAAW,MAAM,EAAE,IAAA,KAC3CK,qKAAAA,EAAS,IAAM,YAAY,MAAA,CAAO,GAAG,CAAC,GACtC,kBAAkB;YAChB,WAAW,CAAC,QAAU,MAAM,IAAA,KAAS;QACtC,CAAA;QAES,OAAA,YAAA,GAAA,CAAI,KAAKL,WAAU,GACxBA;IAAA;AAEX;AAEA,SAAS,gBAAgB,GAAA,EAAU,IAAA,EAAmB;IAC7C,OAAA,iJAAI,aAAA,CAAW,CAAC,aAAa;QAClC,MAAM,aAAa,IAAI,gBAAgB,GACjC,SAAS,WAAW,MAAA;QACpB,OAAA,MAAA,KAAK;YAAC,GAAG,IAAA;YAAM,QAAQ,WAAW,MAAA;QAAO,CAAA,EAAE,IAAA,CAC/C,CAAC,aAAa;YACZ,SAAS,IAAA,CAAK,QAAQ,GACtB,SAAS,QAAA,CAAS;QACpB,GACA,CAAC,QAAQ;YACF,OAAO,OAAA,IACV,SAAS,KAAA,CAAM,GAAG;QAAA,IAIjB,IAAM,WAAW,KAAA,CAAM;IAAA,CAC/B;AACH;AAEA,MAAM,cAAA,aAAA,GAAA,IAAkB,IAAmC;AClKpD,MAAM,yBAAyB;KACpC,MAAA,CAAA;IACA,YAAA,CAAA;IACA,YAAY,MAAA,EAAgC,WAAA,CAA0B;QAC/D,IAAA,EAAA,MAAA,GAAU,QACf,IAAA,EAAK,WAAA,GAAe;IAAA;IAAA;;;;;GAAA,GAStB,OAAOM,KAAAA,EAAc,OAAA,EAAmE;QACtF,OAAO,QAAyB,IAAA,EAAK,MAAA,EAAS,IAAA,EAAK,WAAA,EAAc,OAAOA,OAAM,OAAO;IAAA;IAAA;;;;;GAAA,GASvF,KAAKA,KAAAA,EAAc,OAAA,EAAmE;QACpF,OAAO,QAAyB,IAAA,EAAK,MAAA,EAAS,IAAA,EAAK,WAAA,EAAc,SAASA,OAAM,OAAO;IAAA;IAAA;;;;GAAA,GAQzF,OAAOA,KAAAA,EAA2C;QAChD,OAAO,QAAyB,IAAA,EAAK,MAAA,EAAS,IAAA,EAAK,WAAA,EAAc,UAAUA,KAAI;IAAA;IAAA;;GAAA,GAMjF,OAAqC;QACnC,OAAO,SAA2B,IAAA,EAAK,MAAA,EAAS,IAAA,EAAK,WAAA,EAAc;YACjE,KAAK;YACL,KAAK;QAAA,CACN;IAAA;AAEL;AAGO,MAAM,eAAe;KAC1B,MAAA,CAAA;KACA,WAAA,CAAA;IACA,YAAY,MAAA,EAAsB,WAAA,CAA0B;QACrD,IAAA,EAAA,MAAA,GAAU,QACf,IAAA,EAAK,WAAA,GAAe;IAAA;IAAA;;;;;GAAA,GAStB,OAAOA,KAAAA,EAAc,OAAA,EAAgE;QACnF,iLAAAF,gBAAAA,EAAuB,WAAW,IAAA,CAAK,OAAA,CAAQ,MAAA,CAAQ,CAAA,oJAChD,gBAAA,EACL,QAAyB,IAAA,CAAK,OAAA,EAAS,IAAA,EAAK,WAAA,EAAc,OAAOE,OAAM,OAAO;IAChF;IAAA;;;;;GAAA,GASF,KAAKA,KAAAA,EAAc,OAAA,EAAgE;QACjF,WAAAF,sLAAAA,EAAuB,WAAW,IAAA,EAAK,MAAA,CAAQ,MAAA,CAAQ,CAAA,oJAChD,gBAAA,EACL,QAAyB,IAAA,EAAK,MAAA,EAAS,IAAA,CAAK,YAAA,EAAc,SAASE,OAAM,OAAO;IAClF;IAAA;;;;GAAA,GAQF,OAAOA,KAAAA,EAAwC;QAC7C,iLAAAF,gBAAAA,EAAuB,WAAW,IAAA,EAAK,MAAA,CAAQ,MAAA,CAAQ,CAAA,oJAChD,gBAAA,EAAc,QAAyB,IAAA,CAAK,OAAA,EAAS,IAAA,EAAK,WAAA,EAAc,UAAUE,KAAI,CAAC;IAAA;IAAA;;GAAA,GAMhG,OAAkC;QAChC,iLAAAF,gBAAAA,EAAuB,WAAW,IAAA,EAAK,MAAA,CAAQ,MAAA,CAAQ,CAAA,GAChD,iKAAA,EACL,SAA2B,IAAA,EAAK,MAAA,EAAS,IAAA,EAAK,WAAA,EAAc;YAAC,KAAK;YAAa,KAAK;QAAK,CAAA;IAC3F;AAEJ;AAEA,SAAS,QACP,MAAA,EACA,WAAA,EACA,MAAA,EACAE,KAAAA,EACA,OAAA,EACA;IACA,iLAAAF,gBAAAA,EAAuB,WAAW,OAAO,MAAA,CAAQ,CAAA,6KACjDG,UAAAA,EAAiBD,KAAI,GACd,SAAY,QAAQ,aAAa;QACtC;QACA,KAAK,CAAA,UAAA,EAAaA,KAAI,EAAA;QACtB,MAAM;QACN,KAAK;IAAA,CACN;AACH;ACvHO,MAAM,yBAAyB;KACpC,MAAA,CAAA;KACA,WAAA,CAAA;IACA,YAAY,MAAA,EAAgC,WAAA,CAA0B;QAC/D,IAAA,EAAA,MAAA,GAAU,QACf,IAAA,EAAK,WAAA,GAAe;IAAA;IAWtB,KAAK,OAAA,EAE8D;QACjEF,CAAAA,GAAAA,qKAAAA,CAAAA,gBAAAA,EAAuB,YAAY,IAAA,EAAK,MAAA,CAAQ,MAAA,EAAQ;QACxD,MAAM,MAAM,SAAS,mBAAmB,CAAA,IAAQ,mCAAmC;QACnF,OAAO,SAA0B,IAAA,EAAK,MAAA,EAAS,IAAA,EAAK,WAAA,EAAc;YAAC;QAAA,CAAI;IAAA;IAAA;;;;GAAA,GAQzE,QAAQ,SAAA,EAA8C;QACpD,WAAAA,sLAAAA,EAAuB,YAAY,IAAA,EAAK,MAAA,CAAQ,MAAA,CAAA,CAAQ,GACjD,SAAwB,IAAA,EAAK,MAAA,EAAS,IAAA,CAAK,YAAA,EAAc;YAAC,KAAK,CAAA,UAAA,EAAa,SAAS,EAAA;QAAA,CAAG;IAAA;AAEnG;AAGO,MAAM,eAAe;KAC1B,MAAA,CAAA;KACA,WAAA,CAAA;IACA,YAAY,MAAA,EAAsB,WAAA,CAA0B;QACrD,IAAA,EAAA,MAAA,GAAU,QACf,IAAA,EAAK,WAAA,GAAe;IAAA;IAWtB,KAAK,OAAA,EAAgE;QACnEA,CAAAA,GAAAA,qKAAAA,CAAAA,gBAAAA,EAAuB,YAAY,IAAA,EAAK,MAAA,CAAQ,MAAA,EAAQ;QACxD,MAAM,MAAM,SAAS,mBAAmB,CAAA,IAAQ,mCAAmC;QAC5E,wJAAA,gBAAA,EAAc,SAA0B,IAAA,EAAK,MAAA,EAAS,IAAA,CAAK,YAAA,EAAc;YAAC;QAAG,CAAC,CAAC;IAAA;IAAA;;;;GAAA,GAQxF,QAAQ,SAAA,EAA2C;QACjD,iLAAAA,gBAAAA,EAAuB,YAAY,IAAA,EAAK,MAAA,CAAQ,MAAA,CAAQ,CAAA,mJACjD,iBAAA,EACL,SAAwB,IAAA,EAAK,MAAA,EAAS,IAAA,EAAK,WAAA,EAAc;YAAC,KAAK,CAAA,UAAA,EAAa,SAAS,EAAA;QAAG,CAAA;IAC1F;AAEJ;AC5DO,MAAM,wKAAoB,iBAAA,EAC/B,kEACA,IAIW,uBAAuB,CAAC,aAAqB,YACxD,gBAAY,8LAAA,EAAa,aAAa,SAAS,uLAAI,aAAA,EAAW,WAAW;AAGpE,SAAS,wBACd,EAAA,EACA,EACE,SAAA,EACA,WAAA,EACA,QAAA,EACF,EAKQ;IACJ,IAAA,eAAe,SAAS,GAAA,EAAK;QACzB,MAAA,YAAY,qBAAqB,aAAa,SAAS;QACtC,OAAA,mMAAA,EAAA,WAAW,QAAQ,GACnC;IAAA;IAGT,IAAI,SAAS,GAAA,EAAK;QACV,MAAA,UAAU,+LAAA,EAAU,SAAS,GAAG,GAChC,+LAAY,cAAA,EAAY,SAAS,GAAG;QAEtC,IAAA,CAAC,WAAW,CAAC,WACf,MAAM,IAAI,MACR,CAAA,EAAA,EAAK,EAAE,CAAA,sEAAA,CAAA;QAIX,IAAI,WAAW;YACT,IAAA,SACF,MAAM,IAAI,MACR,CAAA,EAAA,EAAK,EAAE,CAAA,sCAAA,EAAyC,SAAS,GAAG,CAAA,4CAAA,EAA+C,SAAS,CAAA,sBAAA,CAAA;YAIlH,MAAA,qBAAiB,kMAAA,EAAiB,SAAS,GAAG;YACpD,IAAI,mBAAmB,WACrB,MAAM,IAAI,MACR,CAAA,EAAA,EAAK,EAAE,CAAA,sCAAA,EAAyC,SAAS,GAAG,CAAA,gDAAA,EAAmD,SAAS,CAAA,gDAAA,EAAmD,cAAc,CAAA,IAAA,CAAA;QAC3L;QAIJ,OAAO,SAAS,GAAA;IAAA;IAGd,IAAA,aACK,OAAA,qBAAqB,aAAa,SAAS;IAGpD,MAAM,IAAI,MAAM,CAAA,EAAA,EAAK,EAAE,CAAA,gEAAA,CAAkE;AAC3F;ACjEA,MAAM,UAAU,CACd,kBACA,iBACkF;IAEhF,IAAA,OAAO,oBAAqB,YAC5B,qBAAqB,QAAA,CACpB,eAAe,oBAAoB,cAAc,gBAAA,GAEhC;QAClB,MAAM,EAAC,YAAY,kBAAA,CAAA,EAAqB,WAAW,CAAA,CAAA,CAAM,CAAA,GAAA;QAClD,OAAA;YAAC;YAAW;YAAU,YAAY;SAAA;IAAA;IAG3C,OAAO;QAAC,kBAAA;QAAqB,CAAA;QAAI,gBAAqC;KAAA;AACxE,GAGa,gBAAgB,CAC3B,kBACA,iBAIG;IACG,MAAA,CAAC,WAAW,UAAU,OAAO,CAAA,GAAI,QAAQ,kBAAkB,YAAY,GAEvE,gBAA6C;QACjD,GAAG,QAAA;QACH,aAAa,SAAS,WAAA,IAAe;IACvC;IAQA,OAAO;QAAC,QAN0C;YAChD,YAAY;YACZ;YACA,UAAU;QAAA;QAGkB;IAAO;AACvC;AC5BO,MAAM,yBAAyB;KACpC,MAAA,CAAA;KACA,WAAA,CAAA;IACA,YAAY,MAAA,EAAgC,WAAA,CAA0B;QAC/D,IAAA,EAAA,MAAA,GAAU,QACf,IAAA,EAAK,WAAA,GAAe;IAAA;IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;GAAA,GA8BtB,IACE,EAAC,SAAA,CAAS,CAAA,EACV,OAAA,EACyC;QAClC,OAAA,aACL,IAAA,EAAK,MAAA,EACL,IAAA,EAAK,WAAA,EACL,CAAA,WAAA,EAAc,SAAS,EAAA,EACvB;IACF;IAiEF,OACE,gBAAA,EAGA,YAAA,EAC6F;QACvF,MAAA,EAAC,MAAA,EAAQ,OAAA,CAAW,CAAA,GAAA,cAAc,kBAAkB,YAAY,GAChE,EAAC,SAAA,EAAW,QAAA,CAAA,CAAA,GAAY;QAE9B,OAAO,QAAQ,IAAA,EAAK,MAAA,EAAS,IAAA,EAAK,WAAA,EAAc,QAAQ,OAAO,EAAE,IAAA,kJAC/DI,MAAAA,EAAI,CAAC,eAAA,CAAkB;gBACrB,GAAG,YAAA;gBACH;gBACA;YAAA,CAAA,CACA;IACJ;IAAA;;;;;;;;;;;;GAAA,GAgBF,KACE,EAAC,SAAA,EAAW,KAAA,CAAA,CAAA,EACZ,OAAA,EACgC;QAChC,MAAM,aAAgC;YACpC,YAAY;YACZ;YACA;QACF;QAEA,OAAO,QAAQ,IAAA,EAAK,MAAA,EAAS,IAAA,EAAK,WAAA,EAAc,YAAY,OAAO;IAAA;IAAA;;;;;;;;;;;;;;;;;GAAA,GAqBrE,QACE,EAAC,SAAA,CAAS,CAAA,EACV,OAAA,EACgC;QAChC,MAAM,gBAAsC;YAC1C,YAAY;YACZ;QACF;QAEA,OAAO,QAAQ,IAAA,EAAK,MAAA,EAAS,IAAA,CAAK,YAAA,EAAc,eAAe,OAAO;IAAA;IAAA;;;;;;;;;;;;;;GAAA,GAkBxE,QACE,EAAC,SAAA,CAAS,CAAA,EACV,OAAA,EACgC;QAChC,MAAM,gBAAsC;YAC1C,YAAY;YACZ;QACF;QAEA,OAAO,QAAQ,IAAA,EAAK,MAAA,EAAS,IAAA,EAAK,WAAA,EAAc,eAAe,OAAO;IAAA;IAAA;;;;;;;;;;;;GAAA,GAgBxE,UACE,EAAC,SAAA,CAAS,CAAA,EACV,OAAA,EACgC;QAChC,MAAM,kBAA0C;YAC9C,YAAY;YACZ;QACF;QAEA,OAAO,QAAQ,IAAA,EAAK,MAAA,EAAS,IAAA,EAAK,WAAA,EAAc,iBAAiB,OAAO;IAAA;IAAA;;;;;;;;;;;;;;;GAAA,GAmB1E,SACE,EAAC,SAAA,EAAW,SAAA,CAAA,CAAA,EACZ,OAAA,EACgC;QAChC,MAAM,iBAAwC;YAC5C,YAAY;YACZ;YACA;QACF;QAEA,OAAO,QAAQ,IAAA,EAAK,MAAA,EAAS,IAAA,EAAK,WAAA,EAAc,gBAAgB,OAAO;IAAA;IAAA;;;;;;;;;;;;;;GAAA,GAkBzE,WACE,EAAC,SAAA,CAAS,CAAA,EACV,OAAA,EACgC;QAChC,MAAM,mBAA4C;YAChD,YAAY;YACZ;QACF;QAEA,OAAO,QAAQ,IAAA,EAAK,MAAA,EAAS,IAAA,EAAK,WAAA,EAAc,kBAAkB,OAAO;IAAA;IAAA;;;;;;;;;;;;GAAA,GAgB3E,OACE,EAAC,SAAA,CAAS,CAAA,EACV,OAAA,EACgC;QAChC,MAAM,eAAoC;YACxC,YAAY;YACZ;QACF;QAEA,OAAO,QAAQ,IAAA,EAAK,MAAA,EAAS,IAAA,EAAK,WAAA,EAAc,cAAc,OAAO;IAAA;IAAA;;;;;;;;;;;GAAA,GAevE,eACE,EAAC,SAAA,CAAS,CAAA,EACV,OAAA,EACgD;QAChD,OAAO,qBAAqB,IAAA,EAAK,MAAA,EAAS,IAAA,EAAK,WAAA,EAAc,WAAW,OAAO;IAAA;AAEnF;AAGO,MAAM,eAAe;KAC1B,MAAA,CAAA;KACA,WAAA,CAAA;IACA,YAAY,MAAA,EAAsB,WAAA,CAA0B;QACrD,IAAA,CAAA,OAAA,GAAU,QACf,IAAA,EAAK,WAAA,GAAe;IAAA;IAAA;;;;;;;;;;;;;;;;;;;;;;;;;GAAA,GA6BtB,IACE,EAAC,SAAA,CAAS,CAAA,EACV,OAAA,EACsC;QAC/B,wJAAA,gBAAA,EACL,aACE,IAAA,EAAK,MAAA,EACL,IAAA,EAAK,WAAA,EACL,CAAA,WAAA,EAAc,SAAS,EAAA,EACvB;IAEJ;IAyDF,MAAM,OACJ,gBAAA,EAGA,YAAA,EAC0F;QACpF,MAAA,EAAC,MAAA,EAAQ,OAAA,CAAW,CAAA,GAAA,cAAc,kBAAkB,YAAY,GAChE,EAAC,SAAA,EAAW,QAAA,CAAA,CAAA,GAAY;QAMvB,OAAA;YAAC,GAJa,uJAAM,gBAAA,EACzB,QAAQ,IAAA,EAAK,MAAA,EAAS,IAAA,EAAK,WAAA,EAAc,QAAQ,OAAO,EAAA;YAGjC;YAAW;QAAQ;IAAA;IAAA;;;;;;;;;;;;GAAA,GAgB9C,KACE,EAAC,SAAA,EAAW,KAAA,CAAA,CAAA,EACZ,OAAA,EAC6B;QAC7B,MAAM,aAAgC;YACpC,YAAY;YACZ;YACA;QACF;QAEO,OAAA,iKAAA,EAAc,QAAQ,IAAA,EAAK,MAAA,EAAS,IAAA,EAAK,WAAA,EAAc,YAAY,OAAO,CAAC;IAAA;IAAA;;;;;;;;;;;;;;;;;GAAA,GAqBpF,QACE,EAAC,SAAA,CAAS,CAAA,EACV,OAAA,EAC6B;QAC7B,MAAM,gBAAsC;YAC1C,YAAY;YACZ;QACF;QAEO,wJAAA,gBAAA,EAAc,QAAQ,IAAA,EAAK,MAAA,EAAS,IAAA,EAAK,WAAA,EAAc,eAAe,OAAO,CAAC;IAAA;IAAA;;;;;;;;;;;;;;GAAA,GAkBvF,QACE,EAAC,SAAA,CAAS,CAAA,EACV,OAAA,EAC6B;QAC7B,MAAM,gBAAsC;YAC1C,YAAY;YACZ;QACF;QAEO,wJAAA,gBAAA,EAAc,QAAQ,IAAA,EAAK,MAAA,EAAS,IAAA,EAAK,WAAA,EAAc,eAAe,OAAO,CAAC;IAAA;IAAA;;;;;;;;;;;;GAAA,GAgBvF,UACE,EAAC,SAAA,CAAS,CAAA,EACV,OAAA,EAC6B;QAC7B,MAAM,kBAA0C;YAC9C,YAAY;YACZ;QACF;QAEO,wJAAA,gBAAA,EAAc,QAAQ,IAAA,EAAK,MAAA,EAAS,IAAA,EAAK,WAAA,EAAc,iBAAiB,OAAO,CAAC;IAAA;IAAA;;;;;;;;;;;;;;;GAAA,GAmBzF,SACE,EAAC,SAAA,EAAW,SAAA,CAAA,CAAA,EACZ,OAAA,EAC6B;QAC7B,MAAM,iBAAwC;YAC5C,YAAY;YACZ;YACA;QACF;QAEO,wJAAA,gBAAA,EAAc,QAAQ,IAAA,EAAK,MAAA,EAAS,IAAA,EAAK,WAAA,EAAc,gBAAgB,OAAO,CAAC;IAAA;IAAA;;;;;;;;;;;;;;GAAA,GAkBxF,WACE,EAAC,SAAA,CAAS,CAAA,EACV,OAAA,EAC6B;QAC7B,MAAM,mBAA4C;YAChD,YAAY;YACZ;QACF;QAEO,wJAAA,gBAAA,EAAc,QAAQ,IAAA,EAAK,MAAA,EAAS,IAAA,EAAK,WAAA,EAAc,kBAAkB,OAAO,CAAC;IAAA;IAAA;;;;;;;;;;;;GAAA,GAgB1F,OACE,EAAC,SAAA,CAAS,CAAA,EACV,OAAA,EAC6B;QAC7B,MAAM,eAAoC;YACxC,YAAY;YACZ;QACF;QAEO,wJAAA,gBAAA,EAAc,QAAQ,IAAA,EAAK,MAAA,EAAS,IAAA,EAAK,WAAA,EAAc,cAAc,OAAO,CAAC;IAAA;IAAA;;;;;;;;;;;GAAA,GAetF,eACE,EAAC,SAAA,CAAS,CAAA,EACV,OAAA,EAC6C;QACtC,wJAAA,gBAAA,EAAc,qBAAqB,IAAA,EAAK,MAAA,EAAS,IAAA,EAAK,WAAA,EAAc,WAAW,OAAO,CAAC;IAAA;AAElG;ACvqBO,MAAM,sBAAsB;KACjC,MAAA,CAAA;KACA,WAAA,CAAA;IACA,YAAY,MAAA,EAAgC,WAAA,CAA0B;QAC/D,IAAA,EAAA,MAAA,GAAU,QACf,IAAA,EAAK,WAAA,GAAe;IAAA;IAAA;;;;GAAA,GAQtB,QACE,EAAA,EAC6D;QACtD,OAAA,SACL,IAAA,EAAK,MAAA,EACL,IAAA,EAAK,WAAA,EACL;YAAC,KAAK,CAAA,OAAA,EAAU,EAAE,EAAA;QAAE;IACtB;AAEJ;AAGO,MAAM,YAAY;KACvB,MAAA,CAAA;KACA,WAAA,CAAA;IACA,YAAY,MAAA,EAAsB,WAAA,CAA0B;QACrD,IAAA,EAAA,MAAA,GAAU,QACf,IAAA,CAAK,YAAA,GAAe;IAAA;IAAA;;;;GAAA,GAQtB,QACE,EAAA,EAC0D;QACnD,wJAAA,gBAAA,EACL,SAA0D,IAAA,CAAK,OAAA,EAAS,IAAA,EAAK,WAAA,EAAc;YACzF,KAAK,CAAA,OAAA,EAAU,EAAE,EAAA;QAClB,CAAA;IACH;AAEJ;ACWO,MAAM,uBAAuB;IAClC,OAAA;IACA,SAAA;IACA,KAAA;IACA,SAAA;IACA,MAAA;IACA,MAAA;IAGA,SAAA;IAAA;;GAAA,GAKA,aAAA,CAAA;KACA,WAAA,CAAA;IAAA;;GAAA,GAKA,SAAS,QAAA;IAET,YAAY,WAAA,EAA0B,+KAAuB,gBAAA,CAAe;QACrE,IAAA,CAAA,MAAA,CAAO,MAAM,GAElB,IAAA,EAAK,WAAA,GAAe,aAEpB,IAAA,CAAK,MAAA,GAAS,IAAI,uBAAuB,IAAA,EAAM,IAAA,EAAK,WAAY,GAChE,IAAA,CAAK,QAAA,GAAW,IAAI,yBAAyB,IAAA,EAAM,IAAA,EAAK,WAAY,GACpE,IAAA,CAAK,IAAA,GAAO,IAAI,WAAW,IAAI,GAC/B,IAAA,CAAK,QAAA,GAAW,IAAI,yBAAyB,IAAA,EAAM,IAAA,EAAK,WAAY,GACpE,IAAA,CAAK,KAAA,GAAQ,IAAI,sBAAsB,IAAA,EAAM,IAAA,EAAK,WAAY,GAC9D,IAAA,CAAK,KAAA,GAAQ;YACX,QAAQ,IAAI,6BAA6B,IAAA,EAAM,IAAA,EAAK,WAAY;QAAA,GAElE,IAAA,CAAK,QAAA,GAAW,IAAI,yBAAyB,IAAA,EAAM,IAAA,CAAK,YAAY;IAAA;IAAA;;GAAA,GAMtE,QAAgC;QAC9B,OAAO,IAAI,uBAAuB,IAAA,EAAK,WAAA,EAAc,IAAA,CAAK,MAAA,EAAQ;IAAA;IAWpE,OAAO,SAAA,EAAwD;QAC7D,IAAI,cAAc,KAAA,GACT,OAAA;YAAC,GAAG,IAAA,EAAK,YAAA;QAAa;QAG/B,IAAI,IAAA,EAAK,YAAA,IAAiB,IAAA,EAAK,YAAA,CAAc,gBAAA,KAAqB,CAAA,GAChE,MAAM,IAAI,MACR;QAIJ,OAAA,IAAA,EAAK,YAAA,6KAAgB,aAAA,EAAW,WAAW,IAAA,CAAK,aAAA,IAAiB,CAAE,CAAA,GAC5D,IAAA;IAAA;IAAA;;;;GAAA,GAQT,WAAW,SAAA,EAA2D;QAC9D,MAAA,aAAa,IAAA,CAAK,MAAA,CAAO;QACxB,OAAA,IAAI,uBAAuB,IAAA,EAAK,WAAA,EAAc;YACnD,GAAG,UAAA;YACH,GAAG,SAAA;YACH,OAAO;gBACL,GAAI,WAAW,KAAA,IAAS,CAAC,CAAA;gBACzB,GAAI,OAAO,WAAW,SAAU,YAC5B;oBAAC,SAAS,UAAU,KAAA;gBAAK,IACzB,WAAW,SAAS,CAAA,CAAA;YAAC;QAC3B,CACD;IAAA;IA6DH,MACE,KAAA,EACA,MAAA,EACA,OAAA,EACqC;QACrC,OAAOC,OACL,IAAA,EACA,IAAA,EAAK,WAAA,EACL,IAAA,EAAK,YAAA,CAAc,KAAA,EACnB,OACA,QACA;IACF;IAAA;;;;;GAAA,GASF,YACE,EAAA,EACA,OAAA,EAC2C;QAC3C,OAAOC,aAA4B,IAAA,EAAM,IAAA,EAAK,WAAA,EAAc,IAAI,OAAO;IAAA;IAAA;;;;;;;;GAAA,GAYzE,aACE,GAAA,EACA,OAAA,EAC0C;QAC1C,OAAOC,cAA6B,IAAA,EAAM,IAAA,EAAK,WAAA,EAAc,KAAK,OAAO;IAAA;IA0D3E,OACE,QAAA,EACA,OAAA,EAQA;QACA,OAAOC,QAAuB,IAAA,EAAM,IAAA,EAAK,WAAA,EAAc,UAAU,UAAU,OAAO;IAAA;IA0DpF,kBACE,QAAA,EACA,OAAA,EAQA;QACA,OAAOC,mBAAkC,IAAA,EAAM,IAAA,EAAK,WAAA,EAAc,UAAU,OAAO;IAAA;IA0DrF,gBACE,QAAA,EACA,OAAA,EAQA;QACA,OAAOC,iBAAgC,IAAA,EAAM,IAAA,EAAK,WAAA,EAAc,UAAU,OAAO;IAAA;IAuFnF,cACE,EACE,QAAA,EACA,WAAA,EACA,SAAA,EAAA,EAMF,OAAA,EACuD;QACjD,MAAA,oBAAoB,wBAAwB,iBAAiB;YACjE;YACA;YACA;QACD,CAAA,GAEK,kBAAkB;YAAC,GAAG,QAAA;YAAU,KAAK;QAAA,GACrC,qBAAqB,kMAAe,iBAAA,EAAe,SAAS,GAAG;QAErE,OAAOC,eACL,IAAA,EACA,IAAA,EAAK,WAAA,EACL,iBACA,oBACA;IACF;IA2GF,OACE,SAAA,EACA,OAAA,EAQA;QACA,OAAOC,QAAuB,IAAA,EAAM,IAAA,EAAK,WAAA,EAAc,WAAW,OAAO;IAAA;IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;GAAA,GA+B3E,eACE,EAAC,SAAA,EAAW,WAAA,CAAW,CAAA,EACvB,KAAA,EACA,OAAA,EACuD;QACjD,MAAA,oBAAoB,qBAAqB,aAAa,SAAS;QAErE,OAAOC,gBAA4B,IAAA,EAAM,IAAA,EAAK,WAAA,EAAc,mBAAmB,OAAO,OAAO;IAAA;IAoF/F,eACE,EACE,QAAA,EACA,WAAA,EACA,SAAA,EAAA,EAMF,OAAA,EACuD;QACjD,MAAA,oBAAoB,wBAAwB,kBAAkB;YAClE;YACA;YACA;QACD,CAAA,GAEK,kBAAkB;YAAC,GAAG,QAAA;YAAU,KAAK;QAAiB;QAE5D,OAAOC,gBAA+B,IAAA,EAAM,IAAA,EAAK,WAAA,EAAc,iBAAiB,OAAO;IAAA;IAAA;;;;;;;;;;;;;;;;;;;;GAAA,GAwBzF,iBACE,EAAC,SAAA,EAAW,WAAA,CAAA,CAAA,EACZ,OAAA,EACuD;QACjD,MAAA,+LAAY,eAAA,EAAa,aAAa,SAAS;QAErD,OAAOC,kBAA8B,IAAA,EAAM,IAAA,EAAK,WAAA,EAAc,WAAW,aAAa,OAAO;IAAA;IA0D/F,OACE,UAAA,EACA,OAAA,EAQA;QACA,OAAOC,QAAuB,IAAA,EAAM,IAAA,EAAK,WAAA,EAAc,YAAY,OAAO;IAAA;IAAA;;;;;;GAAA,GAqC5E,MAAM,SAAA,EAA2B,UAAA,EAA+C;QAC9E,OAAO,IAAI,gBAAgB,WAAW,YAAY,IAAI;IAAA;IAAA;;;;GAAA,GAQxD,YACE,UAAA,EACuB;QAChB,OAAA,IAAI,sBAAsB,YAAY,IAAI;IAAA;IAAA;;;;;GAAA,GASnD,OACE,UAAA,EACA,OAAA,EACuD;QACvD,OAAOC,QAAoB,IAAA,EAAM,IAAA,CAAK,YAAA,EAAc,YAAY,OAAO;IAAA;IAAA;;;;GAAA,GAQzE,QAAiB,OAAA,EAA2C;QAC1D,OAAOC,SAAqB,IAAA,EAAM,IAAA,EAAK,WAAA,EAAc,OAAO;IAAA;IAAA;;;;;GAAA,GAS9D,OAAO,GAAA,EAAa,SAAA,EAA6B;QAC/C,OAAOC,QAAoB,IAAA,EAAM,KAAK,SAAS;IAAA;IAAA;;;;;GAAA,GASjD,WAAW,SAAA,EAAmB,IAAA,EAAuB;QACnD,OAAOC,YAAwB,IAAA,EAAM,WAAW,IAAI;IAAA;AAExD;AAGO,MAAM,aAAa;IACxB,OAAA;IACA,SAAA;IACA,KAAA;IACA,SAAA;IACA,MAAA;IACA,MAAA;IAGA,SAAA;IAAA;;GAAA,GAKA,WAAA;IAAA;;GAAA,IAKA,YAAA,CAAA;KACA,WAAA,CAAA;IAAA;;GAAA,GAKA,SAAS,QAAA;IAET,YAAY,WAAA,EAA0B,+KAAuB,gBAAA,CAAe;QACrE,IAAA,CAAA,MAAA,CAAO,MAAM,GAElB,IAAA,EAAK,WAAA,GAAe,aAEpB,IAAA,CAAK,MAAA,GAAS,IAAI,aAAa,IAAA,EAAM,IAAA,EAAK,WAAY,GACtD,IAAA,CAAK,QAAA,GAAW,IAAI,eAAe,IAAA,EAAM,IAAA,EAAK,WAAY,GAC1D,IAAA,CAAK,IAAA,GAAO,IAAI,WAAW,IAAI,GAC/B,IAAA,CAAK,QAAA,GAAW,IAAI,eAAe,IAAA,EAAM,IAAA,EAAK,WAAY,GAC1D,IAAA,CAAK,KAAA,GAAQ,IAAI,YAAY,IAAA,EAAM,IAAA,EAAK,WAAY,GACpD,IAAA,CAAK,KAAA,GAAQ;YACX,QAAQ,IAAI,mBAAmB,IAAA,EAAM,IAAA,CAAK,YAAY;QAExD,GAAA,IAAA,CAAK,QAAA,GAAW,IAAI,eAAe,IAAA,EAAM,IAAA,EAAK,WAAY,GAE1D,IAAA,CAAK,UAAA,GAAa,IAAI,uBAAuB,aAAa,MAAM;IAAA;IAAA;;GAAA,GAMlE,QAAsB;QACpB,OAAO,IAAI,aAAa,IAAA,CAAK,YAAA,EAAc,IAAA,CAAK,MAAA,EAAQ;IAAA;IAW1D,OAAO,SAAA,EAAwD;QAC7D,IAAI,cAAc,KAAA,GACT,OAAA;YAAC,GAAG,IAAA,EAAK,YAAA;QAAa;QAG/B,IAAI,IAAA,EAAK,YAAA,IAAiB,IAAA,EAAK,YAAA,CAAc,gBAAA,KAAqB,CAAA,GAChE,MAAM,IAAI,MACR;QAIJ,OAAI,IAAA,CAAK,UAAA,IACP,IAAA,CAAK,UAAA,CAAW,MAAA,CAAO,SAAS,GAGlC,IAAA,CAAK,aAAA,6KAAgB,aAAA,EAAW,WAAW,IAAA,EAAK,YAAA,IAAiB,CAAE,CAAA,GAC5D,IAAA;IAAA;IAAA;;;;GAAA,GAQT,WAAW,SAAA,EAAiD;QACpD,MAAA,aAAa,IAAA,CAAK,MAAA,CAAO;QACxB,OAAA,IAAI,aAAa,IAAA,CAAK,YAAA,EAAc;YACzC,GAAG,UAAA;YACH,GAAG,SAAA;YACH,OAAO;gBACL,GAAI,WAAW,KAAA,IAAS,CAAC,CAAA;gBACzB,GAAI,OAAO,WAAW,SAAU,YAC5B;oBAAC,SAAS,UAAU,KAAA;gBAAK,IACzB,WAAW,SAAS,CAAA,CAAA;YAAC;QAC3B,CACD;IAAA;IA6DH,MACE,KAAA,EACA,MAAA,EACA,OAAA,EACoE;QAC7D,wJAAA,gBAAA,EACLf,OACE,IAAA,EACA,IAAA,CAAK,YAAA,EACL,IAAA,EAAK,YAAA,CAAc,KAAA,EACnB,OACA,QACA;IAEJ;IAAA;;;;;GAAA,GASF,YACE,EAAA,EACA,OAAA,EACwC;QACjC,wJAAA,gBAAA,EAAcC,aAA4B,IAAA,EAAM,IAAA,EAAK,WAAA,EAAc,IAAI,OAAO,CAAC;IAAA;IAAA;;;;;;;;GAAA,GAYxF,aACE,GAAA,EACA,OAAA,EACuC;QAChC,wJAAA,gBAAA,EAAcC,cAA6B,IAAA,EAAM,IAAA,EAAK,WAAA,EAAc,KAAK,OAAO,CAAC;IAAA;IA0D1F,OACE,QAAA,EACA,OAAA,EAQA;QACO,wJAAA,gBAAA,EACLC,QAAuB,IAAA,EAAM,IAAA,CAAK,YAAA,EAAc,UAAU,UAAU,OAAO;IAC7E;IA0DF,kBACE,QAAA,EACA,OAAA,EAQA;QACO,wJAAA,gBAAA,EACLC,mBAAkC,IAAA,EAAM,IAAA,EAAK,WAAA,EAAc,UAAU,OAAO;IAC9E;IA0DF,gBACE,QAAA,EACA,OAAA,EAQA;QACO,wJAAA,gBAAA,EACLC,iBAAgC,IAAA,EAAM,IAAA,EAAK,WAAA,EAAc,UAAU,OAAO;IAC5E;IAuFF,cACE,EACE,QAAA,EACA,WAAA,EACA,SAAA,EAAA,EAMF,OAAA,EACoD;QAC9C,MAAA,oBAAoB,wBAAwB,iBAAiB;YACjE;YACA;YACA;QACD,CAAA,GAEK,kBAAkB;YAAC,GAAG,QAAA;YAAU,KAAK;QAAA,GACrC,qBAAqB,gBAAe,mMAAA,EAAe,SAAS,GAAG;QAE9D,wJAAA,iBAAA,EACLC,eACE,IAAA,EACA,IAAA,EAAK,WAAA,EACL,iBACA,oBACA;IAEJ;IA2GF,OACE,SAAA,EACA,OAAA,EAQA;QACO,wJAAA,gBAAA,EAAcC,QAAuB,IAAA,EAAM,IAAA,EAAK,WAAA,EAAc,WAAW,OAAO,CAAC;IAAA;IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;GAAA,GA+B1F,eACE,EAAC,SAAA,EAAW,WAAA,CAAW,CAAA,EACvB,KAAA,EACA,OAAA,EACoD;QAC9C,MAAA,oBAAoB,qBAAqB,aAAa,SAAS;QAE9D,wJAAA,gBAAA,EACLC,gBAA4B,IAAA,EAAM,IAAA,EAAK,WAAA,EAAc,mBAAmB,OAAO,OAAO;IACxF;IAqFF,eACE,EACE,QAAA,EACA,WAAA,EACA,SAAA,EAAA,EAMF,OAAA,EACoD;QAC9C,MAAA,oBAAoB,wBAAwB,kBAAkB;YAClE;YACA;YACA;QACD,CAAA,GAEK,kBAAkB;YAAC,GAAG,QAAA;YAAU,KAAK;QAAiB;QAErD,QAAA,iKAAA,EACLC,gBAA+B,IAAA,EAAM,IAAA,EAAK,WAAA,EAAc,iBAAiB,OAAO;IAClF;IAAA;;;;;;;;;;;;;;;;;;;;GAAA,GAwBF,iBACE,EAAC,SAAA,EAAW,WAAA,CAAA,CAAA,EACZ,OAAA,EACoD;QAC9C,MAAA,8LAAY,gBAAA,EAAa,aAAa,SAAS;QAE9C,wJAAA,gBAAA,EACLC,kBAA8B,IAAA,EAAM,IAAA,EAAK,WAAA,EAAc,WAAW,aAAa,OAAO;IACxF;IA0DF,OACE,UAAA,EACA,OAAA,EAQA;QACO,wJAAA,gBAAA,EAAcC,QAAuB,IAAA,EAAM,IAAA,EAAK,WAAA,EAAc,YAAY,OAAO,CAAC;IAAA;IAAA;;;;;;GAAA,GAqC3F,MAAM,UAAA,EAA4B,UAAA,EAAqC;QACrE,OAAO,IAAI,MAAM,YAAY,YAAY,IAAI;IAAA;IAAA;;;;GAAA,GAQ/C,YACE,UAAA,EACa;QACN,OAAA,IAAI,YAAY,YAAY,IAAI;IAAA;IAAA;;;;;;GAAA,GAUzC,OACE,UAAA,EACA,OAAA,EACoD;QAC7C,wJAAA,gBAAA,EAAcC,QAAoB,IAAA,EAAM,IAAA,EAAK,WAAA,EAAc,YAAY,OAAO,CAAC;IAAA;IAAA;;;;;;GAAA,GAUxF,QAAiB,OAAA,EAAwC;QACvD,wJAAO,gBAAA,EAAcC,SAAwB,IAAA,EAAM,IAAA,EAAK,WAAA,EAAc,OAAO,CAAC;IAAA;IAAA;;;;;;;;;GAAA,GAahF,YAAY,QAAA,EAAkB,IAAA,EAAe,OAAA,EAA6C;QACjF,wJAAA,gBAAA,EAAcG,aAAyB,IAAA,EAAM,IAAA,CAAK,YAAA,EAAc,UAAU,MAAM,OAAO,CAAC;IAAA;IAAA;;;;;GAAA,GASjG,OAAO,GAAA,EAAa,SAAA,EAA6B;QAC/C,OAAOF,QAAoB,IAAA,EAAM,KAAK,SAAS;IAAA;IAAA;;;;;GAAA,GASjD,WAAW,SAAA,EAAmB,IAAA,EAAuB;QACnD,OAAOC,YAAwB,IAAA,EAAM,WAAW,IAAI;IAAA;AAExD;AC38DwB,SAAA,0BAItB,aAAA,EACA,gBAAA,EACA;IAkBA,OAAO;QAAC,WAhBiB,kBAAkB,aAAa;QAgBnB,cAdhB,CAAC,WAA6B;YAC3C,MAAA,kBAAkB,kBAAkB,aAAa;YACvD,OAAO,IAAI,iBACT,CAAC,SAASE,aAAAA,CACPA,cAAa,eAAA,EAAiB;oBAC7B,cAAc;oBACd,YAAY,OAAO,UAAA;oBACnB,YAAY,OAAO,UAAA;oBACnB,GAAG,OAAA;gBAAA,CACG,GACV;QACF;IAG+C;AACnD;AC1DO,SAAS,6BACdC,aAAAA,EACA;IACA,OAAO,SAAgC,MAAA,EAA0B;QAC1C,iLAAA,uBAAA,CAAA,IACdA,cAAa,MAAM;IAC5B;AACF;;ACNA,MAAM,aAAa;KACjB,2KAAA,EAAM;QAAC,SAAS,CAAA;QAAM,WAAW;IAAA,CAAgB;wKACjD,UAAA,EAAQ;QAAC,cAAc,GAAG,IAAI,CAAA,CAAA,EAAI,OAAO,EAAA;IAAA,CAAG;IAAA,qFAAA;IAAA,qFAAA;IAAA,gFAAA;IAAA,8FAAA;IAAA,wEAAA;IAAA,EAAA;IAAA,0DAAA;IAAA,8HAAA;wKAU5C,QAAA,EAAM;QACJ,WAAW,CAAA;QACX,YAAY;QACZ,iBAAiB;IAClB,CAAA;CACH,ECfM,MAAM,0BAAsDC,YAAe,YAAY,GAGhF,YAAY,IAAI,SAAA,EAOhB,eAAe,IAAI,YAAA,EAM1B,yBAAyB,6BAA6B,YAAY", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34], "debugId": null}}]}