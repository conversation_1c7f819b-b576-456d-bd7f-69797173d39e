{"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\analytics\\CrispChat.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\analytics\\CrispChat.tsx", "statementMap": {"0": {"start": {"line": 132, "column": 13}, "end": {"line": 132, "column": 23}}, "1": {"start": {"line": 129, "column": 0}, "end": {"line": 129, "column": 15}}, "2": {"start": {"line": 3, "column": 26}, "end": {"line": 3, "column": null}}, "3": {"start": {"line": 4, "column": 19}, "end": {"line": 4, "column": null}}, "4": {"start": {"line": 34, "column": 44}, "end": {"line": 127, "column": null}}, "5": {"start": {"line": 36, "column": 25}, "end": {"line": 36, "column": 78}}, "6": {"start": {"line": 38, "column": 2}, "end": {"line": 103, "column": null}}, "7": {"start": {"line": 39, "column": 4}, "end": {"line": 42, "column": null}}, "8": {"start": {"line": 40, "column": 6}, "end": {"line": 40, "column": null}}, "9": {"start": {"line": 41, "column": 6}, "end": {"line": 41, "column": null}}, "10": {"start": {"line": 45, "column": 28}, "end": {"line": 86, "column": null}}, "11": {"start": {"line": 46, "column": 6}, "end": {"line": 85, "column": null}}, "12": {"start": {"line": 48, "column": 8}, "end": {"line": 48, "column": null}}, "13": {"start": {"line": 51, "column": 8}, "end": {"line": 51, "column": null}}, "14": {"start": {"line": 52, "column": 8}, "end": {"line": 56, "column": null}}, "15": {"start": {"line": 59, "column": 8}, "end": {"line": 66, "column": null}}, "16": {"start": {"line": 60, "column": 10}, "end": {"line": 65, "column": null}}, "17": {"start": {"line": 61, "column": 12}, "end": {"line": 64, "column": null}}, "18": {"start": {"line": 68, "column": 8}, "end": {"line": 75, "column": null}}, "19": {"start": {"line": 69, "column": 10}, "end": {"line": 74, "column": null}}, "20": {"start": {"line": 70, "column": 12}, "end": {"line": 73, "column": null}}, "21": {"start": {"line": 77, "column": 8}, "end": {"line": 84, "column": null}}, "22": {"start": {"line": 78, "column": 10}, "end": {"line": 83, "column": null}}, "23": {"start": {"line": 79, "column": 12}, "end": {"line": 82, "column": null}}, "24": {"start": {"line": 89, "column": 4}, "end": {"line": 102, "column": null}}, "25": {"start": {"line": 90, "column": 6}, "end": {"line": 90, "column": null}}, "26": {"start": {"line": 93, "column": 25}, "end": {"line": 98, "column": null}}, "27": {"start": {"line": 94, "column": 8}, "end": {"line": 97, "column": null}}, "28": {"start": {"line": 95, "column": 10}, "end": {"line": 95, "column": null}}, "29": {"start": {"line": 96, "column": 10}, "end": {"line": 96, "column": null}}, "30": {"start": {"line": 101, "column": 6}, "end": {"line": 101, "column": null}}, "31": {"start": {"line": 101, "column": 23}, "end": {"line": 101, "column": 50}}, "32": {"start": {"line": 105, "column": 2}, "end": {"line": 107, "column": null}}, "33": {"start": {"line": 106, "column": 4}, "end": {"line": 106, "column": null}}, "34": {"start": {"line": 129, "column": 15}, "end": {"line": 129, "column": 25}}, "35": {"start": {"line": 132, "column": 26}, "end": {"line": 199, "column": null}}, "36": {"start": {"line": 135, "column": 4}, "end": {"line": 137, "column": null}}, "37": {"start": {"line": 136, "column": 6}, "end": {"line": 136, "column": null}}, "38": {"start": {"line": 142, "column": 4}, "end": {"line": 144, "column": null}}, "39": {"start": {"line": 143, "column": 6}, "end": {"line": 143, "column": null}}, "40": {"start": {"line": 149, "column": 4}, "end": {"line": 151, "column": null}}, "41": {"start": {"line": 150, "column": 6}, "end": {"line": 150, "column": null}}, "42": {"start": {"line": 156, "column": 4}, "end": {"line": 158, "column": null}}, "43": {"start": {"line": 157, "column": 6}, "end": {"line": 157, "column": null}}, "44": {"start": {"line": 163, "column": 4}, "end": {"line": 168, "column": null}}, "45": {"start": {"line": 164, "column": 6}, "end": {"line": 164, "column": null}}, "46": {"start": {"line": 164, "column": 25}, "end": {"line": 164, "column": null}}, "47": {"start": {"line": 165, "column": 6}, "end": {"line": 165, "column": null}}, "48": {"start": {"line": 165, "column": 22}, "end": {"line": 165, "column": null}}, "49": {"start": {"line": 166, "column": 6}, "end": {"line": 166, "column": null}}, "50": {"start": {"line": 166, "column": 22}, "end": {"line": 166, "column": null}}, "51": {"start": {"line": 167, "column": 6}, "end": {"line": 167, "column": null}}, "52": {"start": {"line": 167, "column": 23}, "end": {"line": 167, "column": null}}, "53": {"start": {"line": 173, "column": 4}, "end": {"line": 175, "column": null}}, "54": {"start": {"line": 174, "column": 6}, "end": {"line": 174, "column": null}}, "55": {"start": {"line": 180, "column": 4}, "end": {"line": 182, "column": null}}, "56": {"start": {"line": 181, "column": 6}, "end": {"line": 181, "column": null}}, "57": {"start": {"line": 187, "column": 4}, "end": {"line": 189, "column": null}}, "58": {"start": {"line": 188, "column": 6}, "end": {"line": 188, "column": null}}, "59": {"start": {"line": 190, "column": 4}, "end": {"line": 190, "column": null}}, "60": {"start": {"line": 195, "column": 4}, "end": {"line": 197, "column": null}}, "61": {"start": {"line": 196, "column": 6}, "end": {"line": 196, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 34, "column": 44}, "end": {"line": 34, "column": 45}}, "loc": {"start": {"line": 34, "column": 58}, "end": {"line": 127, "column": null}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 38, "column": 12}, "end": {"line": 38, "column": null}}, "loc": {"start": {"line": 38, "column": 12}, "end": {"line": 103, "column": 5}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 45, "column": 28}, "end": {"line": 45, "column": null}}, "loc": {"start": {"line": 45, "column": 28}, "end": {"line": 86, "column": null}}}, "3": {"name": "(anonymous_7)", "decl": {"start": {"line": 59, "column": 49}, "end": {"line": 59, "column": null}}, "loc": {"start": {"line": 59, "column": 49}, "end": {"line": 66, "column": 10}}}, "4": {"name": "(anonymous_8)", "decl": {"start": {"line": 68, "column": 50}, "end": {"line": 68, "column": null}}, "loc": {"start": {"line": 68, "column": 50}, "end": {"line": 75, "column": 10}}}, "5": {"name": "(anonymous_9)", "decl": {"start": {"line": 77, "column": 54}, "end": {"line": 77, "column": null}}, "loc": {"start": {"line": 77, "column": 54}, "end": {"line": 84, "column": 10}}}, "6": {"name": "(anonymous_10)", "decl": {"start": {"line": 93, "column": 37}, "end": {"line": 93, "column": null}}, "loc": {"start": {"line": 93, "column": 37}, "end": {"line": 98, "column": 9}}}, "7": {"name": "(anonymous_11)", "decl": {"start": {"line": 101, "column": 17}, "end": {"line": 101, "column": 23}}, "loc": {"start": {"line": 101, "column": 23}, "end": {"line": 101, "column": 50}}}, "8": {"name": "(anonymous_12)", "decl": {"start": {"line": 134, "column": 12}, "end": {"line": 134, "column": null}}, "loc": {"start": {"line": 134, "column": 12}, "end": {"line": 138, "column": null}}}, "9": {"name": "(anonymous_13)", "decl": {"start": {"line": 141, "column": 13}, "end": {"line": 141, "column": null}}, "loc": {"start": {"line": 141, "column": 13}, "end": {"line": 145, "column": null}}}, "10": {"name": "(anonymous_14)", "decl": {"start": {"line": 148, "column": 12}, "end": {"line": 148, "column": null}}, "loc": {"start": {"line": 148, "column": 12}, "end": {"line": 152, "column": null}}}, "11": {"name": "(anonymous_15)", "decl": {"start": {"line": 155, "column": 12}, "end": {"line": 155, "column": null}}, "loc": {"start": {"line": 155, "column": 12}, "end": {"line": 159, "column": null}}}, "12": {"name": "(anonymous_16)", "decl": {"start": {"line": 162, "column": 11}, "end": {"line": 162, "column": 12}}, "loc": {"start": {"line": 162, "column": 12}, "end": {"line": 169, "column": null}}}, "13": {"name": "(anonymous_17)", "decl": {"start": {"line": 172, "column": 15}, "end": {"line": 172, "column": 16}}, "loc": {"start": {"line": 172, "column": 16}, "end": {"line": 176, "column": null}}}, "14": {"name": "(anonymous_18)", "decl": {"start": {"line": 179, "column": 18}, "end": {"line": 179, "column": 19}}, "loc": {"start": {"line": 179, "column": 19}, "end": {"line": 183, "column": null}}}, "15": {"name": "(anonymous_19)", "decl": {"start": {"line": 186, "column": 19}, "end": {"line": 186, "column": null}}, "loc": {"start": {"line": 186, "column": 19}, "end": {"line": 191, "column": null}}}, "16": {"name": "(anonymous_20)", "decl": {"start": {"line": 194, "column": 15}, "end": {"line": 194, "column": 16}}, "loc": {"start": {"line": 194, "column": 16}, "end": {"line": 198, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 36, "column": 25}, "end": {"line": 36, "column": 78}}, "type": "binary-expr", "locations": [{"start": {"line": 36, "column": 25}, "end": {"line": 36, "column": 38}}, {"start": {"line": 36, "column": 38}, "end": {"line": 36, "column": 78}}]}, "1": {"loc": {"start": {"line": 39, "column": 4}, "end": {"line": 42, "column": null}}, "type": "if", "locations": [{"start": {"line": 39, "column": 4}, "end": {"line": 42, "column": null}}]}, "2": {"loc": {"start": {"line": 46, "column": 6}, "end": {"line": 85, "column": null}}, "type": "if", "locations": [{"start": {"line": 46, "column": 6}, "end": {"line": 85, "column": null}}]}, "3": {"loc": {"start": {"line": 46, "column": 10}, "end": {"line": 46, "column": 56}}, "type": "binary-expr", "locations": [{"start": {"line": 46, "column": 10}, "end": {"line": 46, "column": 43}}, {"start": {"line": 46, "column": 43}, "end": {"line": 46, "column": 56}}]}, "4": {"loc": {"start": {"line": 60, "column": 10}, "end": {"line": 65, "column": null}}, "type": "if", "locations": [{"start": {"line": 60, "column": 10}, "end": {"line": 65, "column": null}}]}, "5": {"loc": {"start": {"line": 60, "column": 14}, "end": {"line": 60, "column": 58}}, "type": "binary-expr", "locations": [{"start": {"line": 60, "column": 14}, "end": {"line": 60, "column": 47}}, {"start": {"line": 60, "column": 47}, "end": {"line": 60, "column": 58}}]}, "6": {"loc": {"start": {"line": 69, "column": 10}, "end": {"line": 74, "column": null}}, "type": "if", "locations": [{"start": {"line": 69, "column": 10}, "end": {"line": 74, "column": null}}]}, "7": {"loc": {"start": {"line": 69, "column": 14}, "end": {"line": 69, "column": 58}}, "type": "binary-expr", "locations": [{"start": {"line": 69, "column": 14}, "end": {"line": 69, "column": 47}}, {"start": {"line": 69, "column": 47}, "end": {"line": 69, "column": 58}}]}, "8": {"loc": {"start": {"line": 78, "column": 10}, "end": {"line": 83, "column": null}}, "type": "if", "locations": [{"start": {"line": 78, "column": 10}, "end": {"line": 83, "column": null}}]}, "9": {"loc": {"start": {"line": 78, "column": 14}, "end": {"line": 78, "column": 58}}, "type": "binary-expr", "locations": [{"start": {"line": 78, "column": 14}, "end": {"line": 78, "column": 47}}, {"start": {"line": 78, "column": 47}, "end": {"line": 78, "column": 58}}]}, "10": {"loc": {"start": {"line": 89, "column": 4}, "end": {"line": 102, "column": null}}, "type": "if", "locations": [{"start": {"line": 89, "column": 4}, "end": {"line": 102, "column": null}}, {"start": {"line": 91, "column": 11}, "end": {"line": 102, "column": null}}]}, "11": {"loc": {"start": {"line": 94, "column": 8}, "end": {"line": 97, "column": null}}, "type": "if", "locations": [{"start": {"line": 94, "column": 8}, "end": {"line": 97, "column": null}}]}, "12": {"loc": {"start": {"line": 105, "column": 2}, "end": {"line": 107, "column": null}}, "type": "if", "locations": [{"start": {"line": 105, "column": 2}, "end": {"line": 107, "column": null}}]}, "13": {"loc": {"start": {"line": 135, "column": 4}, "end": {"line": 137, "column": null}}, "type": "if", "locations": [{"start": {"line": 135, "column": 4}, "end": {"line": 137, "column": null}}]}, "14": {"loc": {"start": {"line": 135, "column": 8}, "end": {"line": 135, "column": 54}}, "type": "binary-expr", "locations": [{"start": {"line": 135, "column": 8}, "end": {"line": 135, "column": 41}}, {"start": {"line": 135, "column": 41}, "end": {"line": 135, "column": 54}}]}, "15": {"loc": {"start": {"line": 142, "column": 4}, "end": {"line": 144, "column": null}}, "type": "if", "locations": [{"start": {"line": 142, "column": 4}, "end": {"line": 144, "column": null}}]}, "16": {"loc": {"start": {"line": 142, "column": 8}, "end": {"line": 142, "column": 54}}, "type": "binary-expr", "locations": [{"start": {"line": 142, "column": 8}, "end": {"line": 142, "column": 41}}, {"start": {"line": 142, "column": 41}, "end": {"line": 142, "column": 54}}]}, "17": {"loc": {"start": {"line": 149, "column": 4}, "end": {"line": 151, "column": null}}, "type": "if", "locations": [{"start": {"line": 149, "column": 4}, "end": {"line": 151, "column": null}}]}, "18": {"loc": {"start": {"line": 149, "column": 8}, "end": {"line": 149, "column": 54}}, "type": "binary-expr", "locations": [{"start": {"line": 149, "column": 8}, "end": {"line": 149, "column": 41}}, {"start": {"line": 149, "column": 41}, "end": {"line": 149, "column": 54}}]}, "19": {"loc": {"start": {"line": 156, "column": 4}, "end": {"line": 158, "column": null}}, "type": "if", "locations": [{"start": {"line": 156, "column": 4}, "end": {"line": 158, "column": null}}]}, "20": {"loc": {"start": {"line": 156, "column": 8}, "end": {"line": 156, "column": 54}}, "type": "binary-expr", "locations": [{"start": {"line": 156, "column": 8}, "end": {"line": 156, "column": 41}}, {"start": {"line": 156, "column": 41}, "end": {"line": 156, "column": 54}}]}, "21": {"loc": {"start": {"line": 163, "column": 4}, "end": {"line": 168, "column": null}}, "type": "if", "locations": [{"start": {"line": 163, "column": 4}, "end": {"line": 168, "column": null}}]}, "22": {"loc": {"start": {"line": 163, "column": 8}, "end": {"line": 163, "column": 54}}, "type": "binary-expr", "locations": [{"start": {"line": 163, "column": 8}, "end": {"line": 163, "column": 41}}, {"start": {"line": 163, "column": 41}, "end": {"line": 163, "column": 54}}]}, "23": {"loc": {"start": {"line": 164, "column": 6}, "end": {"line": 164, "column": null}}, "type": "if", "locations": [{"start": {"line": 164, "column": 6}, "end": {"line": 164, "column": null}}]}, "24": {"loc": {"start": {"line": 165, "column": 6}, "end": {"line": 165, "column": null}}, "type": "if", "locations": [{"start": {"line": 165, "column": 6}, "end": {"line": 165, "column": null}}]}, "25": {"loc": {"start": {"line": 166, "column": 6}, "end": {"line": 166, "column": null}}, "type": "if", "locations": [{"start": {"line": 166, "column": 6}, "end": {"line": 166, "column": null}}]}, "26": {"loc": {"start": {"line": 167, "column": 6}, "end": {"line": 167, "column": null}}, "type": "if", "locations": [{"start": {"line": 167, "column": 6}, "end": {"line": 167, "column": null}}]}, "27": {"loc": {"start": {"line": 173, "column": 4}, "end": {"line": 175, "column": null}}, "type": "if", "locations": [{"start": {"line": 173, "column": 4}, "end": {"line": 175, "column": null}}]}, "28": {"loc": {"start": {"line": 173, "column": 8}, "end": {"line": 173, "column": 54}}, "type": "binary-expr", "locations": [{"start": {"line": 173, "column": 8}, "end": {"line": 173, "column": 41}}, {"start": {"line": 173, "column": 41}, "end": {"line": 173, "column": 54}}]}, "29": {"loc": {"start": {"line": 180, "column": 4}, "end": {"line": 182, "column": null}}, "type": "if", "locations": [{"start": {"line": 180, "column": 4}, "end": {"line": 182, "column": null}}]}, "30": {"loc": {"start": {"line": 180, "column": 8}, "end": {"line": 180, "column": 54}}, "type": "binary-expr", "locations": [{"start": {"line": 180, "column": 8}, "end": {"line": 180, "column": 41}}, {"start": {"line": 180, "column": 41}, "end": {"line": 180, "column": 54}}]}, "31": {"loc": {"start": {"line": 187, "column": 4}, "end": {"line": 189, "column": null}}, "type": "if", "locations": [{"start": {"line": 187, "column": 4}, "end": {"line": 189, "column": null}}]}, "32": {"loc": {"start": {"line": 187, "column": 8}, "end": {"line": 187, "column": 54}}, "type": "binary-expr", "locations": [{"start": {"line": 187, "column": 8}, "end": {"line": 187, "column": 41}}, {"start": {"line": 187, "column": 41}, "end": {"line": 187, "column": 54}}]}, "33": {"loc": {"start": {"line": 195, "column": 4}, "end": {"line": 197, "column": null}}, "type": "if", "locations": [{"start": {"line": 195, "column": 4}, "end": {"line": 197, "column": null}}]}, "34": {"loc": {"start": {"line": 195, "column": 8}, "end": {"line": 195, "column": 54}}, "type": "binary-expr", "locations": [{"start": {"line": 195, "column": 8}, "end": {"line": 195, "column": 41}}, {"start": {"line": 195, "column": 41}, "end": {"line": 195, "column": 54}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0}, "b": {"0": [0, 0], "1": [0], "2": [0], "3": [0, 0], "4": [0], "5": [0, 0], "6": [0], "7": [0, 0], "8": [0], "9": [0, 0], "10": [0, 0], "11": [0], "12": [0], "13": [0], "14": [0, 0], "15": [0], "16": [0, 0], "17": [0], "18": [0, 0], "19": [0], "20": [0, 0], "21": [0], "22": [0, 0], "23": [0], "24": [0], "25": [0], "26": [0], "27": [0], "28": [0, 0], "29": [0], "30": [0, 0], "31": [0], "32": [0, 0], "33": [0], "34": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\analytics\\GoogleAnalytics.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\analytics\\GoogleAnalytics.tsx", "statementMap": {"0": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 15}}, "1": {"start": {"line": 3, "column": 19}, "end": {"line": 3, "column": null}}, "2": {"start": {"line": 15, "column": 24}, "end": {"line": 39, "column": null}}, "3": {"start": {"line": 16, "column": 16}, "end": {"line": 16, "column": 59}}, "4": {"start": {"line": 19, "column": 2}, "end": {"line": 21, "column": null}}, "5": {"start": {"line": 20, "column": 4}, "end": {"line": 20, "column": null}}, "6": {"start": {"line": 41, "column": 15}, "end": {"line": 41, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 15, "column": 24}, "end": {"line": 15, "column": null}}, "loc": {"start": {"line": 15, "column": 24}, "end": {"line": 39, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 19, "column": 2}, "end": {"line": 21, "column": null}}, "type": "if", "locations": [{"start": {"line": 19, "column": 2}, "end": {"line": 21, "column": null}}]}, "1": {"loc": {"start": {"line": 19, "column": 6}, "end": {"line": 19, "column": 55}}, "type": "binary-expr", "locations": [{"start": {"line": 19, "column": 6}, "end": {"line": 19, "column": 16}}, {"start": {"line": 19, "column": 16}, "end": {"line": 19, "column": 55}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "f": {"0": 0}, "b": {"0": [0], "1": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\analytics\\WebVitals.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\analytics\\WebVitals.tsx", "statementMap": {"0": {"start": {"line": 88, "column": 0}, "end": {"line": 88, "column": 15}}, "1": {"start": {"line": 3, "column": 26}, "end": {"line": 3, "column": null}}, "2": {"start": {"line": 6, "column": 18}, "end": {"line": 86, "column": null}}, "3": {"start": {"line": 7, "column": 2}, "end": {"line": 83, "column": null}}, "4": {"start": {"line": 9, "column": 4}, "end": {"line": 82, "column": null}}, "5": {"start": {"line": 10, "column": 6}, "end": {"line": 81, "column": null}}, "6": {"start": {"line": 10, "column": 6}, "end": {"line": 10, "column": 27}}, "7": {"start": {"line": 12, "column": 8}, "end": {"line": 23, "column": null}}, "8": {"start": {"line": 13, "column": 10}, "end": {"line": 22, "column": null}}, "9": {"start": {"line": 27, "column": 8}, "end": {"line": 38, "column": null}}, "10": {"start": {"line": 28, "column": 10}, "end": {"line": 37, "column": null}}, "11": {"start": {"line": 40, "column": 8}, "end": {"line": 51, "column": null}}, "12": {"start": {"line": 41, "column": 10}, "end": {"line": 50, "column": null}}, "13": {"start": {"line": 53, "column": 8}, "end": {"line": 64, "column": null}}, "14": {"start": {"line": 54, "column": 10}, "end": {"line": 63, "column": null}}, "15": {"start": {"line": 67, "column": 8}, "end": {"line": 78, "column": null}}, "16": {"start": {"line": 68, "column": 10}, "end": {"line": 77, "column": null}}, "17": {"start": {"line": 80, "column": 8}, "end": {"line": 80, "column": null}}, "18": {"start": {"line": 85, "column": 2}, "end": {"line": 85, "column": 15}}, "19": {"start": {"line": 88, "column": 15}, "end": {"line": 88, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 6, "column": 18}, "end": {"line": 6, "column": null}}, "loc": {"start": {"line": 6, "column": 18}, "end": {"line": 86, "column": null}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 7, "column": 12}, "end": {"line": 7, "column": null}}, "loc": {"start": {"line": 7, "column": 12}, "end": {"line": 83, "column": 5}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 10, "column": 6}, "end": {"line": 10, "column": 13}}, "loc": {"start": {"line": 10, "column": 6}, "end": {"line": 10, "column": 27}}}, "3": {"name": "(anonymous_7)", "decl": {"start": {"line": 10, "column": 32}, "end": {"line": 10, "column": 33}}, "loc": {"start": {"line": 10, "column": 71}, "end": {"line": 79, "column": 9}}}, "4": {"name": "(anonymous_8)", "decl": {"start": {"line": 12, "column": 14}, "end": {"line": 12, "column": 15}}, "loc": {"start": {"line": 12, "column": 15}, "end": {"line": 23, "column": null}}}, "5": {"name": "(anonymous_9)", "decl": {"start": {"line": 27, "column": 14}, "end": {"line": 27, "column": 15}}, "loc": {"start": {"line": 27, "column": 15}, "end": {"line": 38, "column": null}}}, "6": {"name": "(anonymous_10)", "decl": {"start": {"line": 40, "column": 14}, "end": {"line": 40, "column": 15}}, "loc": {"start": {"line": 40, "column": 15}, "end": {"line": 51, "column": null}}}, "7": {"name": "(anonymous_11)", "decl": {"start": {"line": 53, "column": 15}, "end": {"line": 53, "column": 16}}, "loc": {"start": {"line": 53, "column": 16}, "end": {"line": 64, "column": null}}}, "8": {"name": "(anonymous_12)", "decl": {"start": {"line": 67, "column": 14}, "end": {"line": 67, "column": 15}}, "loc": {"start": {"line": 67, "column": 15}, "end": {"line": 78, "column": null}}}, "9": {"name": "(anonymous_13)", "decl": {"start": {"line": 79, "column": 15}, "end": {"line": 79, "column": 16}}, "loc": {"start": {"line": 79, "column": 16}, "end": {"line": 81, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 9, "column": 4}, "end": {"line": 82, "column": null}}, "type": "if", "locations": [{"start": {"line": 9, "column": 4}, "end": {"line": 82, "column": null}}]}, "1": {"loc": {"start": {"line": 9, "column": 8}, "end": {"line": 9, "column": 93}}, "type": "binary-expr", "locations": [{"start": {"line": 9, "column": 8}, "end": {"line": 9, "column": 49}}, {"start": {"line": 9, "column": 49}, "end": {"line": 9, "column": 82}}, {"start": {"line": 9, "column": 82}, "end": {"line": 9, "column": 93}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "b": {"0": [0], "1": [0, 0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\analytics\\index.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\analytics\\index.ts", "statementMap": {"0": {"start": {"line": 9, "column": 20}, "end": {"line": 9, "column": 37}}, "1": {"start": {"line": 8, "column": 20}, "end": {"line": 8, "column": 43}}, "2": {"start": {"line": 10, "column": 20}, "end": {"line": 10, "column": 37}}, "3": {"start": {"line": 8, "column": 43}, "end": {"line": 8, "column": null}}, "4": {"start": {"line": 9, "column": 37}, "end": {"line": 9, "column": null}}, "5": {"start": {"line": 10, "column": 37}, "end": {"line": 10, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "f": {}, "b": {}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\layout.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\layout.tsx", "statementMap": {"0": {"start": {"line": 90, "column": 24}, "end": {"line": 90, "column": 35}}, "1": {"start": {"line": 19, "column": 13}, "end": {"line": 19, "column": 21}}, "2": {"start": {"line": 2, "column": 22}, "end": {"line": 2, "column": null}}, "3": {"start": {"line": 3, "column": 7}, "end": {"line": 3, "column": null}}, "4": {"start": {"line": 4, "column": 28}, "end": {"line": 4, "column": null}}, "5": {"start": {"line": 5, "column": 22}, "end": {"line": 5, "column": null}}, "6": {"start": {"line": 6, "column": 31}, "end": {"line": 6, "column": null}}, "7": {"start": {"line": 7, "column": 27}, "end": {"line": 7, "column": null}}, "8": {"start": {"line": 8, "column": 22}, "end": {"line": 8, "column": null}}, "9": {"start": {"line": 9, "column": 30}, "end": {"line": 9, "column": null}}, "10": {"start": {"line": 10, "column": 18}, "end": {"line": 10, "column": null}}, "11": {"start": {"line": 12, "column": 14}, "end": {"line": 17, "column": null}}, "12": {"start": {"line": 19, "column": 34}, "end": {"line": 88, "column": null}}}, "fnMap": {"0": {"name": "RootLayout", "decl": {"start": {"line": 90, "column": 24}, "end": {"line": 90, "column": 35}}, "loc": {"start": {"line": 94, "column": 2}, "end": {"line": 117, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 45, "column": 24}, "end": {"line": 45, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 45, "column": 24}, "end": {"line": 45, "column": 56}}, {"start": {"line": 45, "column": 60}, "end": {"line": 45, "column": null}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0}, "f": {"0": 0}, "b": {"0": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\page.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\page.tsx", "statementMap": {"0": {"start": {"line": 39, "column": 24}, "end": {"line": 39, "column": null}}, "1": {"start": {"line": 1, "column": 20}, "end": {"line": 1, "column": null}}, "2": {"start": {"line": 2, "column": 19}, "end": {"line": 2, "column": null}}, "3": {"start": {"line": 3, "column": 17}, "end": {"line": 3, "column": null}}, "4": {"start": {"line": 4, "column": 18}, "end": {"line": 4, "column": null}}, "5": {"start": {"line": 7, "column": 38}, "end": {"line": 7, "column": null}}, "6": {"start": {"line": 11, "column": 39}, "end": {"line": 11, "column": null}}, "7": {"start": {"line": 15, "column": 30}, "end": {"line": 15, "column": null}}, "8": {"start": {"line": 19, "column": 35}, "end": {"line": 19, "column": null}}, "9": {"start": {"line": 23, "column": 39}, "end": {"line": 23, "column": null}}, "10": {"start": {"line": 27, "column": 30}, "end": {"line": 27, "column": null}}, "11": {"start": {"line": 31, "column": 29}, "end": {"line": 31, "column": null}}, "12": {"start": {"line": 35, "column": 41}, "end": {"line": 35, "column": null}}, "13": {"start": {"line": 36, "column": 17}, "end": {"line": 36, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_5)", "decl": {"start": {"line": 7, "column": 32}, "end": {"line": 7, "column": 38}}, "loc": {"start": {"line": 7, "column": 38}, "end": {"line": 7, "column": null}}}, "1": {"name": "(anonymous_6)", "decl": {"start": {"line": 7, "column": 38}, "end": {"line": 7, "column": 45}}, "loc": {"start": {"line": 7, "column": 38}, "end": {"line": 7, "column": null}}}, "2": {"name": "(anonymous_9)", "decl": {"start": {"line": 11, "column": 33}, "end": {"line": 11, "column": 39}}, "loc": {"start": {"line": 11, "column": 39}, "end": {"line": 11, "column": null}}}, "3": {"name": "(anonymous_10)", "decl": {"start": {"line": 11, "column": 39}, "end": {"line": 11, "column": 46}}, "loc": {"start": {"line": 11, "column": 39}, "end": {"line": 11, "column": null}}}, "4": {"name": "(anonymous_13)", "decl": {"start": {"line": 15, "column": 24}, "end": {"line": 15, "column": 30}}, "loc": {"start": {"line": 15, "column": 30}, "end": {"line": 15, "column": null}}}, "5": {"name": "(anonymous_14)", "decl": {"start": {"line": 15, "column": 30}, "end": {"line": 15, "column": 37}}, "loc": {"start": {"line": 15, "column": 30}, "end": {"line": 15, "column": null}}}, "6": {"name": "(anonymous_17)", "decl": {"start": {"line": 19, "column": 29}, "end": {"line": 19, "column": 35}}, "loc": {"start": {"line": 19, "column": 35}, "end": {"line": 19, "column": null}}}, "7": {"name": "(anonymous_18)", "decl": {"start": {"line": 19, "column": 35}, "end": {"line": 19, "column": 42}}, "loc": {"start": {"line": 19, "column": 35}, "end": {"line": 19, "column": null}}}, "8": {"name": "(anonymous_21)", "decl": {"start": {"line": 23, "column": 33}, "end": {"line": 23, "column": 39}}, "loc": {"start": {"line": 23, "column": 39}, "end": {"line": 23, "column": null}}}, "9": {"name": "(anonymous_22)", "decl": {"start": {"line": 23, "column": 39}, "end": {"line": 23, "column": 46}}, "loc": {"start": {"line": 23, "column": 39}, "end": {"line": 23, "column": null}}}, "10": {"name": "(anonymous_25)", "decl": {"start": {"line": 27, "column": 24}, "end": {"line": 27, "column": 30}}, "loc": {"start": {"line": 27, "column": 30}, "end": {"line": 27, "column": null}}}, "11": {"name": "(anonymous_26)", "decl": {"start": {"line": 27, "column": 30}, "end": {"line": 27, "column": 37}}, "loc": {"start": {"line": 27, "column": 30}, "end": {"line": 27, "column": null}}}, "12": {"name": "(anonymous_29)", "decl": {"start": {"line": 31, "column": 23}, "end": {"line": 31, "column": 29}}, "loc": {"start": {"line": 31, "column": 29}, "end": {"line": 31, "column": null}}}, "13": {"name": "(anonymous_30)", "decl": {"start": {"line": 31, "column": 29}, "end": {"line": 31, "column": 36}}, "loc": {"start": {"line": 31, "column": 29}, "end": {"line": 31, "column": null}}}, "14": {"name": "(anonymous_33)", "decl": {"start": {"line": 35, "column": 35}, "end": {"line": 35, "column": 41}}, "loc": {"start": {"line": 35, "column": 41}, "end": {"line": 35, "column": null}}}, "15": {"name": "(anonymous_34)", "decl": {"start": {"line": 35, "column": 41}, "end": {"line": 35, "column": 48}}, "loc": {"start": {"line": 35, "column": 41}, "end": {"line": 35, "column": null}}}, "16": {"name": "(anonymous_36)", "decl": {"start": {"line": 36, "column": 11}, "end": {"line": 36, "column": 17}}, "loc": {"start": {"line": 36, "column": 17}, "end": {"line": 36, "column": null}}}, "17": {"name": "Home", "decl": {"start": {"line": 39, "column": 24}, "end": {"line": 39, "column": null}}, "loc": {"start": {"line": 39, "column": 24}, "end": {"line": 60, "column": null}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0}, "b": {}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\robots.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\robots.ts", "statementMap": {"0": {"start": {"line": 3, "column": 24}, "end": {"line": 3, "column": null}}, "1": {"start": {"line": 4, "column": 18}, "end": {"line": 4, "column": null}}, "2": {"start": {"line": 6, "column": 2}, "end": {"line": 19, "column": null}}}, "fnMap": {"0": {"name": "robots", "decl": {"start": {"line": 3, "column": 24}, "end": {"line": 3, "column": null}}, "loc": {"start": {"line": 3, "column": 24}, "end": {"line": 20, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 4, "column": 18}, "end": {"line": 4, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 4, "column": 18}, "end": {"line": 4, "column": 50}}, {"start": {"line": 4, "column": 54}, "end": {"line": 4, "column": null}}]}}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0}, "b": {"0": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\sitemap.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\sitemap.ts", "statementMap": {"0": {"start": {"line": 4, "column": 30}, "end": {"line": 4, "column": null}}, "1": {"start": {"line": 5, "column": 18}, "end": {"line": 5, "column": null}}, "2": {"start": {"line": 11, "column": 28}, "end": {"line": 11, "column": 30}}, "3": {"start": {"line": 14, "column": 22}, "end": {"line": 45, "column": null}}, "4": {"start": {"line": 48, "column": 20}, "end": {"line": 53, "column": null}}, "5": {"start": {"line": 48, "column": 41}, "end": {"line": 53, "column": null}}, "6": {"start": {"line": 55, "column": 2}, "end": {"line": 55, "column": null}}}, "fnMap": {"0": {"name": "sitemap", "decl": {"start": {"line": 4, "column": 30}, "end": {"line": 4, "column": null}}, "loc": {"start": {"line": 4, "column": 30}, "end": {"line": 56, "column": null}}}, "1": {"name": "(anonymous_2)", "decl": {"start": {"line": 48, "column": 30}, "end": {"line": 48, "column": 31}}, "loc": {"start": {"line": 48, "column": 41}, "end": {"line": 53, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 5, "column": 18}, "end": {"line": 5, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 5, "column": 18}, "end": {"line": 5, "column": 50}}, {"start": {"line": 5, "column": 54}, "end": {"line": 5, "column": null}}]}, "1": {"loc": {"start": {"line": 50, "column": 27}, "end": {"line": 50, "column": 62}}, "type": "binary-expr", "locations": [{"start": {"line": 50, "column": 27}, "end": {"line": 50, "column": 42}}, {"start": {"line": 50, "column": 46}, "end": {"line": 50, "column": 62}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0, 0], "1": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\about\\page.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\about\\page.tsx", "statementMap": {"0": {"start": {"line": 35, "column": 24}, "end": {"line": 35, "column": null}}, "1": {"start": {"line": 7, "column": 13}, "end": {"line": 7, "column": 21}}, "2": {"start": {"line": 1, "column": 19}, "end": {"line": 1, "column": null}}, "3": {"start": {"line": 2, "column": 19}, "end": {"line": 2, "column": null}}, "4": {"start": {"line": 3, "column": 20}, "end": {"line": 3, "column": null}}, "5": {"start": {"line": 4, "column": 25}, "end": {"line": 4, "column": null}}, "6": {"start": {"line": 5, "column": 26}, "end": {"line": 5, "column": null}}, "7": {"start": {"line": 7, "column": 24}, "end": {"line": 33, "column": null}}}, "fnMap": {"0": {"name": "AboutPage", "decl": {"start": {"line": 35, "column": 24}, "end": {"line": 35, "column": null}}, "loc": {"start": {"line": 35, "column": 24}, "end": {"line": 59, "column": 1}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "f": {"0": 0}, "b": {}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\api\\newsletter\\route.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\api\\newsletter\\route.ts", "statementMap": {"0": {"start": {"line": 138, "column": 22}, "end": {"line": 138, "column": 28}}, "1": {"start": {"line": 124, "column": 22}, "end": {"line": 124, "column": 25}}, "2": {"start": {"line": 20, "column": 22}, "end": {"line": 20, "column": 26}}, "3": {"start": {"line": 131, "column": 22}, "end": {"line": 131, "column": 25}}, "4": {"start": {"line": 1, "column": 42}, "end": {"line": 1, "column": null}}, "5": {"start": {"line": 21, "column": 2}, "end": {"line": 120, "column": null}}, "6": {"start": {"line": 22, "column": 22}, "end": {"line": 22, "column": null}}, "7": {"start": {"line": 25, "column": 4}, "end": {"line": 30, "column": null}}, "8": {"start": {"line": 26, "column": 6}, "end": {"line": 28, "column": null}}, "9": {"start": {"line": 33, "column": 19}, "end": {"line": 33, "column": 48}}, "10": {"start": {"line": 34, "column": 19}, "end": {"line": 34, "column": 48}}, "11": {"start": {"line": 36, "column": 4}, "end": {"line": 42, "column": null}}, "12": {"start": {"line": 37, "column": 6}, "end": {"line": 37, "column": null}}, "13": {"start": {"line": 38, "column": 6}, "end": {"line": 40, "column": null}}, "14": {"start": {"line": 45, "column": 23}, "end": {"line": 45, "column": 43}}, "15": {"start": {"line": 47, "column": 4}, "end": {"line": 53, "column": null}}, "16": {"start": {"line": 48, "column": 6}, "end": {"line": 48, "column": null}}, "17": {"start": {"line": 49, "column": 6}, "end": {"line": 51, "column": null}}, "18": {"start": {"line": 56, "column": 40}, "end": {"line": 62, "column": null}}, "19": {"start": {"line": 65, "column": 16}, "end": {"line": 65, "column": 85}}, "20": {"start": {"line": 67, "column": 21}, "end": {"line": 74, "column": null}}, "21": {"start": {"line": 76, "column": 17}, "end": {"line": 76, "column": null}}, "22": {"start": {"line": 78, "column": 4}, "end": {"line": 112, "column": null}}, "23": {"start": {"line": 80, "column": 6}, "end": {"line": 85, "column": null}}, "24": {"start": {"line": 89, "column": 20}, "end": {"line": 89, "column": null}}, "25": {"start": {"line": 91, "column": 6}, "end": {"line": 96, "column": null}}, "26": {"start": {"line": 92, "column": 8}, "end": {"line": 94, "column": null}}, "27": {"start": {"line": 98, "column": 6}, "end": {"line": 103, "column": null}}, "28": {"start": {"line": 99, "column": 8}, "end": {"line": 101, "column": null}}, "29": {"start": {"line": 106, "column": 6}, "end": {"line": 106, "column": null}}, "30": {"start": {"line": 108, "column": 6}, "end": {"line": 110, "column": null}}, "31": {"start": {"line": 114, "column": 4}, "end": {"line": 114, "column": null}}, "32": {"start": {"line": 116, "column": 4}, "end": {"line": 118, "column": null}}, "33": {"start": {"line": 125, "column": 2}, "end": {"line": 127, "column": null}}, "34": {"start": {"line": 132, "column": 2}, "end": {"line": 134, "column": null}}, "35": {"start": {"line": 139, "column": 2}, "end": {"line": 141, "column": null}}}, "fnMap": {"0": {"name": "POST", "decl": {"start": {"line": 20, "column": 22}, "end": {"line": 20, "column": 26}}, "loc": {"start": {"line": 20, "column": 47}, "end": {"line": 121, "column": null}}}, "1": {"name": "GET", "decl": {"start": {"line": 124, "column": 22}, "end": {"line": 124, "column": 25}}, "loc": {"start": {"line": 124, "column": 22}, "end": {"line": 129, "column": null}}}, "2": {"name": "PUT", "decl": {"start": {"line": 131, "column": 22}, "end": {"line": 131, "column": 25}}, "loc": {"start": {"line": 131, "column": 22}, "end": {"line": 136, "column": null}}}, "3": {"name": "DELETE", "decl": {"start": {"line": 138, "column": 22}, "end": {"line": 138, "column": 28}}, "loc": {"start": {"line": 138, "column": 22}, "end": {"line": 143, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 25, "column": 4}, "end": {"line": 30, "column": null}}, "type": "if", "locations": [{"start": {"line": 25, "column": 4}, "end": {"line": 30, "column": null}}]}, "1": {"loc": {"start": {"line": 25, "column": 8}, "end": {"line": 25, "column": 40}}, "type": "binary-expr", "locations": [{"start": {"line": 25, "column": 8}, "end": {"line": 25, "column": 18}}, {"start": {"line": 25, "column": 18}, "end": {"line": 25, "column": 40}}]}, "2": {"loc": {"start": {"line": 36, "column": 4}, "end": {"line": 42, "column": null}}, "type": "if", "locations": [{"start": {"line": 36, "column": 4}, "end": {"line": 42, "column": null}}]}, "3": {"loc": {"start": {"line": 36, "column": 8}, "end": {"line": 36, "column": 28}}, "type": "binary-expr", "locations": [{"start": {"line": 36, "column": 8}, "end": {"line": 36, "column": 19}}, {"start": {"line": 36, "column": 19}, "end": {"line": 36, "column": 28}}]}, "4": {"loc": {"start": {"line": 47, "column": 4}, "end": {"line": 53, "column": null}}, "type": "if", "locations": [{"start": {"line": 47, "column": 4}, "end": {"line": 53, "column": null}}]}, "5": {"loc": {"start": {"line": 78, "column": 4}, "end": {"line": 112, "column": null}}, "type": "if", "locations": [{"start": {"line": 78, "column": 4}, "end": {"line": 112, "column": null}}, {"start": {"line": 87, "column": 11}, "end": {"line": 112, "column": null}}]}, "6": {"loc": {"start": {"line": 91, "column": 6}, "end": {"line": 96, "column": null}}, "type": "if", "locations": [{"start": {"line": 91, "column": 6}, "end": {"line": 96, "column": null}}]}, "7": {"loc": {"start": {"line": 98, "column": 6}, "end": {"line": 103, "column": null}}, "type": "if", "locations": [{"start": {"line": 98, "column": 6}, "end": {"line": 103, "column": null}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0}, "b": {"0": [0], "1": [0, 0], "2": [0], "3": [0, 0], "4": [0], "5": [0, 0], "6": [0], "7": [0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\blog\\page.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\blog\\page.tsx", "statementMap": {"0": {"start": {"line": 22, "column": 30}, "end": {"line": 22, "column": 39}}, "1": {"start": {"line": 8, "column": 13}, "end": {"line": 8, "column": 21}}, "2": {"start": {"line": 1, "column": 18}, "end": {"line": 1, "column": null}}, "3": {"start": {"line": 2, "column": 17}, "end": {"line": 2, "column": null}}, "4": {"start": {"line": 4, "column": 43}, "end": {"line": 4, "column": null}}, "5": {"start": {"line": 5, "column": 19}, "end": {"line": 5, "column": null}}, "6": {"start": {"line": 6, "column": 19}, "end": {"line": 6, "column": null}}, "7": {"start": {"line": 8, "column": 34}, "end": {"line": 16, "column": null}}, "8": {"start": {"line": 23, "column": 23}, "end": {"line": 23, "column": null}}, "9": {"start": {"line": 25, "column": 27}, "end": {"line": 47, "column": null}}, "10": {"start": {"line": 49, "column": 22}, "end": {"line": 86, "column": null}}, "11": {"start": {"line": 88, "column": 16}, "end": {"line": 88, "column": null}}, "12": {"start": {"line": 89, "column": 21}, "end": {"line": 89, "column": null}}, "13": {"start": {"line": 92, "column": 24}, "end": {"line": 96, "column": null}}, "14": {"start": {"line": 94, "column": 8}, "end": {"line": 94, "column": null}}, "15": {"start": {"line": 94, "column": 36}, "end": {"line": 94, "column": null}}, "16": {"start": {"line": 98, "column": 27}, "end": {"line": 100, "column": null}}, "17": {"start": {"line": 99, "column": 29}, "end": {"line": 99, "column": null}}, "18": {"start": {"line": 102, "column": 21}, "end": {"line": 108, "column": null}}, "19": {"start": {"line": 103, "column": 4}, "end": {"line": 107, "column": null}}, "20": {"start": {"line": 145, "column": 16}, "end": {"line": 146, "column": null}}, "21": {"start": {"line": 186, "column": 18}, "end": {"line": 187, "column": null}}, "22": {"start": {"line": 198, "column": 26}, "end": {"line": 199, "column": null}}}, "fnMap": {"0": {"name": "BlogPage", "decl": {"start": {"line": 22, "column": 30}, "end": {"line": 22, "column": 39}}, "loc": {"start": {"line": 22, "column": 70}, "end": {"line": 261, "column": 1}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 93, "column": 19}, "end": {"line": 93, "column": null}}, "loc": {"start": {"line": 94, "column": 8}, "end": {"line": 94, "column": null}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 94, "column": 29}, "end": {"line": 94, "column": 36}}, "loc": {"start": {"line": 94, "column": 36}, "end": {"line": 94, "column": null}}}, "3": {"name": "(anonymous_7)", "decl": {"start": {"line": 99, "column": 22}, "end": {"line": 99, "column": 29}}, "loc": {"start": {"line": 99, "column": 29}, "end": {"line": 99, "column": null}}}, "4": {"name": "(anonymous_8)", "decl": {"start": {"line": 102, "column": 21}, "end": {"line": 102, "column": 22}}, "loc": {"start": {"line": 102, "column": 22}, "end": {"line": 108, "column": null}}}, "5": {"name": "(anonymous_9)", "decl": {"start": {"line": 144, "column": 30}, "end": {"line": 144, "column": 31}}, "loc": {"start": {"line": 145, "column": 16}, "end": {"line": 146, "column": null}}}, "6": {"name": "(anonymous_10)", "decl": {"start": {"line": 185, "column": 35}, "end": {"line": 185, "column": 36}}, "loc": {"start": {"line": 186, "column": 18}, "end": {"line": 187, "column": null}}}, "7": {"name": "(anonymous_11)", "decl": {"start": {"line": 197, "column": 45}, "end": {"line": 197, "column": 46}}, "loc": {"start": {"line": 198, "column": 26}, "end": {"line": 199, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 92, "column": 24}, "end": {"line": 96, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 93, "column": 6}, "end": {"line": 94, "column": null}}, {"start": {"line": 96, "column": 6}, "end": {"line": 96, "column": null}}]}, "1": {"loc": {"start": {"line": 98, "column": 27}, "end": {"line": 100, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 99, "column": 6}, "end": {"line": 99, "column": null}}, {"start": {"line": 100, "column": 6}, "end": {"line": 100, "column": null}}]}, "2": {"loc": {"start": {"line": 119, "column": 15}, "end": {"line": 119, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 119, "column": 34}, "end": {"line": 119, "column": 56}}, {"start": {"line": 119, "column": 59}, "end": {"line": 119, "column": null}}]}, "3": {"loc": {"start": {"line": 122, "column": 15}, "end": {"line": 124, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 123, "column": 18}, "end": {"line": 123, "column": 106}}, {"start": {"line": 124, "column": 18}, "end": {"line": 124, "column": null}}]}, "4": {"loc": {"start": {"line": 123, "column": 18}, "end": {"line": 123, "column": 106}}, "type": "binary-expr", "locations": [{"start": {"line": 123, "column": 18}, "end": {"line": 123, "column": 46}}, {"start": {"line": 123, "column": 50}, "end": {"line": 123, "column": 106}}]}, "5": {"loc": {"start": {"line": 137, "column": 18}, "end": {"line": 139, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 138, "column": 22}, "end": {"line": 138, "column": null}}, {"start": {"line": 139, "column": 22}, "end": {"line": 139, "column": null}}]}, "6": {"loc": {"start": {"line": 149, "column": 20}, "end": {"line": 151, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 150, "column": 24}, "end": {"line": 150, "column": null}}, {"start": {"line": 151, "column": 24}, "end": {"line": 151, "column": null}}]}, "7": {"loc": {"start": {"line": 165, "column": 14}, "end": {"line": 184, "column": 15}}, "type": "cond-expr", "locations": [{"start": {"line": 165, "column": 14}, "end": {"line": 184, "column": 15}}]}, "8": {"loc": {"start": {"line": 170, "column": 19}, "end": {"line": 172, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 171, "column": 22}, "end": {"line": 171, "column": 81}}, {"start": {"line": 172, "column": 22}, "end": {"line": 172, "column": null}}]}, "9": {"loc": {"start": {"line": 219, "column": 23}, "end": {"line": 219, "column": 35}}, "type": "binary-expr", "locations": [{"start": {"line": 219, "column": 23}, "end": {"line": 219, "column": 35}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0], "8": [0, 0], "9": [0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\blog\\[slug]\\page.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\blog\\[slug]\\page.tsx", "statementMap": {"0": {"start": {"line": 32, "column": 30}, "end": {"line": 32, "column": 43}}, "1": {"start": {"line": 17, "column": 22}, "end": {"line": 17, "column": 38}}, "2": {"start": {"line": 1, "column": 18}, "end": {"line": 1, "column": null}}, "3": {"start": {"line": 2, "column": 17}, "end": {"line": 2, "column": null}}, "4": {"start": {"line": 3, "column": 18}, "end": {"line": 3, "column": null}}, "5": {"start": {"line": 5, "column": 25}, "end": {"line": 5, "column": null}}, "6": {"start": {"line": 6, "column": 54}, "end": {"line": 6, "column": null}}, "7": {"start": {"line": 7, "column": 19}, "end": {"line": 7, "column": null}}, "8": {"start": {"line": 8, "column": 19}, "end": {"line": 8, "column": null}}, "9": {"start": {"line": 9, "column": 25}, "end": {"line": 9, "column": null}}, "10": {"start": {"line": 18, "column": 19}, "end": {"line": 18, "column": null}}, "11": {"start": {"line": 21, "column": 2}, "end": {"line": 29, "column": null}}, "12": {"start": {"line": 33, "column": 19}, "end": {"line": 33, "column": null}}, "13": {"start": {"line": 36, "column": 21}, "end": {"line": 70, "column": null}}, "14": {"start": {"line": 72, "column": 15}, "end": {"line": 72, "column": null}}, "15": {"start": {"line": 74, "column": 2}, "end": {"line": 76, "column": null}}, "16": {"start": {"line": 75, "column": 4}, "end": {"line": 75, "column": null}}, "17": {"start": {"line": 79, "column": 35}, "end": {"line": 79, "column": 37}}, "18": {"start": {"line": 81, "column": 21}, "end": {"line": 87, "column": null}}, "19": {"start": {"line": 82, "column": 4}, "end": {"line": 86, "column": null}}, "20": {"start": {"line": 90, "column": 17}, "end": {"line": 121, "column": null}}, "21": {"start": {"line": 153, "column": 18}, "end": {"line": 154, "column": null}}, "22": {"start": {"line": 220, "column": 20}, "end": {"line": 221, "column": null}}}, "fnMap": {"0": {"name": "generateMetadata", "decl": {"start": {"line": 17, "column": 22}, "end": {"line": 17, "column": 38}}, "loc": {"start": {"line": 17, "column": 68}, "end": {"line": 30, "column": null}}}, "1": {"name": "BlogPostPage", "decl": {"start": {"line": 32, "column": 30}, "end": {"line": 32, "column": 43}}, "loc": {"start": {"line": 32, "column": 72}, "end": {"line": 279, "column": 1}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 81, "column": 21}, "end": {"line": 81, "column": 22}}, "loc": {"start": {"line": 81, "column": 22}, "end": {"line": 87, "column": null}}}, "3": {"name": "(anonymous_7)", "decl": {"start": {"line": 152, "column": 37}, "end": {"line": 152, "column": 38}}, "loc": {"start": {"line": 153, "column": 18}, "end": {"line": 154, "column": null}}}, "4": {"name": "(anonymous_8)", "decl": {"start": {"line": 219, "column": 36}, "end": {"line": 219, "column": 37}}, "loc": {"start": {"line": 220, "column": 20}, "end": {"line": 221, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 74, "column": 2}, "end": {"line": 76, "column": null}}, "type": "if", "locations": [{"start": {"line": 74, "column": 2}, "end": {"line": 76, "column": null}}]}, "1": {"loc": {"start": {"line": 169, "column": 15}, "end": {"line": 169, "column": 27}}, "type": "binary-expr", "locations": [{"start": {"line": 169, "column": 15}, "end": {"line": 169, "column": 27}}]}, "2": {"loc": {"start": {"line": 190, "column": 18}, "end": {"line": 199, "column": 19}}, "type": "cond-expr", "locations": [{"start": {"line": 190, "column": 18}, "end": {"line": 199, "column": 19}}]}, "3": {"loc": {"start": {"line": 211, "column": 11}, "end": {"line": 211, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 211, "column": 11}, "end": {"line": 211, "column": null}}]}, "4": {"loc": {"start": {"line": 226, "column": 26}, "end": {"line": 234, "column": 27}}, "type": "cond-expr", "locations": [{"start": {"line": 226, "column": 26}, "end": {"line": 234, "column": 27}}]}, "5": {"loc": {"start": {"line": 227, "column": 33}, "end": {"line": 227, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 227, "column": 77}, "end": {"line": 227, "column": 98}}, {"start": {"line": 227, "column": 101}, "end": {"line": 227, "column": null}}]}, "6": {"loc": {"start": {"line": 248, "column": 25}, "end": {"line": 248, "column": 44}}, "type": "binary-expr", "locations": [{"start": {"line": 248, "column": 25}, "end": {"line": 248, "column": 44}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "b": {"0": [0], "1": [0], "2": [0], "3": [0], "4": [0], "5": [0, 0], "6": [0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\faq\\FAQClient.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\faq\\FAQClient.tsx", "statementMap": {"0": {"start": {"line": 16, "column": 24}, "end": {"line": 16, "column": 34}}, "1": {"start": {"line": 3, "column": 41}, "end": {"line": 3, "column": null}}, "2": {"start": {"line": 4, "column": 17}, "end": {"line": 4, "column": null}}, "3": {"start": {"line": 5, "column": 61}, "end": {"line": 5, "column": null}}, "4": {"start": {"line": 6, "column": 40}, "end": {"line": 6, "column": null}}, "5": {"start": {"line": 7, "column": 19}, "end": {"line": 7, "column": null}}, "6": {"start": {"line": 8, "column": 19}, "end": {"line": 8, "column": null}}, "7": {"start": {"line": 9, "column": 25}, "end": {"line": 9, "column": null}}, "8": {"start": {"line": 17, "column": 38}, "end": {"line": 17, "column": null}}, "9": {"start": {"line": 18, "column": 36}, "end": {"line": 18, "column": null}}, "10": {"start": {"line": 19, "column": 44}, "end": {"line": 19, "column": null}}, "11": {"start": {"line": 22, "column": 17}, "end": {"line": 25, "column": null}}, "12": {"start": {"line": 23, "column": 21}, "end": {"line": 23, "column": null}}, "13": {"start": {"line": 23, "column": 50}, "end": {"line": 23, "column": 66}}, "14": {"start": {"line": 24, "column": 4}, "end": {"line": 24, "column": null}}, "15": {"start": {"line": 28, "column": 24}, "end": {"line": 43, "column": null}}, "16": {"start": {"line": 29, "column": 4}, "end": {"line": 42, "column": null}}, "17": {"start": {"line": 30, "column": 28}, "end": {"line": 35, "column": null}}, "18": {"start": {"line": 33, "column": 10}, "end": {"line": 35, "column": null}}, "19": {"start": {"line": 35, "column": 12}, "end": {"line": 35, "column": null}}, "20": {"start": {"line": 39, "column": 27}, "end": {"line": 39, "column": null}}, "21": {"start": {"line": 41, "column": 6}, "end": {"line": 41, "column": null}}, "22": {"start": {"line": 46, "column": 23}, "end": {"line": 56, "column": null}}, "23": {"start": {"line": 47, "column": 49}, "end": {"line": 47, "column": null}}, "24": {"start": {"line": 48, "column": 4}, "end": {"line": 54, "column": null}}, "25": {"start": {"line": 49, "column": 25}, "end": {"line": 49, "column": 41}}, "26": {"start": {"line": 50, "column": 6}, "end": {"line": 52, "column": null}}, "27": {"start": {"line": 51, "column": 8}, "end": {"line": 51, "column": null}}, "28": {"start": {"line": 53, "column": 6}, "end": {"line": 53, "column": null}}, "29": {"start": {"line": 55, "column": 4}, "end": {"line": 55, "column": null}}, "30": {"start": {"line": 58, "column": 21}, "end": {"line": 66, "column": null}}, "31": {"start": {"line": 59, "column": 25}, "end": {"line": 59, "column": null}}, "32": {"start": {"line": 60, "column": 4}, "end": {"line": 64, "column": null}}, "33": {"start": {"line": 61, "column": 6}, "end": {"line": 61, "column": null}}, "34": {"start": {"line": 63, "column": 6}, "end": {"line": 63, "column": null}}, "35": {"start": {"line": 65, "column": 4}, "end": {"line": 65, "column": null}}, "36": {"start": {"line": 69, "column": 17}, "end": {"line": 84, "column": null}}, "37": {"start": {"line": 72, "column": 38}, "end": {"line": 83, "column": null}}, "38": {"start": {"line": 78, "column": 10}, "end": {"line": 80, "column": null}}, "39": {"start": {"line": 79, "column": 42}, "end": {"line": 79, "column": 52}}, "40": {"start": {"line": 119, "column": 35}, "end": {"line": 119, "column": null}}, "41": {"start": {"line": 127, "column": 33}, "end": {"line": 127, "column": null}}, "42": {"start": {"line": 137, "column": 18}, "end": {"line": 138, "column": null}}, "43": {"start": {"line": 139, "column": 35}, "end": {"line": 139, "column": null}}, "44": {"start": {"line": 166, "column": 22}, "end": {"line": 166, "column": null}}, "45": {"start": {"line": 167, "column": 22}, "end": {"line": 167, "column": null}}, "46": {"start": {"line": 177, "column": 20}, "end": {"line": 177, "column": null}}, "47": {"start": {"line": 184, "column": 26}, "end": {"line": 185, "column": null}}, "48": {"start": {"line": 191, "column": 45}, "end": {"line": 191, "column": null}}, "49": {"start": {"line": 225, "column": 46}, "end": {"line": 226, "column": null}}}, "fnMap": {"0": {"name": "FAQClient", "decl": {"start": {"line": 16, "column": 24}, "end": {"line": 16, "column": 34}}, "loc": {"start": {"line": 16, "column": 67}, "end": {"line": 275, "column": null}}}, "1": {"name": "(anonymous_6)", "decl": {"start": {"line": 22, "column": 25}, "end": {"line": 22, "column": null}}, "loc": {"start": {"line": 22, "column": 25}, "end": {"line": 25, "column": 5}}}, "2": {"name": "(anonymous_7)", "decl": {"start": {"line": 23, "column": 42}, "end": {"line": 23, "column": 50}}, "loc": {"start": {"line": 23, "column": 50}, "end": {"line": 23, "column": 66}}}, "3": {"name": "(anonymous_8)", "decl": {"start": {"line": 28, "column": 32}, "end": {"line": 28, "column": null}}, "loc": {"start": {"line": 28, "column": 32}, "end": {"line": 43, "column": 5}}}, "4": {"name": "(anonymous_9)", "decl": {"start": {"line": 29, "column": 27}, "end": {"line": 29, "column": null}}, "loc": {"start": {"line": 29, "column": 27}, "end": {"line": 42, "column": null}}}, "5": {"name": "(anonymous_10)", "decl": {"start": {"line": 32, "column": 25}, "end": {"line": 32, "column": null}}, "loc": {"start": {"line": 33, "column": 10}, "end": {"line": 35, "column": null}}}, "6": {"name": "(anonymous_11)", "decl": {"start": {"line": 34, "column": 31}, "end": {"line": 34, "column": null}}, "loc": {"start": {"line": 35, "column": 12}, "end": {"line": 35, "column": null}}}, "7": {"name": "(anonymous_12)", "decl": {"start": {"line": 46, "column": 31}, "end": {"line": 46, "column": null}}, "loc": {"start": {"line": 46, "column": 31}, "end": {"line": 56, "column": 5}}}, "8": {"name": "(anonymous_13)", "decl": {"start": {"line": 48, "column": 26}, "end": {"line": 48, "column": null}}, "loc": {"start": {"line": 48, "column": 26}, "end": {"line": 54, "column": null}}}, "9": {"name": "(anonymous_14)", "decl": {"start": {"line": 58, "column": 21}, "end": {"line": 58, "column": 22}}, "loc": {"start": {"line": 58, "column": 22}, "end": {"line": 66, "column": null}}}, "10": {"name": "(anonymous_15)", "decl": {"start": {"line": 72, "column": 29}, "end": {"line": 72, "column": 38}}, "loc": {"start": {"line": 72, "column": 38}, "end": {"line": 83, "column": null}}}, "11": {"name": "(anonymous_16)", "decl": {"start": {"line": 77, "column": 30}, "end": {"line": 77, "column": null}}, "loc": {"start": {"line": 78, "column": 10}, "end": {"line": 80, "column": null}}}, "12": {"name": "(anonymous_17)", "decl": {"start": {"line": 79, "column": 33}, "end": {"line": 79, "column": 42}}, "loc": {"start": {"line": 79, "column": 42}, "end": {"line": 79, "column": 52}}}, "13": {"name": "(anonymous_18)", "decl": {"start": {"line": 119, "column": 28}, "end": {"line": 119, "column": 29}}, "loc": {"start": {"line": 119, "column": 35}, "end": {"line": 119, "column": null}}}, "14": {"name": "(anonymous_19)", "decl": {"start": {"line": 127, "column": 27}, "end": {"line": 127, "column": 33}}, "loc": {"start": {"line": 127, "column": 33}, "end": {"line": 127, "column": null}}}, "15": {"name": "(anonymous_20)", "decl": {"start": {"line": 136, "column": 28}, "end": {"line": 136, "column": 29}}, "loc": {"start": {"line": 137, "column": 18}, "end": {"line": 138, "column": null}}}, "16": {"name": "(anonymous_21)", "decl": {"start": {"line": 139, "column": 29}, "end": {"line": 139, "column": 35}}, "loc": {"start": {"line": 139, "column": 35}, "end": {"line": 139, "column": null}}}, "17": {"name": "(anonymous_22)", "decl": {"start": {"line": 165, "column": 29}, "end": {"line": 165, "column": null}}, "loc": {"start": {"line": 165, "column": 29}, "end": {"line": 168, "column": null}}}, "18": {"name": "(anonymous_23)", "decl": {"start": {"line": 176, "column": 52}, "end": {"line": 176, "column": 53}}, "loc": {"start": {"line": 177, "column": 20}, "end": {"line": 177, "column": null}}}, "19": {"name": "(anonymous_24)", "decl": {"start": {"line": 183, "column": 35}, "end": {"line": 183, "column": 36}}, "loc": {"start": {"line": 184, "column": 26}, "end": {"line": 185, "column": null}}}, "20": {"name": "(anonymous_25)", "decl": {"start": {"line": 191, "column": 39}, "end": {"line": 191, "column": 45}}, "loc": {"start": {"line": 191, "column": 45}, "end": {"line": 191, "column": null}}}, "21": {"name": "(anonymous_26)", "decl": {"start": {"line": 224, "column": 67}, "end": {"line": 224, "column": 68}}, "loc": {"start": {"line": 225, "column": 46}, "end": {"line": 226, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 16, "column": 36}, "end": {"line": 16, "column": 49}}, "type": "default-arg", "locations": [{"start": {"line": 16, "column": 47}, "end": {"line": 16, "column": 49}}]}, "1": {"loc": {"start": {"line": 30, "column": 28}, "end": {"line": 35, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 30, "column": 28}, "end": {"line": 30, "column": null}}, {"start": {"line": 31, "column": 8}, "end": {"line": 31, "column": null}}, {"start": {"line": 32, "column": 8}, "end": {"line": 35, "column": null}}]}, "2": {"loc": {"start": {"line": 33, "column": 10}, "end": {"line": 35, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 33, "column": 10}, "end": {"line": 33, "column": null}}, {"start": {"line": 34, "column": 10}, "end": {"line": 35, "column": null}}]}, "3": {"loc": {"start": {"line": 39, "column": 27}, "end": {"line": 39, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 39, "column": 27}, "end": {"line": 39, "column": 54}}, {"start": {"line": 39, "column": 54}, "end": {"line": 39, "column": null}}]}, "4": {"loc": {"start": {"line": 41, "column": 13}, "end": {"line": 41, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 41, "column": 13}, "end": {"line": 41, "column": 30}}, {"start": {"line": 41, "column": 30}, "end": {"line": 41, "column": null}}]}, "5": {"loc": {"start": {"line": 50, "column": 6}, "end": {"line": 52, "column": null}}, "type": "if", "locations": [{"start": {"line": 50, "column": 6}, "end": {"line": 52, "column": null}}]}, "6": {"loc": {"start": {"line": 60, "column": 4}, "end": {"line": 64, "column": null}}, "type": "if", "locations": [{"start": {"line": 60, "column": 4}, "end": {"line": 64, "column": null}}, {"start": {"line": 62, "column": 11}, "end": {"line": 64, "column": null}}]}, "7": {"loc": {"start": {"line": 78, "column": 10}, "end": {"line": 80, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 79, "column": 14}, "end": {"line": 79, "column": null}}, {"start": {"line": 80, "column": 14}, "end": {"line": 80, "column": null}}]}, "8": {"loc": {"start": {"line": 78, "column": 10}, "end": {"line": 78, "column": 51}}, "type": "binary-expr", "locations": [{"start": {"line": 78, "column": 10}, "end": {"line": 78, "column": 37}}, {"start": {"line": 78, "column": 37}, "end": {"line": 78, "column": 51}}]}, "9": {"loc": {"start": {"line": 129, "column": 20}, "end": {"line": 131, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 130, "column": 24}, "end": {"line": 130, "column": null}}, {"start": {"line": 131, "column": 24}, "end": {"line": 131, "column": null}}]}, "10": {"loc": {"start": {"line": 141, "column": 22}, "end": {"line": 143, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 142, "column": 26}, "end": {"line": 142, "column": null}}, {"start": {"line": 143, "column": 26}, "end": {"line": 143, "column": null}}]}, "11": {"loc": {"start": {"line": 157, "column": 16}, "end": {"line": 175, "column": 17}}, "type": "cond-expr", "locations": [{"start": {"line": 157, "column": 16}, "end": {"line": 175, "column": 17}}]}, "12": {"loc": {"start": {"line": 198, "column": 32}, "end": {"line": 200, "column": 33}}, "type": "cond-expr", "locations": [{"start": {"line": 198, "column": 32}, "end": {"line": 200, "column": 33}}]}, "13": {"loc": {"start": {"line": 205, "column": 31}, "end": {"line": 205, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 205, "column": 31}, "end": {"line": 205, "column": null}}]}, "14": {"loc": {"start": {"line": 218, "column": 39}, "end": {"line": 218, "column": 56}}, "type": "binary-expr", "locations": [{"start": {"line": 218, "column": 39}, "end": {"line": 218, "column": 56}}, {"start": {"line": 218, "column": 60}, "end": {"line": 218, "column": null}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0}, "b": {"0": [0], "1": [0, 0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0], "12": [0], "13": [0], "14": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\faq\\page.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\faq\\page.tsx", "statementMap": {"0": {"start": {"line": 24, "column": 24}, "end": {"line": 24, "column": null}}, "1": {"start": {"line": 22, "column": 13}, "end": {"line": 22, "column": 21}}, "2": {"start": {"line": 1, "column": 18}, "end": {"line": 1, "column": null}}, "3": {"start": {"line": 3, "column": 22}, "end": {"line": 3, "column": null}}, "4": {"start": {"line": 4, "column": 36}, "end": {"line": 4, "column": null}}, "5": {"start": {"line": 7, "column": 19}, "end": {"line": 20, "column": null}}, "6": {"start": {"line": 22, "column": 34}, "end": {"line": 22, "column": null}}, "7": {"start": {"line": 26, "column": 25}, "end": {"line": 172, "column": null}}}, "fnMap": {"0": {"name": "FAQPage", "decl": {"start": {"line": 24, "column": 24}, "end": {"line": 24, "column": null}}, "loc": {"start": {"line": 24, "column": 24}, "end": {"line": 175, "column": 1}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "f": {"0": 0}, "b": {}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\services\\page.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\services\\page.tsx", "statementMap": {"0": {"start": {"line": 34, "column": 24}, "end": {"line": 34, "column": null}}, "1": {"start": {"line": 6, "column": 13}, "end": {"line": 6, "column": 21}}, "2": {"start": {"line": 1, "column": 19}, "end": {"line": 1, "column": null}}, "3": {"start": {"line": 2, "column": 19}, "end": {"line": 2, "column": null}}, "4": {"start": {"line": 3, "column": 25}, "end": {"line": 3, "column": null}}, "5": {"start": {"line": 4, "column": 24}, "end": {"line": 4, "column": null}}, "6": {"start": {"line": 6, "column": 24}, "end": {"line": 32, "column": null}}}, "fnMap": {"0": {"name": "ServicesPage", "decl": {"start": {"line": 34, "column": 24}, "end": {"line": 34, "column": null}}, "loc": {"start": {"line": 34, "column": 24}, "end": {"line": 56, "column": 1}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "f": {"0": 0}, "b": {}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\AnimationWrapper.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\AnimationWrapper.tsx", "statementMap": {"0": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 15}}, "1": {"start": {"line": 3, "column": 36}, "end": {"line": 3, "column": null}}, "2": {"start": {"line": 4, "column": 36}, "end": {"line": 4, "column": null}}, "3": {"start": {"line": 11, "column": 25}, "end": {"line": 23, "column": null}}, "4": {"start": {"line": 12, "column": 38}, "end": {"line": 12, "column": null}}, "5": {"start": {"line": 14, "column": 2}, "end": {"line": 16, "column": null}}, "6": {"start": {"line": 15, "column": 4}, "end": {"line": 15, "column": null}}, "7": {"start": {"line": 18, "column": 2}, "end": {"line": 20, "column": null}}, "8": {"start": {"line": 25, "column": 15}, "end": {"line": 25, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 11, "column": 25}, "end": {"line": 11, "column": 26}}, "loc": {"start": {"line": 11, "column": 94}, "end": {"line": 23, "column": null}}}, "1": {"name": "(anonymous_2)", "decl": {"start": {"line": 14, "column": 12}, "end": {"line": 14, "column": null}}, "loc": {"start": {"line": 14, "column": 12}, "end": {"line": 16, "column": 5}}}}, "branchMap": {"0": {"loc": {"start": {"line": 11, "column": 38}, "end": {"line": 11, "column": 53}}, "type": "default-arg", "locations": [{"start": {"line": 11, "column": 49}, "end": {"line": 11, "column": 53}}]}, "1": {"loc": {"start": {"line": 18, "column": 2}, "end": {"line": 20, "column": null}}, "type": "if", "locations": [{"start": {"line": 18, "column": 2}, "end": {"line": 20, "column": null}}]}, "2": {"loc": {"start": {"line": 19, "column": 17}, "end": {"line": 19, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 19, "column": 17}, "end": {"line": 19, "column": 29}}, {"start": {"line": 19, "column": 29}, "end": {"line": 19, "column": null}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0], "1": [0], "2": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ChatTrigger.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ChatTrigger.tsx", "statementMap": {"0": {"start": {"line": 119, "column": 13}, "end": {"line": 119, "column": 25}}, "1": {"start": {"line": 116, "column": 0}, "end": {"line": 116, "column": 15}}, "2": {"start": {"line": 3, "column": 18}, "end": {"line": 3, "column": null}}, "3": {"start": {"line": 4, "column": 49}, "end": {"line": 4, "column": null}}, "4": {"start": {"line": 5, "column": 23}, "end": {"line": 5, "column": null}}, "5": {"start": {"line": 6, "column": 27}, "end": {"line": 6, "column": null}}, "6": {"start": {"line": 17, "column": 48}, "end": {"line": 114, "column": null}}, "7": {"start": {"line": 25, "column": 25}, "end": {"line": 44, "column": null}}, "8": {"start": {"line": 27, "column": 4}, "end": {"line": 31, "column": null}}, "9": {"start": {"line": 34, "column": 4}, "end": {"line": 34, "column": null}}, "10": {"start": {"line": 37, "column": 4}, "end": {"line": 43, "column": null}}, "11": {"start": {"line": 38, "column": 6}, "end": {"line": 42, "column": null}}, "12": {"start": {"line": 46, "column": 18}, "end": {"line": 59, "column": null}}, "13": {"start": {"line": 47, "column": 22}, "end": {"line": 49, "column": null}}, "14": {"start": {"line": 51, "column": 4}, "end": {"line": 58, "column": null}}, "15": {"start": {"line": 61, "column": 25}, "end": {"line": 70, "column": null}}, "16": {"start": {"line": 62, "column": 4}, "end": {"line": 69, "column": null}}, "17": {"start": {"line": 64, "column": 8}, "end": {"line": 64, "column": null}}, "18": {"start": {"line": 66, "column": 8}, "end": {"line": 66, "column": null}}, "19": {"start": {"line": 68, "column": 8}, "end": {"line": 68, "column": null}}, "20": {"start": {"line": 72, "column": 2}, "end": {"line": 88, "column": null}}, "21": {"start": {"line": 90, "column": 2}, "end": {"line": 100, "column": null}}, "22": {"start": {"line": 116, "column": 15}, "end": {"line": 116, "column": 27}}, "23": {"start": {"line": 119, "column": 28}, "end": {"line": 196, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 17, "column": 48}, "end": {"line": 17, "column": 49}}, "loc": {"start": {"line": 24, "column": 1}, "end": {"line": 114, "column": null}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 25, "column": 25}, "end": {"line": 25, "column": null}}, "loc": {"start": {"line": 25, "column": 25}, "end": {"line": 44, "column": null}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 46, "column": 18}, "end": {"line": 46, "column": null}}, "loc": {"start": {"line": 46, "column": 18}, "end": {"line": 59, "column": null}}}, "3": {"name": "(anonymous_7)", "decl": {"start": {"line": 61, "column": 25}, "end": {"line": 61, "column": null}}, "loc": {"start": {"line": 61, "column": 25}, "end": {"line": 70, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": 20}}, "type": "default-arg", "locations": [{"start": {"line": 18, "column": 12}, "end": {"line": 18, "column": 20}}]}, "1": {"loc": {"start": {"line": 19, "column": 2}, "end": {"line": 19, "column": 23}}, "type": "default-arg", "locations": [{"start": {"line": 19, "column": 9}, "end": {"line": 19, "column": 23}}]}, "2": {"loc": {"start": {"line": 20, "column": 2}, "end": {"line": 20, "column": 18}}, "type": "default-arg", "locations": [{"start": {"line": 20, "column": 9}, "end": {"line": 20, "column": 18}}]}, "3": {"loc": {"start": {"line": 21, "column": 2}, "end": {"line": 21, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 21, "column": 14}, "end": {"line": 21, "column": 16}}]}, "4": {"loc": {"start": {"line": 22, "column": 2}, "end": {"line": 22, "column": 13}}, "type": "default-arg", "locations": [{"start": {"line": 22, "column": 9}, "end": {"line": 22, "column": 13}}]}, "5": {"loc": {"start": {"line": 23, "column": 2}, "end": {"line": 23, "column": 21}}, "type": "default-arg", "locations": [{"start": {"line": 23, "column": 12}, "end": {"line": 23, "column": 21}}]}, "6": {"loc": {"start": {"line": 37, "column": 4}, "end": {"line": 43, "column": null}}, "type": "if", "locations": [{"start": {"line": 37, "column": 4}, "end": {"line": 43, "column": null}}]}, "7": {"loc": {"start": {"line": 37, "column": 8}, "end": {"line": 37, "column": 52}}, "type": "binary-expr", "locations": [{"start": {"line": 37, "column": 8}, "end": {"line": 37, "column": 41}}, {"start": {"line": 37, "column": 41}, "end": {"line": 37, "column": 52}}]}, "8": {"loc": {"start": {"line": 48, "column": 17}, "end": {"line": 48, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 48, "column": 33}, "end": {"line": 48, "column": 45}}, {"start": {"line": 48, "column": 45}, "end": {"line": 48, "column": null}}]}, "9": {"loc": {"start": {"line": 48, "column": 45}, "end": {"line": 48, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 48, "column": 61}, "end": {"line": 48, "column": 73}}, {"start": {"line": 48, "column": 73}, "end": {"line": 48, "column": null}}]}, "10": {"loc": {"start": {"line": 62, "column": 4}, "end": {"line": 69, "column": null}}, "type": "switch", "locations": [{"start": {"line": 63, "column": 6}, "end": {"line": 64, "column": null}}, {"start": {"line": 65, "column": 6}, "end": {"line": 66, "column": null}}, {"start": {"line": 67, "column": 6}, "end": {"line": 68, "column": null}}]}, "11": {"loc": {"start": {"line": 72, "column": 2}, "end": {"line": 88, "column": null}}, "type": "if", "locations": [{"start": {"line": 72, "column": 2}, "end": {"line": 88, "column": null}}]}, "12": {"loc": {"start": {"line": 90, "column": 2}, "end": {"line": 100, "column": null}}, "type": "if", "locations": [{"start": {"line": 90, "column": 2}, "end": {"line": 100, "column": null}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0}, "b": {"0": [0], "1": [0], "2": [0], "3": [0], "4": [0], "5": [0], "6": [0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0, 0], "11": [0], "12": [0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ClientOnly.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ClientOnly.tsx", "statementMap": {"0": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 15}}, "1": {"start": {"line": 3, "column": 36}, "end": {"line": 3, "column": null}}, "2": {"start": {"line": 10, "column": 19}, "end": {"line": 22, "column": null}}, "3": {"start": {"line": 11, "column": 38}, "end": {"line": 11, "column": null}}, "4": {"start": {"line": 13, "column": 2}, "end": {"line": 15, "column": null}}, "5": {"start": {"line": 14, "column": 4}, "end": {"line": 14, "column": null}}, "6": {"start": {"line": 17, "column": 2}, "end": {"line": 19, "column": null}}, "7": {"start": {"line": 24, "column": 15}, "end": {"line": 24, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 10, "column": 19}, "end": {"line": 10, "column": 20}}, "loc": {"start": {"line": 10, "column": 66}, "end": {"line": 22, "column": null}}}, "1": {"name": "(anonymous_2)", "decl": {"start": {"line": 13, "column": 12}, "end": {"line": 13, "column": null}}, "loc": {"start": {"line": 13, "column": 12}, "end": {"line": 15, "column": 5}}}}, "branchMap": {"0": {"loc": {"start": {"line": 10, "column": 32}, "end": {"line": 10, "column": 47}}, "type": "default-arg", "locations": [{"start": {"line": 10, "column": 43}, "end": {"line": 10, "column": 47}}]}, "1": {"loc": {"start": {"line": 17, "column": 2}, "end": {"line": 19, "column": null}}, "type": "if", "locations": [{"start": {"line": 17, "column": 2}, "end": {"line": 19, "column": null}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0], "1": [0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\CompanyValues.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\CompanyValues.tsx", "statementMap": {"0": {"start": {"line": 103, "column": 0}, "end": {"line": 103, "column": 15}}, "1": {"start": {"line": 3, "column": 18}, "end": {"line": 3, "column": null}}, "2": {"start": {"line": 4, "column": 17}, "end": {"line": 4, "column": null}}, "3": {"start": {"line": 5, "column": 23}, "end": {"line": 5, "column": null}}, "4": {"start": {"line": 6, "column": 39}, "end": {"line": 6, "column": null}}, "5": {"start": {"line": 8, "column": 22}, "end": {"line": 101, "column": null}}, "6": {"start": {"line": 9, "column": 17}, "end": {"line": 30, "column": null}}, "7": {"start": {"line": 52, "column": 12}, "end": {"line": 53, "column": null}}, "8": {"start": {"line": 103, "column": 15}, "end": {"line": 103, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 8, "column": 22}, "end": {"line": 8, "column": null}}, "loc": {"start": {"line": 8, "column": 22}, "end": {"line": 101, "column": null}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 51, "column": 22}, "end": {"line": 51, "column": 23}}, "loc": {"start": {"line": 52, "column": 12}, "end": {"line": 53, "column": null}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\DarkModeToggle.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\DarkModeToggle.tsx", "statementMap": {"0": {"start": {"line": 134, "column": 13}, "end": {"line": 134, "column": 28}}, "1": {"start": {"line": 131, "column": 0}, "end": {"line": 131, "column": 15}}, "2": {"start": {"line": 3, "column": 18}, "end": {"line": 3, "column": null}}, "3": {"start": {"line": 4, "column": 26}, "end": {"line": 4, "column": null}}, "4": {"start": {"line": 5, "column": 23}, "end": {"line": 5, "column": null}}, "5": {"start": {"line": 6, "column": 25}, "end": {"line": 6, "column": null}}, "6": {"start": {"line": 15, "column": 54}, "end": {"line": 129, "column": null}}, "7": {"start": {"line": 21, "column": 33}, "end": {"line": 21, "column": null}}, "8": {"start": {"line": 23, "column": 25}, "end": {"line": 32, "column": null}}, "9": {"start": {"line": 24, "column": 4}, "end": {"line": 31, "column": null}}, "10": {"start": {"line": 26, "column": 8}, "end": {"line": 26, "column": null}}, "11": {"start": {"line": 28, "column": 8}, "end": {"line": 28, "column": null}}, "12": {"start": {"line": 30, "column": 8}, "end": {"line": 30, "column": null}}, "13": {"start": {"line": 34, "column": 22}, "end": {"line": 43, "column": null}}, "14": {"start": {"line": 35, "column": 4}, "end": {"line": 42, "column": null}}, "15": {"start": {"line": 37, "column": 8}, "end": {"line": 37, "column": null}}, "16": {"start": {"line": 39, "column": 8}, "end": {"line": 39, "column": null}}, "17": {"start": {"line": 41, "column": 8}, "end": {"line": 41, "column": null}}, "18": {"start": {"line": 45, "column": 2}, "end": {"line": 83, "column": null}}, "19": {"start": {"line": 131, "column": 15}, "end": {"line": 131, "column": 30}}, "20": {"start": {"line": 134, "column": 31}, "end": {"line": 171, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 15, "column": 54}, "end": {"line": 15, "column": 55}}, "loc": {"start": {"line": 20, "column": 1}, "end": {"line": 129, "column": null}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 23, "column": 25}, "end": {"line": 23, "column": null}}, "loc": {"start": {"line": 23, "column": 25}, "end": {"line": 32, "column": null}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 34, "column": 22}, "end": {"line": 34, "column": null}}, "loc": {"start": {"line": 34, "column": 22}, "end": {"line": 43, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 16, "column": 2}, "end": {"line": 16, "column": 20}}, "type": "default-arg", "locations": [{"start": {"line": 16, "column": 12}, "end": {"line": 16, "column": 20}}]}, "1": {"loc": {"start": {"line": 17, "column": 2}, "end": {"line": 17, "column": 13}}, "type": "default-arg", "locations": [{"start": {"line": 17, "column": 9}, "end": {"line": 17, "column": 13}}]}, "2": {"loc": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 18, "column": 14}, "end": {"line": 18, "column": 16}}]}, "3": {"loc": {"start": {"line": 19, "column": 2}, "end": {"line": 19, "column": 19}}, "type": "default-arg", "locations": [{"start": {"line": 19, "column": 14}, "end": {"line": 19, "column": 19}}]}, "4": {"loc": {"start": {"line": 24, "column": 4}, "end": {"line": 31, "column": null}}, "type": "switch", "locations": [{"start": {"line": 25, "column": 6}, "end": {"line": 26, "column": null}}, {"start": {"line": 27, "column": 6}, "end": {"line": 28, "column": null}}, {"start": {"line": 29, "column": 6}, "end": {"line": 30, "column": null}}]}, "5": {"loc": {"start": {"line": 35, "column": 4}, "end": {"line": 42, "column": null}}, "type": "switch", "locations": [{"start": {"line": 36, "column": 6}, "end": {"line": 37, "column": null}}, {"start": {"line": 38, "column": 6}, "end": {"line": 39, "column": null}}, {"start": {"line": 40, "column": 6}, "end": {"line": 41, "column": null}}]}, "6": {"loc": {"start": {"line": 45, "column": 2}, "end": {"line": 83, "column": null}}, "type": "if", "locations": [{"start": {"line": 45, "column": 2}, "end": {"line": 83, "column": null}}]}, "7": {"loc": {"start": {"line": 48, "column": 9}, "end": {"line": 48, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 48, "column": 9}, "end": {"line": 48, "column": null}}]}, "8": {"loc": {"start": {"line": 50, "column": 13}, "end": {"line": 50, "column": 49}}, "type": "cond-expr", "locations": [{"start": {"line": 50, "column": 32}, "end": {"line": 50, "column": 41}}, {"start": {"line": 50, "column": 41}, "end": {"line": 50, "column": 49}}]}, "9": {"loc": {"start": {"line": 58, "column": 35}, "end": {"line": 58, "column": 71}}, "type": "cond-expr", "locations": [{"start": {"line": 58, "column": 54}, "end": {"line": 58, "column": 64}}, {"start": {"line": 58, "column": 64}, "end": {"line": 58, "column": 71}}]}, "10": {"loc": {"start": {"line": 63, "column": 17}, "end": {"line": 63, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 63, "column": 36}, "end": {"line": 63, "column": 41}}, {"start": {"line": 63, "column": 41}, "end": {"line": 63, "column": null}}]}, "11": {"loc": {"start": {"line": 70, "column": 33}, "end": {"line": 70, "column": 60}}, "type": "cond-expr", "locations": [{"start": {"line": 70, "column": 52}, "end": {"line": 70, "column": 58}}, {"start": {"line": 70, "column": 58}, "end": {"line": 70, "column": 60}}]}, "12": {"loc": {"start": {"line": 74, "column": 16}, "end": {"line": 76, "column": 17}}, "type": "cond-expr", "locations": [{"start": {"line": 74, "column": 16}, "end": {"line": 76, "column": 17}}]}, "13": {"loc": {"start": {"line": 103, "column": 33}, "end": {"line": 103, "column": 69}}, "type": "cond-expr", "locations": [{"start": {"line": 103, "column": 52}, "end": {"line": 103, "column": 62}}, {"start": {"line": 103, "column": 62}, "end": {"line": 103, "column": 69}}]}, "14": {"loc": {"start": {"line": 104, "column": 28}, "end": {"line": 104, "column": 64}}, "type": "cond-expr", "locations": [{"start": {"line": 104, "column": 47}, "end": {"line": 104, "column": 57}}, {"start": {"line": 104, "column": 57}, "end": {"line": 104, "column": 64}}]}, "15": {"loc": {"start": {"line": 109, "column": 20}, "end": {"line": 109, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 109, "column": 39}, "end": {"line": 109, "column": 45}}, {"start": {"line": 109, "column": 45}, "end": {"line": 109, "column": null}}]}, "16": {"loc": {"start": {"line": 110, "column": 19}, "end": {"line": 110, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 110, "column": 38}, "end": {"line": 110, "column": 44}}, {"start": {"line": 110, "column": 44}, "end": {"line": 110, "column": null}}]}, "17": {"loc": {"start": {"line": 115, "column": 12}, "end": {"line": 117, "column": 13}}, "type": "cond-expr", "locations": [{"start": {"line": 115, "column": 12}, "end": {"line": 117, "column": 13}}]}, "18": {"loc": {"start": {"line": 122, "column": 7}, "end": {"line": 122, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 122, "column": 7}, "end": {"line": 122, "column": null}}]}, "19": {"loc": {"start": {"line": 124, "column": 11}, "end": {"line": 124, "column": 47}}, "type": "cond-expr", "locations": [{"start": {"line": 124, "column": 30}, "end": {"line": 124, "column": 40}}, {"start": {"line": 124, "column": 40}, "end": {"line": 124, "column": 47}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {"0": [0], "1": [0], "2": [0], "3": [0], "4": [0, 0, 0], "5": [0, 0, 0], "6": [0], "7": [0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0], "18": [0], "19": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\Logo.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\Logo.tsx", "statementMap": {"0": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 15}}, "1": {"start": {"line": 1, "column": 18}, "end": {"line": 1, "column": null}}, "2": {"start": {"line": 3, "column": 13}, "end": {"line": 9, "column": null}}, "3": {"start": {"line": 11, "column": 15}, "end": {"line": 11, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 3, "column": 13}, "end": {"line": 3, "column": 14}}, "loc": {"start": {"line": 3, "column": 56}, "end": {"line": 9, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 3, "column": 16}, "end": {"line": 3, "column": 30}}, "type": "default-arg", "locations": [{"start": {"line": 3, "column": 28}, "end": {"line": 3, "column": 30}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {"0": 0}, "b": {"0": [0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\Mission.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\Mission.tsx", "statementMap": {"0": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 15}}, "1": {"start": {"line": 3, "column": 18}, "end": {"line": 3, "column": null}}, "2": {"start": {"line": 4, "column": 23}, "end": {"line": 4, "column": null}}, "3": {"start": {"line": 6, "column": 16}, "end": {"line": 45, "column": null}}, "4": {"start": {"line": 47, "column": 15}, "end": {"line": 47, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 6, "column": 16}, "end": {"line": 6, "column": null}}, "loc": {"start": {"line": 6, "column": 16}, "end": {"line": 45, "column": null}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "f": {"0": 0}, "b": {}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\NewsletterSignup.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\NewsletterSignup.tsx", "statementMap": {"0": {"start": {"line": 204, "column": 0}, "end": {"line": 204, "column": 15}}, "1": {"start": {"line": 3, "column": 32}, "end": {"line": 3, "column": null}}, "2": {"start": {"line": 4, "column": 23}, "end": {"line": 4, "column": null}}, "3": {"start": {"line": 5, "column": 30}, "end": {"line": 5, "column": null}}, "4": {"start": {"line": 12, "column": 58}, "end": {"line": 202, "column": null}}, "5": {"start": {"line": 16, "column": 28}, "end": {"line": 16, "column": null}}, "6": {"start": {"line": 17, "column": 42}, "end": {"line": 17, "column": null}}, "7": {"start": {"line": 18, "column": 42}, "end": {"line": 18, "column": null}}, "8": {"start": {"line": 20, "column": 23}, "end": {"line": 82, "column": null}}, "9": {"start": {"line": 21, "column": 4}, "end": {"line": 21, "column": null}}, "10": {"start": {"line": 22, "column": 4}, "end": {"line": 22, "column": null}}, "11": {"start": {"line": 22, "column": 23}, "end": {"line": 22, "column": null}}, "12": {"start": {"line": 24, "column": 4}, "end": {"line": 24, "column": null}}, "13": {"start": {"line": 25, "column": 4}, "end": {"line": 25, "column": null}}, "14": {"start": {"line": 27, "column": 4}, "end": {"line": 81, "column": null}}, "15": {"start": {"line": 29, "column": 21}, "end": {"line": 29, "column": 62}}, "16": {"start": {"line": 30, "column": 21}, "end": {"line": 30, "column": 50}}, "17": {"start": {"line": 32, "column": 6}, "end": {"line": 47, "column": null}}, "18": {"start": {"line": 33, "column": 8}, "end": {"line": 33, "column": null}}, "19": {"start": {"line": 35, "column": 8}, "end": {"line": 35, "column": null}}, "20": {"start": {"line": 35, "column": 37}, "end": {"line": 35, "column": null}}, "21": {"start": {"line": 36, "column": 8}, "end": {"line": 36, "column": null}}, "22": {"start": {"line": 37, "column": 8}, "end": {"line": 37, "column": null}}, "23": {"start": {"line": 40, "column": 8}, "end": {"line": 45, "column": null}}, "24": {"start": {"line": 41, "column": 10}, "end": {"line": 44, "column": null}}, "25": {"start": {"line": 46, "column": 8}, "end": {"line": 46, "column": null}}, "26": {"start": {"line": 51, "column": 23}, "end": {"line": 60, "column": null}}, "27": {"start": {"line": 62, "column": 6}, "end": {"line": 75, "column": null}}, "28": {"start": {"line": 63, "column": 8}, "end": {"line": 63, "column": null}}, "29": {"start": {"line": 64, "column": 8}, "end": {"line": 64, "column": null}}, "30": {"start": {"line": 67, "column": 8}, "end": {"line": 72, "column": null}}, "31": {"start": {"line": 68, "column": 10}, "end": {"line": 71, "column": null}}, "32": {"start": {"line": 74, "column": 8}, "end": {"line": 74, "column": null}}, "33": {"start": {"line": 77, "column": 6}, "end": {"line": 77, "column": null}}, "34": {"start": {"line": 78, "column": 6}, "end": {"line": 78, "column": null}}, "35": {"start": {"line": 80, "column": 6}, "end": {"line": 80, "column": null}}, "36": {"start": {"line": 109, "column": 35}, "end": {"line": 109, "column": null}}, "37": {"start": {"line": 161, "column": 31}, "end": {"line": 161, "column": null}}, "38": {"start": {"line": 204, "column": 15}, "end": {"line": 204, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 12, "column": 58}, "end": {"line": 12, "column": 59}}, "loc": {"start": {"line": 15, "column": 1}, "end": {"line": 202, "column": null}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 20, "column": 23}, "end": {"line": 20, "column": 30}}, "loc": {"start": {"line": 20, "column": 30}, "end": {"line": 82, "column": null}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 35, "column": 26}, "end": {"line": 35, "column": 37}}, "loc": {"start": {"line": 35, "column": 37}, "end": {"line": 35, "column": null}}}, "3": {"name": "(anonymous_7)", "decl": {"start": {"line": 109, "column": 28}, "end": {"line": 109, "column": 29}}, "loc": {"start": {"line": 109, "column": 35}, "end": {"line": 109, "column": null}}}, "4": {"name": "(anonymous_8)", "decl": {"start": {"line": 161, "column": 24}, "end": {"line": 161, "column": 25}}, "loc": {"start": {"line": 161, "column": 31}, "end": {"line": 161, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 13, "column": 2}, "end": {"line": 13, "column": 20}}, "type": "default-arg", "locations": [{"start": {"line": 13, "column": 12}, "end": {"line": 13, "column": 20}}]}, "1": {"loc": {"start": {"line": 14, "column": 2}, "end": {"line": 14, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 14, "column": 14}, "end": {"line": 14, "column": 16}}]}, "2": {"loc": {"start": {"line": 22, "column": 4}, "end": {"line": 22, "column": null}}, "type": "if", "locations": [{"start": {"line": 22, "column": 4}, "end": {"line": 22, "column": null}}]}, "3": {"loc": {"start": {"line": 32, "column": 6}, "end": {"line": 47, "column": null}}, "type": "if", "locations": [{"start": {"line": 32, "column": 6}, "end": {"line": 47, "column": null}}]}, "4": {"loc": {"start": {"line": 32, "column": 10}, "end": {"line": 32, "column": 30}}, "type": "binary-expr", "locations": [{"start": {"line": 32, "column": 10}, "end": {"line": 32, "column": 21}}, {"start": {"line": 32, "column": 21}, "end": {"line": 32, "column": 30}}]}, "5": {"loc": {"start": {"line": 40, "column": 8}, "end": {"line": 45, "column": null}}, "type": "if", "locations": [{"start": {"line": 40, "column": 8}, "end": {"line": 45, "column": null}}]}, "6": {"loc": {"start": {"line": 40, "column": 12}, "end": {"line": 40, "column": 56}}, "type": "binary-expr", "locations": [{"start": {"line": 40, "column": 12}, "end": {"line": 40, "column": 45}}, {"start": {"line": 40, "column": 45}, "end": {"line": 40, "column": 56}}]}, "7": {"loc": {"start": {"line": 62, "column": 6}, "end": {"line": 75, "column": null}}, "type": "if", "locations": [{"start": {"line": 62, "column": 6}, "end": {"line": 75, "column": null}}, {"start": {"line": 73, "column": 13}, "end": {"line": 75, "column": null}}]}, "8": {"loc": {"start": {"line": 67, "column": 8}, "end": {"line": 72, "column": null}}, "type": "if", "locations": [{"start": {"line": 67, "column": 8}, "end": {"line": 72, "column": null}}]}, "9": {"loc": {"start": {"line": 67, "column": 12}, "end": {"line": 67, "column": 56}}, "type": "binary-expr", "locations": [{"start": {"line": 67, "column": 12}, "end": {"line": 67, "column": 45}}, {"start": {"line": 67, "column": 45}, "end": {"line": 67, "column": 56}}]}, "10": {"loc": {"start": {"line": 88, "column": 8}, "end": {"line": 88, "column": 32}}, "type": "binary-expr", "locations": [{"start": {"line": 88, "column": 8}, "end": {"line": 88, "column": 32}}, {"start": {"line": 88, "column": 32}, "end": {"line": 88, "column": 52}}]}, "11": {"loc": {"start": {"line": 119, "column": 28}, "end": {"line": 119, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 119, "column": 28}, "end": {"line": 119, "column": 45}}, {"start": {"line": 119, "column": 45}, "end": {"line": 119, "column": null}}]}, "12": {"loc": {"start": {"line": 126, "column": 15}, "end": {"line": 126, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 126, "column": 15}, "end": {"line": 126, "column": null}}]}, "13": {"loc": {"start": {"line": 136, "column": 15}, "end": {"line": 136, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 136, "column": 15}, "end": {"line": 136, "column": null}}]}, "14": {"loc": {"start": {"line": 150, "column": 7}, "end": {"line": 150, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 150, "column": 7}, "end": {"line": 150, "column": null}}]}, "15": {"loc": {"start": {"line": 172, "column": 24}, "end": {"line": 172, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 172, "column": 24}, "end": {"line": 172, "column": 41}}, {"start": {"line": 172, "column": 41}, "end": {"line": 172, "column": null}}]}, "16": {"loc": {"start": {"line": 178, "column": 13}, "end": {"line": 178, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 178, "column": 13}, "end": {"line": 178, "column": null}}]}, "17": {"loc": {"start": {"line": 188, "column": 13}, "end": {"line": 188, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 188, "column": 13}, "end": {"line": 188, "column": null}}]}}, "s": {"0": 18, "1": 1, "2": 1, "3": 1, "4": 1, "5": 156, "6": 156, "7": 156, "8": 156, "9": 7, "10": 7, "11": 0, "12": 7, "13": 7, "14": 7, "15": 7, "16": 7, "17": 7, "18": 4, "19": 4, "20": 4, "21": 4, "22": 4, "23": 4, "24": 4, "25": 4, "26": 3, "27": 2, "28": 2, "29": 2, "30": 2, "31": 2, "32": 0, "33": 1, "34": 1, "35": 7, "36": 16, "37": 109, "38": 1}, "f": {"0": 156, "1": 7, "2": 4, "3": 16, "4": 109}, "b": {"0": [0], "1": [156], "2": [0], "3": [4], "4": [7, 3], "5": [4], "6": [4, 4], "7": [2, 0], "8": [2], "9": [2, 2], "10": [156, 134], "11": [23, 17], "12": [23], "13": [23], "14": [156], "15": [133, 116], "16": [133], "17": [133]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\NoSSR.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\NoSSR.tsx", "statementMap": {"0": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 15}}, "1": {"start": {"line": 3, "column": 36}, "end": {"line": 3, "column": null}}, "2": {"start": {"line": 10, "column": 14}, "end": {"line": 22, "column": null}}, "3": {"start": {"line": 11, "column": 32}, "end": {"line": 11, "column": null}}, "4": {"start": {"line": 13, "column": 2}, "end": {"line": 15, "column": null}}, "5": {"start": {"line": 14, "column": 4}, "end": {"line": 14, "column": null}}, "6": {"start": {"line": 17, "column": 2}, "end": {"line": 19, "column": null}}, "7": {"start": {"line": 24, "column": 15}, "end": {"line": 24, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 10, "column": 14}, "end": {"line": 10, "column": 15}}, "loc": {"start": {"line": 10, "column": 56}, "end": {"line": 22, "column": null}}}, "1": {"name": "(anonymous_2)", "decl": {"start": {"line": 13, "column": 12}, "end": {"line": 13, "column": null}}, "loc": {"start": {"line": 13, "column": 12}, "end": {"line": 15, "column": 5}}}}, "branchMap": {"0": {"loc": {"start": {"line": 10, "column": 27}, "end": {"line": 10, "column": 42}}, "type": "default-arg", "locations": [{"start": {"line": 10, "column": 38}, "end": {"line": 10, "column": 42}}]}, "1": {"loc": {"start": {"line": 17, "column": 2}, "end": {"line": 19, "column": null}}, "type": "if", "locations": [{"start": {"line": 17, "column": 2}, "end": {"line": 19, "column": null}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0], "1": [0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\OrganizationSchema.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\OrganizationSchema.tsx", "statementMap": {"0": {"start": {"line": 86, "column": 0}, "end": {"line": 86, "column": 15}}, "1": {"start": {"line": 3, "column": 18}, "end": {"line": 3, "column": null}}, "2": {"start": {"line": 5, "column": 27}, "end": {"line": 84, "column": null}}, "3": {"start": {"line": 6, "column": 29}, "end": {"line": 74, "column": null}}, "4": {"start": {"line": 86, "column": 15}, "end": {"line": 86, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": null}}, "loc": {"start": {"line": 5, "column": 27}, "end": {"line": 84, "column": null}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "f": {"0": 0}, "b": {}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\PortableText.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\PortableText.tsx", "statementMap": {"0": {"start": {"line": 227, "column": 0}, "end": {"line": 227, "column": 15}}, "1": {"start": {"line": 1, "column": 18}, "end": {"line": 1, "column": null}}, "2": {"start": {"line": 2, "column": 17}, "end": {"line": 2, "column": null}}, "3": {"start": {"line": 47, "column": 50}, "end": {"line": 225, "column": null}}, "4": {"start": {"line": 48, "column": 22}, "end": {"line": 176, "column": null}}, "5": {"start": {"line": 49, "column": 4}, "end": {"line": 65, "column": null}}, "6": {"start": {"line": 50, "column": 25}, "end": {"line": 50, "column": null}}, "7": {"start": {"line": 51, "column": 6}, "end": {"line": 52, "column": 39}}, "8": {"start": {"line": 67, "column": 22}, "end": {"line": 67, "column": null}}, "9": {"start": {"line": 69, "column": 4}, "end": {"line": 69, "column": null}}, "10": {"start": {"line": 69, "column": 29}, "end": {"line": 69, "column": null}}, "11": {"start": {"line": 71, "column": 23}, "end": {"line": 120, "column": null}}, "12": {"start": {"line": 72, "column": 19}, "end": {"line": 72, "column": 28}}, "13": {"start": {"line": 74, "column": 6}, "end": {"line": 76, "column": null}}, "14": {"start": {"line": 75, "column": 8}, "end": {"line": 75, "column": null}}, "15": {"start": {"line": 78, "column": 20}, "end": {"line": 78, "column": 43}}, "16": {"start": {"line": 80, "column": 6}, "end": {"line": 117, "column": null}}, "17": {"start": {"line": 81, "column": 8}, "end": {"line": 116, "column": null}}, "18": {"start": {"line": 82, "column": 10}, "end": {"line": 82, "column": 56}}, "19": {"start": {"line": 83, "column": 15}, "end": {"line": 116, "column": null}}, "20": {"start": {"line": 84, "column": 10}, "end": {"line": 84, "column": 48}}, "21": {"start": {"line": 85, "column": 15}, "end": {"line": 116, "column": null}}, "22": {"start": {"line": 86, "column": 10}, "end": {"line": 86, "column": 52}}, "23": {"start": {"line": 89, "column": 27}, "end": {"line": 89, "column": null}}, "24": {"start": {"line": 89, "column": 59}, "end": {"line": 89, "column": null}}, "25": {"start": {"line": 90, "column": 10}, "end": {"line": 115, "column": null}}, "26": {"start": {"line": 91, "column": 31}, "end": {"line": 91, "column": null}}, "27": {"start": {"line": 92, "column": 12}, "end": {"line": 114, "column": null}}, "28": {"start": {"line": 93, "column": 14}, "end": {"line": 95, "column": null}}, "29": {"start": {"line": 105, "column": 14}, "end": {"line": 107, "column": null}}, "30": {"start": {"line": 119, "column": 6}, "end": {"line": 119, "column": null}}, "31": {"start": {"line": 122, "column": 21}, "end": {"line": 122, "column": null}}, "32": {"start": {"line": 124, "column": 4}, "end": {"line": 175, "column": null}}, "33": {"start": {"line": 126, "column": 8}, "end": {"line": 127, "column": 44}}, "34": {"start": {"line": 132, "column": 8}, "end": {"line": 133, "column": 44}}, "35": {"start": {"line": 138, "column": 8}, "end": {"line": 139, "column": 44}}, "36": {"start": {"line": 144, "column": 8}, "end": {"line": 145, "column": 44}}, "37": {"start": {"line": 150, "column": 8}, "end": {"line": 151, "column": 52}}, "38": {"start": {"line": 156, "column": 8}, "end": {"line": 162, "column": null}}, "39": {"start": {"line": 157, "column": 10}, "end": {"line": 158, "column": 46}}, "40": {"start": {"line": 163, "column": 8}, "end": {"line": 169, "column": null}}, "41": {"start": {"line": 164, "column": 10}, "end": {"line": 165, "column": 46}}, "42": {"start": {"line": 170, "column": 8}, "end": {"line": 171, "column": 43}}, "43": {"start": {"line": 179, "column": 74}, "end": {"line": 179, "column": 76}}, "44": {"start": {"line": 180, "column": 43}, "end": {"line": 180, "column": 45}}, "45": {"start": {"line": 181, "column": 39}, "end": {"line": 181, "column": null}}, "46": {"start": {"line": 183, "column": 2}, "end": {"line": 203, "column": null}}, "47": {"start": {"line": 184, "column": 4}, "end": {"line": 202, "column": null}}, "48": {"start": {"line": 185, "column": 23}, "end": {"line": 185, "column": 60}}, "49": {"start": {"line": 186, "column": 6}, "end": {"line": 194, "column": null}}, "50": {"start": {"line": 187, "column": 8}, "end": {"line": 187, "column": null}}, "51": {"start": {"line": 189, "column": 8}, "end": {"line": 191, "column": null}}, "52": {"start": {"line": 190, "column": 10}, "end": {"line": 190, "column": null}}, "53": {"start": {"line": 192, "column": 8}, "end": {"line": 192, "column": null}}, "54": {"start": {"line": 193, "column": 8}, "end": {"line": 193, "column": null}}, "55": {"start": {"line": 196, "column": 6}, "end": {"line": 200, "column": null}}, "56": {"start": {"line": 197, "column": 8}, "end": {"line": 197, "column": null}}, "57": {"start": {"line": 198, "column": 8}, "end": {"line": 198, "column": null}}, "58": {"start": {"line": 199, "column": 8}, "end": {"line": 199, "column": null}}, "59": {"start": {"line": 201, "column": 6}, "end": {"line": 201, "column": null}}, "60": {"start": {"line": 205, "column": 2}, "end": {"line": 207, "column": null}}, "61": {"start": {"line": 206, "column": 4}, "end": {"line": 206, "column": null}}, "62": {"start": {"line": 212, "column": 8}, "end": {"line": 220, "column": null}}, "63": {"start": {"line": 213, "column": 27}, "end": {"line": 213, "column": 66}}, "64": {"start": {"line": 214, "column": 32}, "end": {"line": 214, "column": null}}, "65": {"start": {"line": 215, "column": 10}, "end": {"line": 216, "column": 39}}, "66": {"start": {"line": 217, "column": 49}, "end": {"line": 217, "column": null}}, "67": {"start": {"line": 221, "column": 8}, "end": {"line": 221, "column": null}}, "68": {"start": {"line": 227, "column": 15}, "end": {"line": 227, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 47, "column": 50}, "end": {"line": 47, "column": 51}}, "loc": {"start": {"line": 47, "column": 78}, "end": {"line": 225, "column": null}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 48, "column": 22}, "end": {"line": 48, "column": 23}}, "loc": {"start": {"line": 48, "column": 51}, "end": {"line": 176, "column": null}}}, "2": {"name": "(anonymous_4)", "decl": {"start": {"line": 71, "column": 23}, "end": {"line": 71, "column": 24}}, "loc": {"start": {"line": 71, "column": 48}, "end": {"line": 120, "column": null}}}, "3": {"name": "(anonymous_5)", "decl": {"start": {"line": 80, "column": 25}, "end": {"line": 80, "column": 26}}, "loc": {"start": {"line": 80, "column": 26}, "end": {"line": 117, "column": null}}}, "4": {"name": "(anonymous_6)", "decl": {"start": {"line": 89, "column": 52}, "end": {"line": 89, "column": 59}}, "loc": {"start": {"line": 89, "column": 59}, "end": {"line": 89, "column": null}}}, "5": {"name": "(anonymous_7)", "decl": {"start": {"line": 183, "column": 18}, "end": {"line": 183, "column": 19}}, "loc": {"start": {"line": 183, "column": 19}, "end": {"line": 203, "column": null}}}, "6": {"name": "(anonymous_8)", "decl": {"start": {"line": 211, "column": 26}, "end": {"line": 211, "column": 27}}, "loc": {"start": {"line": 211, "column": 33}, "end": {"line": 222, "column": null}}}, "7": {"name": "(anonymous_9)", "decl": {"start": {"line": 217, "column": 24}, "end": {"line": 217, "column": 25}}, "loc": {"start": {"line": 217, "column": 49}, "end": {"line": 217, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 47, "column": 62}, "end": {"line": 47, "column": 76}}, "type": "default-arg", "locations": [{"start": {"line": 47, "column": 74}, "end": {"line": 47, "column": 76}}]}, "1": {"loc": {"start": {"line": 49, "column": 4}, "end": {"line": 65, "column": null}}, "type": "if", "locations": [{"start": {"line": 49, "column": 4}, "end": {"line": 65, "column": null}}]}, "2": {"loc": {"start": {"line": 55, "column": 15}, "end": {"line": 55, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 55, "column": 15}, "end": {"line": 55, "column": 29}}, {"start": {"line": 55, "column": 33}, "end": {"line": 55, "column": null}}]}, "3": {"loc": {"start": {"line": 58, "column": 11}, "end": {"line": 58, "column": 29}}, "type": "binary-expr", "locations": [{"start": {"line": 58, "column": 11}, "end": {"line": 58, "column": 29}}]}, "4": {"loc": {"start": {"line": 52, "column": 18}, "end": {"line": 52, "column": 39}}, "type": "binary-expr", "locations": [{"start": {"line": 52, "column": 18}, "end": {"line": 52, "column": 28}}, {"start": {"line": 52, "column": 32}, "end": {"line": 52, "column": 39}}]}, "5": {"loc": {"start": {"line": 69, "column": 4}, "end": {"line": 69, "column": null}}, "type": "if", "locations": [{"start": {"line": 69, "column": 4}, "end": {"line": 69, "column": null}}]}, "6": {"loc": {"start": {"line": 74, "column": 6}, "end": {"line": 76, "column": null}}, "type": "if", "locations": [{"start": {"line": 74, "column": 6}, "end": {"line": 76, "column": null}}]}, "7": {"loc": {"start": {"line": 74, "column": 10}, "end": {"line": 74, "column": 50}}, "type": "binary-expr", "locations": [{"start": {"line": 74, "column": 10}, "end": {"line": 74, "column": 21}}, {"start": {"line": 74, "column": 25}, "end": {"line": 74, "column": 50}}]}, "8": {"loc": {"start": {"line": 81, "column": 8}, "end": {"line": 116, "column": null}}, "type": "if", "locations": [{"start": {"line": 81, "column": 8}, "end": {"line": 116, "column": null}}, {"start": {"line": 83, "column": 15}, "end": {"line": 116, "column": null}}]}, "9": {"loc": {"start": {"line": 83, "column": 15}, "end": {"line": 116, "column": null}}, "type": "if", "locations": [{"start": {"line": 83, "column": 15}, "end": {"line": 116, "column": null}}, {"start": {"line": 85, "column": 15}, "end": {"line": 116, "column": null}}]}, "10": {"loc": {"start": {"line": 85, "column": 15}, "end": {"line": 116, "column": null}}, "type": "if", "locations": [{"start": {"line": 85, "column": 15}, "end": {"line": 116, "column": null}}, {"start": {"line": 87, "column": 15}, "end": {"line": 116, "column": null}}]}, "11": {"loc": {"start": {"line": 90, "column": 10}, "end": {"line": 115, "column": null}}, "type": "if", "locations": [{"start": {"line": 90, "column": 10}, "end": {"line": 115, "column": null}}]}, "12": {"loc": {"start": {"line": 90, "column": 14}, "end": {"line": 90, "column": 68}}, "type": "binary-expr", "locations": [{"start": {"line": 90, "column": 14}, "end": {"line": 90, "column": 26}}, {"start": {"line": 90, "column": 26}, "end": {"line": 90, "column": 55}}, {"start": {"line": 90, "column": 55}, "end": {"line": 90, "column": 68}}]}, "13": {"loc": {"start": {"line": 92, "column": 12}, "end": {"line": 114, "column": null}}, "type": "if", "locations": [{"start": {"line": 92, "column": 12}, "end": {"line": 114, "column": null}}, {"start": {"line": 104, "column": 19}, "end": {"line": 114, "column": null}}]}, "14": {"loc": {"start": {"line": 124, "column": 4}, "end": {"line": 175, "column": null}}, "type": "switch", "locations": [{"start": {"line": 125, "column": 6}, "end": {"line": 127, "column": 44}}, {"start": {"line": 131, "column": 6}, "end": {"line": 133, "column": 44}}, {"start": {"line": 137, "column": 6}, "end": {"line": 139, "column": 44}}, {"start": {"line": 143, "column": 6}, "end": {"line": 145, "column": 44}}, {"start": {"line": 149, "column": 6}, "end": {"line": 151, "column": 52}}, {"start": {"line": 155, "column": 6}, "end": {"line": 171, "column": 43}}]}, "15": {"loc": {"start": {"line": 127, "column": 19}, "end": {"line": 127, "column": 44}}, "type": "binary-expr", "locations": [{"start": {"line": 127, "column": 19}, "end": {"line": 127, "column": 33}}, {"start": {"line": 127, "column": 37}, "end": {"line": 127, "column": 44}}]}, "16": {"loc": {"start": {"line": 133, "column": 19}, "end": {"line": 133, "column": 44}}, "type": "binary-expr", "locations": [{"start": {"line": 133, "column": 19}, "end": {"line": 133, "column": 33}}, {"start": {"line": 133, "column": 37}, "end": {"line": 133, "column": 44}}]}, "17": {"loc": {"start": {"line": 139, "column": 19}, "end": {"line": 139, "column": 44}}, "type": "binary-expr", "locations": [{"start": {"line": 139, "column": 19}, "end": {"line": 139, "column": 33}}, {"start": {"line": 139, "column": 37}, "end": {"line": 139, "column": 44}}]}, "18": {"loc": {"start": {"line": 145, "column": 19}, "end": {"line": 145, "column": 44}}, "type": "binary-expr", "locations": [{"start": {"line": 145, "column": 19}, "end": {"line": 145, "column": 33}}, {"start": {"line": 145, "column": 37}, "end": {"line": 145, "column": 44}}]}, "19": {"loc": {"start": {"line": 151, "column": 27}, "end": {"line": 151, "column": 52}}, "type": "binary-expr", "locations": [{"start": {"line": 151, "column": 27}, "end": {"line": 151, "column": 41}}, {"start": {"line": 151, "column": 45}, "end": {"line": 151, "column": 52}}]}, "20": {"loc": {"start": {"line": 156, "column": 8}, "end": {"line": 162, "column": null}}, "type": "if", "locations": [{"start": {"line": 156, "column": 8}, "end": {"line": 162, "column": null}}]}, "21": {"loc": {"start": {"line": 158, "column": 21}, "end": {"line": 158, "column": 46}}, "type": "binary-expr", "locations": [{"start": {"line": 158, "column": 21}, "end": {"line": 158, "column": 35}}, {"start": {"line": 158, "column": 39}, "end": {"line": 158, "column": 46}}]}, "22": {"loc": {"start": {"line": 163, "column": 8}, "end": {"line": 169, "column": null}}, "type": "if", "locations": [{"start": {"line": 163, "column": 8}, "end": {"line": 169, "column": null}}]}, "23": {"loc": {"start": {"line": 165, "column": 21}, "end": {"line": 165, "column": 46}}, "type": "binary-expr", "locations": [{"start": {"line": 165, "column": 21}, "end": {"line": 165, "column": 35}}, {"start": {"line": 165, "column": 39}, "end": {"line": 165, "column": 46}}]}, "24": {"loc": {"start": {"line": 171, "column": 18}, "end": {"line": 171, "column": 43}}, "type": "binary-expr", "locations": [{"start": {"line": 171, "column": 18}, "end": {"line": 171, "column": 32}}, {"start": {"line": 171, "column": 36}, "end": {"line": 171, "column": 43}}]}, "25": {"loc": {"start": {"line": 184, "column": 4}, "end": {"line": 202, "column": null}}, "type": "if", "locations": [{"start": {"line": 184, "column": 4}, "end": {"line": 202, "column": null}}, {"start": {"line": 195, "column": 11}, "end": {"line": 202, "column": null}}]}, "26": {"loc": {"start": {"line": 184, "column": 8}, "end": {"line": 184, "column": 72}}, "type": "binary-expr", "locations": [{"start": {"line": 184, "column": 8}, "end": {"line": 184, "column": 35}}, {"start": {"line": 184, "column": 35}, "end": {"line": 184, "column": 72}}]}, "27": {"loc": {"start": {"line": 186, "column": 6}, "end": {"line": 194, "column": null}}, "type": "if", "locations": [{"start": {"line": 186, "column": 6}, "end": {"line": 194, "column": null}}, {"start": {"line": 188, "column": 13}, "end": {"line": 194, "column": null}}]}, "28": {"loc": {"start": {"line": 189, "column": 8}, "end": {"line": 191, "column": null}}, "type": "if", "locations": [{"start": {"line": 189, "column": 8}, "end": {"line": 191, "column": null}}]}, "29": {"loc": {"start": {"line": 196, "column": 6}, "end": {"line": 200, "column": null}}, "type": "if", "locations": [{"start": {"line": 196, "column": 6}, "end": {"line": 200, "column": null}}]}, "30": {"loc": {"start": {"line": 205, "column": 2}, "end": {"line": 207, "column": null}}, "type": "if", "locations": [{"start": {"line": 205, "column": 2}, "end": {"line": 207, "column": null}}]}, "31": {"loc": {"start": {"line": 212, "column": 8}, "end": {"line": 220, "column": null}}, "type": "if", "locations": [{"start": {"line": 212, "column": 8}, "end": {"line": 220, "column": null}}]}, "32": {"loc": {"start": {"line": 214, "column": 32}, "end": {"line": 214, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 214, "column": 56}, "end": {"line": 214, "column": 63}}, {"start": {"line": 214, "column": 63}, "end": {"line": 214, "column": null}}]}, "33": {"loc": {"start": {"line": 216, "column": 50}, "end": {"line": 216, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 216, "column": 74}, "end": {"line": 216, "column": 98}}, {"start": {"line": 216, "column": 98}, "end": {"line": 216, "column": null}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "b": {"0": [0], "1": [0], "2": [0, 0], "3": [0], "4": [0, 0], "5": [0], "6": [0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0], "12": [0, 0, 0], "13": [0, 0], "14": [0, 0, 0, 0, 0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0], "21": [0, 0], "22": [0], "23": [0, 0], "24": [0, 0], "25": [0, 0], "26": [0, 0], "27": [0, 0], "28": [0], "29": [0], "30": [0], "31": [0], "32": [0, 0], "33": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\PricingTable.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\PricingTable.tsx", "statementMap": {"0": {"start": {"line": 226, "column": 0}, "end": {"line": 226, "column": 15}}, "1": {"start": {"line": 3, "column": 18}, "end": {"line": 3, "column": null}}, "2": {"start": {"line": 4, "column": 23}, "end": {"line": 4, "column": null}}, "3": {"start": {"line": 5, "column": 25}, "end": {"line": 5, "column": null}}, "4": {"start": {"line": 7, "column": 21}, "end": {"line": 224, "column": null}}, "5": {"start": {"line": 8, "column": 19}, "end": {"line": 27, "column": null}}, "6": {"start": {"line": 29, "column": 19}, "end": {"line": 90, "column": null}}, "7": {"start": {"line": 92, "column": 29}, "end": {"line": 101, "column": null}}, "8": {"start": {"line": 93, "column": 4}, "end": {"line": 99, "column": null}}, "9": {"start": {"line": 120, "column": 14}, "end": {"line": 121, "column": null}}, "10": {"start": {"line": 146, "column": 20}, "end": {"line": 146, "column": 44}}, "11": {"start": {"line": 169, "column": 20}, "end": {"line": 169, "column": 39}}, "12": {"start": {"line": 190, "column": 18}, "end": {"line": 190, "column": 41}}, "13": {"start": {"line": 211, "column": 30}, "end": {"line": 211, "column": null}}, "14": {"start": {"line": 212, "column": 14}, "end": {"line": 214, "column": null}}, "15": {"start": {"line": 213, "column": 16}, "end": {"line": 213, "column": null}}, "16": {"start": {"line": 226, "column": 15}, "end": {"line": 226, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 7, "column": 21}, "end": {"line": 7, "column": null}}, "loc": {"start": {"line": 7, "column": 21}, "end": {"line": 224, "column": null}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 92, "column": 29}, "end": {"line": 92, "column": 30}}, "loc": {"start": {"line": 92, "column": 30}, "end": {"line": 101, "column": null}}}, "2": {"name": "(anonymous_4)", "decl": {"start": {"line": 119, "column": 26}, "end": {"line": 119, "column": 27}}, "loc": {"start": {"line": 120, "column": 14}, "end": {"line": 121, "column": null}}}, "3": {"name": "(anonymous_5)", "decl": {"start": {"line": 145, "column": 32}, "end": {"line": 145, "column": 33}}, "loc": {"start": {"line": 146, "column": 20}, "end": {"line": 146, "column": 44}}}, "4": {"name": "(anonymous_6)", "decl": {"start": {"line": 168, "column": 32}, "end": {"line": 168, "column": 33}}, "loc": {"start": {"line": 169, "column": 20}, "end": {"line": 169, "column": 39}}}, "5": {"name": "(anonymous_7)", "decl": {"start": {"line": 189, "column": 30}, "end": {"line": 189, "column": 31}}, "loc": {"start": {"line": 190, "column": 18}, "end": {"line": 190, "column": 41}}}, "6": {"name": "(anonymous_8)", "decl": {"start": {"line": 210, "column": 21}, "end": {"line": 210, "column": null}}, "loc": {"start": {"line": 210, "column": 21}, "end": {"line": 215, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 93, "column": 4}, "end": {"line": 99, "column": null}}, "type": "if", "locations": [{"start": {"line": 93, "column": 4}, "end": {"line": 99, "column": null}}]}, "1": {"loc": {"start": {"line": 95, "column": 8}, "end": {"line": 97, "column": 9}}, "type": "cond-expr", "locations": [{"start": {"line": 95, "column": 8}, "end": {"line": 97, "column": 9}}]}, "2": {"loc": {"start": {"line": 127, "column": 18}, "end": {"line": 127, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 127, "column": 32}, "end": {"line": 127, "column": 59}}, {"start": {"line": 127, "column": 59}, "end": {"line": 127, "column": null}}]}, "3": {"loc": {"start": {"line": 130, "column": 17}, "end": {"line": 130, "column": 28}}, "type": "binary-expr", "locations": [{"start": {"line": 130, "column": 17}, "end": {"line": 130, "column": 28}}]}, "4": {"loc": {"start": {"line": 150, "column": 26}, "end": {"line": 152, "column": 44}}, "type": "cond-expr", "locations": [{"start": {"line": 150, "column": 55}, "end": {"line": 150, "column": 70}}, {"start": {"line": 151, "column": 26}, "end": {"line": 152, "column": 44}}]}, "5": {"loc": {"start": {"line": 151, "column": 26}, "end": {"line": 152, "column": 44}}, "type": "cond-expr", "locations": [{"start": {"line": 151, "column": 54}, "end": {"line": 151, "column": 68}}, {"start": {"line": 152, "column": 26}, "end": {"line": 152, "column": 44}}]}, "6": {"loc": {"start": {"line": 171, "column": 24}, "end": {"line": 171, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 171, "column": 38}, "end": {"line": 171, "column": 78}}, {"start": {"line": 171, "column": 78}, "end": {"line": 171, "column": null}}]}, "7": {"loc": {"start": {"line": 173, "column": 25}, "end": {"line": 173, "column": 36}}, "type": "binary-expr", "locations": [{"start": {"line": 173, "column": 25}, "end": {"line": 173, "column": 36}}]}, "8": {"loc": {"start": {"line": 190, "column": 52}, "end": {"line": 190, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 190, "column": 70}, "end": {"line": 190, "column": 85}}, {"start": {"line": 190, "column": 85}, "end": {"line": 190, "column": null}}]}, "9": {"loc": {"start": {"line": 212, "column": 14}, "end": {"line": 214, "column": null}}, "type": "if", "locations": [{"start": {"line": 212, "column": 14}, "end": {"line": 214, "column": null}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "b": {"0": [0], "1": [0], "2": [0, 0], "3": [0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0], "8": [0, 0], "9": [0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ServicesFAQ.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ServicesFAQ.tsx", "statementMap": {"0": {"start": {"line": 131, "column": 0}, "end": {"line": 131, "column": 15}}, "1": {"start": {"line": 3, "column": 32}, "end": {"line": 3, "column": null}}, "2": {"start": {"line": 4, "column": 17}, "end": {"line": 4, "column": null}}, "3": {"start": {"line": 5, "column": 40}, "end": {"line": 5, "column": null}}, "4": {"start": {"line": 6, "column": 28}, "end": {"line": 6, "column": null}}, "5": {"start": {"line": 8, "column": 20}, "end": {"line": 129, "column": null}}, "6": {"start": {"line": 9, "column": 36}, "end": {"line": 9, "column": null}}, "7": {"start": {"line": 11, "column": 15}, "end": {"line": 44, "column": null}}, "8": {"start": {"line": 46, "column": 20}, "end": {"line": 48, "column": null}}, "9": {"start": {"line": 47, "column": 4}, "end": {"line": 47, "column": null}}, "10": {"start": {"line": 70, "column": 12}, "end": {"line": 71, "column": null}}, "11": {"start": {"line": 79, "column": 31}, "end": {"line": 79, "column": null}}, "12": {"start": {"line": 131, "column": 15}, "end": {"line": 131, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_5)", "decl": {"start": {"line": 8, "column": 20}, "end": {"line": 8, "column": null}}, "loc": {"start": {"line": 8, "column": 20}, "end": {"line": 129, "column": null}}}, "1": {"name": "(anonymous_6)", "decl": {"start": {"line": 46, "column": 20}, "end": {"line": 46, "column": 21}}, "loc": {"start": {"line": 46, "column": 21}, "end": {"line": 48, "column": null}}}, "2": {"name": "(anonymous_7)", "decl": {"start": {"line": 69, "column": 20}, "end": {"line": 69, "column": 21}}, "loc": {"start": {"line": 70, "column": 12}, "end": {"line": 71, "column": null}}}, "3": {"name": "(anonymous_8)", "decl": {"start": {"line": 79, "column": 25}, "end": {"line": 79, "column": 31}}, "loc": {"start": {"line": 79, "column": 31}, "end": {"line": 79, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 47, "column": 17}, "end": {"line": 47, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 47, "column": 39}, "end": {"line": 47, "column": 46}}, {"start": {"line": 47, "column": 46}, "end": {"line": 47, "column": null}}]}, "1": {"loc": {"start": {"line": 85, "column": 20}, "end": {"line": 85, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 85, "column": 42}, "end": {"line": 85, "column": 67}}, {"start": {"line": 85, "column": 67}, "end": {"line": 85, "column": null}}]}, "2": {"loc": {"start": {"line": 91, "column": 17}, "end": {"line": 91, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 91, "column": 17}, "end": {"line": 91, "column": null}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\SimpleDarkModeToggle.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\SimpleDarkModeToggle.tsx", "statementMap": {"0": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 15}}, "1": {"start": {"line": 3, "column": 18}, "end": {"line": 3, "column": null}}, "2": {"start": {"line": 4, "column": 26}, "end": {"line": 4, "column": null}}, "3": {"start": {"line": 5, "column": 25}, "end": {"line": 5, "column": null}}, "4": {"start": {"line": 12, "column": 66}, "end": {"line": 42, "column": null}}, "5": {"start": {"line": 16, "column": 33}, "end": {"line": 16, "column": null}}, "6": {"start": {"line": 18, "column": 25}, "end": {"line": 27, "column": null}}, "7": {"start": {"line": 19, "column": 4}, "end": {"line": 26, "column": null}}, "8": {"start": {"line": 21, "column": 8}, "end": {"line": 21, "column": null}}, "9": {"start": {"line": 23, "column": 8}, "end": {"line": 23, "column": null}}, "10": {"start": {"line": 25, "column": 8}, "end": {"line": 25, "column": null}}, "11": {"start": {"line": 44, "column": 15}, "end": {"line": 44, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 12, "column": 66}, "end": {"line": 12, "column": 67}}, "loc": {"start": {"line": 15, "column": 1}, "end": {"line": 42, "column": null}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 18, "column": 25}, "end": {"line": 18, "column": null}}, "loc": {"start": {"line": 18, "column": 25}, "end": {"line": 27, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 13, "column": 2}, "end": {"line": 13, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 13, "column": 14}, "end": {"line": 13, "column": 16}}]}, "1": {"loc": {"start": {"line": 14, "column": 2}, "end": {"line": 14, "column": 13}}, "type": "default-arg", "locations": [{"start": {"line": 14, "column": 9}, "end": {"line": 14, "column": 13}}]}, "2": {"loc": {"start": {"line": 19, "column": 4}, "end": {"line": 26, "column": null}}, "type": "switch", "locations": [{"start": {"line": 20, "column": 6}, "end": {"line": 21, "column": null}}, {"start": {"line": 22, "column": 6}, "end": {"line": 23, "column": null}}, {"start": {"line": 24, "column": 6}, "end": {"line": 25, "column": null}}]}, "3": {"loc": {"start": {"line": 33, "column": 31}, "end": {"line": 33, "column": 67}}, "type": "cond-expr", "locations": [{"start": {"line": 33, "column": 50}, "end": {"line": 33, "column": 60}}, {"start": {"line": 33, "column": 60}, "end": {"line": 33, "column": 67}}]}, "4": {"loc": {"start": {"line": 36, "column": 8}, "end": {"line": 38, "column": 9}}, "type": "cond-expr", "locations": [{"start": {"line": 36, "column": 8}, "end": {"line": 38, "column": 9}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0], "1": [0], "2": [0, 0, 0], "3": [0, 0], "4": [0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\SimpleFloatingChat.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\SimpleFloatingChat.tsx", "statementMap": {"0": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 15}}, "1": {"start": {"line": 3, "column": 18}, "end": {"line": 3, "column": null}}, "2": {"start": {"line": 4, "column": 30}, "end": {"line": 4, "column": null}}, "3": {"start": {"line": 6, "column": 37}, "end": {"line": 40, "column": null}}, "4": {"start": {"line": 7, "column": 25}, "end": {"line": 23, "column": null}}, "5": {"start": {"line": 9, "column": 4}, "end": {"line": 14, "column": null}}, "6": {"start": {"line": 10, "column": 7}, "end": {"line": 10, "column": null}}, "7": {"start": {"line": 13, "column": 6}, "end": {"line": 13, "column": null}}, "8": {"start": {"line": 17, "column": 4}, "end": {"line": 22, "column": null}}, "9": {"start": {"line": 18, "column": 7}, "end": {"line": 21, "column": null}}, "10": {"start": {"line": 42, "column": 15}, "end": {"line": 42, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 6, "column": 37}, "end": {"line": 6, "column": null}}, "loc": {"start": {"line": 6, "column": 37}, "end": {"line": 40, "column": null}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 7, "column": 25}, "end": {"line": 7, "column": null}}, "loc": {"start": {"line": 7, "column": 25}, "end": {"line": 23, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 9, "column": 4}, "end": {"line": 14, "column": null}}, "type": "if", "locations": [{"start": {"line": 9, "column": 4}, "end": {"line": 14, "column": null}}, {"start": {"line": 11, "column": 11}, "end": {"line": 14, "column": null}}]}, "1": {"loc": {"start": {"line": 9, "column": 8}, "end": {"line": 9, "column": 63}}, "type": "binary-expr", "locations": [{"start": {"line": 9, "column": 8}, "end": {"line": 9, "column": 41}}, {"start": {"line": 9, "column": 41}, "end": {"line": 9, "column": 63}}]}, "2": {"loc": {"start": {"line": 17, "column": 4}, "end": {"line": 22, "column": null}}, "type": "if", "locations": [{"start": {"line": 17, "column": 4}, "end": {"line": 22, "column": null}}]}, "3": {"loc": {"start": {"line": 17, "column": 8}, "end": {"line": 17, "column": 61}}, "type": "binary-expr", "locations": [{"start": {"line": 17, "column": 8}, "end": {"line": 17, "column": 41}}, {"start": {"line": 17, "column": 41}, "end": {"line": 17, "column": 61}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0], "3": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\SimpleHeaderChat.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\SimpleHeaderChat.tsx", "statementMap": {"0": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 15}}, "1": {"start": {"line": 3, "column": 18}, "end": {"line": 3, "column": null}}, "2": {"start": {"line": 4, "column": 30}, "end": {"line": 4, "column": null}}, "3": {"start": {"line": 6, "column": 35}, "end": {"line": 35, "column": null}}, "4": {"start": {"line": 7, "column": 25}, "end": {"line": 23, "column": null}}, "5": {"start": {"line": 9, "column": 4}, "end": {"line": 14, "column": null}}, "6": {"start": {"line": 10, "column": 7}, "end": {"line": 10, "column": null}}, "7": {"start": {"line": 13, "column": 6}, "end": {"line": 13, "column": null}}, "8": {"start": {"line": 17, "column": 4}, "end": {"line": 22, "column": null}}, "9": {"start": {"line": 18, "column": 7}, "end": {"line": 21, "column": null}}, "10": {"start": {"line": 37, "column": 15}, "end": {"line": 37, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 6, "column": 35}, "end": {"line": 6, "column": null}}, "loc": {"start": {"line": 6, "column": 35}, "end": {"line": 35, "column": null}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 7, "column": 25}, "end": {"line": 7, "column": null}}, "loc": {"start": {"line": 7, "column": 25}, "end": {"line": 23, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 9, "column": 4}, "end": {"line": 14, "column": null}}, "type": "if", "locations": [{"start": {"line": 9, "column": 4}, "end": {"line": 14, "column": null}}, {"start": {"line": 11, "column": 11}, "end": {"line": 14, "column": null}}]}, "1": {"loc": {"start": {"line": 9, "column": 8}, "end": {"line": 9, "column": 63}}, "type": "binary-expr", "locations": [{"start": {"line": 9, "column": 8}, "end": {"line": 9, "column": 41}}, {"start": {"line": 9, "column": 41}, "end": {"line": 9, "column": 63}}]}, "2": {"loc": {"start": {"line": 17, "column": 4}, "end": {"line": 22, "column": null}}, "type": "if", "locations": [{"start": {"line": 17, "column": 4}, "end": {"line": 22, "column": null}}]}, "3": {"loc": {"start": {"line": 17, "column": 8}, "end": {"line": 17, "column": 61}}, "type": "binary-expr", "locations": [{"start": {"line": 17, "column": 8}, "end": {"line": 17, "column": 41}}, {"start": {"line": 17, "column": 41}, "end": {"line": 17, "column": 61}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0], "3": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\StructuredData.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\StructuredData.tsx", "statementMap": {"0": {"start": {"line": 158, "column": 0}, "end": {"line": 158, "column": 15}}, "1": {"start": {"line": 1, "column": 19}, "end": {"line": 1, "column": null}}, "2": {"start": {"line": 8, "column": 54}, "end": {"line": 156, "column": null}}, "3": {"start": {"line": 9, "column": 18}, "end": {"line": 9, "column": null}}, "4": {"start": {"line": 11, "column": 28}, "end": {"line": 139, "column": null}}, "5": {"start": {"line": 12, "column": 4}, "end": {"line": 138, "column": null}}, "6": {"start": {"line": 14, "column": 8}, "end": {"line": 42, "column": null}}, "7": {"start": {"line": 45, "column": 8}, "end": {"line": 83, "column": null}}, "8": {"start": {"line": 86, "column": 8}, "end": {"line": 97, "column": null}}, "9": {"start": {"line": 89, "column": 55}, "end": {"line": 96, "column": 17}}, "10": {"start": {"line": 100, "column": 8}, "end": {"line": 109, "column": null}}, "11": {"start": {"line": 103, "column": 83}, "end": {"line": 108, "column": 17}}, "12": {"start": {"line": 112, "column": 8}, "end": {"line": 134, "column": null}}, "13": {"start": {"line": 137, "column": 8}, "end": {"line": 137, "column": null}}, "14": {"start": {"line": 141, "column": 25}, "end": {"line": 141, "column": null}}, "15": {"start": {"line": 143, "column": 2}, "end": {"line": 145, "column": null}}, "16": {"start": {"line": 144, "column": 4}, "end": {"line": 144, "column": null}}, "17": {"start": {"line": 158, "column": 15}, "end": {"line": 158, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 8, "column": 54}, "end": {"line": 8, "column": 55}}, "loc": {"start": {"line": 8, "column": 69}, "end": {"line": 156, "column": null}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 11, "column": 28}, "end": {"line": 11, "column": null}}, "loc": {"start": {"line": 11, "column": 28}, "end": {"line": 139, "column": null}}}, "2": {"name": "(anonymous_4)", "decl": {"start": {"line": 89, "column": 40}, "end": {"line": 89, "column": 41}}, "loc": {"start": {"line": 89, "column": 55}, "end": {"line": 96, "column": 17}}}, "3": {"name": "(anonymous_5)", "decl": {"start": {"line": 103, "column": 52}, "end": {"line": 103, "column": 53}}, "loc": {"start": {"line": 103, "column": 83}, "end": {"line": 108, "column": 17}}}}, "branchMap": {"0": {"loc": {"start": {"line": 9, "column": 18}, "end": {"line": 9, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 9, "column": 18}, "end": {"line": 9, "column": 50}}, {"start": {"line": 9, "column": 54}, "end": {"line": 9, "column": null}}]}, "1": {"loc": {"start": {"line": 12, "column": 4}, "end": {"line": 138, "column": null}}, "type": "switch", "locations": [{"start": {"line": 13, "column": 6}, "end": {"line": 42, "column": null}}, {"start": {"line": 44, "column": 6}, "end": {"line": 83, "column": null}}, {"start": {"line": 85, "column": 6}, "end": {"line": 97, "column": null}}, {"start": {"line": 99, "column": 6}, "end": {"line": 109, "column": null}}, {"start": {"line": 111, "column": 6}, "end": {"line": 134, "column": null}}, {"start": {"line": 136, "column": 6}, "end": {"line": 137, "column": null}}]}, "2": {"loc": {"start": {"line": 89, "column": 24}, "end": {"line": 96, "column": 19}}, "type": "binary-expr", "locations": [{"start": {"line": 89, "column": 24}, "end": {"line": 96, "column": 17}}, {"start": {"line": 96, "column": 17}, "end": {"line": 96, "column": 19}}]}, "3": {"loc": {"start": {"line": 103, "column": 29}, "end": {"line": 108, "column": 19}}, "type": "binary-expr", "locations": [{"start": {"line": 103, "column": 29}, "end": {"line": 108, "column": 17}}, {"start": {"line": 108, "column": 17}, "end": {"line": 108, "column": 19}}]}, "4": {"loc": {"start": {"line": 143, "column": 2}, "end": {"line": 145, "column": null}}, "type": "if", "locations": [{"start": {"line": 143, "column": 2}, "end": {"line": 145, "column": null}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0}, "b": {"0": [0, 0], "1": [0, 0, 0, 0, 0, 0], "2": [0, 0], "3": [0, 0], "4": [0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\TeamProfiles.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\TeamProfiles.tsx", "statementMap": {"0": {"start": {"line": 104, "column": 0}, "end": {"line": 104, "column": 15}}, "1": {"start": {"line": 3, "column": 18}, "end": {"line": 3, "column": null}}, "2": {"start": {"line": 4, "column": 18}, "end": {"line": 4, "column": null}}, "3": {"start": {"line": 5, "column": 23}, "end": {"line": 5, "column": null}}, "4": {"start": {"line": 7, "column": 21}, "end": {"line": 102, "column": null}}, "5": {"start": {"line": 8, "column": 15}, "end": {"line": 23, "column": null}}, "6": {"start": {"line": 45, "column": 12}, "end": {"line": 46, "column": null}}, "7": {"start": {"line": 104, "column": 15}, "end": {"line": 104, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 7, "column": 21}, "end": {"line": 7, "column": null}}, "loc": {"start": {"line": 7, "column": 21}, "end": {"line": 102, "column": null}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 44, "column": 20}, "end": {"line": 44, "column": 21}}, "loc": {"start": {"line": 45, "column": 12}, "end": {"line": 46, "column": null}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\Footer.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\Footer.tsx", "statementMap": {"0": {"start": {"line": 85, "column": 0}, "end": {"line": 85, "column": 15}}, "1": {"start": {"line": 29, "column": 18}, "end": {"line": 29, "column": null}}, "2": {"start": {"line": 30, "column": 17}, "end": {"line": 30, "column": null}}, "3": {"start": {"line": 31, "column": 33}, "end": {"line": 31, "column": null}}, "4": {"start": {"line": 32, "column": 18}, "end": {"line": 32, "column": null}}, "5": {"start": {"line": 33, "column": 22}, "end": {"line": 33, "column": null}}, "6": {"start": {"line": 34, "column": 29}, "end": {"line": 34, "column": null}}, "7": {"start": {"line": 35, "column": 28}, "end": {"line": 35, "column": null}}, "8": {"start": {"line": 40, "column": 15}, "end": {"line": 83, "column": null}}, "9": {"start": {"line": 85, "column": 15}, "end": {"line": 85, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 40, "column": 15}, "end": {"line": 40, "column": null}}, "loc": {"start": {"line": 40, "column": 15}, "end": {"line": 83, "column": null}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "f": {"0": 0}, "b": {}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\FooterNav.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\FooterNav.tsx", "statementMap": {"0": {"start": {"line": 111, "column": 0}, "end": {"line": 111, "column": 15}}, "1": {"start": {"line": 10, "column": 18}, "end": {"line": 10, "column": null}}, "2": {"start": {"line": 11, "column": 17}, "end": {"line": 11, "column": null}}, "3": {"start": {"line": 12, "column": 29}, "end": {"line": 12, "column": null}}, "4": {"start": {"line": 27, "column": 34}, "end": {"line": 64, "column": null}}, "5": {"start": {"line": 66, "column": 44}, "end": {"line": 109, "column": null}}, "6": {"start": {"line": 67, "column": 30}, "end": {"line": 67, "column": null}}, "7": {"start": {"line": 69, "column": 26}, "end": {"line": 71, "column": null}}, "8": {"start": {"line": 70, "column": 4}, "end": {"line": 70, "column": null}}, "9": {"start": {"line": 76, "column": 8}, "end": {"line": 76, "column": null}}, "10": {"start": {"line": 82, "column": 14}, "end": {"line": 82, "column": null}}, "11": {"start": {"line": 88, "column": 35}, "end": {"line": 88, "column": null}}, "12": {"start": {"line": 96, "column": 35}, "end": {"line": 96, "column": null}}, "13": {"start": {"line": 111, "column": 15}, "end": {"line": 111, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 66, "column": 44}, "end": {"line": 66, "column": 45}}, "loc": {"start": {"line": 66, "column": 63}, "end": {"line": 109, "column": null}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 69, "column": 26}, "end": {"line": 69, "column": 27}}, "loc": {"start": {"line": 69, "column": 27}, "end": {"line": 71, "column": null}}}, "2": {"name": "(anonymous_4)", "decl": {"start": {"line": 75, "column": 23}, "end": {"line": 75, "column": 24}}, "loc": {"start": {"line": 76, "column": 8}, "end": {"line": 76, "column": null}}}, "3": {"name": "(anonymous_5)", "decl": {"start": {"line": 81, "column": 31}, "end": {"line": 81, "column": 32}}, "loc": {"start": {"line": 82, "column": 14}, "end": {"line": 82, "column": null}}}, "4": {"name": "(anonymous_6)", "decl": {"start": {"line": 88, "column": 29}, "end": {"line": 88, "column": 35}}, "loc": {"start": {"line": 88, "column": 35}, "end": {"line": 88, "column": null}}}, "5": {"name": "(anonymous_7)", "decl": {"start": {"line": 96, "column": 29}, "end": {"line": 96, "column": 35}}, "loc": {"start": {"line": 96, "column": 35}, "end": {"line": 96, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 66, "column": 47}, "end": {"line": 66, "column": 61}}, "type": "default-arg", "locations": [{"start": {"line": 66, "column": 59}, "end": {"line": 66, "column": 61}}]}, "1": {"loc": {"start": {"line": 84, "column": 18}, "end": {"line": 94, "column": 19}}, "type": "cond-expr", "locations": [{"start": {"line": 84, "column": 18}, "end": {"line": 94, "column": 19}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "b": {"0": [0], "1": [0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\FooterNewsletter.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\FooterNewsletter.tsx", "statementMap": {"0": {"start": {"line": 183, "column": 0}, "end": {"line": 183, "column": 15}}, "1": {"start": {"line": 10, "column": 32}, "end": {"line": 10, "column": null}}, "2": {"start": {"line": 11, "column": 23}, "end": {"line": 11, "column": null}}, "3": {"start": {"line": 12, "column": 47}, "end": {"line": 12, "column": null}}, "4": {"start": {"line": 13, "column": 29}, "end": {"line": 13, "column": null}}, "5": {"start": {"line": 14, "column": 33}, "end": {"line": 14, "column": null}}, "6": {"start": {"line": 27, "column": 58}, "end": {"line": 181, "column": null}}, "7": {"start": {"line": 28, "column": 28}, "end": {"line": 33, "column": null}}, "8": {"start": {"line": 35, "column": 34}, "end": {"line": 35, "column": null}}, "9": {"start": {"line": 37, "column": 24}, "end": {"line": 40, "column": null}}, "10": {"start": {"line": 38, "column": 23}, "end": {"line": 38, "column": null}}, "11": {"start": {"line": 39, "column": 4}, "end": {"line": 39, "column": null}}, "12": {"start": {"line": 42, "column": 23}, "end": {"line": 86, "column": null}}, "13": {"start": {"line": 43, "column": 4}, "end": {"line": 43, "column": null}}, "14": {"start": {"line": 45, "column": 4}, "end": {"line": 48, "column": null}}, "15": {"start": {"line": 46, "column": 6}, "end": {"line": 46, "column": null}}, "16": {"start": {"line": 46, "column": 24}, "end": {"line": 46, "column": null}}, "17": {"start": {"line": 47, "column": 6}, "end": {"line": 47, "column": null}}, "18": {"start": {"line": 50, "column": 4}, "end": {"line": 50, "column": null}}, "19": {"start": {"line": 50, "column": 22}, "end": {"line": 50, "column": null}}, "20": {"start": {"line": 52, "column": 4}, "end": {"line": 85, "column": null}}, "21": {"start": {"line": 55, "column": 6}, "end": {"line": 55, "column": null}}, "22": {"start": {"line": 55, "column": 35}, "end": {"line": 55, "column": null}}, "23": {"start": {"line": 57, "column": 6}, "end": {"line": 62, "column": null}}, "24": {"start": {"line": 57, "column": 24}, "end": {"line": 62, "column": null}}, "25": {"start": {"line": 64, "column": 6}, "end": {"line": 67, "column": null}}, "26": {"start": {"line": 70, "column": 6}, "end": {"line": 72, "column": null}}, "27": {"start": {"line": 71, "column": 8}, "end": {"line": 71, "column": null}}, "28": {"start": {"line": 71, "column": 26}, "end": {"line": 71, "column": null}}, "29": {"start": {"line": 75, "column": 6}, "end": {"line": 79, "column": null}}, "30": {"start": {"line": 75, "column": 24}, "end": {"line": 79, "column": null}}, "31": {"start": {"line": 81, "column": 6}, "end": {"line": 84, "column": null}}, "32": {"start": {"line": 88, "column": 28}, "end": {"line": 94, "column": null}}, "33": {"start": {"line": 89, "column": 4}, "end": {"line": 93, "column": null}}, "34": {"start": {"line": 89, "column": 22}, "end": {"line": 93, "column": null}}, "35": {"start": {"line": 183, "column": 15}, "end": {"line": 183, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 27, "column": 58}, "end": {"line": 27, "column": 59}}, "loc": {"start": {"line": 27, "column": 77}, "end": {"line": 181, "column": null}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 37, "column": 24}, "end": {"line": 37, "column": 25}}, "loc": {"start": {"line": 37, "column": 25}, "end": {"line": 40, "column": null}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 42, "column": 23}, "end": {"line": 42, "column": 30}}, "loc": {"start": {"line": 42, "column": 30}, "end": {"line": 86, "column": null}}}, "3": {"name": "(anonymous_7)", "decl": {"start": {"line": 46, "column": 15}, "end": {"line": 46, "column": 24}}, "loc": {"start": {"line": 46, "column": 24}, "end": {"line": 46, "column": null}}}, "4": {"name": "(anonymous_8)", "decl": {"start": {"line": 50, "column": 13}, "end": {"line": 50, "column": 22}}, "loc": {"start": {"line": 50, "column": 22}, "end": {"line": 50, "column": null}}}, "5": {"name": "(anonymous_9)", "decl": {"start": {"line": 55, "column": 24}, "end": {"line": 55, "column": 35}}, "loc": {"start": {"line": 55, "column": 35}, "end": {"line": 55, "column": null}}}, "6": {"name": "(anonymous_10)", "decl": {"start": {"line": 57, "column": 15}, "end": {"line": 57, "column": 24}}, "loc": {"start": {"line": 57, "column": 24}, "end": {"line": 62, "column": null}}}, "7": {"name": "(anonymous_11)", "decl": {"start": {"line": 70, "column": 17}, "end": {"line": 70, "column": null}}, "loc": {"start": {"line": 70, "column": 17}, "end": {"line": 72, "column": 9}}}, "8": {"name": "(anonymous_12)", "decl": {"start": {"line": 71, "column": 17}, "end": {"line": 71, "column": 26}}, "loc": {"start": {"line": 71, "column": 26}, "end": {"line": 71, "column": null}}}, "9": {"name": "(anonymous_13)", "decl": {"start": {"line": 75, "column": 15}, "end": {"line": 75, "column": 24}}, "loc": {"start": {"line": 75, "column": 24}, "end": {"line": 79, "column": null}}}, "10": {"name": "(anonymous_14)", "decl": {"start": {"line": 88, "column": 28}, "end": {"line": 88, "column": 29}}, "loc": {"start": {"line": 88, "column": 29}, "end": {"line": 94, "column": null}}}, "11": {"name": "(anonymous_15)", "decl": {"start": {"line": 89, "column": 13}, "end": {"line": 89, "column": 22}}, "loc": {"start": {"line": 89, "column": 22}, "end": {"line": 93, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 27, "column": 61}, "end": {"line": 27, "column": 75}}, "type": "default-arg", "locations": [{"start": {"line": 27, "column": 73}, "end": {"line": 27, "column": 75}}]}, "1": {"loc": {"start": {"line": 45, "column": 4}, "end": {"line": 48, "column": null}}, "type": "if", "locations": [{"start": {"line": 45, "column": 4}, "end": {"line": 48, "column": null}}]}, "2": {"loc": {"start": {"line": 115, "column": 22}, "end": {"line": 115, "column": 61}}, "type": "binary-expr", "locations": [{"start": {"line": 115, "column": 22}, "end": {"line": 115, "column": 40}}, {"start": {"line": 115, "column": 44}, "end": {"line": 115, "column": 61}}]}, "3": {"loc": {"start": {"line": 129, "column": 9}, "end": {"line": 129, "column": 20}}, "type": "binary-expr", "locations": [{"start": {"line": 129, "column": 9}, "end": {"line": 129, "column": 20}}]}, "4": {"loc": {"start": {"line": 142, "column": 20}, "end": {"line": 142, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 142, "column": 20}, "end": {"line": 142, "column": 38}}, {"start": {"line": 142, "column": 42}, "end": {"line": 142, "column": 59}}, {"start": {"line": 142, "column": 63}, "end": {"line": 142, "column": null}}]}, "5": {"loc": {"start": {"line": 153, "column": 11}, "end": {"line": 164, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 154, "column": 12}, "end": {"line": 158, "column": 20}}, {"start": {"line": 158, "column": 14}, "end": {"line": 164, "column": null}}]}, "6": {"loc": {"start": {"line": 158, "column": 14}, "end": {"line": 164, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 159, "column": 12}, "end": {"line": 164, "column": null}}, {"start": {"line": 164, "column": 12}, "end": {"line": 164, "column": null}}]}, "7": {"loc": {"start": {"line": 169, "column": 7}, "end": {"line": 169, "column": 24}}, "type": "binary-expr", "locations": [{"start": {"line": 169, "column": 7}, "end": {"line": 169, "column": 24}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0}, "b": {"0": [0], "1": [0], "2": [0, 0], "3": [0], "4": [0, 0, 0], "5": [0, 0], "6": [0, 0], "7": [0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\Header.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\Header.tsx", "statementMap": {"0": {"start": {"line": 93, "column": 0}, "end": {"line": 93, "column": 15}}, "1": {"start": {"line": 25, "column": 32}, "end": {"line": 25, "column": null}}, "2": {"start": {"line": 26, "column": 24}, "end": {"line": 26, "column": null}}, "3": {"start": {"line": 27, "column": 17}, "end": {"line": 27, "column": null}}, "4": {"start": {"line": 28, "column": 18}, "end": {"line": 28, "column": null}}, "5": {"start": {"line": 29, "column": 29}, "end": {"line": 29, "column": null}}, "6": {"start": {"line": 30, "column": 33}, "end": {"line": 30, "column": null}}, "7": {"start": {"line": 31, "column": 23}, "end": {"line": 31, "column": null}}, "8": {"start": {"line": 32, "column": 23}, "end": {"line": 32, "column": null}}, "9": {"start": {"line": 33, "column": 29}, "end": {"line": 33, "column": null}}, "10": {"start": {"line": 38, "column": 15}, "end": {"line": 91, "column": null}}, "11": {"start": {"line": 40, "column": 38}, "end": {"line": 40, "column": null}}, "12": {"start": {"line": 41, "column": 30}, "end": {"line": 41, "column": null}}, "13": {"start": {"line": 46, "column": 33}, "end": {"line": 49, "column": null}}, "14": {"start": {"line": 47, "column": 4}, "end": {"line": 47, "column": null}}, "15": {"start": {"line": 48, "column": 4}, "end": {"line": 48, "column": null}}, "16": {"start": {"line": 87, "column": 23}, "end": {"line": 87, "column": null}}, "17": {"start": {"line": 93, "column": 15}, "end": {"line": 93, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_5)", "decl": {"start": {"line": 38, "column": 15}, "end": {"line": 38, "column": null}}, "loc": {"start": {"line": 38, "column": 15}, "end": {"line": 91, "column": null}}}, "1": {"name": "(anonymous_6)", "decl": {"start": {"line": 46, "column": 33}, "end": {"line": 46, "column": null}}, "loc": {"start": {"line": 46, "column": 33}, "end": {"line": 49, "column": null}}}, "2": {"name": "(anonymous_7)", "decl": {"start": {"line": 87, "column": 17}, "end": {"line": 87, "column": 23}}, "loc": {"start": {"line": 87, "column": 23}, "end": {"line": 87, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 78, "column": 26}, "end": {"line": 78, "column": 45}}, "type": "cond-expr", "locations": [{"start": {"line": 78, "column": 26}, "end": {"line": 78, "column": 45}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {"0": [0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\MobileMenu.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\MobileMenu.tsx", "statementMap": {"0": {"start": {"line": 114, "column": 0}, "end": {"line": 114, "column": 15}}, "1": {"start": {"line": 30, "column": 18}, "end": {"line": 30, "column": null}}, "2": {"start": {"line": 31, "column": 40}, "end": {"line": 31, "column": null}}, "3": {"start": {"line": 32, "column": 18}, "end": {"line": 32, "column": null}}, "4": {"start": {"line": 33, "column": 23}, "end": {"line": 33, "column": null}}, "5": {"start": {"line": 34, "column": 29}, "end": {"line": 34, "column": null}}, "6": {"start": {"line": 35, "column": 33}, "end": {"line": 35, "column": null}}, "7": {"start": {"line": 36, "column": 33}, "end": {"line": 36, "column": null}}, "8": {"start": {"line": 43, "column": 46}, "end": {"line": 112, "column": null}}, "9": {"start": {"line": 114, "column": 15}, "end": {"line": 114, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 43, "column": 46}, "end": {"line": 43, "column": 47}}, "loc": {"start": {"line": 43, "column": 66}, "end": {"line": 112, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 46, "column": 7}, "end": {"line": 46, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 46, "column": 7}, "end": {"line": 46, "column": null}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "f": {"0": 0}, "b": {"0": [0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\Navigation.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\Navigation.tsx", "statementMap": {"0": {"start": {"line": 80, "column": 0}, "end": {"line": 80, "column": 15}}, "1": {"start": {"line": 25, "column": 18}, "end": {"line": 25, "column": null}}, "2": {"start": {"line": 26, "column": 27}, "end": {"line": 26, "column": null}}, "3": {"start": {"line": 27, "column": 29}, "end": {"line": 27, "column": null}}, "4": {"start": {"line": 35, "column": 46}, "end": {"line": 78, "column": null}}, "5": {"start": {"line": 40, "column": 30}, "end": {"line": 40, "column": null}}, "6": {"start": {"line": 42, "column": 26}, "end": {"line": 49, "column": null}}, "7": {"start": {"line": 43, "column": 20}, "end": {"line": 43, "column": null}}, "8": {"start": {"line": 44, "column": 4}, "end": {"line": 47, "column": null}}, "9": {"start": {"line": 45, "column": 6}, "end": {"line": 45, "column": null}}, "10": {"start": {"line": 46, "column": 6}, "end": {"line": 46, "column": null}}, "11": {"start": {"line": 48, "column": 4}, "end": {"line": 48, "column": null}}, "12": {"start": {"line": 51, "column": 19}, "end": {"line": 51, "column": 34}}, "13": {"start": {"line": 53, "column": 22}, "end": {"line": 55, "column": null}}, "14": {"start": {"line": 60, "column": 8}, "end": {"line": 61, "column": null}}, "15": {"start": {"line": 62, "column": 25}, "end": {"line": 62, "column": null}}, "16": {"start": {"line": 80, "column": 15}, "end": {"line": 80, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 35, "column": 46}, "end": {"line": 35, "column": 47}}, "loc": {"start": {"line": 39, "column": 1}, "end": {"line": 78, "column": null}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 42, "column": 26}, "end": {"line": 42, "column": 27}}, "loc": {"start": {"line": 42, "column": 46}, "end": {"line": 49, "column": null}}}, "2": {"name": "(anonymous_4)", "decl": {"start": {"line": 59, "column": 20}, "end": {"line": 59, "column": 21}}, "loc": {"start": {"line": 60, "column": 8}, "end": {"line": 61, "column": null}}}, "3": {"name": "(anonymous_5)", "decl": {"start": {"line": 62, "column": 19}, "end": {"line": 62, "column": 25}}, "loc": {"start": {"line": 62, "column": 25}, "end": {"line": 62, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 36, "column": 2}, "end": {"line": 36, "column": 18}}, "type": "default-arg", "locations": [{"start": {"line": 36, "column": 13}, "end": {"line": 36, "column": 18}}]}, "1": {"loc": {"start": {"line": 38, "column": 2}, "end": {"line": 38, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 38, "column": 14}, "end": {"line": 38, "column": 16}}]}, "2": {"loc": {"start": {"line": 44, "column": 4}, "end": {"line": 47, "column": null}}, "type": "if", "locations": [{"start": {"line": 44, "column": 4}, "end": {"line": 47, "column": null}}]}, "3": {"loc": {"start": {"line": 46, "column": 29}, "end": {"line": 46, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 46, "column": 40}, "end": {"line": 46, "column": 55}}, {"start": {"line": 46, "column": 55}, "end": {"line": 46, "column": null}}]}, "4": {"loc": {"start": {"line": 53, "column": 22}, "end": {"line": 55, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 54, "column": 6}, "end": {"line": 54, "column": null}}, {"start": {"line": 55, "column": 6}, "end": {"line": 55, "column": null}}]}, "5": {"loc": {"start": {"line": 66, "column": 14}, "end": {"line": 68, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 67, "column": 16}, "end": {"line": 67, "column": null}}, {"start": {"line": 68, "column": 16}, "end": {"line": 68, "column": null}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0}, "b": {"0": [0], "1": [0], "2": [0], "3": [0, 0], "4": [0, 0], "5": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\index.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\index.ts", "statementMap": {"0": {"start": {"line": 9, "column": 20}, "end": {"line": 9, "column": 34}}, "1": {"start": {"line": 12, "column": 20}, "end": {"line": 12, "column": 37}}, "2": {"start": {"line": 13, "column": 20}, "end": {"line": 13, "column": 44}}, "3": {"start": {"line": 8, "column": 20}, "end": {"line": 8, "column": 34}}, "4": {"start": {"line": 11, "column": 20}, "end": {"line": 11, "column": 38}}, "5": {"start": {"line": 10, "column": 20}, "end": {"line": 10, "column": 38}}, "6": {"start": {"line": 8, "column": 34}, "end": {"line": 8, "column": null}}, "7": {"start": {"line": 9, "column": 34}, "end": {"line": 9, "column": null}}, "8": {"start": {"line": 10, "column": 38}, "end": {"line": 10, "column": null}}, "9": {"start": {"line": 11, "column": 38}, "end": {"line": 11, "column": null}}, "10": {"start": {"line": 12, "column": 37}, "end": {"line": 12, "column": null}}, "11": {"start": {"line": 13, "column": 44}, "end": {"line": 13, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0}, "f": {}, "b": {}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\AboutSnippet.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\AboutSnippet.tsx", "statementMap": {"0": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 15}}, "1": {"start": {"line": 3, "column": 18}, "end": {"line": 3, "column": null}}, "2": {"start": {"line": 4, "column": 23}, "end": {"line": 4, "column": null}}, "3": {"start": {"line": 5, "column": 17}, "end": {"line": 5, "column": null}}, "4": {"start": {"line": 6, "column": 27}, "end": {"line": 6, "column": null}}, "5": {"start": {"line": 7, "column": 34}, "end": {"line": 7, "column": null}}, "6": {"start": {"line": 9, "column": 21}, "end": {"line": 61, "column": null}}, "7": {"start": {"line": 63, "column": 15}, "end": {"line": 63, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 9, "column": 21}, "end": {"line": 9, "column": null}}, "loc": {"start": {"line": 9, "column": 21}, "end": {"line": 61, "column": null}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "f": {"0": 0}, "b": {}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\Contact.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\Contact.tsx", "statementMap": {"0": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 15}}, "1": {"start": {"line": 3, "column": 18}, "end": {"line": 3, "column": null}}, "2": {"start": {"line": 4, "column": 23}, "end": {"line": 4, "column": null}}, "3": {"start": {"line": 5, "column": 29}, "end": {"line": 5, "column": null}}, "4": {"start": {"line": 6, "column": 35}, "end": {"line": 6, "column": null}}, "5": {"start": {"line": 7, "column": 33}, "end": {"line": 7, "column": null}}, "6": {"start": {"line": 8, "column": 24}, "end": {"line": 8, "column": null}}, "7": {"start": {"line": 10, "column": 16}, "end": {"line": 53, "column": null}}, "8": {"start": {"line": 11, "column": 26}, "end": {"line": 11, "column": null}}, "9": {"start": {"line": 55, "column": 15}, "end": {"line": 55, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 10, "column": 16}, "end": {"line": 10, "column": null}}, "loc": {"start": {"line": 10, "column": 16}, "end": {"line": 53, "column": null}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "f": {"0": 0}, "b": {}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\ContactForm.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\ContactForm.tsx", "statementMap": {"0": {"start": {"line": 263, "column": 0}, "end": {"line": 263, "column": 15}}, "1": {"start": {"line": 39, "column": 18}, "end": {"line": 39, "column": null}}, "2": {"start": {"line": 40, "column": 23}, "end": {"line": 40, "column": null}}, "3": {"start": {"line": 41, "column": 51}, "end": {"line": 41, "column": null}}, "4": {"start": {"line": 42, "column": 33}, "end": {"line": 42, "column": null}}, "5": {"start": {"line": 47, "column": 20}, "end": {"line": 261, "column": null}}, "6": {"start": {"line": 59, "column": 6}, "end": {"line": 59, "column": null}}, "7": {"start": {"line": 61, "column": 26}, "end": {"line": 61, "column": null}}, "8": {"start": {"line": 63, "column": 23}, "end": {"line": 66, "column": null}}, "9": {"start": {"line": 64, "column": 4}, "end": {"line": 64, "column": null}}, "10": {"start": {"line": 65, "column": 4}, "end": {"line": 65, "column": null}}, "11": {"start": {"line": 68, "column": 23}, "end": {"line": 70, "column": null}}, "12": {"start": {"line": 69, "column": 4}, "end": {"line": 69, "column": null}}, "13": {"start": {"line": 72, "column": 21}, "end": {"line": 74, "column": null}}, "14": {"start": {"line": 73, "column": 4}, "end": {"line": 73, "column": null}}, "15": {"start": {"line": 76, "column": 22}, "end": {"line": 78, "column": null}}, "16": {"start": {"line": 77, "column": 4}, "end": {"line": 77, "column": null}}, "17": {"start": {"line": 81, "column": 2}, "end": {"line": 103, "column": null}}, "18": {"start": {"line": 263, "column": 15}, "end": {"line": 263, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 47, "column": 20}, "end": {"line": 47, "column": null}}, "loc": {"start": {"line": 47, "column": 20}, "end": {"line": 261, "column": null}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 63, "column": 23}, "end": {"line": 63, "column": 30}}, "loc": {"start": {"line": 63, "column": 30}, "end": {"line": 66, "column": null}}}, "2": {"name": "(anonymous_4)", "decl": {"start": {"line": 68, "column": 23}, "end": {"line": 68, "column": 24}}, "loc": {"start": {"line": 68, "column": 24}, "end": {"line": 70, "column": null}}}, "3": {"name": "(anonymous_5)", "decl": {"start": {"line": 72, "column": 21}, "end": {"line": 72, "column": 22}}, "loc": {"start": {"line": 72, "column": 22}, "end": {"line": 74, "column": null}}}, "4": {"name": "(anonymous_6)", "decl": {"start": {"line": 76, "column": 22}, "end": {"line": 76, "column": 23}}, "loc": {"start": {"line": 76, "column": 23}, "end": {"line": 78, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 81, "column": 2}, "end": {"line": 103, "column": null}}, "type": "if", "locations": [{"start": {"line": 81, "column": 2}, "end": {"line": 103, "column": null}}]}, "1": {"loc": {"start": {"line": 81, "column": 6}, "end": {"line": 81, "column": 36}}, "type": "binary-expr", "locations": [{"start": {"line": 81, "column": 6}, "end": {"line": 81, "column": 21}}, {"start": {"line": 81, "column": 21}, "end": {"line": 81, "column": 36}}]}, "2": {"loc": {"start": {"line": 108, "column": 7}, "end": {"line": 108, "column": 21}}, "type": "binary-expr", "locations": [{"start": {"line": 108, "column": 7}, "end": {"line": 108, "column": 21}}]}, "3": {"loc": {"start": {"line": 128, "column": 12}, "end": {"line": 128, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 128, "column": 26}, "end": {"line": 128, "column": 85}}, {"start": {"line": 128, "column": 85}, "end": {"line": 128, "column": null}}]}, "4": {"loc": {"start": {"line": 133, "column": 9}, "end": {"line": 133, "column": 20}}, "type": "binary-expr", "locations": [{"start": {"line": 133, "column": 9}, "end": {"line": 133, "column": 20}}]}, "5": {"loc": {"start": {"line": 152, "column": 12}, "end": {"line": 152, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 152, "column": 27}, "end": {"line": 152, "column": 86}}, {"start": {"line": 152, "column": 86}, "end": {"line": 152, "column": null}}]}, "6": {"loc": {"start": {"line": 157, "column": 9}, "end": {"line": 157, "column": 21}}, "type": "binary-expr", "locations": [{"start": {"line": 157, "column": 9}, "end": {"line": 157, "column": 21}}]}, "7": {"loc": {"start": {"line": 176, "column": 12}, "end": {"line": 176, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 176, "column": 29}, "end": {"line": 176, "column": 88}}, {"start": {"line": 176, "column": 88}, "end": {"line": 176, "column": null}}]}, "8": {"loc": {"start": {"line": 181, "column": 9}, "end": {"line": 181, "column": 23}}, "type": "binary-expr", "locations": [{"start": {"line": 181, "column": 9}, "end": {"line": 181, "column": 23}}]}, "9": {"loc": {"start": {"line": 199, "column": 12}, "end": {"line": 199, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 199, "column": 33}, "end": {"line": 199, "column": 92}}, {"start": {"line": 199, "column": 92}, "end": {"line": 199, "column": null}}]}, "10": {"loc": {"start": {"line": 210, "column": 9}, "end": {"line": 210, "column": 27}}, "type": "binary-expr", "locations": [{"start": {"line": 210, "column": 9}, "end": {"line": 210, "column": 27}}]}, "11": {"loc": {"start": {"line": 229, "column": 12}, "end": {"line": 229, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 229, "column": 29}, "end": {"line": 229, "column": 88}}, {"start": {"line": 229, "column": 88}, "end": {"line": 229, "column": null}}]}, "12": {"loc": {"start": {"line": 234, "column": 9}, "end": {"line": 234, "column": 23}}, "type": "binary-expr", "locations": [{"start": {"line": 234, "column": 9}, "end": {"line": 234, "column": 23}}]}, "13": {"loc": {"start": {"line": 248, "column": 9}, "end": {"line": 248, "column": 65}}, "type": "cond-expr", "locations": [{"start": {"line": 248, "column": 24}, "end": {"line": 248, "column": 39}}, {"start": {"line": 248, "column": 39}, "end": {"line": 248, "column": 65}}]}, "14": {"loc": {"start": {"line": 252, "column": 7}, "end": {"line": 252, "column": 22}}, "type": "binary-expr", "locations": [{"start": {"line": 252, "column": 7}, "end": {"line": 252, "column": 22}}, {"start": {"line": 252, "column": 22}, "end": {"line": 252, "column": null}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "b": {"0": [0], "1": [0, 0], "2": [0], "3": [0, 0], "4": [0], "5": [0, 0], "6": [0], "7": [0, 0], "8": [0], "9": [0, 0], "10": [0], "11": [0, 0], "12": [0], "13": [0, 0], "14": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\DemoInput.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\DemoInput.tsx", "statementMap": {"0": {"start": {"line": 154, "column": 0}, "end": {"line": 154, "column": 15}}, "1": {"start": {"line": 37, "column": 18}, "end": {"line": 37, "column": null}}, "2": {"start": {"line": 38, "column": 23}, "end": {"line": 38, "column": null}}, "3": {"start": {"line": 39, "column": 37}, "end": {"line": 39, "column": null}}, "4": {"start": {"line": 50, "column": 44}, "end": {"line": 152, "column": null}}, "5": {"start": {"line": 58, "column": 25}, "end": {"line": 69, "column": null}}, "6": {"start": {"line": 59, "column": 4}, "end": {"line": 68, "column": null}}, "7": {"start": {"line": 61, "column": 8}, "end": {"line": 61, "column": null}}, "8": {"start": {"line": 63, "column": 8}, "end": {"line": 63, "column": null}}, "9": {"start": {"line": 65, "column": 8}, "end": {"line": 65, "column": null}}, "10": {"start": {"line": 67, "column": 8}, "end": {"line": 67, "column": null}}, "11": {"start": {"line": 71, "column": 23}, "end": {"line": 73, "column": null}}, "12": {"start": {"line": 72, "column": 4}, "end": {"line": 72, "column": null}}, "13": {"start": {"line": 75, "column": 23}, "end": {"line": 88, "column": null}}, "14": {"start": {"line": 76, "column": 4}, "end": {"line": 76, "column": null}}, "15": {"start": {"line": 76, "column": 23}, "end": {"line": 76, "column": null}}, "16": {"start": {"line": 78, "column": 4}, "end": {"line": 85, "column": null}}, "17": {"start": {"line": 79, "column": 6}, "end": {"line": 84, "column": null}}, "18": {"start": {"line": 80, "column": 8}, "end": {"line": 80, "column": null}}, "19": {"start": {"line": 81, "column": 8}, "end": {"line": 81, "column": null}}, "20": {"start": {"line": 83, "column": 8}, "end": {"line": 83, "column": null}}, "21": {"start": {"line": 87, "column": 4}, "end": {"line": 87, "column": null}}, "22": {"start": {"line": 90, "column": 25}, "end": {"line": 94, "column": null}}, "23": {"start": {"line": 91, "column": 4}, "end": {"line": 93, "column": null}}, "24": {"start": {"line": 92, "column": 6}, "end": {"line": 92, "column": null}}, "25": {"start": {"line": 110, "column": 27}, "end": {"line": 110, "column": null}}, "26": {"start": {"line": 154, "column": 15}, "end": {"line": 154, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 50, "column": 44}, "end": {"line": 50, "column": 45}}, "loc": {"start": {"line": 57, "column": 1}, "end": {"line": 152, "column": null}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 58, "column": 25}, "end": {"line": 58, "column": null}}, "loc": {"start": {"line": 58, "column": 25}, "end": {"line": 69, "column": null}}}, "2": {"name": "(anonymous_4)", "decl": {"start": {"line": 71, "column": 23}, "end": {"line": 71, "column": null}}, "loc": {"start": {"line": 71, "column": 23}, "end": {"line": 73, "column": null}}}, "3": {"name": "(anonymous_5)", "decl": {"start": {"line": 75, "column": 23}, "end": {"line": 75, "column": null}}, "loc": {"start": {"line": 75, "column": 23}, "end": {"line": 88, "column": null}}}, "4": {"name": "(anonymous_6)", "decl": {"start": {"line": 90, "column": 25}, "end": {"line": 90, "column": 26}}, "loc": {"start": {"line": 90, "column": 26}, "end": {"line": 94, "column": null}}}, "5": {"name": "(anonymous_7)", "decl": {"start": {"line": 110, "column": 20}, "end": {"line": 110, "column": 21}}, "loc": {"start": {"line": 110, "column": 27}, "end": {"line": 110, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 56, "column": 2}, "end": {"line": 56, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 56, "column": 14}, "end": {"line": 56, "column": 16}}]}, "1": {"loc": {"start": {"line": 59, "column": 4}, "end": {"line": 68, "column": null}}, "type": "switch", "locations": [{"start": {"line": 60, "column": 6}, "end": {"line": 61, "column": null}}, {"start": {"line": 62, "column": 6}, "end": {"line": 63, "column": null}}, {"start": {"line": 64, "column": 6}, "end": {"line": 65, "column": null}}, {"start": {"line": 66, "column": 6}, "end": {"line": 67, "column": null}}]}, "2": {"loc": {"start": {"line": 61, "column": 15}, "end": {"line": 61, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 61, "column": 15}, "end": {"line": 61, "column": 30}}, {"start": {"line": 61, "column": 30}, "end": {"line": 61, "column": null}}]}, "3": {"loc": {"start": {"line": 63, "column": 15}, "end": {"line": 63, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 63, "column": 15}, "end": {"line": 63, "column": 30}}, {"start": {"line": 63, "column": 30}, "end": {"line": 63, "column": null}}]}, "4": {"loc": {"start": {"line": 65, "column": 15}, "end": {"line": 65, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 65, "column": 15}, "end": {"line": 65, "column": 30}}, {"start": {"line": 65, "column": 30}, "end": {"line": 65, "column": null}}]}, "5": {"loc": {"start": {"line": 67, "column": 15}, "end": {"line": 67, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 67, "column": 15}, "end": {"line": 67, "column": 30}}, {"start": {"line": 67, "column": 30}, "end": {"line": 67, "column": null}}]}, "6": {"loc": {"start": {"line": 72, "column": 11}, "end": {"line": 72, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 72, "column": 37}, "end": {"line": 72, "column": 45}}, {"start": {"line": 72, "column": 45}, "end": {"line": 72, "column": null}}]}, "7": {"loc": {"start": {"line": 76, "column": 4}, "end": {"line": 76, "column": null}}, "type": "if", "locations": [{"start": {"line": 76, "column": 4}, "end": {"line": 76, "column": null}}]}, "8": {"loc": {"start": {"line": 78, "column": 4}, "end": {"line": 85, "column": null}}, "type": "if", "locations": [{"start": {"line": 78, "column": 4}, "end": {"line": 85, "column": null}}]}, "9": {"loc": {"start": {"line": 91, "column": 4}, "end": {"line": 93, "column": null}}, "type": "if", "locations": [{"start": {"line": 91, "column": 4}, "end": {"line": 93, "column": null}}]}, "10": {"loc": {"start": {"line": 91, "column": 8}, "end": {"line": 91, "column": 45}}, "type": "binary-expr", "locations": [{"start": {"line": 91, "column": 8}, "end": {"line": 91, "column": 29}}, {"start": {"line": 91, "column": 29}, "end": {"line": 91, "column": 45}}]}, "11": {"loc": {"start": {"line": 101, "column": 12}, "end": {"line": 103, "column": 13}}, "type": "cond-expr", "locations": [{"start": {"line": 101, "column": 12}, "end": {"line": 103, "column": 13}}]}, "12": {"loc": {"start": {"line": 129, "column": 12}, "end": {"line": 131, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 130, "column": 14}, "end": {"line": 130, "column": null}}, {"start": {"line": 131, "column": 14}, "end": {"line": 131, "column": null}}]}, "13": {"loc": {"start": {"line": 134, "column": 20}, "end": {"line": 134, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 134, "column": 37}, "end": {"line": 134, "column": 55}}, {"start": {"line": 134, "column": 55}, "end": {"line": 134, "column": null}}]}, "14": {"loc": {"start": {"line": 135, "column": 18}, "end": {"line": 135, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 135, "column": 35}, "end": {"line": 135, "column": 53}}, {"start": {"line": 135, "column": 53}, "end": {"line": 135, "column": null}}]}, "15": {"loc": {"start": {"line": 138, "column": 9}, "end": {"line": 138, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 138, "column": 35}, "end": {"line": 138, "column": 62}}, {"start": {"line": 138, "column": 62}, "end": {"line": 138, "column": null}}]}, "16": {"loc": {"start": {"line": 141, "column": 7}, "end": {"line": 141, "column": 34}}, "type": "binary-expr", "locations": [{"start": {"line": 141, "column": 7}, "end": {"line": 141, "column": 34}}, {"start": {"line": 141, "column": 34}, "end": {"line": 141, "column": 43}}, {"start": {"line": 141, "column": 43}, "end": {"line": 141, "column": null}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "b": {"0": [0], "1": [0, 0, 0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0], "8": [0], "9": [0], "10": [0, 0], "11": [0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\DemoPreview.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\DemoPreview.tsx", "statementMap": {"0": {"start": {"line": 209, "column": 0}, "end": {"line": 209, "column": 15}}, "1": {"start": {"line": 37, "column": 18}, "end": {"line": 37, "column": null}}, "2": {"start": {"line": 38, "column": 40}, "end": {"line": 38, "column": null}}, "3": {"start": {"line": 39, "column": 62}, "end": {"line": 39, "column": null}}, "4": {"start": {"line": 40, "column": 33}, "end": {"line": 40, "column": null}}, "5": {"start": {"line": 50, "column": 48}, "end": {"line": 207, "column": null}}, "6": {"start": {"line": 57, "column": 28}, "end": {"line": 98, "column": null}}, "7": {"start": {"line": 58, "column": 4}, "end": {"line": 97, "column": null}}, "8": {"start": {"line": 60, "column": 8}, "end": {"line": 70, "column": null}}, "9": {"start": {"line": 72, "column": 8}, "end": {"line": 82, "column": null}}, "10": {"start": {"line": 84, "column": 8}, "end": {"line": 94, "column": null}}, "11": {"start": {"line": 96, "column": 8}, "end": {"line": 96, "column": null}}, "12": {"start": {"line": 100, "column": 18}, "end": {"line": 100, "column": null}}, "13": {"start": {"line": 101, "column": 2}, "end": {"line": 101, "column": null}}, "14": {"start": {"line": 101, "column": 16}, "end": {"line": 101, "column": null}}, "15": {"start": {"line": 158, "column": 24}, "end": {"line": 158, "column": 37}}, "16": {"start": {"line": 169, "column": 16}, "end": {"line": 170, "column": null}}, "17": {"start": {"line": 209, "column": 15}, "end": {"line": 209, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 50, "column": 48}, "end": {"line": 50, "column": 49}}, "loc": {"start": {"line": 56, "column": 1}, "end": {"line": 207, "column": null}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 57, "column": 28}, "end": {"line": 57, "column": null}}, "loc": {"start": {"line": 57, "column": 28}, "end": {"line": 98, "column": null}}}, "2": {"name": "(anonymous_4)", "decl": {"start": {"line": 157, "column": 37}, "end": {"line": 157, "column": 38}}, "loc": {"start": {"line": 158, "column": 24}, "end": {"line": 158, "column": 37}}}, "3": {"name": "(anonymous_5)", "decl": {"start": {"line": 168, "column": 36}, "end": {"line": 168, "column": 37}}, "loc": {"start": {"line": 169, "column": 16}, "end": {"line": 170, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 55, "column": 2}, "end": {"line": 55, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 55, "column": 14}, "end": {"line": 55, "column": 16}}]}, "1": {"loc": {"start": {"line": 58, "column": 4}, "end": {"line": 97, "column": null}}, "type": "switch", "locations": [{"start": {"line": 59, "column": 6}, "end": {"line": 70, "column": null}}, {"start": {"line": 71, "column": 6}, "end": {"line": 82, "column": null}}, {"start": {"line": 83, "column": 6}, "end": {"line": 94, "column": null}}, {"start": {"line": 95, "column": 6}, "end": {"line": 96, "column": null}}]}, "2": {"loc": {"start": {"line": 101, "column": 2}, "end": {"line": 101, "column": null}}, "type": "if", "locations": [{"start": {"line": 101, "column": 2}, "end": {"line": 101, "column": null}}]}, "3": {"loc": {"start": {"line": 105, "column": 7}, "end": {"line": 105, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 105, "column": 7}, "end": {"line": 105, "column": null}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0}, "b": {"0": [0], "1": [0, 0, 0, 0], "2": [0], "3": [0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\DemoTabs.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\DemoTabs.tsx", "statementMap": {"0": {"start": {"line": 114, "column": 0}, "end": {"line": 114, "column": 15}}, "1": {"start": {"line": 31, "column": 18}, "end": {"line": 31, "column": null}}, "2": {"start": {"line": 32, "column": 23}, "end": {"line": 32, "column": null}}, "3": {"start": {"line": 33, "column": 42}, "end": {"line": 33, "column": null}}, "4": {"start": {"line": 48, "column": 28}, "end": {"line": 67, "column": null}}, "5": {"start": {"line": 69, "column": 42}, "end": {"line": 112, "column": null}}, "6": {"start": {"line": 77, "column": 8}, "end": {"line": 78, "column": null}}, "7": {"start": {"line": 79, "column": 25}, "end": {"line": 79, "column": null}}, "8": {"start": {"line": 114, "column": 15}, "end": {"line": 114, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 69, "column": 42}, "end": {"line": 69, "column": 43}}, "loc": {"start": {"line": 73, "column": 1}, "end": {"line": 112, "column": null}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 76, "column": 20}, "end": {"line": 76, "column": 21}}, "loc": {"start": {"line": 77, "column": 8}, "end": {"line": 78, "column": null}}}, "2": {"name": "(anonymous_4)", "decl": {"start": {"line": 79, "column": 19}, "end": {"line": 79, "column": 25}}, "loc": {"start": {"line": 79, "column": 25}, "end": {"line": 79, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 72, "column": 2}, "end": {"line": 72, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 72, "column": 14}, "end": {"line": 72, "column": 16}}]}, "1": {"loc": {"start": {"line": 82, "column": 14}, "end": {"line": 84, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 83, "column": 16}, "end": {"line": 83, "column": null}}, {"start": {"line": 84, "column": 16}, "end": {"line": 84, "column": null}}]}, "2": {"loc": {"start": {"line": 92, "column": 11}, "end": {"line": 92, "column": 31}}, "type": "binary-expr", "locations": [{"start": {"line": 92, "column": 11}, "end": {"line": 92, "column": 31}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {"0": [0], "1": [0, 0], "2": [0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\Hero.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\Hero.tsx", "statementMap": {"0": {"start": {"line": 105, "column": 0}, "end": {"line": 105, "column": 15}}, "1": {"start": {"line": 3, "column": 18}, "end": {"line": 3, "column": null}}, "2": {"start": {"line": 4, "column": 23}, "end": {"line": 4, "column": null}}, "3": {"start": {"line": 5, "column": 23}, "end": {"line": 5, "column": null}}, "4": {"start": {"line": 6, "column": 49}, "end": {"line": 6, "column": null}}, "5": {"start": {"line": 7, "column": 33}, "end": {"line": 7, "column": null}}, "6": {"start": {"line": 9, "column": 13}, "end": {"line": 103, "column": null}}, "7": {"start": {"line": 10, "column": 23}, "end": {"line": 10, "column": null}}, "8": {"start": {"line": 11, "column": 30}, "end": {"line": 11, "column": null}}, "9": {"start": {"line": 13, "column": 26}, "end": {"line": 19, "column": null}}, "10": {"start": {"line": 14, "column": 20}, "end": {"line": 14, "column": null}}, "11": {"start": {"line": 15, "column": 4}, "end": {"line": 18, "column": null}}, "12": {"start": {"line": 16, "column": 6}, "end": {"line": 16, "column": null}}, "13": {"start": {"line": 17, "column": 6}, "end": {"line": 17, "column": null}}, "14": {"start": {"line": 42, "column": 31}, "end": {"line": 42, "column": null}}, "15": {"start": {"line": 105, "column": 15}, "end": {"line": 105, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 9, "column": 13}, "end": {"line": 9, "column": null}}, "loc": {"start": {"line": 9, "column": 13}, "end": {"line": 103, "column": null}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 13, "column": 26}, "end": {"line": 13, "column": 27}}, "loc": {"start": {"line": 13, "column": 27}, "end": {"line": 19, "column": null}}}, "2": {"name": "(anonymous_4)", "decl": {"start": {"line": 42, "column": 25}, "end": {"line": 42, "column": 31}}, "loc": {"start": {"line": 42, "column": 31}, "end": {"line": 42, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 15, "column": 4}, "end": {"line": 18, "column": null}}, "type": "if", "locations": [{"start": {"line": 15, "column": 4}, "end": {"line": 18, "column": null}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {"0": [0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\InteractiveDemo.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\InteractiveDemo.tsx", "statementMap": {"0": {"start": {"line": 127, "column": 0}, "end": {"line": 127, "column": 15}}, "1": {"start": {"line": 29, "column": 32}, "end": {"line": 29, "column": null}}, "2": {"start": {"line": 30, "column": 23}, "end": {"line": 30, "column": null}}, "3": {"start": {"line": 31, "column": 49}, "end": {"line": 31, "column": null}}, "4": {"start": {"line": 32, "column": 33}, "end": {"line": 32, "column": null}}, "5": {"start": {"line": 33, "column": 21}, "end": {"line": 33, "column": null}}, "6": {"start": {"line": 34, "column": 22}, "end": {"line": 34, "column": null}}, "7": {"start": {"line": 35, "column": 24}, "end": {"line": 35, "column": null}}, "8": {"start": {"line": 40, "column": 24}, "end": {"line": 125, "column": null}}, "9": {"start": {"line": 41, "column": 36}, "end": {"line": 41, "column": null}}, "10": {"start": {"line": 42, "column": 38}, "end": {"line": 42, "column": null}}, "11": {"start": {"line": 43, "column": 34}, "end": {"line": 43, "column": null}}, "12": {"start": {"line": 44, "column": 35}, "end": {"line": 44, "column": null}}, "13": {"start": {"line": 45, "column": 23}, "end": {"line": 45, "column": 55}}, "14": {"start": {"line": 47, "column": 24}, "end": {"line": 52, "column": null}}, "15": {"start": {"line": 48, "column": 4}, "end": {"line": 51, "column": null}}, "16": {"start": {"line": 49, "column": 6}, "end": {"line": 49, "column": null}}, "17": {"start": {"line": 50, "column": 6}, "end": {"line": 50, "column": null}}, "18": {"start": {"line": 54, "column": 26}, "end": {"line": 59, "column": null}}, "19": {"start": {"line": 55, "column": 4}, "end": {"line": 55, "column": null}}, "20": {"start": {"line": 56, "column": 4}, "end": {"line": 56, "column": null}}, "21": {"start": {"line": 57, "column": 4}, "end": {"line": 57, "column": null}}, "22": {"start": {"line": 58, "column": 4}, "end": {"line": 58, "column": null}}, "23": {"start": {"line": 61, "column": 34}, "end": {"line": 63, "column": null}}, "24": {"start": {"line": 62, "column": 4}, "end": {"line": 62, "column": null}}, "25": {"start": {"line": 127, "column": 15}, "end": {"line": 127, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_5)", "decl": {"start": {"line": 40, "column": 24}, "end": {"line": 40, "column": null}}, "loc": {"start": {"line": 40, "column": 24}, "end": {"line": 125, "column": null}}}, "1": {"name": "(anonymous_6)", "decl": {"start": {"line": 47, "column": 24}, "end": {"line": 47, "column": null}}, "loc": {"start": {"line": 47, "column": 24}, "end": {"line": 52, "column": null}}}, "2": {"name": "(anonymous_7)", "decl": {"start": {"line": 54, "column": 26}, "end": {"line": 54, "column": 27}}, "loc": {"start": {"line": 54, "column": 27}, "end": {"line": 59, "column": null}}}, "3": {"name": "(anonymous_8)", "decl": {"start": {"line": 61, "column": 34}, "end": {"line": 61, "column": null}}, "loc": {"start": {"line": 61, "column": 34}, "end": {"line": 63, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 48, "column": 4}, "end": {"line": 51, "column": null}}, "type": "if", "locations": [{"start": {"line": 48, "column": 4}, "end": {"line": 51, "column": null}}]}, "1": {"loc": {"start": {"line": 76, "column": 13}, "end": {"line": 76, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 76, "column": 13}, "end": {"line": 76, "column": 34}}, {"start": {"line": 76, "column": 38}, "end": {"line": 76, "column": null}}]}, "2": {"loc": {"start": {"line": 79, "column": 13}, "end": {"line": 79, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 79, "column": 13}, "end": {"line": 79, "column": 33}}, {"start": {"line": 79, "column": 37}, "end": {"line": 79, "column": null}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0}, "b": {"0": [0], "1": [0, 0], "2": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\Process.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\Process.tsx", "statementMap": {"0": {"start": {"line": 119, "column": 0}, "end": {"line": 119, "column": 15}}, "1": {"start": {"line": 3, "column": 18}, "end": {"line": 3, "column": null}}, "2": {"start": {"line": 4, "column": 23}, "end": {"line": 4, "column": null}}, "3": {"start": {"line": 5, "column": 40}, "end": {"line": 5, "column": null}}, "4": {"start": {"line": 6, "column": 34}, "end": {"line": 6, "column": null}}, "5": {"start": {"line": 8, "column": 16}, "end": {"line": 117, "column": null}}, "6": {"start": {"line": 9, "column": 16}, "end": {"line": 28, "column": null}}, "7": {"start": {"line": 50, "column": 12}, "end": {"line": 51, "column": null}}, "8": {"start": {"line": 119, "column": 15}, "end": {"line": 119, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 8, "column": 16}, "end": {"line": 8, "column": null}}, "loc": {"start": {"line": 8, "column": 16}, "end": {"line": 117, "column": null}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 49, "column": 21}, "end": {"line": 49, "column": 22}}, "loc": {"start": {"line": 50, "column": 12}, "end": {"line": 51, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 59, "column": 15}, "end": {"line": 59, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 59, "column": 15}, "end": {"line": 59, "column": null}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\ServicesOverview.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\ServicesOverview.tsx", "statementMap": {"0": {"start": {"line": 125, "column": 0}, "end": {"line": 125, "column": 15}}, "1": {"start": {"line": 3, "column": 18}, "end": {"line": 3, "column": null}}, "2": {"start": {"line": 4, "column": 23}, "end": {"line": 4, "column": null}}, "3": {"start": {"line": 5, "column": 60}, "end": {"line": 5, "column": null}}, "4": {"start": {"line": 6, "column": 17}, "end": {"line": 6, "column": null}}, "5": {"start": {"line": 7, "column": 35}, "end": {"line": 7, "column": null}}, "6": {"start": {"line": 8, "column": 43}, "end": {"line": 8, "column": null}}, "7": {"start": {"line": 10, "column": 25}, "end": {"line": 123, "column": null}}, "8": {"start": {"line": 11, "column": 27}, "end": {"line": 11, "column": null}}, "9": {"start": {"line": 13, "column": 19}, "end": {"line": 26, "column": null}}, "10": {"start": {"line": 28, "column": 29}, "end": {"line": 36, "column": null}}, "11": {"start": {"line": 30, "column": 4}, "end": {"line": 35, "column": null}}, "12": {"start": {"line": 31, "column": 6}, "end": {"line": 34, "column": null}}, "13": {"start": {"line": 58, "column": 12}, "end": {"line": 59, "column": null}}, "14": {"start": {"line": 94, "column": 18}, "end": {"line": 94, "column": 41}}, "15": {"start": {"line": 125, "column": 15}, "end": {"line": 125, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 10, "column": 25}, "end": {"line": 10, "column": null}}, "loc": {"start": {"line": 10, "column": 25}, "end": {"line": 123, "column": null}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 28, "column": 29}, "end": {"line": 28, "column": null}}, "loc": {"start": {"line": 28, "column": 29}, "end": {"line": 36, "column": null}}}, "2": {"name": "(anonymous_4)", "decl": {"start": {"line": 57, "column": 24}, "end": {"line": 57, "column": 25}}, "loc": {"start": {"line": 58, "column": 12}, "end": {"line": 59, "column": null}}}, "3": {"name": "(anonymous_5)", "decl": {"start": {"line": 93, "column": 38}, "end": {"line": 93, "column": 39}}, "loc": {"start": {"line": 94, "column": 18}, "end": {"line": 94, "column": 41}}}}, "branchMap": {"0": {"loc": {"start": {"line": 30, "column": 4}, "end": {"line": 35, "column": null}}, "type": "if", "locations": [{"start": {"line": 30, "column": 4}, "end": {"line": 35, "column": null}}]}, "1": {"loc": {"start": {"line": 30, "column": 8}, "end": {"line": 30, "column": 52}}, "type": "binary-expr", "locations": [{"start": {"line": 30, "column": 8}, "end": {"line": 30, "column": 41}}, {"start": {"line": 30, "column": 41}, "end": {"line": 30, "column": 52}}]}, "2": {"loc": {"start": {"line": 65, "column": 16}, "end": {"line": 65, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 65, "column": 34}, "end": {"line": 65, "column": 64}}, {"start": {"line": 65, "column": 64}, "end": {"line": 65, "column": null}}]}, "3": {"loc": {"start": {"line": 68, "column": 15}, "end": {"line": 68, "column": 30}}, "type": "binary-expr", "locations": [{"start": {"line": 68, "column": 15}, "end": {"line": 68, "column": 30}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0}, "b": {"0": [0], "1": [0, 0], "2": [0, 0], "3": [0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\index.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\index.ts", "statementMap": {"0": {"start": {"line": 12, "column": 20}, "end": {"line": 12, "column": 40}}, "1": {"start": {"line": 13, "column": 20}, "end": {"line": 13, "column": 35}}, "2": {"start": {"line": 14, "column": 20}, "end": {"line": 14, "column": 39}}, "3": {"start": {"line": 8, "column": 20}, "end": {"line": 8, "column": 32}}, "4": {"start": {"line": 9, "column": 20}, "end": {"line": 9, "column": 43}}, "5": {"start": {"line": 11, "column": 20}, "end": {"line": 11, "column": 35}}, "6": {"start": {"line": 10, "column": 20}, "end": {"line": 10, "column": 44}}, "7": {"start": {"line": 8, "column": 32}, "end": {"line": 8, "column": null}}, "8": {"start": {"line": 9, "column": 43}, "end": {"line": 9, "column": null}}, "9": {"start": {"line": 10, "column": 44}, "end": {"line": 10, "column": null}}, "10": {"start": {"line": 11, "column": 35}, "end": {"line": 11, "column": null}}, "11": {"start": {"line": 12, "column": 40}, "end": {"line": 12, "column": null}}, "12": {"start": {"line": 13, "column": 35}, "end": {"line": 13, "column": null}}, "13": {"start": {"line": 14, "column": 39}, "end": {"line": 14, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0}, "f": {}, "b": {}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\Button.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\Button.tsx", "statementMap": {"0": {"start": {"line": 116, "column": 0}, "end": {"line": 116, "column": 15}}, "1": {"start": {"line": 38, "column": 18}, "end": {"line": 38, "column": null}}, "2": {"start": {"line": 39, "column": 19}, "end": {"line": 39, "column": null}}, "3": {"start": {"line": 52, "column": 15}, "end": {"line": 111, "column": null}}, "4": {"start": {"line": 54, "column": 24}, "end": {"line": 54, "column": null}}, "5": {"start": {"line": 56, "column": 21}, "end": {"line": 60, "column": null}}, "6": {"start": {"line": 62, "column": 18}, "end": {"line": 66, "column": null}}, "7": {"start": {"line": 68, "column": 26}, "end": {"line": 72, "column": null}}, "8": {"start": {"line": 114, "column": 0}, "end": {"line": 114, "column": null}}, "9": {"start": {"line": 116, "column": 15}, "end": {"line": 116, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 53, "column": 2}, "end": {"line": 53, "column": 3}}, "loc": {"start": {"line": 53, "column": 101}, "end": {"line": 111, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 53, "column": 16}, "end": {"line": 53, "column": 35}}, "type": "default-arg", "locations": [{"start": {"line": 53, "column": 26}, "end": {"line": 53, "column": 35}}]}, "1": {"loc": {"start": {"line": 53, "column": 37}, "end": {"line": 53, "column": 48}}, "type": "default-arg", "locations": [{"start": {"line": 53, "column": 44}, "end": {"line": 53, "column": 48}}]}, "2": {"loc": {"start": {"line": 53, "column": 50}, "end": {"line": 53, "column": 67}}, "type": "default-arg", "locations": [{"start": {"line": 53, "column": 62}, "end": {"line": 53, "column": 67}}]}, "3": {"loc": {"start": {"line": 79, "column": 18}, "end": {"line": 79, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 79, "column": 18}, "end": {"line": 79, "column": 30}}, {"start": {"line": 79, "column": 30}, "end": {"line": 79, "column": null}}]}, "4": {"loc": {"start": {"line": 82, "column": 9}, "end": {"line": 107, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 83, "column": 10}, "end": {"line": 107, "column": null}}, {"start": {"line": 107, "column": 10}, "end": {"line": 107, "column": null}}]}}, "s": {"0": 186, "1": 2, "2": 2, "3": 2, "4": 186, "5": 186, "6": 186, "7": 186, "8": 2, "9": 2}, "f": {"0": 186}, "b": {"0": [25], "1": [47], "2": [25], "3": [186, 152], "4": [12, 174]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\Card.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\Card.tsx", "statementMap": {"0": {"start": {"line": 53, "column": 13}, "end": {"line": 53, "column": 24}}, "1": {"start": {"line": 64, "column": 13}, "end": {"line": 64, "column": 23}}, "2": {"start": {"line": 42, "column": 13}, "end": {"line": 42, "column": 23}}, "3": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 15}}, "4": {"start": {"line": 3, "column": 18}, "end": {"line": 3, "column": null}}, "5": {"start": {"line": 4, "column": 19}, "end": {"line": 4, "column": null}}, "6": {"start": {"line": 11, "column": 13}, "end": {"line": 36, "column": null}}, "7": {"start": {"line": 13, "column": 24}, "end": {"line": 13, "column": null}}, "8": {"start": {"line": 15, "column": 21}, "end": {"line": 19, "column": null}}, "9": {"start": {"line": 21, "column": 24}, "end": {"line": 24, "column": null}}, "10": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": null}}, "11": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": null}}, "12": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": null}}, "13": {"start": {"line": 73, "column": 0}, "end": {"line": 73, "column": null}}, "14": {"start": {"line": 75, "column": 15}, "end": {"line": 75, "column": 20}}}, "fnMap": {"0": {"name": "(anonymous_6)", "decl": {"start": {"line": 12, "column": 2}, "end": {"line": 12, "column": 3}}, "loc": {"start": {"line": 12, "column": 56}, "end": {"line": 36, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 12, "column": 16}, "end": {"line": 12, "column": 32}}, "type": "default-arg", "locations": [{"start": {"line": 12, "column": 26}, "end": {"line": 12, "column": 32}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 1, "5": 1, "6": 1, "7": 0, "8": 0, "9": 0, "10": 1, "11": 1, "12": 1, "13": 1, "14": 1}, "f": {"0": 0}, "b": {"0": [0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\Input.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\Input.tsx", "statementMap": {"0": {"start": {"line": 107, "column": 0}, "end": {"line": 107, "column": 15}}, "1": {"start": {"line": 45, "column": 18}, "end": {"line": 45, "column": null}}, "2": {"start": {"line": 46, "column": 19}, "end": {"line": 46, "column": null}}, "3": {"start": {"line": 59, "column": 14}, "end": {"line": 102, "column": null}}, "4": {"start": {"line": 61, "column": 24}, "end": {"line": 61, "column": null}}, "5": {"start": {"line": 63, "column": 21}, "end": {"line": 67, "column": null}}, "6": {"start": {"line": 69, "column": 25}, "end": {"line": 72, "column": null}}, "7": {"start": {"line": 75, "column": 32}, "end": {"line": 75, "column": null}}, "8": {"start": {"line": 76, "column": 30}, "end": {"line": 76, "column": null}}, "9": {"start": {"line": 105, "column": 0}, "end": {"line": 105, "column": null}}, "10": {"start": {"line": 107, "column": 15}, "end": {"line": 107, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 60, "column": 2}, "end": {"line": 60, "column": 3}}, "loc": {"start": {"line": 60, "column": 79}, "end": {"line": 102, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 60, "column": 16}, "end": {"line": 60, "column": 32}}, "type": "default-arg", "locations": [{"start": {"line": 60, "column": 26}, "end": {"line": 60, "column": 32}}]}, "1": {"loc": {"start": {"line": 75, "column": 32}, "end": {"line": 75, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 75, "column": 32}, "end": {"line": 75, "column": 55}}, {"start": {"line": 75, "column": 55}, "end": {"line": 75, "column": null}}]}, "2": {"loc": {"start": {"line": 76, "column": 30}, "end": {"line": 76, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 76, "column": 30}, "end": {"line": 76, "column": 53}}, {"start": {"line": 76, "column": 53}, "end": {"line": 76, "column": null}}]}, "3": {"loc": {"start": {"line": 80, "column": 9}, "end": {"line": 80, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 80, "column": 9}, "end": {"line": 80, "column": null}}]}, "4": {"loc": {"start": {"line": 90, "column": 9}, "end": {"line": 90, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 90, "column": 9}, "end": {"line": 90, "column": null}}]}, "5": {"loc": {"start": {"line": 95, "column": 9}, "end": {"line": 95, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 95, "column": 9}, "end": {"line": 95, "column": null}}]}}, "s": {"0": 190, "1": 2, "2": 2, "3": 2, "4": 190, "5": 190, "6": 190, "7": 190, "8": 190, "9": 2, "10": 2}, "f": {"0": 190}, "b": {"0": [183], "1": [190, 5], "2": [190, 185], "3": [190], "4": [190], "5": [190]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\index.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\index.ts", "statementMap": {"0": {"start": {"line": 1, "column": 20}, "end": {"line": 1, "column": 34}}, "1": {"start": {"line": 7, "column": 20}, "end": {"line": 7, "column": 26}}, "2": {"start": {"line": 7, "column": 38}, "end": {"line": 7, "column": 51}}, "3": {"start": {"line": 7, "column": 51}, "end": {"line": 7, "column": 69}}, "4": {"start": {"line": 7, "column": 26}, "end": {"line": 7, "column": 38}}, "5": {"start": {"line": 4, "column": 20}, "end": {"line": 4, "column": 33}}, "6": {"start": {"line": 1, "column": 34}, "end": {"line": 1, "column": null}}, "7": {"start": {"line": 4, "column": 33}, "end": {"line": 4, "column": null}}, "8": {"start": {"line": 7, "column": 69}, "end": {"line": 7, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 156, "1": 0, "2": 0, "3": 0, "4": 0, "5": 156, "6": 1, "7": 1, "8": 1}, "f": {}, "b": {}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\config\\site.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\config\\site.ts", "statementMap": {"0": {"start": {"line": 176, "column": 13}, "end": {"line": 176, "column": 29}}, "1": {"start": {"line": 18, "column": 13}, "end": {"line": 18, "column": 25}}, "2": {"start": {"line": 191, "column": 13}, "end": {"line": 191, "column": 23}}, "3": {"start": {"line": 120, "column": 13}, "end": {"line": 120, "column": 30}}, "4": {"start": {"line": 67, "column": 13}, "end": {"line": 67, "column": 29}}, "5": {"start": {"line": 40, "column": 13}, "end": {"line": 40, "column": 23}}, "6": {"start": {"line": 138, "column": 13}, "end": {"line": 138, "column": 23}}, "7": {"start": {"line": 95, "column": 13}, "end": {"line": 95, "column": 21}}, "8": {"start": {"line": 9, "column": 13}, "end": {"line": 9, "column": 24}}, "9": {"start": {"line": 31, "column": 13}, "end": {"line": 31, "column": 25}}, "10": {"start": {"line": 9, "column": 27}, "end": {"line": 15, "column": null}}, "11": {"start": {"line": 18, "column": 28}, "end": {"line": 28, "column": null}}, "12": {"start": {"line": 31, "column": 28}, "end": {"line": 37, "column": null}}, "13": {"start": {"line": 40, "column": 26}, "end": {"line": 64, "column": null}}, "14": {"start": {"line": 67, "column": 32}, "end": {"line": 92, "column": null}}, "15": {"start": {"line": 95, "column": 24}, "end": {"line": 117, "column": null}}, "16": {"start": {"line": 120, "column": 33}, "end": {"line": 135, "column": null}}, "17": {"start": {"line": 138, "column": 26}, "end": {"line": 173, "column": null}}, "18": {"start": {"line": 176, "column": 32}, "end": {"line": 188, "column": null}}, "19": {"start": {"line": 191, "column": 26}, "end": {"line": 196, "column": null}}}, "fnMap": {}, "branchMap": {"0": {"loc": {"start": {"line": 13, "column": 7}, "end": {"line": 13, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 13, "column": 7}, "end": {"line": 13, "column": 39}}, {"start": {"line": 13, "column": 43}, "end": {"line": 13, "column": null}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0}, "f": {}, "b": {"0": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\contexts\\ThemeContext.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\contexts\\ThemeContext.tsx", "statementMap": {"0": {"start": {"line": 27, "column": 13}, "end": {"line": 27, "column": 26}}, "1": {"start": {"line": 120, "column": 13}, "end": {"line": 120, "column": 28}}, "2": {"start": {"line": 101, "column": 13}, "end": {"line": 101, "column": 27}}, "3": {"start": {"line": 15, "column": 13}, "end": {"line": 15, "column": 21}}, "4": {"start": {"line": 3, "column": 70}, "end": {"line": 3, "column": null}}, "5": {"start": {"line": 13, "column": 21}, "end": {"line": 13, "column": null}}, "6": {"start": {"line": 15, "column": 24}, "end": {"line": 21, "column": null}}, "7": {"start": {"line": 16, "column": 18}, "end": {"line": 16, "column": null}}, "8": {"start": {"line": 17, "column": 2}, "end": {"line": 19, "column": null}}, "9": {"start": {"line": 18, "column": 4}, "end": {"line": 18, "column": null}}, "10": {"start": {"line": 20, "column": 2}, "end": {"line": 20, "column": null}}, "11": {"start": {"line": 27, "column": 59}, "end": {"line": 98, "column": null}}, "12": {"start": {"line": 28, "column": 33}, "end": {"line": 28, "column": null}}, "13": {"start": {"line": 29, "column": 32}, "end": {"line": 29, "column": null}}, "14": {"start": {"line": 32, "column": 2}, "end": {"line": 39, "column": null}}, "15": {"start": {"line": 33, "column": 23}, "end": {"line": 33, "column": null}}, "16": {"start": {"line": 34, "column": 24}, "end": {"line": 34, "column": null}}, "17": {"start": {"line": 35, "column": 25}, "end": {"line": 35, "column": null}}, "18": {"start": {"line": 37, "column": 4}, "end": {"line": 37, "column": null}}, "19": {"start": {"line": 38, "column": 4}, "end": {"line": 38, "column": null}}, "20": {"start": {"line": 42, "column": 2}, "end": {"line": 63, "column": null}}, "21": {"start": {"line": 43, "column": 4}, "end": {"line": 43, "column": null}}, "22": {"start": {"line": 43, "column": 18}, "end": {"line": 43, "column": null}}, "23": {"start": {"line": 45, "column": 17}, "end": {"line": 45, "column": 41}}, "24": {"start": {"line": 47, "column": 4}, "end": {"line": 51, "column": null}}, "25": {"start": {"line": 48, "column": 6}, "end": {"line": 48, "column": null}}, "26": {"start": {"line": 50, "column": 6}, "end": {"line": 50, "column": null}}, "27": {"start": {"line": 54, "column": 4}, "end": {"line": 54, "column": null}}, "28": {"start": {"line": 57, "column": 4}, "end": {"line": 62, "column": null}}, "29": {"start": {"line": 58, "column": 7}, "end": {"line": 61, "column": null}}, "30": {"start": {"line": 66, "column": 2}, "end": {"line": 81, "column": null}}, "31": {"start": {"line": 67, "column": 4}, "end": {"line": 67, "column": null}}, "32": {"start": {"line": 67, "column": 18}, "end": {"line": 67, "column": null}}, "33": {"start": {"line": 69, "column": 23}, "end": {"line": 69, "column": null}}, "34": {"start": {"line": 71, "column": 25}, "end": {"line": 77, "column": null}}, "35": {"start": {"line": 73, "column": 25}, "end": {"line": 73, "column": null}}, "36": {"start": {"line": 74, "column": 6}, "end": {"line": 76, "column": null}}, "37": {"start": {"line": 75, "column": 8}, "end": {"line": 75, "column": null}}, "38": {"start": {"line": 79, "column": 4}, "end": {"line": 79, "column": null}}, "39": {"start": {"line": 80, "column": 4}, "end": {"line": 80, "column": null}}, "40": {"start": {"line": 80, "column": 17}, "end": {"line": 80, "column": null}}, "41": {"start": {"line": 83, "column": 22}, "end": {"line": 85, "column": null}}, "42": {"start": {"line": 84, "column": 4}, "end": {"line": 84, "column": null}}, "43": {"start": {"line": 84, "column": 31}, "end": {"line": 84, "column": null}}, "44": {"start": {"line": 87, "column": 19}, "end": {"line": 89, "column": null}}, "45": {"start": {"line": 88, "column": 4}, "end": {"line": 88, "column": null}}, "46": {"start": {"line": 101, "column": 30}, "end": {"line": 117, "column": null}}, "47": {"start": {"line": 102, "column": 40}, "end": {"line": 102, "column": null}}, "48": {"start": {"line": 104, "column": 2}, "end": {"line": 114, "column": null}}, "49": {"start": {"line": 105, "column": 23}, "end": {"line": 105, "column": null}}, "50": {"start": {"line": 106, "column": 4}, "end": {"line": 106, "column": null}}, "51": {"start": {"line": 108, "column": 25}, "end": {"line": 110, "column": null}}, "52": {"start": {"line": 109, "column": 6}, "end": {"line": 109, "column": null}}, "53": {"start": {"line": 112, "column": 4}, "end": {"line": 112, "column": null}}, "54": {"start": {"line": 113, "column": 4}, "end": {"line": 113, "column": null}}, "55": {"start": {"line": 113, "column": 17}, "end": {"line": 113, "column": null}}, "56": {"start": {"line": 116, "column": 2}, "end": {"line": 116, "column": null}}, "57": {"start": {"line": 120, "column": 31}, "end": {"line": 122, "column": null}}, "58": {"start": {"line": 121, "column": 2}, "end": {"line": 121, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_8)", "decl": {"start": {"line": 15, "column": 24}, "end": {"line": 15, "column": null}}, "loc": {"start": {"line": 15, "column": 24}, "end": {"line": 21, "column": null}}}, "1": {"name": "(anonymous_9)", "decl": {"start": {"line": 27, "column": 59}, "end": {"line": 27, "column": 60}}, "loc": {"start": {"line": 27, "column": 72}, "end": {"line": 98, "column": null}}}, "2": {"name": "(anonymous_10)", "decl": {"start": {"line": 32, "column": 12}, "end": {"line": 32, "column": null}}, "loc": {"start": {"line": 32, "column": 12}, "end": {"line": 39, "column": 5}}}, "3": {"name": "(anonymous_11)", "decl": {"start": {"line": 42, "column": 12}, "end": {"line": 42, "column": null}}, "loc": {"start": {"line": 42, "column": 12}, "end": {"line": 63, "column": 5}}}, "4": {"name": "(anonymous_12)", "decl": {"start": {"line": 66, "column": 12}, "end": {"line": 66, "column": null}}, "loc": {"start": {"line": 66, "column": 12}, "end": {"line": 81, "column": 5}}}, "5": {"name": "(anonymous_13)", "decl": {"start": {"line": 71, "column": 25}, "end": {"line": 71, "column": 26}}, "loc": {"start": {"line": 71, "column": 26}, "end": {"line": 77, "column": null}}}, "6": {"name": "(anonymous_14)", "decl": {"start": {"line": 80, "column": 11}, "end": {"line": 80, "column": 17}}, "loc": {"start": {"line": 80, "column": 17}, "end": {"line": 80, "column": null}}}, "7": {"name": "(anonymous_15)", "decl": {"start": {"line": 83, "column": 22}, "end": {"line": 83, "column": null}}, "loc": {"start": {"line": 83, "column": 22}, "end": {"line": 85, "column": null}}}, "8": {"name": "(anonymous_16)", "decl": {"start": {"line": 84, "column": 18}, "end": {"line": 84, "column": 31}}, "loc": {"start": {"line": 84, "column": 31}, "end": {"line": 84, "column": null}}}, "9": {"name": "(anonymous_17)", "decl": {"start": {"line": 87, "column": 19}, "end": {"line": 87, "column": 20}}, "loc": {"start": {"line": 87, "column": 20}, "end": {"line": 89, "column": null}}}, "10": {"name": "(anonymous_18)", "decl": {"start": {"line": 101, "column": 30}, "end": {"line": 101, "column": null}}, "loc": {"start": {"line": 101, "column": 30}, "end": {"line": 117, "column": null}}}, "11": {"name": "(anonymous_19)", "decl": {"start": {"line": 104, "column": 12}, "end": {"line": 104, "column": null}}, "loc": {"start": {"line": 104, "column": 12}, "end": {"line": 114, "column": 5}}}, "12": {"name": "(anonymous_20)", "decl": {"start": {"line": 108, "column": 25}, "end": {"line": 108, "column": 26}}, "loc": {"start": {"line": 108, "column": 26}, "end": {"line": 110, "column": null}}}, "13": {"name": "(anonymous_21)", "decl": {"start": {"line": 113, "column": 11}, "end": {"line": 113, "column": 17}}, "loc": {"start": {"line": 113, "column": 17}, "end": {"line": 113, "column": null}}}, "14": {"name": "(anonymous_22)", "decl": {"start": {"line": 120, "column": 31}, "end": {"line": 120, "column": 32}}, "loc": {"start": {"line": 120, "column": 54}, "end": {"line": 122, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 17, "column": 2}, "end": {"line": 19, "column": null}}, "type": "if", "locations": [{"start": {"line": 17, "column": 2}, "end": {"line": 19, "column": null}}]}, "1": {"loc": {"start": {"line": 34, "column": 24}, "end": {"line": 34, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 34, "column": 84}, "end": {"line": 34, "column": 93}}, {"start": {"line": 34, "column": 93}, "end": {"line": 34, "column": null}}]}, "2": {"loc": {"start": {"line": 35, "column": 25}, "end": {"line": 35, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 35, "column": 25}, "end": {"line": 35, "column": 39}}, {"start": {"line": 35, "column": 39}, "end": {"line": 35, "column": null}}]}, "3": {"loc": {"start": {"line": 43, "column": 4}, "end": {"line": 43, "column": null}}, "type": "if", "locations": [{"start": {"line": 43, "column": 4}, "end": {"line": 43, "column": null}}]}, "4": {"loc": {"start": {"line": 47, "column": 4}, "end": {"line": 51, "column": null}}, "type": "if", "locations": [{"start": {"line": 47, "column": 4}, "end": {"line": 51, "column": null}}, {"start": {"line": 49, "column": 11}, "end": {"line": 51, "column": null}}]}, "5": {"loc": {"start": {"line": 57, "column": 4}, "end": {"line": 62, "column": null}}, "type": "if", "locations": [{"start": {"line": 57, "column": 4}, "end": {"line": 62, "column": null}}]}, "6": {"loc": {"start": {"line": 57, "column": 8}, "end": {"line": 57, "column": 61}}, "type": "binary-expr", "locations": [{"start": {"line": 57, "column": 8}, "end": {"line": 57, "column": 41}}, {"start": {"line": 57, "column": 41}, "end": {"line": 57, "column": 61}}]}, "7": {"loc": {"start": {"line": 67, "column": 4}, "end": {"line": 67, "column": null}}, "type": "if", "locations": [{"start": {"line": 67, "column": 4}, "end": {"line": 67, "column": null}}]}, "8": {"loc": {"start": {"line": 74, "column": 6}, "end": {"line": 76, "column": null}}, "type": "if", "locations": [{"start": {"line": 74, "column": 6}, "end": {"line": 76, "column": null}}]}, "9": {"loc": {"start": {"line": 75, "column": 22}, "end": {"line": 75, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 75, "column": 34}, "end": {"line": 75, "column": 43}}, {"start": {"line": 75, "column": 43}, "end": {"line": 75, "column": null}}]}, "10": {"loc": {"start": {"line": 84, "column": 31}, "end": {"line": 84, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 84, "column": 55}, "end": {"line": 84, "column": 64}}, {"start": {"line": 84, "column": 64}, "end": {"line": 84, "column": null}}]}, "11": {"loc": {"start": {"line": 106, "column": 19}, "end": {"line": 106, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 106, "column": 40}, "end": {"line": 106, "column": 49}}, {"start": {"line": 106, "column": 49}, "end": {"line": 106, "column": null}}]}, "12": {"loc": {"start": {"line": 109, "column": 21}, "end": {"line": 109, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 109, "column": 33}, "end": {"line": 109, "column": 42}}, {"start": {"line": 109, "column": 42}, "end": {"line": 109, "column": null}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0}, "b": {"0": [0], "1": [0, 0], "2": [0, 0], "3": [0], "4": [0, 0], "5": [0], "6": [0, 0], "7": [0], "8": [0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\index.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\index.ts", "statementMap": {"0": {"start": {"line": 8, "column": 23}, "end": {"line": 8, "column": 47}}, "1": {"start": {"line": 16, "column": 46}, "end": {"line": 16, "column": 75}}, "2": {"start": {"line": 8, "column": 9}, "end": {"line": 8, "column": 23}}, "3": {"start": {"line": 12, "column": 9}, "end": {"line": 12, "column": 31}}, "4": {"start": {"line": 16, "column": 26}, "end": {"line": 16, "column": 46}}, "5": {"start": {"line": 16, "column": 9}, "end": {"line": 16, "column": 26}}, "6": {"start": {"line": 8, "column": 47}, "end": {"line": 8, "column": null}}, "7": {"start": {"line": 12, "column": 31}, "end": {"line": 12, "column": null}}, "8": {"start": {"line": 16, "column": 75}, "end": {"line": 16, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0}, "f": {}, "b": {}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\useAnalytics.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\useAnalytics.ts", "statementMap": {"0": {"start": {"line": 21, "column": 13}, "end": {"line": 21, "column": 29}}, "1": {"start": {"line": 56, "column": 16}, "end": {"line": 56, "column": 28}}, "2": {"start": {"line": 8, "column": 28}, "end": {"line": 8, "column": null}}, "3": {"start": {"line": 9, "column": 27}, "end": {"line": 9, "column": null}}, "4": {"start": {"line": 21, "column": 32}, "end": {"line": 53, "column": null}}, "5": {"start": {"line": 57, "column": 21}, "end": {"line": 87, "column": null}}, "6": {"start": {"line": 62, "column": 4}, "end": {"line": 64, "column": null}}, "7": {"start": {"line": 63, "column": 6}, "end": {"line": 63, "column": null}}, "8": {"start": {"line": 67, "column": 4}, "end": {"line": 72, "column": null}}, "9": {"start": {"line": 68, "column": 6}, "end": {"line": 71, "column": null}}, "10": {"start": {"line": 75, "column": 4}, "end": {"line": 86, "column": null}}, "11": {"start": {"line": 76, "column": 6}, "end": {"line": 85, "column": null}}, "12": {"start": {"line": 77, "column": 9}, "end": {"line": 82, "column": null}}, "13": {"start": {"line": 84, "column": 8}, "end": {"line": 84, "column": null}}, "14": {"start": {"line": 90, "column": 26}, "end": {"line": 96, "column": null}}, "15": {"start": {"line": 91, "column": 4}, "end": {"line": 95, "column": null}}, "16": {"start": {"line": 98, "column": 31}, "end": {"line": 115, "column": null}}, "17": {"start": {"line": 103, "column": 21}, "end": {"line": 108, "column": null}}, "18": {"start": {"line": 110, "column": 4}, "end": {"line": 114, "column": null}}, "19": {"start": {"line": 117, "column": 31}, "end": {"line": 131, "column": null}}, "20": {"start": {"line": 121, "column": 21}, "end": {"line": 125, "column": null}}, "21": {"start": {"line": 127, "column": 4}, "end": {"line": 130, "column": null}}, "22": {"start": {"line": 133, "column": 34}, "end": {"line": 149, "column": null}}, "23": {"start": {"line": 138, "column": 21}, "end": {"line": 142, "column": null}}, "24": {"start": {"line": 144, "column": 4}, "end": {"line": 148, "column": null}}, "25": {"start": {"line": 151, "column": 34}, "end": {"line": 166, "column": null}}, "26": {"start": {"line": 155, "column": 21}, "end": {"line": 159, "column": null}}, "27": {"start": {"line": 161, "column": 4}, "end": {"line": 165, "column": null}}, "28": {"start": {"line": 168, "column": 24}, "end": {"line": 177, "column": null}}, "29": {"start": {"line": 169, "column": 4}, "end": {"line": 176, "column": null}}, "30": {"start": {"line": 179, "column": 30}, "end": {"line": 188, "column": null}}, "31": {"start": {"line": 180, "column": 4}, "end": {"line": 187, "column": null}}, "32": {"start": {"line": 190, "column": 2}, "end": {"line": 205, "column": null}}}, "fnMap": {"0": {"name": "useAnalytics", "decl": {"start": {"line": 56, "column": 16}, "end": {"line": 56, "column": 28}}, "loc": {"start": {"line": 56, "column": 16}, "end": {"line": 206, "column": null}}}, "1": {"name": "(anonymous_4)", "decl": {"start": {"line": 57, "column": 33}, "end": {"line": 57, "column": null}}, "loc": {"start": {"line": 59, "column": 4}, "end": {"line": 87, "column": 5}}}, "2": {"name": "(anonymous_5)", "decl": {"start": {"line": 90, "column": 38}, "end": {"line": 90, "column": 39}}, "loc": {"start": {"line": 90, "column": 60}, "end": {"line": 96, "column": 5}}}, "3": {"name": "(anonymous_6)", "decl": {"start": {"line": 98, "column": 43}, "end": {"line": 98, "column": null}}, "loc": {"start": {"line": 101, "column": 4}, "end": {"line": 115, "column": 5}}}, "4": {"name": "(anonymous_7)", "decl": {"start": {"line": 117, "column": 43}, "end": {"line": 117, "column": null}}, "loc": {"start": {"line": 119, "column": 4}, "end": {"line": 131, "column": 5}}}, "5": {"name": "(anonymous_8)", "decl": {"start": {"line": 133, "column": 46}, "end": {"line": 133, "column": null}}, "loc": {"start": {"line": 136, "column": 4}, "end": {"line": 149, "column": 5}}}, "6": {"name": "(anonymous_9)", "decl": {"start": {"line": 151, "column": 46}, "end": {"line": 151, "column": null}}, "loc": {"start": {"line": 153, "column": 4}, "end": {"line": 166, "column": 5}}}, "7": {"name": "(anonymous_10)", "decl": {"start": {"line": 168, "column": 36}, "end": {"line": 168, "column": 37}}, "loc": {"start": {"line": 168, "column": 55}, "end": {"line": 177, "column": 5}}}, "8": {"name": "(anonymous_11)", "decl": {"start": {"line": 179, "column": 42}, "end": {"line": 179, "column": 43}}, "loc": {"start": {"line": 179, "column": 79}, "end": {"line": 188, "column": 5}}}}, "branchMap": {"0": {"loc": {"start": {"line": 62, "column": 4}, "end": {"line": 64, "column": null}}, "type": "if", "locations": [{"start": {"line": 62, "column": 4}, "end": {"line": 64, "column": null}}]}, "1": {"loc": {"start": {"line": 62, "column": 8}, "end": {"line": 62, "column": 64}}, "type": "binary-expr", "locations": [{"start": {"line": 62, "column": 8}, "end": {"line": 62, "column": 35}}, {"start": {"line": 62, "column": 39}, "end": {"line": 62, "column": 64}}]}, "2": {"loc": {"start": {"line": 67, "column": 4}, "end": {"line": 72, "column": null}}, "type": "if", "locations": [{"start": {"line": 67, "column": 4}, "end": {"line": 72, "column": null}}]}, "3": {"loc": {"start": {"line": 67, "column": 8}, "end": {"line": 67, "column": 62}}, "type": "binary-expr", "locations": [{"start": {"line": 67, "column": 8}, "end": {"line": 67, "column": 32}}, {"start": {"line": 67, "column": 36}, "end": {"line": 67, "column": 62}}]}, "4": {"loc": {"start": {"line": 75, "column": 4}, "end": {"line": 86, "column": null}}, "type": "if", "locations": [{"start": {"line": 75, "column": 4}, "end": {"line": 86, "column": null}}]}, "5": {"loc": {"start": {"line": 75, "column": 8}, "end": {"line": 75, "column": 61}}, "type": "binary-expr", "locations": [{"start": {"line": 75, "column": 8}, "end": {"line": 75, "column": 41}}, {"start": {"line": 75, "column": 41}, "end": {"line": 75, "column": 61}}]}, "6": {"loc": {"start": {"line": 78, "column": 26}, "end": {"line": 78, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 78, "column": 26}, "end": {"line": 78, "column": 49}}, {"start": {"line": 78, "column": 49}, "end": {"line": 78, "column": null}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0}, "b": {"0": [0], "1": [0, 0], "2": [0], "3": [0, 0], "4": [0], "5": [0, 0], "6": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\useContactForm.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\useContactForm.ts", "statementMap": {"0": {"start": {"line": 107, "column": 16}, "end": {"line": 107, "column": null}}, "1": {"start": {"line": 8, "column": 38}, "end": {"line": 8, "column": null}}, "2": {"start": {"line": 9, "column": 34}, "end": {"line": 9, "column": null}}, "3": {"start": {"line": 10, "column": 29}, "end": {"line": 10, "column": null}}, "4": {"start": {"line": 41, "column": 41}, "end": {"line": 47, "column": null}}, "5": {"start": {"line": 50, "column": 36}, "end": {"line": 56, "column": null}}, "6": {"start": {"line": 59, "column": 22}, "end": {"line": 90, "column": null}}, "7": {"start": {"line": 60, "column": 2}, "end": {"line": 89, "column": null}}, "8": {"start": {"line": 62, "column": 6}, "end": {"line": 62, "column": null}}, "9": {"start": {"line": 62, "column": 25}, "end": {"line": 62, "column": null}}, "10": {"start": {"line": 63, "column": 6}, "end": {"line": 63, "column": null}}, "11": {"start": {"line": 63, "column": 35}, "end": {"line": 63, "column": null}}, "12": {"start": {"line": 64, "column": 6}, "end": {"line": 64, "column": null}}, "13": {"start": {"line": 67, "column": 6}, "end": {"line": 67, "column": null}}, "14": {"start": {"line": 67, "column": 25}, "end": {"line": 67, "column": null}}, "15": {"start": {"line": 68, "column": 25}, "end": {"line": 68, "column": null}}, "16": {"start": {"line": 69, "column": 6}, "end": {"line": 69, "column": null}}, "17": {"start": {"line": 69, "column": 35}, "end": {"line": 69, "column": null}}, "18": {"start": {"line": 70, "column": 6}, "end": {"line": 70, "column": null}}, "19": {"start": {"line": 74, "column": 6}, "end": {"line": 74, "column": null}}, "20": {"start": {"line": 74, "column": 51}, "end": {"line": 74, "column": null}}, "21": {"start": {"line": 75, "column": 6}, "end": {"line": 75, "column": null}}, "22": {"start": {"line": 78, "column": 6}, "end": {"line": 78, "column": null}}, "23": {"start": {"line": 78, "column": 25}, "end": {"line": 78, "column": null}}, "24": {"start": {"line": 79, "column": 6}, "end": {"line": 79, "column": null}}, "25": {"start": {"line": 82, "column": 6}, "end": {"line": 82, "column": null}}, "26": {"start": {"line": 82, "column": 25}, "end": {"line": 82, "column": null}}, "27": {"start": {"line": 83, "column": 6}, "end": {"line": 83, "column": null}}, "28": {"start": {"line": 83, "column": 36}, "end": {"line": 83, "column": null}}, "29": {"start": {"line": 84, "column": 6}, "end": {"line": 84, "column": null}}, "30": {"start": {"line": 84, "column": 38}, "end": {"line": 84, "column": null}}, "31": {"start": {"line": 85, "column": 6}, "end": {"line": 85, "column": null}}, "32": {"start": {"line": 88, "column": 6}, "end": {"line": 88, "column": null}}, "33": {"start": {"line": 93, "column": 21}, "end": {"line": 104, "column": null}}, "34": {"start": {"line": 94, "column": 29}, "end": {"line": 94, "column": null}}, "35": {"start": {"line": 96, "column": 3}, "end": {"line": 101, "column": null}}, "36": {"start": {"line": 97, "column": 18}, "end": {"line": 97, "column": null}}, "37": {"start": {"line": 98, "column": 4}, "end": {"line": 100, "column": null}}, "38": {"start": {"line": 99, "column": 6}, "end": {"line": 99, "column": null}}, "39": {"start": {"line": 103, "column": 2}, "end": {"line": 103, "column": null}}, "40": {"start": {"line": 108, "column": 36}, "end": {"line": 108, "column": null}}, "41": {"start": {"line": 109, "column": 35}, "end": {"line": 109, "column": null}}, "42": {"start": {"line": 112, "column": 22}, "end": {"line": 126, "column": null}}, "43": {"start": {"line": 113, "column": 4}, "end": {"line": 125, "column": null}}, "44": {"start": {"line": 113, "column": 26}, "end": {"line": 125, "column": null}}, "45": {"start": {"line": 129, "column": 27}, "end": {"line": 131, "column": null}}, "46": {"start": {"line": 130, "column": 4}, "end": {"line": 130, "column": null}}, "47": {"start": {"line": 134, "column": 30}, "end": {"line": 144, "column": null}}, "48": {"start": {"line": 135, "column": 18}, "end": {"line": 135, "column": null}}, "49": {"start": {"line": 136, "column": 4}, "end": {"line": 142, "column": null}}, "50": {"start": {"line": 136, "column": 26}, "end": {"line": 142, "column": null}}, "51": {"start": {"line": 143, "column": 4}, "end": {"line": 143, "column": null}}, "52": {"start": {"line": 147, "column": 20}, "end": {"line": 149, "column": null}}, "53": {"start": {"line": 148, "column": 4}, "end": {"line": 148, "column": null}}, "54": {"start": {"line": 152, "column": 21}, "end": {"line": 228, "column": null}}, "55": {"start": {"line": 154, "column": 19}, "end": {"line": 154, "column": null}}, "56": {"start": {"line": 156, "column": 4}, "end": {"line": 162, "column": null}}, "57": {"start": {"line": 157, "column": 6}, "end": {"line": 160, "column": null}}, "58": {"start": {"line": 157, "column": 28}, "end": {"line": 160, "column": null}}, "59": {"start": {"line": 161, "column": 6}, "end": {"line": 161, "column": null}}, "60": {"start": {"line": 165, "column": 4}, "end": {"line": 174, "column": null}}, "61": {"start": {"line": 166, "column": 6}, "end": {"line": 171, "column": null}}, "62": {"start": {"line": 166, "column": 28}, "end": {"line": 171, "column": null}}, "63": {"start": {"line": 172, "column": 6}, "end": {"line": 172, "column": null}}, "64": {"start": {"line": 173, "column": 6}, "end": {"line": 173, "column": null}}, "65": {"start": {"line": 176, "column": 4}, "end": {"line": 180, "column": null}}, "66": {"start": {"line": 176, "column": 26}, "end": {"line": 180, "column": null}}, "67": {"start": {"line": 182, "column": 4}, "end": {"line": 182, "column": null}}, "68": {"start": {"line": 184, "column": 4}, "end": {"line": 227, "column": null}}, "69": {"start": {"line": 185, "column": 23}, "end": {"line": 185, "column": null}}, "70": {"start": {"line": 186, "column": 6}, "end": {"line": 186, "column": null}}, "71": {"start": {"line": 187, "column": 6}, "end": {"line": 187, "column": null}}, "72": {"start": {"line": 188, "column": 6}, "end": {"line": 188, "column": null}}, "73": {"start": {"line": 189, "column": 6}, "end": {"line": 189, "column": null}}, "74": {"start": {"line": 190, "column": 6}, "end": {"line": 190, "column": null}}, "75": {"start": {"line": 191, "column": 6}, "end": {"line": 191, "column": null}}, "76": {"start": {"line": 192, "column": 6}, "end": {"line": 192, "column": null}}, "77": {"start": {"line": 193, "column": 6}, "end": {"line": 193, "column": null}}, "78": {"start": {"line": 195, "column": 23}, "end": {"line": 198, "column": null}}, "79": {"start": {"line": 200, "column": 21}, "end": {"line": 200, "column": null}}, "80": {"start": {"line": 202, "column": 6}, "end": {"line": 213, "column": null}}, "81": {"start": {"line": 203, "column": 8}, "end": {"line": 208, "column": null}}, "82": {"start": {"line": 203, "column": 30}, "end": {"line": 208, "column": null}}, "83": {"start": {"line": 209, "column": 8}, "end": {"line": 209, "column": null}}, "84": {"start": {"line": 210, "column": 8}, "end": {"line": 210, "column": null}}, "85": {"start": {"line": 212, "column": 8}, "end": {"line": 212, "column": null}}, "86": {"start": {"line": 215, "column": 6}, "end": {"line": 215, "column": null}}, "87": {"start": {"line": 216, "column": 6}, "end": {"line": 224, "column": null}}, "88": {"start": {"line": 216, "column": 28}, "end": {"line": 224, "column": null}}, "89": {"start": {"line": 225, "column": 6}, "end": {"line": 225, "column": null}}, "90": {"start": {"line": 226, "column": 6}, "end": {"line": 226, "column": null}}, "91": {"start": {"line": 230, "column": 2}, "end": {"line": 248, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 59, "column": 22}, "end": {"line": 59, "column": 23}}, "loc": {"start": {"line": 59, "column": 52}, "end": {"line": 90, "column": null}}}, "1": {"name": "(anonymous_2)", "decl": {"start": {"line": 93, "column": 21}, "end": {"line": 93, "column": 22}}, "loc": {"start": {"line": 93, "column": 22}, "end": {"line": 104, "column": null}}}, "2": {"name": "(anonymous_3)", "decl": {"start": {"line": 96, "column": 62}, "end": {"line": 96, "column": null}}, "loc": {"start": {"line": 96, "column": 62}, "end": {"line": 101, "column": null}}}, "3": {"name": "useContactForm", "decl": {"start": {"line": 107, "column": 16}, "end": {"line": 107, "column": null}}, "loc": {"start": {"line": 107, "column": 16}, "end": {"line": 249, "column": null}}}, "4": {"name": "(anonymous_5)", "decl": {"start": {"line": 112, "column": 34}, "end": {"line": 112, "column": 35}}, "loc": {"start": {"line": 112, "column": 65}, "end": {"line": 126, "column": 5}}}, "5": {"name": "(anonymous_6)", "decl": {"start": {"line": 113, "column": 17}, "end": {"line": 113, "column": 26}}, "loc": {"start": {"line": 113, "column": 26}, "end": {"line": 125, "column": null}}}, "6": {"name": "(anonymous_7)", "decl": {"start": {"line": 129, "column": 39}, "end": {"line": 129, "column": 40}}, "loc": {"start": {"line": 129, "column": 40}, "end": {"line": 131, "column": 5}}}, "7": {"name": "(anonymous_8)", "decl": {"start": {"line": 134, "column": 42}, "end": {"line": 134, "column": 43}}, "loc": {"start": {"line": 134, "column": 43}, "end": {"line": 144, "column": 5}}}, "8": {"name": "(anonymous_9)", "decl": {"start": {"line": 136, "column": 17}, "end": {"line": 136, "column": 26}}, "loc": {"start": {"line": 136, "column": 26}, "end": {"line": 142, "column": null}}}, "9": {"name": "(anonymous_10)", "decl": {"start": {"line": 147, "column": 32}, "end": {"line": 147, "column": null}}, "loc": {"start": {"line": 147, "column": 32}, "end": {"line": 149, "column": 5}}}, "10": {"name": "(anonymous_11)", "decl": {"start": {"line": 152, "column": 33}, "end": {"line": 152, "column": null}}, "loc": {"start": {"line": 152, "column": 33}, "end": {"line": 228, "column": 5}}}, "11": {"name": "(anonymous_12)", "decl": {"start": {"line": 157, "column": 19}, "end": {"line": 157, "column": 28}}, "loc": {"start": {"line": 157, "column": 28}, "end": {"line": 160, "column": null}}}, "12": {"name": "(anonymous_13)", "decl": {"start": {"line": 166, "column": 19}, "end": {"line": 166, "column": 28}}, "loc": {"start": {"line": 166, "column": 28}, "end": {"line": 171, "column": null}}}, "13": {"name": "(anonymous_14)", "decl": {"start": {"line": 176, "column": 17}, "end": {"line": 176, "column": 26}}, "loc": {"start": {"line": 176, "column": 26}, "end": {"line": 180, "column": null}}}, "14": {"name": "(anonymous_15)", "decl": {"start": {"line": 203, "column": 21}, "end": {"line": 203, "column": 30}}, "loc": {"start": {"line": 203, "column": 30}, "end": {"line": 208, "column": null}}}, "15": {"name": "(anonymous_16)", "decl": {"start": {"line": 216, "column": 19}, "end": {"line": 216, "column": 28}}, "loc": {"start": {"line": 216, "column": 28}, "end": {"line": 224, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 60, "column": 2}, "end": {"line": 89, "column": null}}, "type": "switch", "locations": [{"start": {"line": 61, "column": 4}, "end": {"line": 64, "column": null}}, {"start": {"line": 66, "column": 4}, "end": {"line": 70, "column": null}}, {"start": {"line": 72, "column": 4}, "end": {"line": 75, "column": null}}, {"start": {"line": 77, "column": 4}, "end": {"line": 79, "column": null}}, {"start": {"line": 81, "column": 4}, "end": {"line": 85, "column": null}}, {"start": {"line": 87, "column": 4}, "end": {"line": 88, "column": null}}]}, "1": {"loc": {"start": {"line": 62, "column": 6}, "end": {"line": 62, "column": null}}, "type": "if", "locations": [{"start": {"line": 62, "column": 6}, "end": {"line": 62, "column": null}}]}, "2": {"loc": {"start": {"line": 63, "column": 6}, "end": {"line": 63, "column": null}}, "type": "if", "locations": [{"start": {"line": 63, "column": 6}, "end": {"line": 63, "column": null}}]}, "3": {"loc": {"start": {"line": 67, "column": 6}, "end": {"line": 67, "column": null}}, "type": "if", "locations": [{"start": {"line": 67, "column": 6}, "end": {"line": 67, "column": null}}]}, "4": {"loc": {"start": {"line": 69, "column": 6}, "end": {"line": 69, "column": null}}, "type": "if", "locations": [{"start": {"line": 69, "column": 6}, "end": {"line": 69, "column": null}}]}, "5": {"loc": {"start": {"line": 74, "column": 6}, "end": {"line": 74, "column": null}}, "type": "if", "locations": [{"start": {"line": 74, "column": 6}, "end": {"line": 74, "column": null}}]}, "6": {"loc": {"start": {"line": 74, "column": 10}, "end": {"line": 74, "column": 51}}, "type": "binary-expr", "locations": [{"start": {"line": 74, "column": 10}, "end": {"line": 74, "column": 26}}, {"start": {"line": 74, "column": 26}, "end": {"line": 74, "column": 51}}]}, "7": {"loc": {"start": {"line": 78, "column": 6}, "end": {"line": 78, "column": null}}, "type": "if", "locations": [{"start": {"line": 78, "column": 6}, "end": {"line": 78, "column": null}}]}, "8": {"loc": {"start": {"line": 82, "column": 6}, "end": {"line": 82, "column": null}}, "type": "if", "locations": [{"start": {"line": 82, "column": 6}, "end": {"line": 82, "column": null}}]}, "9": {"loc": {"start": {"line": 83, "column": 6}, "end": {"line": 83, "column": null}}, "type": "if", "locations": [{"start": {"line": 83, "column": 6}, "end": {"line": 83, "column": null}}]}, "10": {"loc": {"start": {"line": 84, "column": 6}, "end": {"line": 84, "column": null}}, "type": "if", "locations": [{"start": {"line": 84, "column": 6}, "end": {"line": 84, "column": null}}]}, "11": {"loc": {"start": {"line": 98, "column": 4}, "end": {"line": 100, "column": null}}, "type": "if", "locations": [{"start": {"line": 98, "column": 4}, "end": {"line": 100, "column": null}}]}, "12": {"loc": {"start": {"line": 156, "column": 4}, "end": {"line": 162, "column": null}}, "type": "if", "locations": [{"start": {"line": 156, "column": 4}, "end": {"line": 162, "column": null}}]}, "13": {"loc": {"start": {"line": 165, "column": 4}, "end": {"line": 174, "column": null}}, "type": "if", "locations": [{"start": {"line": 165, "column": 4}, "end": {"line": 174, "column": null}}]}, "14": {"loc": {"start": {"line": 202, "column": 6}, "end": {"line": 213, "column": null}}, "type": "if", "locations": [{"start": {"line": 202, "column": 6}, "end": {"line": 213, "column": null}}, {"start": {"line": 211, "column": 13}, "end": {"line": 213, "column": null}}]}, "15": {"loc": {"start": {"line": 212, "column": 24}, "end": {"line": 212, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 212, "column": 24}, "end": {"line": 212, "column": 38}}, {"start": {"line": 212, "column": 42}, "end": {"line": 212, "column": null}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0}, "b": {"0": [0, 0, 0, 0, 0, 0], "1": [0], "2": [0], "3": [0], "4": [0], "5": [0], "6": [0, 0], "7": [0], "8": [0], "9": [0], "10": [0], "11": [0], "12": [0], "13": [0], "14": [0, 0], "15": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\useSiteSettings.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\useSiteSettings.ts", "statementMap": {"0": {"start": {"line": 137, "column": 16}, "end": {"line": 137, "column": 37}}, "1": {"start": {"line": 142, "column": 16}, "end": {"line": 142, "column": 34}}, "2": {"start": {"line": 97, "column": 16}, "end": {"line": 97, "column": 31}}, "3": {"start": {"line": 8, "column": 36}, "end": {"line": 8, "column": null}}, "4": {"start": {"line": 9, "column": 51}, "end": {"line": 9, "column": null}}, "5": {"start": {"line": 10, "column": 33}, "end": {"line": 10, "column": null}}, "6": {"start": {"line": 51, "column": 36}, "end": {"line": 94, "column": null}}, "7": {"start": {"line": 56, "column": 22}, "end": {"line": 56, "column": null}}, "8": {"start": {"line": 58, "column": 2}, "end": {"line": 93, "column": null}}, "9": {"start": {"line": 98, "column": 40}, "end": {"line": 98, "column": null}}, "10": {"start": {"line": 99, "column": 36}, "end": {"line": 99, "column": null}}, "11": {"start": {"line": 100, "column": 28}, "end": {"line": 100, "column": null}}, "12": {"start": {"line": 102, "column": 2}, "end": {"line": 131, "column": null}}, "13": {"start": {"line": 103, "column": 20}, "end": {"line": 103, "column": null}}, "14": {"start": {"line": 105, "column": 26}, "end": {"line": 124, "column": null}}, "15": {"start": {"line": 106, "column": 6}, "end": {"line": 123, "column": null}}, "16": {"start": {"line": 107, "column": 8}, "end": {"line": 107, "column": null}}, "17": {"start": {"line": 108, "column": 8}, "end": {"line": 108, "column": null}}, "18": {"start": {"line": 110, "column": 25}, "end": {"line": 110, "column": null}}, "19": {"start": {"line": 112, "column": 8}, "end": {"line": 115, "column": null}}, "20": {"start": {"line": 113, "column": 10}, "end": {"line": 113, "column": null}}, "21": {"start": {"line": 114, "column": 10}, "end": {"line": 114, "column": null}}, "22": {"start": {"line": 117, "column": 8}, "end": {"line": 122, "column": null}}, "23": {"start": {"line": 118, "column": 31}, "end": {"line": 118, "column": null}}, "24": {"start": {"line": 119, "column": 10}, "end": {"line": 119, "column": null}}, "25": {"start": {"line": 120, "column": 10}, "end": {"line": 120, "column": null}}, "26": {"start": {"line": 121, "column": 10}, "end": {"line": 121, "column": null}}, "27": {"start": {"line": 126, "column": 4}, "end": {"line": 126, "column": null}}, "28": {"start": {"line": 128, "column": 4}, "end": {"line": 130, "column": null}}, "29": {"start": {"line": 129, "column": 6}, "end": {"line": 129, "column": null}}, "30": {"start": {"line": 133, "column": 2}, "end": {"line": 133, "column": null}}, "31": {"start": {"line": 138, "column": 2}, "end": {"line": 138, "column": null}}, "32": {"start": {"line": 143, "column": 19}, "end": {"line": 143, "column": null}}, "33": {"start": {"line": 145, "column": 2}, "end": {"line": 195, "column": null}}, "34": {"start": {"line": 147, "column": 6}, "end": {"line": 153, "column": null}}, "35": {"start": {"line": 156, "column": 6}, "end": {"line": 164, "column": null}}, "36": {"start": {"line": 167, "column": 6}, "end": {"line": 172, "column": null}}, "37": {"start": {"line": 175, "column": 6}, "end": {"line": 180, "column": null}}, "38": {"start": {"line": 183, "column": 6}, "end": {"line": 188, "column": null}}, "39": {"start": {"line": 191, "column": 6}, "end": {"line": 194, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 51, "column": 36}, "end": {"line": 51, "column": null}}, "loc": {"start": {"line": 54, "column": 29}, "end": {"line": 94, "column": null}}}, "1": {"name": "useSiteSettings", "decl": {"start": {"line": 97, "column": 16}, "end": {"line": 97, "column": 31}}, "loc": {"start": {"line": 97, "column": 16}, "end": {"line": 134, "column": null}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 102, "column": 12}, "end": {"line": 102, "column": null}}, "loc": {"start": {"line": 102, "column": 12}, "end": {"line": 131, "column": 5}}}, "3": {"name": "(anonymous_7)", "decl": {"start": {"line": 105, "column": 26}, "end": {"line": 105, "column": null}}, "loc": {"start": {"line": 105, "column": 26}, "end": {"line": 124, "column": null}}}, "4": {"name": "(anonymous_8)", "decl": {"start": {"line": 128, "column": 11}, "end": {"line": 128, "column": null}}, "loc": {"start": {"line": 128, "column": 11}, "end": {"line": 130, "column": null}}}, "5": {"name": "getStaticSiteSettings", "decl": {"start": {"line": 137, "column": 16}, "end": {"line": 137, "column": 37}}, "loc": {"start": {"line": 137, "column": 16}, "end": {"line": 139, "column": null}}}, "6": {"name": "useSectionSettings", "decl": {"start": {"line": 142, "column": 16}, "end": {"line": 142, "column": 34}}, "loc": {"start": {"line": 142, "column": 97}, "end": {"line": 196, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 53, "column": 2}, "end": {"line": 53, "column": 28}}, "type": "default-arg", "locations": [{"start": {"line": 53, "column": 23}, "end": {"line": 53, "column": 28}}]}, "1": {"loc": {"start": {"line": 54, "column": 2}, "end": {"line": 54, "column": 29}}, "type": "default-arg", "locations": [{"start": {"line": 54, "column": 25}, "end": {"line": 54, "column": 29}}]}, "2": {"loc": {"start": {"line": 60, "column": 18}, "end": {"line": 60, "column": 77}}, "type": "binary-expr", "locations": [{"start": {"line": 60, "column": 18}, "end": {"line": 60, "column": 47}}, {"start": {"line": 60, "column": 47}, "end": {"line": 60, "column": 77}}]}, "3": {"loc": {"start": {"line": 61, "column": 17}, "end": {"line": 61, "column": 74}}, "type": "binary-expr", "locations": [{"start": {"line": 61, "column": 17}, "end": {"line": 61, "column": 45}}, {"start": {"line": 61, "column": 45}, "end": {"line": 61, "column": 74}}]}, "4": {"loc": {"start": {"line": 62, "column": 20}, "end": {"line": 62, "column": 83}}, "type": "binary-expr", "locations": [{"start": {"line": 62, "column": 20}, "end": {"line": 62, "column": 51}}, {"start": {"line": 62, "column": 51}, "end": {"line": 62, "column": 83}}]}, "5": {"loc": {"start": {"line": 65, "column": 21}, "end": {"line": 65, "column": 86}}, "type": "binary-expr", "locations": [{"start": {"line": 65, "column": 21}, "end": {"line": 65, "column": 53}}, {"start": {"line": 65, "column": 53}, "end": {"line": 65, "column": 86}}]}, "6": {"loc": {"start": {"line": 66, "column": 20}, "end": {"line": 66, "column": 83}}, "type": "binary-expr", "locations": [{"start": {"line": 66, "column": 20}, "end": {"line": 66, "column": 51}}, {"start": {"line": 66, "column": 51}, "end": {"line": 66, "column": 83}}]}, "7": {"loc": {"start": {"line": 67, "column": 23}, "end": {"line": 67, "column": 92}}, "type": "binary-expr", "locations": [{"start": {"line": 67, "column": 23}, "end": {"line": 67, "column": 57}}, {"start": {"line": 67, "column": 57}, "end": {"line": 67, "column": 92}}]}, "8": {"loc": {"start": {"line": 70, "column": 24}, "end": {"line": 70, "column": 98}}, "type": "binary-expr", "locations": [{"start": {"line": 70, "column": 24}, "end": {"line": 70, "column": 59}}, {"start": {"line": 70, "column": 59}, "end": {"line": 70, "column": 98}}]}, "9": {"loc": {"start": {"line": 71, "column": 22}, "end": {"line": 71, "column": 92}}, "type": "binary-expr", "locations": [{"start": {"line": 71, "column": 22}, "end": {"line": 71, "column": 55}}, {"start": {"line": 71, "column": 55}, "end": {"line": 71, "column": 92}}]}, "10": {"loc": {"start": {"line": 74, "column": 22}, "end": {"line": 74, "column": 89}}, "type": "binary-expr", "locations": [{"start": {"line": 74, "column": 22}, "end": {"line": 74, "column": 55}}, {"start": {"line": 74, "column": 55}, "end": {"line": 74, "column": 89}}]}, "11": {"loc": {"start": {"line": 75, "column": 21}, "end": {"line": 75, "column": 86}}, "type": "binary-expr", "locations": [{"start": {"line": 75, "column": 21}, "end": {"line": 75, "column": 53}}, {"start": {"line": 75, "column": 53}, "end": {"line": 75, "column": 86}}]}, "12": {"loc": {"start": {"line": 78, "column": 21}, "end": {"line": 78, "column": 86}}, "type": "binary-expr", "locations": [{"start": {"line": 78, "column": 21}, "end": {"line": 78, "column": 53}}, {"start": {"line": 78, "column": 53}, "end": {"line": 78, "column": 86}}]}, "13": {"loc": {"start": {"line": 79, "column": 20}, "end": {"line": 79, "column": 83}}, "type": "binary-expr", "locations": [{"start": {"line": 79, "column": 20}, "end": {"line": 79, "column": 51}}, {"start": {"line": 79, "column": 51}, "end": {"line": 79, "column": 83}}]}, "14": {"loc": {"start": {"line": 82, "column": 19}, "end": {"line": 82, "column": 80}}, "type": "binary-expr", "locations": [{"start": {"line": 82, "column": 19}, "end": {"line": 82, "column": 49}}, {"start": {"line": 82, "column": 49}, "end": {"line": 82, "column": 80}}]}, "15": {"loc": {"start": {"line": 83, "column": 18}, "end": {"line": 83, "column": 77}}, "type": "binary-expr", "locations": [{"start": {"line": 83, "column": 18}, "end": {"line": 83, "column": 47}}, {"start": {"line": 83, "column": 47}, "end": {"line": 83, "column": 77}}]}, "16": {"loc": {"start": {"line": 86, "column": 19}, "end": {"line": 86, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 86, "column": 19}, "end": {"line": 86, "column": 49}}, {"start": {"line": 86, "column": 49}, "end": {"line": 86, "column": null}}]}, "17": {"loc": {"start": {"line": 87, "column": 21}, "end": {"line": 87, "column": 102}}, "type": "binary-expr", "locations": [{"start": {"line": 87, "column": 21}, "end": {"line": 87, "column": 53}}, {"start": {"line": 87, "column": 53}, "end": {"line": 87, "column": 102}}]}, "18": {"loc": {"start": {"line": 112, "column": 8}, "end": {"line": 115, "column": null}}, "type": "if", "locations": [{"start": {"line": 112, "column": 8}, "end": {"line": 115, "column": null}}]}, "19": {"loc": {"start": {"line": 117, "column": 8}, "end": {"line": 122, "column": null}}, "type": "if", "locations": [{"start": {"line": 117, "column": 8}, "end": {"line": 122, "column": null}}]}, "20": {"loc": {"start": {"line": 118, "column": 31}, "end": {"line": 118, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 118, "column": 54}, "end": {"line": 118, "column": 65}}, {"start": {"line": 118, "column": 68}, "end": {"line": 118, "column": null}}]}, "21": {"loc": {"start": {"line": 145, "column": 2}, "end": {"line": 195, "column": null}}, "type": "switch", "locations": [{"start": {"line": 146, "column": 4}, "end": {"line": 153, "column": null}}, {"start": {"line": 155, "column": 4}, "end": {"line": 164, "column": null}}, {"start": {"line": 166, "column": 4}, "end": {"line": 172, "column": null}}, {"start": {"line": 174, "column": 4}, "end": {"line": 180, "column": null}}, {"start": {"line": 182, "column": 4}, "end": {"line": 188, "column": null}}, {"start": {"line": 190, "column": 4}, "end": {"line": 194, "column": null}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "b": {"0": [0], "1": [0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0], "19": [0], "20": [0, 0], "21": [0, 0, 0, 0, 0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\lib\\metadata.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\lib\\metadata.ts", "statementMap": {"0": {"start": {"line": 15, "column": 13}, "end": {"line": 15, "column": 26}}, "1": {"start": {"line": 64, "column": 16}, "end": {"line": 64, "column": 40}}, "2": {"start": {"line": 126, "column": 16}, "end": {"line": 126, "column": 35}}, "3": {"start": {"line": 26, "column": 16}, "end": {"line": 26, "column": 39}}, "4": {"start": {"line": 154, "column": 16}, "end": {"line": 154, "column": 39}}, "5": {"start": {"line": 4, "column": 24}, "end": {"line": 12, "column": null}}, "6": {"start": {"line": 15, "column": 29}, "end": {"line": 23, "column": null}}, "7": {"start": {"line": 32, "column": 23}, "end": {"line": 35, "column": null}}, "8": {"start": {"line": 38, "column": 2}, "end": {"line": 40, "column": null}}, "9": {"start": {"line": 39, "column": 4}, "end": {"line": 39, "column": null}}, "10": {"start": {"line": 46, "column": 21}, "end": {"line": 46, "column": 36}}, "11": {"start": {"line": 47, "column": 20}, "end": {"line": 47, "column": null}}, "12": {"start": {"line": 50, "column": 20}, "end": {"line": 50, "column": null}}, "13": {"start": {"line": 51, "column": 2}, "end": {"line": 53, "column": null}}, "14": {"start": {"line": 52, "column": 4}, "end": {"line": 52, "column": null}}, "15": {"start": {"line": 56, "column": 2}, "end": {"line": 58, "column": null}}, "16": {"start": {"line": 57, "column": 4}, "end": {"line": 57, "column": null}}, "17": {"start": {"line": 60, "column": 2}, "end": {"line": 60, "column": null}}, "18": {"start": {"line": 74, "column": 22}, "end": {"line": 76, "column": 124}}, "19": {"start": {"line": 76, "column": 61}, "end": {"line": 76, "column": 84}}, "20": {"start": {"line": 78, "column": 16}, "end": {"line": 78, "column": 47}}, "21": {"start": {"line": 79, "column": 14}, "end": {"line": 79, "column": 58}}, "22": {"start": {"line": 81, "column": 2}, "end": {"line": 122, "column": null}}, "23": {"start": {"line": 84, "column": 72}, "end": {"line": 84, "column": 98}}, "24": {"start": {"line": 127, "column": 23}, "end": {"line": 127, "column": null}}, "25": {"start": {"line": 127, "column": 51}, "end": {"line": 127, "column": 63}}, "26": {"start": {"line": 128, "column": 22}, "end": {"line": 130, "column": null}}, "27": {"start": {"line": 133, "column": 2}, "end": {"line": 150, "column": null}}, "28": {"start": {"line": 160, "column": 22}, "end": {"line": 162, "column": null}}, "29": {"start": {"line": 165, "column": 16}, "end": {"line": 165, "column": 54}}, "30": {"start": {"line": 166, "column": 14}, "end": {"line": 166, "column": 65}}, "31": {"start": {"line": 168, "column": 2}, "end": {"line": 188, "column": null}}}, "fnMap": {"0": {"name": "generateMetaDescription", "decl": {"start": {"line": 26, "column": 16}, "end": {"line": 26, "column": 39}}, "loc": {"start": {"line": 29, "column": 25}, "end": {"line": 61, "column": null}}}, "1": {"name": "generateBlogPostMetadata", "decl": {"start": {"line": 64, "column": 16}, "end": {"line": 64, "column": 40}}, "loc": {"start": {"line": 73, "column": 1}, "end": {"line": 123, "column": null}}}, "2": {"name": "(anonymous_8)", "decl": {"start": {"line": 76, "column": 56}, "end": {"line": 76, "column": 61}}, "loc": {"start": {"line": 76, "column": 61}, "end": {"line": 76, "column": 84}}}, "3": {"name": "(anonymous_9)", "decl": {"start": {"line": 84, "column": 67}, "end": {"line": 84, "column": 72}}, "loc": {"start": {"line": 84, "column": 72}, "end": {"line": 84, "column": 98}}}, "4": {"name": "generateFAQMetadata", "decl": {"start": {"line": 126, "column": 16}, "end": {"line": 126, "column": 35}}, "loc": {"start": {"line": 126, "column": 85}, "end": {"line": 151, "column": null}}}, "5": {"name": "(anonymous_11)", "decl": {"start": {"line": 127, "column": 44}, "end": {"line": 127, "column": 51}}, "loc": {"start": {"line": 127, "column": 51}, "end": {"line": 127, "column": 63}}}, "6": {"name": "generateServiceMetadata", "decl": {"start": {"line": 154, "column": 16}, "end": {"line": 154, "column": 39}}, "loc": {"start": {"line": 159, "column": 1}, "end": {"line": 189, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 28, "column": 2}, "end": {"line": 28, "column": 31}}, "type": "default-arg", "locations": [{"start": {"line": 28, "column": 29}, "end": {"line": 28, "column": 31}}]}, "1": {"loc": {"start": {"line": 29, "column": 2}, "end": {"line": 29, "column": 25}}, "type": "default-arg", "locations": [{"start": {"line": 29, "column": 22}, "end": {"line": 29, "column": 25}}]}, "2": {"loc": {"start": {"line": 38, "column": 2}, "end": {"line": 40, "column": null}}, "type": "if", "locations": [{"start": {"line": 38, "column": 2}, "end": {"line": 40, "column": null}}]}, "3": {"loc": {"start": {"line": 51, "column": 2}, "end": {"line": 53, "column": null}}, "type": "if", "locations": [{"start": {"line": 51, "column": 2}, "end": {"line": 53, "column": null}}]}, "4": {"loc": {"start": {"line": 56, "column": 2}, "end": {"line": 58, "column": null}}, "type": "if", "locations": [{"start": {"line": 56, "column": 2}, "end": {"line": 58, "column": null}}]}, "5": {"loc": {"start": {"line": 74, "column": 22}, "end": {"line": 76, "column": 124}}, "type": "binary-expr", "locations": [{"start": {"line": 74, "column": 22}, "end": {"line": 74, "column": 34}}, {"start": {"line": 75, "column": 5}, "end": {"line": 76, "column": 124}}]}, "6": {"loc": {"start": {"line": 75, "column": 5}, "end": {"line": 76, "column": 124}}, "type": "cond-expr", "locations": [{"start": {"line": 75, "column": 20}, "end": {"line": 75, "column": null}}, {"start": {"line": 76, "column": 4}, "end": {"line": 76, "column": 124}}]}, "7": {"loc": {"start": {"line": 76, "column": 35}, "end": {"line": 76, "column": 123}}, "type": "binary-expr", "locations": [{"start": {"line": 76, "column": 35}, "end": {"line": 76, "column": 98}}, {"start": {"line": 76, "column": 98}, "end": {"line": 76, "column": 123}}]}, "8": {"loc": {"start": {"line": 84, "column": 46}, "end": {"line": 84, "column": 100}}, "type": "binary-expr", "locations": [{"start": {"line": 84, "column": 46}, "end": {"line": 84, "column": 98}}, {"start": {"line": 84, "column": 98}, "end": {"line": 84, "column": 100}}]}, "9": {"loc": {"start": {"line": 85, "column": 22}, "end": {"line": 85, "column": 57}}, "type": "binary-expr", "locations": [{"start": {"line": 85, "column": 22}, "end": {"line": 85, "column": 33}}, {"start": {"line": 85, "column": 37}, "end": {"line": 85, "column": 57}}]}, "10": {"loc": {"start": {"line": 92, "column": 14}, "end": {"line": 108, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 92, "column": 31}, "end": {"line": 100, "column": 10}}, {"start": {"line": 100, "column": 10}, "end": {"line": 108, "column": null}}]}, "11": {"loc": {"start": {"line": 110, "column": 16}, "end": {"line": 110, "column": 51}}, "type": "binary-expr", "locations": [{"start": {"line": 110, "column": 16}, "end": {"line": 110, "column": 27}}, {"start": {"line": 110, "column": 31}, "end": {"line": 110, "column": 51}}]}, "12": {"loc": {"start": {"line": 117, "column": 14}, "end": {"line": 117, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 117, "column": 31}, "end": {"line": 117, "column": 50}}, {"start": {"line": 117, "column": 50}, "end": {"line": 117, "column": null}}]}, "13": {"loc": {"start": {"line": 171, "column": 75}, "end": {"line": 171, "column": 97}}, "type": "binary-expr", "locations": [{"start": {"line": 171, "column": 75}, "end": {"line": 171, "column": 91}}, {"start": {"line": 171, "column": 95}, "end": {"line": 171, "column": 97}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "b": {"0": [0], "1": [0], "2": [0], "3": [0], "4": [0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\lib\\sanity.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\lib\\sanity.ts", "statementMap": {"0": {"start": {"line": 152, "column": 13}, "end": {"line": 152, "column": 29}}, "1": {"start": {"line": 175, "column": 13}, "end": {"line": 175, "column": 28}}, "2": {"start": {"line": 193, "column": 13}, "end": {"line": 193, "column": 29}}, "3": {"start": {"line": 159, "column": 13}, "end": {"line": 159, "column": 36}}, "4": {"start": {"line": 117, "column": 13}, "end": {"line": 117, "column": 24}}, "5": {"start": {"line": 133, "column": 13}, "end": {"line": 133, "column": 23}}, "6": {"start": {"line": 200, "column": 13}, "end": {"line": 200, "column": 32}}, "7": {"start": {"line": 210, "column": 13}, "end": {"line": 210, "column": 32}}, "8": {"start": {"line": 11, "column": 13}, "end": {"line": 11, "column": 19}}, "9": {"start": {"line": 247, "column": 22}, "end": {"line": 247, "column": 38}}, "10": {"start": {"line": 263, "column": 22}, "end": {"line": 263, "column": 36}}, "11": {"start": {"line": 271, "column": 22}, "end": {"line": 271, "column": 37}}, "12": {"start": {"line": 231, "column": 22}, "end": {"line": 231, "column": 33}}, "13": {"start": {"line": 239, "column": 22}, "end": {"line": 239, "column": 35}}, "14": {"start": {"line": 255, "column": 22}, "end": {"line": 255, "column": 40}}, "15": {"start": {"line": 279, "column": 22}, "end": {"line": 279, "column": 37}}, "16": {"start": {"line": 287, "column": 22}, "end": {"line": 287, "column": 37}}, "17": {"start": {"line": 22, "column": 16}, "end": {"line": 22, "column": 22}}, "18": {"start": {"line": 1, "column": 29}, "end": {"line": 1, "column": null}}, "19": {"start": {"line": 2, "column": 28}, "end": {"line": 2, "column": null}}, "20": {"start": {"line": 5, "column": 21}, "end": {"line": 7, "column": 39}}, "21": {"start": {"line": 11, "column": 22}, "end": {"line": 17, "column": null}}, "22": {"start": {"line": 20, "column": 16}, "end": {"line": 20, "column": null}}, "23": {"start": {"line": 23, "column": 2}, "end": {"line": 26, "column": null}}, "24": {"start": {"line": 24, "column": 4}, "end": {"line": 24, "column": null}}, "25": {"start": {"line": 25, "column": 4}, "end": {"line": 25, "column": null}}, "26": {"start": {"line": 25, "column": 24}, "end": {"line": 25, "column": 49}}, "27": {"start": {"line": 27, "column": 2}, "end": {"line": 27, "column": null}}, "28": {"start": {"line": 117, "column": 27}, "end": {"line": 131, "column": 2}}, "29": {"start": {"line": 133, "column": 26}, "end": {"line": 150, "column": 2}}, "30": {"start": {"line": 152, "column": 32}, "end": {"line": 157, "column": 2}}, "31": {"start": {"line": 159, "column": 39}, "end": {"line": 173, "column": 2}}, "32": {"start": {"line": 175, "column": 31}, "end": {"line": 191, "column": 2}}, "33": {"start": {"line": 193, "column": 32}, "end": {"line": 198, "column": 2}}, "34": {"start": {"line": 200, "column": 35}, "end": {"line": 207, "column": 2}}, "35": {"start": {"line": 210, "column": 35}, "end": {"line": 228, "column": 2}}, "36": {"start": {"line": 232, "column": 2}, "end": {"line": 235, "column": null}}, "37": {"start": {"line": 233, "column": 4}, "end": {"line": 233, "column": null}}, "38": {"start": {"line": 234, "column": 4}, "end": {"line": 234, "column": null}}, "39": {"start": {"line": 236, "column": 2}, "end": {"line": 236, "column": null}}, "40": {"start": {"line": 240, "column": 2}, "end": {"line": 243, "column": null}}, "41": {"start": {"line": 241, "column": 4}, "end": {"line": 241, "column": null}}, "42": {"start": {"line": 242, "column": 4}, "end": {"line": 242, "column": null}}, "43": {"start": {"line": 244, "column": 2}, "end": {"line": 244, "column": null}}, "44": {"start": {"line": 248, "column": 2}, "end": {"line": 251, "column": null}}, "45": {"start": {"line": 249, "column": 4}, "end": {"line": 249, "column": null}}, "46": {"start": {"line": 250, "column": 4}, "end": {"line": 250, "column": null}}, "47": {"start": {"line": 252, "column": 2}, "end": {"line": 252, "column": null}}, "48": {"start": {"line": 256, "column": 2}, "end": {"line": 259, "column": null}}, "49": {"start": {"line": 257, "column": 4}, "end": {"line": 257, "column": null}}, "50": {"start": {"line": 258, "column": 4}, "end": {"line": 258, "column": null}}, "51": {"start": {"line": 260, "column": 2}, "end": {"line": 260, "column": null}}, "52": {"start": {"line": 264, "column": 2}, "end": {"line": 267, "column": null}}, "53": {"start": {"line": 265, "column": 4}, "end": {"line": 265, "column": null}}, "54": {"start": {"line": 266, "column": 4}, "end": {"line": 266, "column": null}}, "55": {"start": {"line": 268, "column": 2}, "end": {"line": 268, "column": null}}, "56": {"start": {"line": 272, "column": 2}, "end": {"line": 275, "column": null}}, "57": {"start": {"line": 273, "column": 4}, "end": {"line": 273, "column": null}}, "58": {"start": {"line": 274, "column": 4}, "end": {"line": 274, "column": null}}, "59": {"start": {"line": 276, "column": 2}, "end": {"line": 276, "column": null}}, "60": {"start": {"line": 280, "column": 2}, "end": {"line": 283, "column": null}}, "61": {"start": {"line": 281, "column": 4}, "end": {"line": 281, "column": null}}, "62": {"start": {"line": 282, "column": 4}, "end": {"line": 282, "column": null}}, "63": {"start": {"line": 284, "column": 2}, "end": {"line": 284, "column": null}}, "64": {"start": {"line": 288, "column": 2}, "end": {"line": 291, "column": null}}, "65": {"start": {"line": 289, "column": 4}, "end": {"line": 289, "column": null}}, "66": {"start": {"line": 290, "column": 4}, "end": {"line": 290, "column": null}}, "67": {"start": {"line": 292, "column": 2}, "end": {"line": 297, "column": null}}, "68": {"start": {"line": 293, "column": 4}, "end": {"line": 293, "column": null}}, "69": {"start": {"line": 295, "column": 4}, "end": {"line": 295, "column": null}}, "70": {"start": {"line": 296, "column": 4}, "end": {"line": 296, "column": null}}}, "fnMap": {"0": {"name": "url<PERSON><PERSON>", "decl": {"start": {"line": 22, "column": 16}, "end": {"line": 22, "column": 22}}, "loc": {"start": {"line": 22, "column": 34}, "end": {"line": 28, "column": null}}}, "1": {"name": "(anonymous_21)", "decl": {"start": {"line": 25, "column": 18}, "end": {"line": 25, "column": 24}}, "loc": {"start": {"line": 25, "column": 24}, "end": {"line": 25, "column": 49}}}, "2": {"name": "getAllPosts", "decl": {"start": {"line": 231, "column": 22}, "end": {"line": 231, "column": 33}}, "loc": {"start": {"line": 231, "column": 22}, "end": {"line": 237, "column": null}}}, "3": {"name": "getPostBySlug", "decl": {"start": {"line": 239, "column": 22}, "end": {"line": 239, "column": 35}}, "loc": {"start": {"line": 239, "column": 48}, "end": {"line": 245, "column": null}}}, "4": {"name": "getAllCategories", "decl": {"start": {"line": 247, "column": 22}, "end": {"line": 247, "column": 38}}, "loc": {"start": {"line": 247, "column": 22}, "end": {"line": 253, "column": null}}}, "5": {"name": "getPostsByCategory", "decl": {"start": {"line": 255, "column": 22}, "end": {"line": 255, "column": 40}}, "loc": {"start": {"line": 255, "column": 59}, "end": {"line": 261, "column": null}}}, "6": {"name": "getAllFAQItems", "decl": {"start": {"line": 263, "column": 22}, "end": {"line": 263, "column": 36}}, "loc": {"start": {"line": 263, "column": 22}, "end": {"line": 269, "column": null}}}, "7": {"name": "getAllFAQTopics", "decl": {"start": {"line": 271, "column": 22}, "end": {"line": 271, "column": 37}}, "loc": {"start": {"line": 271, "column": 22}, "end": {"line": 277, "column": null}}}, "8": {"name": "getRelatedPosts", "decl": {"start": {"line": 279, "column": 22}, "end": {"line": 279, "column": 37}}, "loc": {"start": {"line": 279, "column": 75}, "end": {"line": 285, "column": null}}}, "9": {"name": "getSiteSettings", "decl": {"start": {"line": 287, "column": 22}, "end": {"line": 287, "column": 37}}, "loc": {"start": {"line": 287, "column": 22}, "end": {"line": 298, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 6, "column": 2}, "end": {"line": 7, "column": 39}}, "type": "binary-expr", "locations": [{"start": {"line": 6, "column": 2}, "end": {"line": 6, "column": 43}}, {"start": {"line": 7, "column": 2}, "end": {"line": 7, "column": 39}}]}, "1": {"loc": {"start": {"line": 11, "column": 22}, "end": {"line": 17, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 11, "column": 37}, "end": {"line": 17, "column": 5}}, {"start": {"line": 17, "column": 5}, "end": {"line": 17, "column": null}}]}, "2": {"loc": {"start": {"line": 13, "column": 11}, "end": {"line": 13, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 13, "column": 11}, "end": {"line": 13, "column": 49}}, {"start": {"line": 13, "column": 53}, "end": {"line": 13, "column": null}}]}, "3": {"loc": {"start": {"line": 20, "column": 16}, "end": {"line": 20, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 20, "column": 41}, "end": {"line": 20, "column": 67}}, {"start": {"line": 20, "column": 67}, "end": {"line": 20, "column": null}}]}, "4": {"loc": {"start": {"line": 20, "column": 16}, "end": {"line": 20, "column": 41}}, "type": "binary-expr", "locations": [{"start": {"line": 20, "column": 16}, "end": {"line": 20, "column": 32}}, {"start": {"line": 20, "column": 32}, "end": {"line": 20, "column": 41}}]}, "5": {"loc": {"start": {"line": 23, "column": 2}, "end": {"line": 26, "column": null}}, "type": "if", "locations": [{"start": {"line": 23, "column": 2}, "end": {"line": 26, "column": null}}]}, "6": {"loc": {"start": {"line": 232, "column": 2}, "end": {"line": 235, "column": null}}, "type": "if", "locations": [{"start": {"line": 232, "column": 2}, "end": {"line": 235, "column": null}}]}, "7": {"loc": {"start": {"line": 240, "column": 2}, "end": {"line": 243, "column": null}}, "type": "if", "locations": [{"start": {"line": 240, "column": 2}, "end": {"line": 243, "column": null}}]}, "8": {"loc": {"start": {"line": 248, "column": 2}, "end": {"line": 251, "column": null}}, "type": "if", "locations": [{"start": {"line": 248, "column": 2}, "end": {"line": 251, "column": null}}]}, "9": {"loc": {"start": {"line": 256, "column": 2}, "end": {"line": 259, "column": null}}, "type": "if", "locations": [{"start": {"line": 256, "column": 2}, "end": {"line": 259, "column": null}}]}, "10": {"loc": {"start": {"line": 264, "column": 2}, "end": {"line": 267, "column": null}}, "type": "if", "locations": [{"start": {"line": 264, "column": 2}, "end": {"line": 267, "column": null}}]}, "11": {"loc": {"start": {"line": 272, "column": 2}, "end": {"line": 275, "column": null}}, "type": "if", "locations": [{"start": {"line": 272, "column": 2}, "end": {"line": 275, "column": null}}]}, "12": {"loc": {"start": {"line": 280, "column": 2}, "end": {"line": 283, "column": null}}, "type": "if", "locations": [{"start": {"line": 280, "column": 2}, "end": {"line": 283, "column": null}}]}, "13": {"loc": {"start": {"line": 288, "column": 2}, "end": {"line": 291, "column": null}}, "type": "if", "locations": [{"start": {"line": 288, "column": 2}, "end": {"line": 291, "column": null}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0], "6": [0], "7": [0], "8": [0], "9": [0], "10": [0], "11": [0], "12": [0], "13": [0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\lib\\utils.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\lib\\utils.ts", "statementMap": {"0": {"start": {"line": 4, "column": 16}, "end": {"line": 4, "column": 19}}, "1": {"start": {"line": 1, "column": 38}, "end": {"line": 1, "column": null}}, "2": {"start": {"line": 2, "column": 24}, "end": {"line": 2, "column": null}}, "3": {"start": {"line": 5, "column": 2}, "end": {"line": 5, "column": null}}}, "fnMap": {"0": {"name": "cn", "decl": {"start": {"line": 4, "column": 16}, "end": {"line": 4, "column": 19}}, "loc": {"start": {"line": 4, "column": 42}, "end": {"line": 6, "column": null}}}}, "branchMap": {}, "s": {"0": 376, "1": 3, "2": 3, "3": 376}, "f": {"0": 376}, "b": {}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\types\\index.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\types\\index.ts", "statementMap": {"0": {"start": {"line": 9, "column": 14}, "end": {"line": 9, "column": null}}, "1": {"start": {"line": 10, "column": 14}, "end": {"line": 10, "column": null}}, "2": {"start": {"line": 11, "column": 14}, "end": {"line": 11, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {}, "b": {}}}