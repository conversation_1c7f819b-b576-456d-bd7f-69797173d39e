(()=>{var e={};e.id=103,e.ids=[103],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},46721:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>w,routeModule:()=>c,serverHooks:()=>f,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{DELETE:()=>d,GET:()=>l,POST:()=>u,PUT:()=>p});var o=r(96559),n=r(48088),a=r(37719),i=r(32190);async function u(e){try{let{email:t}=await e.json();if(!t||!t.includes("@"))return i.NextResponse.json({error:"Please provide a valid email address"},{status:400});let r=process.env.MAILCHIMP_API_KEY,s=process.env.MAILCHIMP_LIST_ID;if(!r||!s)return i.NextResponse.json({error:"Newsletter service is not configured. Please try again later."},{status:500});let o=r.split("-")[1];if(!o)return i.NextResponse.json({error:"Newsletter service configuration error. Please try again later."},{status:500});let n={email_address:t.toLowerCase(),status:"subscribed",merge_fields:{SOURCE:"Mobilify Website"}},a=`https://${o}.api.mailchimp.com/3.0/lists/${s}/members`,u=await fetch(a,{method:"POST",headers:{Authorization:`Bearer ${r}`,"Content-Type":"application/json"},body:JSON.stringify(n)}),l=await u.json();if(u.ok)return i.NextResponse.json({message:"Successfully subscribed to newsletter",email:t.toLowerCase()},{status:200});if("Member Exists"===l.title)return i.NextResponse.json({error:"This email is already subscribed to our newsletter."},{status:400});if("Invalid Resource"===l.title)return i.NextResponse.json({error:"Please provide a valid email address."},{status:400});return i.NextResponse.json({error:"Unable to subscribe at this time. Please try again later."},{status:500})}catch(e){return i.NextResponse.json({error:"An unexpected error occurred. Please try again later."},{status:500})}}async function l(){return i.NextResponse.json({error:"Method not allowed"},{status:405})}async function p(){return i.NextResponse.json({error:"Method not allowed"},{status:405})}async function d(){return i.NextResponse.json({error:"Method not allowed"},{status:405})}let c=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/newsletter/route",pathname:"/api/newsletter",filename:"route",bundlePath:"app/api/newsletter/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\api\\newsletter\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:m,serverHooks:f}=c;function w(){return(0,a.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:m})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580],()=>r(46721));module.exports=s})();