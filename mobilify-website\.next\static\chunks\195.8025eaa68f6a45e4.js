"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[195],{4195:(e,t,r)=>{r.r(t),r.d(t,{encodeIntoResult:()=>p,stegaEncodeSourceMap:()=>m,stegaEncodeSourceMap$1:()=>_});var n=r(1501);let i=/_key\s*==\s*['"](.*)['"]/;function o(e){if(!Array.isArray(e))throw Error("Path is not an array");return e.reduce((e,t,r)=>{let n=typeof t;if("number"===n)return`${e}[${t}]`;if("string"===n)return`${e}${0===r?"":"."}${t}`;if(("string"==typeof t?i.test(t.trim()):"object"==typeof t&&"_key"in t)&&t._key)return`${e}[_key=="${t._key}"]`;if(Array.isArray(t)){let[r,n]=t;return`${e}[${r}:${n}]`}throw Error(`Unsupported path segment \`${JSON.stringify(t)}\``)},"")}let s={"\f":"\\f","\n":"\\n","\r":"\\r","	":"\\t","'":"\\'","\\":"\\\\"},a={"\\f":"\f","\\n":`
`,"\\r":"\r","\\t":"	","\\'":"'","\\\\":"\\"};function l(e){let t,r=[],n=/\['(.*?)'\]|\[(\d+)\]|\[\?\(@\._key=='(.*?)'\)\]/g;for(;null!==(t=n.exec(e));){if(void 0!==t[1]){let e=t[1].replace(/\\(\\|f|n|r|t|')/g,e=>a[e]);r.push(e);continue}if(void 0!==t[2]){r.push(parseInt(t[2],10));continue}if(void 0!==t[3]){let e=t[3].replace(/\\(\\')/g,e=>a[e]);r.push({_key:e,_index:-1});continue}}return r}function u(e){return e.map(e=>{if("string"==typeof e||"number"==typeof e)return e;if(""!==e._key)return{_key:e._key};if(-1!==e._index)return e._index;throw Error(`invalid segment:${JSON.stringify(e)}`)})}function p(e,t,r){return function e(t,r,i=[]){if(null!==t&&Array.isArray(t))return t.map((t,o)=>{if((0,n.u4)(t)){let n=t._key;if("string"==typeof n)return e(t,r,i.concat({_key:n,_index:o}))}return e(t,r,i.concat(o))});if((0,n.u4)(t)){if("block"===t._type||"span"===t._type){let n={...t};return"block"===t._type?n.children=e(t.children,r,i.concat("children")):"span"===t._type&&(n.text=e(t.text,r,i.concat("text"))),n}return Object.fromEntries(Object.entries(t).map(([t,n])=>[t,e(n,r,i.concat(t))]))}return r(t,i)}(e,(e,n)=>{if("string"!=typeof e)return e;let i=function(e,t){var r;if(!t?.mappings)return;let n=(r=e.map(e=>{if("string"==typeof e||"number"==typeof e)return e;if(-1!==e._index)return e._index;throw Error(`invalid segment:${JSON.stringify(e)}`)}),`$${r.map(e=>"string"==typeof e?`['${e.replace(/[\f\n\r\t'\\]/g,e=>s[e])}']`:"number"==typeof e?`[${e}]`:""!==e._key?`[?(@._key=='${e._key.replace(/['\\]/g,e=>s[e])}')]`:`[${e._index}]`).join("")}`);if(void 0!==t.mappings[n])return{mapping:t.mappings[n],matchedPath:n,pathSuffix:""};let i=Object.entries(t.mappings).filter(([e])=>n.startsWith(e)).sort(([e],[t])=>t.length-e.length);if(0==i.length)return;let[o,a]=i[0],l=n.substring(o.length);return{mapping:a,matchedPath:o,pathSuffix:l}}(n,t);if(!i)return e;let{mapping:o,matchedPath:a}=i;if("value"!==o.type||"documentValue"!==o.source.type)return e;let u=t.documents[o.source.document],p=t.paths[o.source.path],c=l(a);return r({sourcePath:l(p).concat(n.slice(c.length)),sourceDocument:u,resultPath:n,value:e})})}let c="drafts.";function f(e){return e.startsWith(c)}function d(e){return e.startsWith("versions.")}let h=({sourcePath:e,resultPath:t,value:r})=>{if(function(e){return!!/^\d{4}-\d{2}-\d{2}/.test(e)&&!!Date.parse(e)}(r)||function(e){try{new URL(e,e.startsWith("/")?"https://acme.com":void 0)}catch{return!1}return!0}(r))return!1;let n=e.at(-1);return!("slug"===e.at(-2)&&"current"===n||"string"==typeof n&&(n.startsWith("_")||n.endsWith("Id"))||e.some(e=>"meta"===e||"metadata"===e||"openGraph"===e||"seo"===e)||y(e)||y(t)||"string"==typeof n&&g.has(n))},g=new Set(["color","colour","currency","email","format","gid","hex","href","hsl","hsla","icon","id","index","key","language","layout","link","linkAction","locale","lqip","page","path","ref","rgb","rgba","route","secret","slug","status","tag","template","theme","type","textTheme","unit","url","username","variant","website"]);function y(e){return e.some(e=>"string"==typeof e&&null!==e.match(/type/i))}function m(e,t,r){let{filter:s,logger:a,enabled:l}=r;if(!l){let n="config.enabled must be true, don't call this function otherwise";throw a?.error?.(`[@sanity/client]: ${n}`,{result:e,resultSourceMap:t,config:r}),TypeError(n)}if(!t)return a?.error?.("[@sanity/client]: Missing Content Source Map from response body",{result:e,resultSourceMap:t,config:r}),e;if(!r.studioUrl){let n="config.studioUrl must be defined";throw a?.error?.(`[@sanity/client]: ${n}`,{result:e,resultSourceMap:t,config:r}),TypeError(n)}let g={encoded:[],skipped:[]},y=p(e,t,({sourcePath:e,sourceDocument:t,resultPath:i,value:l})=>{var p;let y;if(("function"==typeof s?s({sourcePath:e,resultPath:i,filterDefault:h,sourceDocument:t,value:l}):h({sourcePath:e,resultPath:i,value:l}))===!1)return a&&g.skipped.push({path:o(u(e)),value:`${l.slice(0,20)}${l.length>20?"...":""}`,length:l.length}),l;a&&g.encoded.push({path:o(u(e)),value:`${l.slice(0,20)}${l.length>20?"...":""}`,length:l.length});let{baseUrl:m,workspace:_,tool:$}=("/"!==(y="string"==typeof(p="function"==typeof r.studioUrl?r.studioUrl(t):r.studioUrl)?p:p.baseUrl)&&(y=y.replace(/\/$/,"")),"string"==typeof p?{baseUrl:y}:{...p,baseUrl:y});if(!m)return l;let{_id:k,_type:b,_projectId:v,_dataset:w}=t;return(0,n.C)(l,{origin:"sanity.io",href:function(e){let{baseUrl:t,workspace:r="default",tool:n="default",id:i,type:s,path:a,projectId:l,dataset:p}=e;if(!t)throw Error("baseUrl is required");if(!a)throw Error("path is required");if(!i)throw Error("id is required");if("/"!==t&&t.endsWith("/"))throw Error("baseUrl must not end with a slash");let h="default"===r?void 0:r,g="default"===n?void 0:n,y=d(i)?i.split(".").slice(2).join("."):f(i)?i.slice(c.length):i,m=Array.isArray(a)?o(u(a)):a,_=new URLSearchParams({baseUrl:t,id:y,type:s,path:m});if(h&&_.set("workspace",h),g&&_.set("tool",g),l&&_.set("projectId",l),p&&_.set("dataset",p),f(i)||d(i)){if(d(i)){let e=function(e){if(!d(e))return;let[t,r,...n]=e.split(".");return r}(i);_.set("perspective",e)}}else _.set("perspective","published");let $=["/"===t?"":t];h&&$.push(h);let k=["mode=presentation",`id=${y}`,`type=${s}`,`path=${encodeURIComponent(m)}`];return g&&k.push(`tool=${g}`),$.push("intent","edit",`${k.join(";")}?${_}`),$.join("/")}({baseUrl:m,workspace:_,tool:$,id:k,type:b,path:e,...!r.omitCrossDatasetReferenceData&&{dataset:w,projectId:v}})},!1)});if(a){let e=g.skipped.length,t=g.encoded.length;if((e||t)&&((a?.groupCollapsed||a.log)?.("[@sanity/client]: Encoding source map into result"),a.log?.(`[@sanity/client]: Paths encoded: ${g.encoded.length}, skipped: ${g.skipped.length}`)),g.encoded.length>0&&(a?.log?.("[@sanity/client]: Table of encoded paths"),(a?.table||a.log)?.(g.encoded)),g.skipped.length>0){let e=new Set;for(let{path:t}of g.skipped)e.add(t.replace(i,"0").replace(/\[\d+\]/g,"[]"));a?.log?.("[@sanity/client]: List of skipped paths",[...e.values()])}(e||t)&&a?.groupEnd?.()}return y}var _=Object.freeze({__proto__:null,stegaEncodeSourceMap:m})}}]);