{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/analytics/GoogleAnalytics.tsx"], "sourcesContent": ["'use client';\n\nimport Script from 'next/script';\n\n// Google Analytics Types\ntype GtagCommand = 'config' | 'event' | 'js' | 'set';\n\ndeclare global {\n  interface Window {\n    gtag: (command: GtagCommand, ...args: unknown[]) => void;\n    dataLayer: unknown[];\n  }\n}\n\nconst GoogleAnalytics = () => {\n  const GA_ID = process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID;\n\n  // Only render in production and if GA_ID is provided\n  if (!GA_ID || process.env.NODE_ENV !== 'production') {\n    return null;\n  }\n\n  return (\n    <>\n      <Script\n        src={`https://www.googletagmanager.com/gtag/js?id=${GA_ID}`}\n        strategy=\"afterInteractive\"\n      />\n      <Script id=\"google-analytics\" strategy=\"afterInteractive\">\n        {`\n          window.dataLayer = window.dataLayer || [];\n          function gtag(){dataLayer.push(arguments);}\n          gtag('js', new Date());\n          gtag('config', '${GA_ID}');\n        `}\n      </Script>\n    </>\n  );\n};\n\nexport default GoogleAnalytics;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAcA,MAAM,kBAAkB;IACtB,MAAM;IAEN,qDAAqD;IACrD,wCAAqD;QACnD,OAAO;IACT;;AAkBF;uCAEe", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/analytics/CrispChat.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport <PERSON>ript from 'next/script';\n\n// Crisp Chat Types\ntype CrispCommand = string;\ntype CrispData = string | number | boolean | object | null;\ntype CrispCallback = (...args: unknown[]) => void;\n\n// Google Analytics Types\ntype GtagEvent = (\n  command: 'event',\n  eventName: string,\n  parameters?: {\n    event_category?: string;\n    event_label?: string;\n    value?: number;\n    [key: string]: unknown;\n  }\n) => void;\n\ndeclare global {\n  interface Window {\n    $crisp: unknown[][];\n    CRISP_WEBSITE_ID: string;\n  }\n}\n\ninterface CrispChatProps {\n  websiteId?: string;\n}\n\nconst CrispChat: React.FC<CrispChatProps> = ({ websiteId }) => {\n  // Get website ID from props or environment variable\n  const crispWebsiteId = websiteId || process.env.NEXT_PUBLIC_CRISP_WEBSITE_ID;\n\n  // Check if Crisp website ID is valid\n  const isValidCrispId = (id: string | undefined): boolean => {\n    if (!id) return false;\n    // Exclude placeholder values and ensure it's a valid format\n    return !id.includes('your_crisp_website_id') &&\n           !id.includes('placeholder') &&\n           id.length > 10; // Crisp IDs are typically longer\n  };\n\n  useEffect(() => {\n    if (!crispWebsiteId || !isValidCrispId(crispWebsiteId)) {\n      if (!crispWebsiteId) {\n        console.warn('Crisp Website ID not found. Please set NEXT_PUBLIC_CRISP_WEBSITE_ID environment variable.');\n      } else {\n        console.warn('Invalid Crisp Website ID detected. Please check your NEXT_PUBLIC_CRISP_WEBSITE_ID environment variable.');\n      }\n      return;\n    }\n\n    // Initialize Crisp configuration after script loads\n    const initializeCrisp = () => {\n      if (typeof window !== 'undefined' && window.$crisp) {\n        // Configure Crisp settings\n        window.$crisp.push(['safe', true]);\n\n        // Set custom data for better support\n        window.$crisp.push(['set', 'user:company', 'Mobilify Website Visitor']);\n        window.$crisp.push(['set', 'session:data', {\n          source: 'website',\n          page: window.location.pathname,\n          timestamp: new Date().toISOString()\n        }]);\n\n        // Track chat interactions for analytics\n        window.$crisp.push(['on', 'chat:opened', () => {\n          if (typeof window !== 'undefined' && window.gtag) {\n            window.gtag('event', 'chat_opened', {\n              event_category: 'engagement',\n              event_label: 'crisp_chat'\n            });\n          }\n        }]);\n\n        window.$crisp.push(['on', 'message:sent', () => {\n          if (typeof window !== 'undefined' && window.gtag) {\n            window.gtag('event', 'chat_message_sent', {\n              event_category: 'engagement',\n              event_label: 'crisp_chat'\n            });\n          }\n        }]);\n\n        window.$crisp.push(['on', 'message:received', () => {\n          if (typeof window !== 'undefined' && window.gtag) {\n            window.gtag('event', 'chat_message_received', {\n              event_category: 'engagement',\n              event_label: 'crisp_chat'\n            });\n          }\n        }]);\n      }\n    };\n\n    // Initialize if Crisp is already loaded, otherwise wait for it\n    if (window.$crisp) {\n      initializeCrisp();\n    } else {\n      // Set up a check for when Crisp loads\n      const checkCrisp = setInterval(() => {\n        if (window.$crisp) {\n          initializeCrisp();\n          clearInterval(checkCrisp);\n        }\n      }, 100);\n\n      // Cleanup interval after 10 seconds\n      setTimeout(() => clearInterval(checkCrisp), 10000);\n    }\n  }, [crispWebsiteId]);\n\n  if (!crispWebsiteId || !isValidCrispId(crispWebsiteId)) {\n    return null;\n  }\n\n  return (\n    <>\n      <Script\n        id=\"crisp-chat-init\"\n        strategy=\"afterInteractive\"\n        dangerouslySetInnerHTML={{\n          __html: `\n            window.$crisp = [];\n            window.CRISP_WEBSITE_ID = \"${crispWebsiteId}\";\n          `\n        }}\n      />\n      <Script\n        src=\"https://client.crisp.chat/l.js\"\n        strategy=\"afterInteractive\"\n      />\n    </>\n  );\n};\n\nexport default CrispChat;\n\n// Utility functions for programmatic chat control\nexport const crispUtils = {\n  // Open the chat widget\n  openChat: () => {\n    if (typeof window !== 'undefined' && window.$crisp) {\n      window.$crisp.push(['do', 'chat:open']);\n    }\n  },\n\n  // Close the chat widget\n  closeChat: () => {\n    if (typeof window !== 'undefined' && window.$crisp) {\n      window.$crisp.push(['do', 'chat:close']);\n    }\n  },\n\n  // Show the chat widget\n  showChat: () => {\n    if (typeof window !== 'undefined' && window.$crisp) {\n      window.$crisp.push(['do', 'chat:show']);\n    }\n  },\n\n  // Hide the chat widget\n  hideChat: () => {\n    if (typeof window !== 'undefined' && window.$crisp) {\n      window.$crisp.push(['do', 'chat:hide']);\n    }\n  },\n\n  // Set user information\n  setUser: (user: { nickname?: string; email?: string; phone?: string; avatar?: string }) => {\n    if (typeof window !== 'undefined' && window.$crisp) {\n      if (user.nickname) window.$crisp.push(['set', 'user:nickname', user.nickname]);\n      if (user.email) window.$crisp.push(['set', 'user:email', user.email]);\n      if (user.phone) window.$crisp.push(['set', 'user:phone', user.phone]);\n      if (user.avatar) window.$crisp.push(['set', 'user:avatar', user.avatar]);\n    }\n  },\n\n  // Send a message programmatically\n  sendMessage: (message: string) => {\n    if (typeof window !== 'undefined' && window.$crisp) {\n      window.$crisp.push(['do', 'message:send', ['text', message]]);\n    }\n  },\n\n  // Set session data\n  setSessionData: (data: Record<string, unknown>) => {\n    if (typeof window !== 'undefined' && window.$crisp) {\n      window.$crisp.push(['set', 'session:data', data]);\n    }\n  },\n\n  // Check if chat is available\n  isChatAvailable: (): boolean => {\n    if (typeof window !== 'undefined' && window.$crisp) {\n      return window.$crisp.length > 0;\n    }\n    return false;\n  },\n\n  // Set custom segments for targeting\n  setSegments: (segments: string[]) => {\n    if (typeof window !== 'undefined' && window.$crisp) {\n      window.$crisp.push(['set', 'session:segments', segments]);\n    }\n  }\n};\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAiCA,MAAM,YAAsC,CAAC,EAAE,SAAS,EAAE;IACxD,oDAAoD;IACpD,MAAM,iBAAiB;IAEvB,qCAAqC;IACrC,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,IAAI,OAAO;QAChB,4DAA4D;QAC5D,OAAO,CAAC,GAAG,QAAQ,CAAC,4BACb,CAAC,GAAG,QAAQ,CAAC,kBACb,GAAG,MAAM,GAAG,IAAI,iCAAiC;IAC1D;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,kBAAkB,CAAC,eAAe,iBAAiB;YACtD,uCAAqB;;YAErB,OAAO;gBACL,QAAQ,IAAI,CAAC;YACf;YACA;QACF;QAEA,oDAAoD;QACpD,MAAM,kBAAkB;YACtB,uCAAoD;;YAuCpD;QACF;QAEA,+DAA+D;QAC/D,IAAI,OAAO,MAAM,EAAE;YACjB;QACF,OAAO;YACL,sCAAsC;YACtC,MAAM,aAAa,YAAY;gBAC7B,IAAI,OAAO,MAAM,EAAE;oBACjB;oBACA,cAAc;gBAChB;YACF,GAAG;YAEH,oCAAoC;YACpC,WAAW,IAAM,cAAc,aAAa;QAC9C;IACF,GAAG;QAAC;KAAe;IAEnB,IAAI,CAAC,kBAAkB,CAAC,eAAe,iBAAiB;QACtD,OAAO;IACT;IAEA,qBACE;;0BACE,8OAAC,8HAAA,CAAA,UAAM;gBACL,IAAG;gBACH,UAAS;gBACT,yBAAyB;oBACvB,QAAQ,CAAC;;uCAEoB,EAAE,eAAe;UAC9C,CAAC;gBACH;;;;;;0BAEF,8OAAC,8HAAA,CAAA,UAAM;gBACL,KAAI;gBACJ,UAAS;;;;;;;;AAIjB;uCAEe;AAGR,MAAM,aAAa;IACxB,uBAAuB;IACvB,UAAU;QACR,uCAAoD;;QAEpD;IACF;IAEA,wBAAwB;IACxB,WAAW;QACT,uCAAoD;;QAEpD;IACF;IAEA,uBAAuB;IACvB,UAAU;QACR,uCAAoD;;QAEpD;IACF;IAEA,uBAAuB;IACvB,UAAU;QACR,uCAAoD;;QAEpD;IACF;IAEA,uBAAuB;IACvB,SAAS,CAAC;QACR,uCAAoD;;QAKpD;IACF;IAEA,kCAAkC;IAClC,aAAa,CAAC;QACZ,uCAAoD;;QAEpD;IACF;IAEA,mBAAmB;IACnB,gBAAgB,CAAC;QACf,uCAAoD;;QAEpD;IACF;IAEA,6BAA6B;IAC7B,iBAAiB;QACf,uCAAoD;;QAEpD;QACA,OAAO;IACT;IAEA,oCAAoC;IACpC,aAAa,CAAC;QACZ,uCAAoD;;QAEpD;IACF;AACF", "debugId": null}}, {"offset": {"line": 184, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/OrganizationSchema.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n\nconst OrganizationSchema = () => {\n  const organizationSchema = {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"Organization\",\n    \"name\": \"Mobilify\",\n    \"description\": \"Mobilify converts your existing website or new idea into a high-quality, native mobile app for iOS and Android. We specialize in custom mobile app development for startups and businesses.\",\n    \"url\": \"https://mobilify.vercel.app\",\n    \"logo\": {\n      \"@type\": \"ImageObject\",\n      \"url\": \"https://mobilify.vercel.app/logo.svg\",\n      \"width\": 200,\n      \"height\": 200\n    },\n    \"foundingDate\": \"2024\",\n    \"founder\": {\n      \"@type\": \"Person\",\n      \"name\": \"Mobilify Team\"\n    },\n    \"address\": {\n      \"@type\": \"PostalAddress\",\n      \"addressCountry\": \"US\"\n    },\n    \"contactPoint\": {\n      \"@type\": \"ContactPoint\",\n      \"contactType\": \"customer service\",\n      \"availableLanguage\": \"English\"\n    },\n    \"sameAs\": [\n      \"https://linkedin.com/company/mobilify\",\n      \"https://twitter.com/mobilify\"\n    ],\n    \"service\": [\n      {\n        \"@type\": \"Service\",\n        \"name\": \"Website to Mobile App Conversion\",\n        \"description\": \"Convert your existing website into a native mobile app for iOS and Android\",\n        \"provider\": {\n          \"@type\": \"Organization\",\n          \"name\": \"Mobilify\"\n        }\n      },\n      {\n        \"@type\": \"Service\", \n        \"name\": \"Custom Mobile App Development\",\n        \"description\": \"Build custom mobile applications from scratch based on your ideas and requirements\",\n        \"provider\": {\n          \"@type\": \"Organization\",\n          \"name\": \"Mobilify\"\n        }\n      },\n      {\n        \"@type\": \"Service\",\n        \"name\": \"Mobile App Consulting\",\n        \"description\": \"Expert consultation on mobile app strategy, design, and development\",\n        \"provider\": {\n          \"@type\": \"Organization\",\n          \"name\": \"Mobilify\"\n        }\n      }\n    ],\n    \"keywords\": [\n      \"mobile app development\",\n      \"website to app conversion\", \n      \"custom app development\",\n      \"iOS app development\",\n      \"Android app development\",\n      \"startup app development\",\n      \"mobile app consulting\"\n    ]\n  };\n\n  return (\n    <script\n      type=\"application/ld+json\"\n      dangerouslySetInnerHTML={{\n        __html: JSON.stringify(organizationSchema, null, 2)\n      }}\n    />\n  );\n};\n\nexport default OrganizationSchema;\n"], "names": [], "mappings": ";;;;AAAA;;AAIA,MAAM,qBAAqB;IACzB,MAAM,qBAAqB;QACzB,YAAY;QACZ,SAAS;QACT,QAAQ;QACR,eAAe;QACf,OAAO;QACP,QAAQ;YACN,SAAS;YACT,OAAO;YACP,SAAS;YACT,UAAU;QACZ;QACA,gBAAgB;QAChB,WAAW;YACT,SAAS;YACT,QAAQ;QACV;QACA,WAAW;YACT,SAAS;YACT,kBAAkB;QACpB;QACA,gBAAgB;YACd,SAAS;YACT,eAAe;YACf,qBAAqB;QACvB;QACA,UAAU;YACR;YACA;SACD;QACD,WAAW;YACT;gBACE,SAAS;gBACT,QAAQ;gBACR,eAAe;gBACf,YAAY;oBACV,SAAS;oBACT,QAAQ;gBACV;YACF;YACA;gBACE,SAAS;gBACT,QAAQ;gBACR,eAAe;gBACf,YAAY;oBACV,SAAS;oBACT,QAAQ;gBACV;YACF;YACA;gBACE,SAAS;gBACT,QAAQ;gBACR,eAAe;gBACf,YAAY;oBACV,SAAS;oBACT,QAAQ;gBACV;YACF;SACD;QACD,YAAY;YACV;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,qBACE,8OAAC;QACC,MAAK;QACL,yBAAyB;YACvB,QAAQ,KAAK,SAAS,CAAC,oBAAoB,MAAM;QACnD;;;;;;AAGN;uCAEe", "debugId": null}}, {"offset": {"line": 278, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/analytics/WebVitals.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\n\n// Web Vitals monitoring component\nconst WebVitals = () => {\n  useEffect(() => {\n    // Only load web-vitals in production and when analytics is available\n    if (process.env.NODE_ENV === 'production' && typeof window !== 'undefined' && window.gtag) {\n      import('web-vitals').then(({ onCLS, onFCP, onLCP, onTTFB, onINP }) => {\n        // Core Web Vitals\n        onCLS((metric) => {\n          window.gtag('event', 'web_vitals', {\n            event_category: 'Web Vitals',\n            event_label: 'CLS',\n            value: Math.round(metric.value * 1000),\n            custom_map: {\n              metric_id: metric.id,\n              metric_value: metric.value,\n              metric_delta: metric.delta,\n            },\n          });\n        });\n\n        // FID has been replaced by INP in web-vitals v3+\n\n        onFCP((metric) => {\n          window.gtag('event', 'web_vitals', {\n            event_category: 'Web Vitals',\n            event_label: 'FCP',\n            value: Math.round(metric.value),\n            custom_map: {\n              metric_id: metric.id,\n              metric_value: metric.value,\n              metric_delta: metric.delta,\n            },\n          });\n        });\n\n        onLCP((metric) => {\n          window.gtag('event', 'web_vitals', {\n            event_category: 'Web Vitals',\n            event_label: 'LCP',\n            value: Math.round(metric.value),\n            custom_map: {\n              metric_id: metric.id,\n              metric_value: metric.value,\n              metric_delta: metric.delta,\n            },\n          });\n        });\n\n        onTTFB((metric) => {\n          window.gtag('event', 'web_vitals', {\n            event_category: 'Web Vitals',\n            event_label: 'TTFB',\n            value: Math.round(metric.value),\n            custom_map: {\n              metric_id: metric.id,\n              metric_value: metric.value,\n              metric_delta: metric.delta,\n            },\n          });\n        });\n\n        // Interaction to Next Paint (INP) - new Core Web Vital\n        onINP((metric) => {\n          window.gtag('event', 'web_vitals', {\n            event_category: 'Web Vitals',\n            event_label: 'INP',\n            value: Math.round(metric.value),\n            custom_map: {\n              metric_id: metric.id,\n              metric_value: metric.value,\n              metric_delta: metric.delta,\n            },\n          });\n        });\n      }).catch((error) => {\n        console.warn('Failed to load web-vitals:', error);\n      });\n    }\n  }, []);\n\n  return null; // This component doesn't render anything\n};\n\nexport default WebVitals;\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAIA,kCAAkC;AAClC,MAAM,YAAY;IAChB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,qEAAqE;QACrE,uCAA2F;;QAyE3F;IACF,GAAG,EAAE;IAEL,OAAO,MAAM,yCAAyC;AACxD;uCAEe", "debugId": null}}, {"offset": {"line": 301, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/contexts/ThemeContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState } from 'react';\n\ntype Theme = 'light' | 'dark';\n\ninterface ThemeContextType {\n  theme: Theme;\n  toggleTheme: () => void;\n  setTheme: (theme: Theme) => void;\n}\n\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined);\n\nexport const useTheme = () => {\n  const context = useContext(ThemeContext);\n  if (context === undefined) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n\ninterface ThemeProviderProps {\n  children: React.ReactNode;\n}\n\nexport const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {\n  const [theme, setThemeState] = useState<Theme>('light');\n  const [mounted, setMounted] = useState(false);\n\n  // Initialize theme from localStorage or system preference\n  useEffect(() => {\n    const savedTheme = localStorage.getItem('mobilify-theme') as Theme;\n    const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n    const initialTheme = savedTheme || systemTheme;\n    \n    setThemeState(initialTheme);\n    setMounted(true);\n  }, []);\n\n  // Apply theme to document\n  useEffect(() => {\n    if (!mounted) return;\n\n    const root = document.documentElement;\n    \n    if (theme === 'dark') {\n      root.classList.add('dark');\n    } else {\n      root.classList.remove('dark');\n    }\n\n    // Save to localStorage\n    localStorage.setItem('mobilify-theme', theme);\n\n    // Track theme change for analytics\n    if (typeof window !== 'undefined' && (window as any).gtag) {\n      (window as any).gtag('event', 'theme_changed', {\n        event_category: 'user_preference',\n        event_label: theme\n      });\n    }\n  }, [theme, mounted]);\n\n  // Listen for system theme changes\n  useEffect(() => {\n    if (!mounted) return;\n\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n    \n    const handleChange = (e: MediaQueryListEvent) => {\n      // Only auto-switch if user hasn't manually set a preference\n      const savedTheme = localStorage.getItem('mobilify-theme');\n      if (!savedTheme) {\n        setThemeState(e.matches ? 'dark' : 'light');\n      }\n    };\n\n    mediaQuery.addEventListener('change', handleChange);\n    return () => mediaQuery.removeEventListener('change', handleChange);\n  }, [mounted]);\n\n  const toggleTheme = () => {\n    setThemeState(prevTheme => prevTheme === 'light' ? 'dark' : 'light');\n  };\n\n  const setTheme = (newTheme: Theme) => {\n    setThemeState(newTheme);\n  };\n\n  return (\n    <ThemeContext.Provider value={{ theme, toggleTheme, setTheme }}>\n      <div suppressHydrationWarning>\n        {children}\n      </div>\n    </ThemeContext.Provider>\n  );\n};\n\n// Hook for getting system theme preference\nexport const useSystemTheme = (): Theme => {\n  const [systemTheme, setSystemTheme] = useState<Theme>('light');\n\n  useEffect(() => {\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n    setSystemTheme(mediaQuery.matches ? 'dark' : 'light');\n\n    const handleChange = (e: MediaQueryListEvent) => {\n      setSystemTheme(e.matches ? 'dark' : 'light');\n    };\n\n    mediaQuery.addEventListener('change', handleChange);\n    return () => mediaQuery.removeEventListener('change', handleChange);\n  }, []);\n\n  return systemTheme;\n};\n\n// Utility function to get theme-aware classes\nexport const getThemeClasses = (lightClasses: string, darkClasses: string) => {\n  return `${lightClasses} dark:${darkClasses}`;\n};\n"], "names": [], "mappings": ";;;;;;;AAEA;AAFA;;;AAYA,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAgC;AAE1D,MAAM,WAAW;IACtB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAMO,MAAM,gBAA8C,CAAC,EAAE,QAAQ,EAAE;IACtE,MAAM,CAAC,OAAO,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,0DAA0D;IAC1D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa,aAAa,OAAO,CAAC;QACxC,MAAM,cAAc,OAAO,UAAU,CAAC,gCAAgC,OAAO,GAAG,SAAS;QACzF,MAAM,eAAe,cAAc;QAEnC,cAAc;QACd,WAAW;IACb,GAAG,EAAE;IAEL,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS;QAEd,MAAM,OAAO,SAAS,eAAe;QAErC,IAAI,UAAU,QAAQ;YACpB,KAAK,SAAS,CAAC,GAAG,CAAC;QACrB,OAAO;YACL,KAAK,SAAS,CAAC,MAAM,CAAC;QACxB;QAEA,uBAAuB;QACvB,aAAa,OAAO,CAAC,kBAAkB;QAEvC,mCAAmC;QACnC,uCAA2D;;QAK3D;IACF,GAAG;QAAC;QAAO;KAAQ;IAEnB,kCAAkC;IAClC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS;QAEd,MAAM,aAAa,OAAO,UAAU,CAAC;QAErC,MAAM,eAAe,CAAC;YACpB,4DAA4D;YAC5D,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,IAAI,CAAC,YAAY;gBACf,cAAc,EAAE,OAAO,GAAG,SAAS;YACrC;QACF;QAEA,WAAW,gBAAgB,CAAC,UAAU;QACtC,OAAO,IAAM,WAAW,mBAAmB,CAAC,UAAU;IACxD,GAAG;QAAC;KAAQ;IAEZ,MAAM,cAAc;QAClB,cAAc,CAAA,YAAa,cAAc,UAAU,SAAS;IAC9D;IAEA,MAAM,WAAW,CAAC;QAChB,cAAc;IAChB;IAEA,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;YAAO;YAAa;QAAS;kBAC3D,cAAA,8OAAC;YAAI,wBAAwB;sBAC1B;;;;;;;;;;;AAIT;AAGO,MAAM,iBAAiB;IAC5B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS;IAEtD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa,OAAO,UAAU,CAAC;QACrC,eAAe,WAAW,OAAO,GAAG,SAAS;QAE7C,MAAM,eAAe,CAAC;YACpB,eAAe,EAAE,OAAO,GAAG,SAAS;QACtC;QAEA,WAAW,gBAAgB,CAAC,UAAU;QACtC,OAAO,IAAM,WAAW,mBAAmB,CAAC,UAAU;IACxD,GAAG,EAAE;IAEL,OAAO;AACT;AAGO,MAAM,kBAAkB,CAAC,cAAsB;IACpD,OAAO,GAAG,aAAa,MAAM,EAAE,aAAa;AAC9C", "debugId": null}}, {"offset": {"line": 414, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/NoSSR.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\n\ninterface NoSSRProps {\n  children: React.ReactNode;\n  fallback?: React.ReactNode;\n}\n\nconst NoSSR = ({ children, fallback = null }: NoSSRProps) => {\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  if (!mounted) {\n    return <>{fallback}</>;\n  }\n\n  return <>{children}</>;\n};\n\nexport default NoSSR;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASA,MAAM,QAAQ,CAAC,EAAE,QAAQ,EAAE,WAAW,IAAI,EAAc;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,qBAAO;sBAAG;;IACZ;IAEA,qBAAO;kBAAG;;AACZ;uCAEe", "debugId": null}}]}