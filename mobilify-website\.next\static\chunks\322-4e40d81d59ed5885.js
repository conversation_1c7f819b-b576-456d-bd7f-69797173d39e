(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[322],{646:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},760:(t,e,r)=>{"use strict";r.d(e,{N:()=>v});var n=r(5155),i=r(2115),s=r(869),o=r(2885),a=r(7494),u=r(845),l=r(7351),h=r(1508);class c extends i.Component{getSnapshotBeforeUpdate(t){let e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){let t=e.offsetParent,r=(0,l.s)(t)&&t.offsetWidth||0,n=this.props.sizeRef.current;n.height=e.offsetHeight||0,n.width=e.offsetWidth||0,n.top=e.offsetTop,n.left=e.offsetLeft,n.right=r-n.width-n.left}return null}componentDidUpdate(){}render(){return this.props.children}}function f(t){let{children:e,isPresent:r,anchorX:s,root:o}=t,a=(0,i.useId)(),u=(0,i.useRef)(null),l=(0,i.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:f}=(0,i.useContext)(h.Q);return(0,i.useInsertionEffect)(()=>{let{width:t,height:e,top:n,left:i,right:h}=l.current;if(r||!u.current||!t||!e)return;u.current.dataset.motionPopId=a;let c=document.createElement("style");f&&(c.nonce=f);let p=null!=o?o:document.head;return p.appendChild(c),c.sheet&&c.sheet.insertRule('\n          [data-motion-pop-id="'.concat(a,'"] {\n            position: absolute !important;\n            width: ').concat(t,"px !important;\n            height: ").concat(e,"px !important;\n            ").concat("left"===s?"left: ".concat(i):"right: ".concat(h),"px !important;\n            top: ").concat(n,"px !important;\n          }\n        ")),()=>{p.removeChild(c),p.contains(c)&&p.removeChild(c)}},[r]),(0,n.jsx)(c,{isPresent:r,childRef:u,sizeRef:l,children:i.cloneElement(e,{ref:u})})}let p=t=>{let{children:e,initial:r,isPresent:s,onExitComplete:a,custom:l,presenceAffectsLayout:h,mode:c,anchorX:p,root:m}=t,y=(0,o.M)(d),g=(0,i.useId)(),v=!0,b=(0,i.useMemo)(()=>(v=!1,{id:g,initial:r,isPresent:s,custom:l,onExitComplete:t=>{for(let e of(y.set(t,!0),y.values()))if(!e)return;a&&a()},register:t=>(y.set(t,!1),()=>y.delete(t))}),[s,y,a]);return h&&v&&(b={...b}),(0,i.useMemo)(()=>{y.forEach((t,e)=>y.set(e,!1))},[s]),i.useEffect(()=>{s||y.size||!a||a()},[s]),"popLayout"===c&&(e=(0,n.jsx)(f,{isPresent:s,anchorX:p,root:m,children:e})),(0,n.jsx)(u.t.Provider,{value:b,children:e})};function d(){return new Map}var m=r(2082);let y=t=>t.key||"";function g(t){let e=[];return i.Children.forEach(t,t=>{(0,i.isValidElement)(t)&&e.push(t)}),e}let v=t=>{let{children:e,custom:r,initial:u=!0,onExitComplete:l,presenceAffectsLayout:h=!0,mode:c="sync",propagate:f=!1,anchorX:d="left",root:v}=t,[b,w]=(0,m.xQ)(f),x=(0,i.useMemo)(()=>g(e),[e]),E=f&&!b?[]:x.map(y),T=(0,i.useRef)(!0),C=(0,i.useRef)(x),A=(0,o.M)(()=>new Map),[S,P]=(0,i.useState)(x),[R,M]=(0,i.useState)(x);(0,a.E)(()=>{T.current=!1,C.current=x;for(let t=0;t<R.length;t++){let e=y(R[t]);E.includes(e)?A.delete(e):!0!==A.get(e)&&A.set(e,!1)}},[R,E.length,E.join("-")]);let j=[];if(x!==S){let t=[...x];for(let e=0;e<R.length;e++){let r=R[e],n=y(r);E.includes(n)||(t.splice(e,0,r),j.push(r))}return"wait"===c&&j.length&&(t=j),M(g(t)),P(x),null}let{forceRender:O}=(0,i.useContext)(s.L);return(0,n.jsx)(n.Fragment,{children:R.map(t=>{let e=y(t),i=(!f||!!b)&&(x===R||E.includes(e));return(0,n.jsx)(p,{isPresent:i,initial:(!T.current||!!u)&&void 0,custom:r,presenceAffectsLayout:h,mode:c,root:v,onExitComplete:i?void 0:()=>{if(!A.has(e))return;A.set(e,!0);let t=!0;A.forEach(e=>{e||(t=!1)}),t&&(null==O||O(),M(C.current),f&&(null==w||w()),l&&l())},anchorX:d,children:t},e)})})}},845:(t,e,r)=>{"use strict";r.d(e,{t:()=>n});let n=(0,r(2115).createContext)(null)},869:(t,e,r)=>{"use strict";r.d(e,{L:()=>n});let n=(0,r(2115).createContext)({})},1366:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},1501:(t,e,r)=>{"use strict";function n(t){return"object"==typeof t&&null!==t&&!Array.isArray(t)}r.d(e,{C:()=>a,Q:()=>h,u4:()=>n});var i={0:8203,1:8204,2:8205,3:8290,4:8291,5:8288,6:65279,7:8289,8:119155,9:119156,a:119157,b:119158,c:119159,d:119160,e:119161,f:119162},s={0:8203,1:8204,2:8205,3:65279},o=[,,,,].fill(String.fromCodePoint(s[0])).join("");function a(t,e,r="auto"){let n;return!0===r||"auto"===r&&(!(!Number.isNaN(Number(t))||/[a-z]/i.test(t)&&!/\d+(?:[-:\/]\d+){2}(?:T\d+(?:[-:\/]\d+){1,2}(\.\d+)?Z?)?/.test(t))&&Date.parse(t)||function(t){try{new URL(t,t.startsWith("/")?"https://acme.com":void 0)}catch{return!1}return!0}(t))?t:`${t}${n=JSON.stringify(e),`${o}${Array.from(n).map(t=>{let e=t.charCodeAt(0);if(e>255)throw Error(`Only ASCII edit info can be encoded. Error attempting to encode ${n} on character ${t} (${e})`);return Array.from(e.toString(4).padStart(4,"0")).map(t=>String.fromCodePoint(s[t])).join("")}).join("")}`}`}Object.fromEntries(Object.entries(s).map(t=>t.reverse())),Object.fromEntries(Object.entries(i).map(t=>t.reverse()));var u=`${Object.values(i).map(t=>`\\u{${t.toString(16)}}`).join("")}`,l=RegExp(`[${u}]{4,}`,"gu");function h(t){var e,r;return t&&JSON.parse({cleaned:(e=JSON.stringify(t)).replace(l,""),encoded:(null==(r=e.match(l))?void 0:r[0])||""}.cleaned)}},1508:(t,e,r)=>{"use strict";r.d(e,{Q:()=>n});let n=(0,r(2115).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},2082:(t,e,r)=>{"use strict";r.d(e,{xQ:()=>s});var n=r(2115),i=r(845);function s(){let t=!(arguments.length>0)||void 0===arguments[0]||arguments[0],e=(0,n.useContext)(i.t);if(null===e)return[!0,null];let{isPresent:r,onExitComplete:s,register:o}=e,a=(0,n.useId)();(0,n.useEffect)(()=>{if(t)return o(a)},[t]);let u=(0,n.useCallback)(()=>t&&s&&s(a),[a,s,t]);return!r&&s?[!1,u]:[!0]}},2098:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},2664:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"isLocalURL",{enumerable:!0,get:function(){return s}});let n=r(9991),i=r(7102);function s(t){if(!(0,n.isAbsoluteUrl)(t))return!0;try{let e=(0,n.getLocationOrigin)(),r=new URL(t,e);return r.origin===e&&(0,i.hasBasePath)(r.pathname)}catch(t){return!1}}},2757:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var r in e)Object.defineProperty(t,r,{enumerable:!0,get:e[r]})}(e,{formatUrl:function(){return s},formatWithValidation:function(){return a},urlObjectKeys:function(){return o}});let n=r(6966)._(r(8859)),i=/https?|ftp|gopher|file/;function s(t){let{auth:e,hostname:r}=t,s=t.protocol||"",o=t.pathname||"",a=t.hash||"",u=t.query||"",l=!1;e=e?encodeURIComponent(e).replace(/%3A/i,":")+"@":"",t.host?l=e+t.host:r&&(l=e+(~r.indexOf(":")?"["+r+"]":r),t.port&&(l+=":"+t.port)),u&&"object"==typeof u&&(u=String(n.urlQueryToSearchParams(u)));let h=t.search||u&&"?"+u||"";return s&&!s.endsWith(":")&&(s+=":"),t.slashes||(!s||i.test(s))&&!1!==l?(l="//"+(l||""),o&&"/"!==o[0]&&(o="/"+o)):l||(l=""),a&&"#"!==a[0]&&(a="#"+a),h&&"?"!==h[0]&&(h="?"+h),""+s+l+(o=o.replace(/[?#]/g,encodeURIComponent))+(h=h.replace("#","%23"))+a}let o=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function a(t){return s(t)}},2885:(t,e,r)=>{"use strict";r.d(e,{M:()=>i});var n=r(2115);function i(t){let e=(0,n.useRef)(null);return null===e.current&&(e.current=t()),e.current}},3180:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"errorOnce",{enumerable:!0,get:function(){return r}});let r=t=>{}},3406:(t,e,r)=>{"use strict";r.d(e,{UU:()=>nw});let n=!(typeof navigator>"u")&&"ReactNative"===navigator.product,i={timeout:n?6e4:12e4},s=function(t){let e={...i,..."string"==typeof t?{url:t}:t};if(e.timeout=function t(e){if(!1===e||0===e)return!1;if(e.connect||e.socket)return e;let r=Number(e);return isNaN(r)?t(i.timeout):{connect:r,socket:r}}(e.timeout),e.query){let{url:t,searchParams:r}=function(t){let e=t.indexOf("?");if(-1===e)return{url:t,searchParams:new URLSearchParams};let r=t.slice(0,e),i=t.slice(e+1);if(!n)return{url:r,searchParams:new URLSearchParams(i)};if("function"!=typeof decodeURIComponent)throw Error("Broken `URLSearchParams` implementation, and `decodeURIComponent` is not defined");let s=new URLSearchParams;for(let t of i.split("&")){let[e,r]=t.split("=");e&&s.append(o(e),o(r||""))}return{url:r,searchParams:s}}(e.url);for(let[n,i]of Object.entries(e.query)){if(void 0!==i)if(Array.isArray(i))for(let t of i)r.append(n,t);else r.append(n,i);let s=r.toString();s&&(e.url=`${t}?${s}`)}}return e.method=e.body&&!e.method?"POST":(e.method||"GET").toUpperCase(),e};function o(t){return decodeURIComponent(t.replace(/\+/g," "))}let a=/^https?:\/\//i,u=function(t){if(!a.test(t.url))throw Error(`"${t.url}" is not a valid URL`)};function l(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}let h=["request","response","progress","error","abort"],c=["processOptions","validateOptions","interceptRequest","finalizeOptions","onRequest","onResponse","onError","onReturn","onHeaders"];var f,p,d=l(function(){if(p)return f;p=1;var t=function(t){return t.replace(/^\s+|\s+$/g,"")};return f=function(e){if(!e)return{};for(var r=Object.create(null),n=t(e).split("\n"),i=0;i<n.length;i++){var s,o=n[i],a=o.indexOf(":"),u=t(o.slice(0,a)).toLowerCase(),l=t(o.slice(a+1));typeof r[u]>"u"?r[u]=l:(s=r[u],"[object Array]"===Object.prototype.toString.call(s))?r[u].push(l):r[u]=[r[u],l]}return r}}());class m{onabort;onerror;onreadystatechange;ontimeout;readyState=0;response;responseText="";responseType="";status;statusText;withCredentials;#t;#e;#r;#n={};#i;#s={};#o;open(t,e,r){this.#t=t,this.#e=e,this.#r="",this.readyState=1,this.onreadystatechange?.(),this.#i=void 0}abort(){this.#i&&this.#i.abort()}getAllResponseHeaders(){return this.#r}setRequestHeader(t,e){this.#n[t]=e}setInit(t,e=!0){this.#s=t,this.#o=e}send(t){let e="arraybuffer"!==this.responseType,r={...this.#s,method:this.#t,headers:this.#n,body:t};"function"==typeof AbortController&&this.#o&&(this.#i=new AbortController,"u">typeof EventTarget&&this.#i.signal instanceof EventTarget&&(r.signal=this.#i.signal)),"u">typeof document&&(r.credentials=this.withCredentials?"include":"omit"),fetch(this.#e,r).then(t=>(t.headers.forEach((t,e)=>{this.#r+=`${e}: ${t}\r
`}),this.status=t.status,this.statusText=t.statusText,this.readyState=3,this.onreadystatechange?.(),e?t.text():t.arrayBuffer())).then(t=>{"string"==typeof t?this.responseText=t:this.response=t,this.readyState=4,this.onreadystatechange?.()}).catch(t=>{"AbortError"!==t.name?this.onerror?.(t):this.onabort?.()})}}let y="function"==typeof XMLHttpRequest?"xhr":"fetch",g="xhr"===y?XMLHttpRequest:m,v=(t,e)=>{let r=t.options,n=t.applyMiddleware("finalizeOptions",r),i={},s=t.applyMiddleware("interceptRequest",void 0,{adapter:y,context:t});if(s){let t=setTimeout(e,0,null,s);return{abort:()=>clearTimeout(t)}}let o=new g;o instanceof m&&"object"==typeof n.fetch&&o.setInit(n.fetch,n.useAbortSignal??!0);let a=n.headers,u=n.timeout,l=!1,h=!1,c=!1;if(o.onerror=t=>{v(o instanceof m?t instanceof Error?t:Error(`Request error while attempting to reach is ${n.url}`,{cause:t}):Error(`Request error while attempting to reach is ${n.url}${t.lengthComputable?`(${t.loaded} of ${t.total} bytes transferred)`:""}`))},o.ontimeout=t=>{v(Error(`Request timeout while attempting to reach ${n.url}${t.lengthComputable?`(${t.loaded} of ${t.total} bytes transferred)`:""}`))},o.onabort=()=>{p(!0),l=!0},o.onreadystatechange=function(){u&&(p(),i.socket=setTimeout(()=>f("ESOCKETTIMEDOUT"),u.socket)),!l&&o&&4===o.readyState&&0!==o.status&&function(){if(!(l||h||c)){if(0===o.status)return v(Error("Unknown XHR error"));p(),h=!0,e(null,{body:o.response||(""===o.responseType||"text"===o.responseType?o.responseText:""),url:n.url,method:n.method,headers:d(o.getAllResponseHeaders()),statusCode:o.status,statusMessage:o.statusText})}}()},o.open(n.method,n.url,!0),o.withCredentials=!!n.withCredentials,a&&o.setRequestHeader)for(let t in a)a.hasOwnProperty(t)&&o.setRequestHeader(t,a[t]);return n.rawBody&&(o.responseType="arraybuffer"),t.applyMiddleware("onRequest",{options:n,adapter:y,request:o,context:t}),o.send(n.body||null),u&&(i.connect=setTimeout(()=>f("ETIMEDOUT"),u.connect)),{abort:function(){l=!0,o&&o.abort()}};function f(e){c=!0,o.abort();let r=Error("ESOCKETTIMEDOUT"===e?`Socket timed out on request to ${n.url}`:`Connection timed out on request to ${n.url}`);r.code=e,t.channels.error.publish(r)}function p(t){(t||l||o&&o.readyState>=2&&i.connect)&&clearTimeout(i.connect),i.socket&&clearTimeout(i.socket)}function v(t){if(h)return;p(!0),h=!0,o=null;let r=t||Error(`Network error while attempting to reach ${n.url}`);r.isNetworkError=!0,r.request=n,e(r)}},b=(t=[],e=v)=>(function t(e,r){let n=[],i=c.reduce((t,e)=>(t[e]=t[e]||[],t),{processOptions:[s],validateOptions:[u]});function o(t){let e,n=h.reduce((t,e)=>(t[e]=function(){let t=Object.create(null),e=0;return{publish:function(e){for(let r in t)t[r](e)},subscribe:function(r){let n=e++;return t[n]=r,function(){delete t[n]}}}}(),t),{}),s=function(t,e,...r){let n="onError"===t,s=e;for(let e=0;e<i[t].length&&(s=(0,i[t][e])(s,...r),!n||s);e++);return s},o=s("processOptions",t);s("validateOptions",o);let a={options:o,channels:n,applyMiddleware:s},u=n.request.subscribe(t=>{e=r(t,(e,r)=>((t,e,r)=>{let i=t,o=e;if(!i)try{o=s("onResponse",e,r)}catch(t){o=null,i=t}(i=i&&s("onError",i,r))?n.error.publish(i):o&&n.response.publish(o)})(e,r,t))});n.abort.subscribe(()=>{u(),e&&e.abort()});let l=s("onReturn",n,a);return l===n&&n.request.publish(a),l}return o.use=function(t){if(!t)throw Error("Tried to add middleware that resolved to falsey value");if("function"==typeof t)throw Error("Tried to add middleware that was a function. It probably expects you to pass options to it.");if(t.onReturn&&i.onReturn.length>0)throw Error("Tried to add new middleware with `onReturn` handler, but another handler has already been registered for this event");return c.forEach(e=>{t[e]&&i[e].push(t[e])}),n.push(t),o},o.clone=()=>t(n,r),e.forEach(o.use),o})(t,e);var w=r(9509),x=r(9641).Buffer,E,T,C,A,S,P={exports:{}};S||(S=1,function(t,e){let r;e.formatArgs=function(e){if(e[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+e[0]+(this.useColors?"%c ":" ")+"+"+t.exports.humanize(this.diff),!this.useColors)return;let r="color: "+this.color;e.splice(1,0,r,"color: inherit");let n=0,i=0;e[0].replace(/%[a-zA-Z%]/g,t=>{"%%"!==t&&(n++,"%c"===t&&(i=n))}),e.splice(i,0,r)},e.save=function(t){try{t?e.storage.setItem("debug",t):e.storage.removeItem("debug")}catch{}},e.load=function(){let t;try{t=e.storage.getItem("debug")||e.storage.getItem("DEBUG")}catch{}return!t&&"u">typeof w&&"env"in w&&(t=w.env.DEBUG),t},e.useColors=function(){let t;return"u">typeof window&&!!window.process&&("renderer"===window.process.type||!!window.process.__nwjs)||!("u">typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("u">typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"u">typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"u">typeof navigator&&navigator.userAgent&&(t=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(t[1],10)>=31||"u">typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},e.storage=function(){try{return localStorage}catch{}}(),r=!1,e.destroy=()=>{r||(r=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))},e.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],e.log=console.debug||console.log||(()=>{}),t.exports=(A?C:(A=1,C=function(t){function e(t){let n,i,s,o=null;function a(...t){if(!a.enabled)return;let r=Number(new Date);a.diff=r-(n||r),a.prev=n,a.curr=r,n=r,t[0]=e.coerce(t[0]),"string"!=typeof t[0]&&t.unshift("%O");let i=0;t[0]=t[0].replace(/%([a-zA-Z%])/g,(r,n)=>{if("%%"===r)return"%";i++;let s=e.formatters[n];if("function"==typeof s){let e=t[i];r=s.call(a,e),t.splice(i,1),i--}return r}),e.formatArgs.call(a,t),(a.log||e.log).apply(a,t)}return a.namespace=t,a.useColors=e.useColors(),a.color=e.selectColor(t),a.extend=r,a.destroy=e.destroy,Object.defineProperty(a,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==o?o:(i!==e.namespaces&&(i=e.namespaces,s=e.enabled(t)),s),set:t=>{o=t}}),"function"==typeof e.init&&e.init(a),a}function r(t,r){let n=e(this.namespace+(typeof r>"u"?":":r)+t);return n.log=this.log,n}function n(t,e){let r=0,n=0,i=-1,s=0;for(;r<t.length;)if(n<e.length&&(e[n]===t[r]||"*"===e[n]))"*"===e[n]?(i=n,s=r):r++,n++;else{if(-1===i)return!1;n=i+1,r=++s}for(;n<e.length&&"*"===e[n];)n++;return n===e.length}return e.debug=e,e.default=e,e.coerce=function(t){return t instanceof Error?t.stack||t.message:t},e.disable=function(){let t=[...e.names,...e.skips.map(t=>"-"+t)].join(",");return e.enable(""),t},e.enable=function(t){for(let r of(e.save(t),e.namespaces=t,e.names=[],e.skips=[],("string"==typeof t?t:"").trim().replace(/\s+/g,",").split(",").filter(Boolean)))"-"===r[0]?e.skips.push(r.slice(1)):e.names.push(r)},e.enabled=function(t){for(let r of e.skips)if(n(t,r))return!1;for(let r of e.names)if(n(t,r))return!0;return!1},e.humanize=function(){if(T)return E;function t(t,e,r,n){return Math.round(t/r)+" "+n+(e>=1.5*r?"s":"")}return T=1,E=function(e,r){r=r||{};var n,i,s=typeof e;if("string"===s&&e.length>0){var o=e;if(!((o=String(o)).length>100)){var a=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(o);if(a){var u=parseFloat(a[1]);switch((a[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*u;case"weeks":case"week":case"w":return 6048e5*u;case"days":case"day":case"d":return 864e5*u;case"hours":case"hour":case"hrs":case"hr":case"h":return 36e5*u;case"minutes":case"minute":case"mins":case"min":case"m":return 6e4*u;case"seconds":case"second":case"secs":case"sec":case"s":return 1e3*u;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return u}}}return}if("number"===s&&isFinite(e))return r.long?(i=Math.abs(e))>=864e5?t(e,i,864e5,"day"):i>=36e5?t(e,i,36e5,"hour"):i>=6e4?t(e,i,6e4,"minute"):i>=1e3?t(e,i,1e3,"second"):e+" ms":(n=Math.abs(e))>=864e5?Math.round(e/864e5)+"d":n>=36e5?Math.round(e/36e5)+"h":n>=6e4?Math.round(e/6e4)+"m":n>=1e3?Math.round(e/1e3)+"s":e+"ms";throw Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}}(),e.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(t).forEach(r=>{e[r]=t[r]}),e.names=[],e.skips=[],e.formatters={},e.selectColor=function(t){let r=0;for(let e=0;e<t.length;e++)r=(r<<5)-r+t.charCodeAt(e)|0;return e.colors[Math.abs(r)%e.colors.length]},e.enable(e.load()),e}))(e);let{formatters:n}=t.exports;n.j=function(t){try{return JSON.stringify(t)}catch(t){return"[UnexpectedJSONParseError]: "+t.message}}}(P,P.exports)),P.exports,Object.prototype.hasOwnProperty;let R=typeof x>"u"?()=>!1:t=>x.isBuffer(t);function M(t){return"[object Object]"===Object.prototype.toString.call(t)}let j=["boolean","string","number"],O={};"u">typeof globalThis?O=globalThis:"u">typeof window?O=window:"u">typeof global?O=global:"u">typeof self&&(O=self);var I=O;let k=(t={})=>{let e=t.implementation||Promise;if(!e)throw Error("`Promise` is not available in global scope, and no implementation was passed");return{onReturn:(r,n)=>new e((e,i)=>{let s=n.options.cancelToken;s&&s.promise.then(t=>{r.abort.publish(t),i(t)}),r.error.subscribe(i),r.response.subscribe(r=>{e(t.onlyBody?r.body:r)}),setTimeout(()=>{try{r.request.publish(n)}catch(t){i(t)}},0)})}};class D{__CANCEL__=!0;message;constructor(t){this.message=t}toString(){return"Cancel"+(this.message?`: ${this.message}`:"")}}class V{promise;reason;constructor(t){if("function"!=typeof t)throw TypeError("executor must be a function.");let e=null;this.promise=new Promise(t=>{e=t}),t(t=>{this.reason||(this.reason=new D(t),e(this.reason))})}static source=()=>{let t;return{token:new V(e=>{t=e}),cancel:t}}}k.Cancel=D,k.CancelToken=V,k.isCancel=t=>!(!t||!t?.__CANCEL__);var L=(t,e,r)=>("GET"===r.method||"HEAD"===r.method)&&(t.isNetworkError||!1);function _(t){return 100*Math.pow(2,t)+100*Math.random()}let q=(t={})=>(t=>{let e=t.maxRetries||5,r=t.retryDelay||_,n=t.shouldRetry;return{onError:(t,i)=>{var s;let o=i.options,a=o.maxRetries||e,u=o.retryDelay||r,l=o.shouldRetry||n,h=o.attemptNumber||0;if(null!==(s=o.body)&&"object"==typeof s&&"function"==typeof s.pipe||!l(t,h,o)||h>=a)return t;let c=Object.assign({},i,{options:Object.assign({},o,{attemptNumber:h+1})});return setTimeout(()=>i.channels.request.publish(c),u(h)),null}}})({shouldRetry:L,...t});q.shouldRetry=L;var F=function(t,e){return(F=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)};function B(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}F(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}function U(t,e){var r,n,i,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},o=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return o.next=a(0),o.throw=a(1),o.return=a(2),"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(a){return function(u){var l=[a,u];if(r)throw TypeError("Generator is already executing.");for(;o&&(o=0,l[0]&&(s=0)),s;)try{if(r=1,n&&(i=2&l[0]?n.return:l[0]?n.throw||((i=n.return)&&i.call(n),0):n.next)&&!(i=i.call(n,l[1])).done)return i;switch(n=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return s.label++,{value:l[1],done:!1};case 5:s.label++,n=l[1],l=[0];continue;case 7:l=s.ops.pop(),s.trys.pop();continue;default:if(!(i=(i=s.trys).length>0&&i[i.length-1])&&(6===l[0]||2===l[0])){s=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){s.label=l[1];break}if(6===l[0]&&s.label<i[1]){s.label=i[1],i=l;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(l);break}i[2]&&s.ops.pop(),s.trys.pop();continue}l=e.call(t,s)}catch(t){l=[6,t],n=0}finally{r=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}}}function $(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function N(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,i,s=r.call(t),o=[];try{for(;(void 0===e||e-- >0)&&!(n=s.next()).done;)o.push(n.value)}catch(t){i={error:t}}finally{try{n&&!n.done&&(r=s.return)&&r.call(s)}finally{if(i)throw i.error}}return o}function z(t,e,r){if(r||2==arguments.length)for(var n,i=0,s=e.length;i<s;i++)!n&&i in e||(n||(n=Array.prototype.slice.call(e,0,i)),n[i]=e[i]);return t.concat(n||Array.prototype.slice.call(e))}function W(t){return this instanceof W?(this.v=t,this):new W(t)}function H(t){return"function"==typeof t}function Y(t){var e=t(function(t){Error.call(t),t.stack=Error().stack});return e.prototype=Object.create(Error.prototype),e.prototype.constructor=e,e}Object.create,Object.create,"function"==typeof SuppressedError&&SuppressedError;var X=Y(function(t){return function(e){t(this),this.message=e?e.length+" errors occurred during unsubscription:\n"+e.map(function(t,e){return e+1+") "+t.toString()}).join("\n  "):"",this.name="UnsubscriptionError",this.errors=e}});function K(t,e){if(t){var r=t.indexOf(e);0<=r&&t.splice(r,1)}}var G=function(){var t;function e(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}return e.prototype.unsubscribe=function(){if(!this.closed){this.closed=!0;var t,e,r,n,i,s=this._parentage;if(s)if(this._parentage=null,Array.isArray(s))try{for(var o=$(s),a=o.next();!a.done;a=o.next())a.value.remove(this)}catch(e){t={error:e}}finally{try{a&&!a.done&&(e=o.return)&&e.call(o)}finally{if(t)throw t.error}}else s.remove(this);var u=this.initialTeardown;if(H(u))try{u()}catch(t){i=t instanceof X?t.errors:[t]}var l=this._finalizers;if(l){this._finalizers=null;try{for(var h=$(l),c=h.next();!c.done;c=h.next()){var f=c.value;try{Q(f)}catch(t){i=null!=i?i:[],t instanceof X?i=z(z([],N(i)),N(t.errors)):i.push(t)}}}catch(t){r={error:t}}finally{try{c&&!c.done&&(n=h.return)&&n.call(h)}finally{if(r)throw r.error}}}if(i)throw new X(i)}},e.prototype.add=function(t){var r;if(t&&t!==this)if(this.closed)Q(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=null!=(r=this._finalizers)?r:[]).push(t)}},e.prototype._hasParent=function(t){var e=this._parentage;return e===t||Array.isArray(e)&&e.includes(t)},e.prototype._addParent=function(t){var e=this._parentage;this._parentage=Array.isArray(e)?(e.push(t),e):e?[e,t]:t},e.prototype._removeParent=function(t){var e=this._parentage;e===t?this._parentage=null:Array.isArray(e)&&K(e,t)},e.prototype.remove=function(t){var r=this._finalizers;r&&K(r,t),t instanceof e&&t._removeParent(this)},(t=new e).closed=!0,e.EMPTY=t,e}(),Z=G.EMPTY;function J(t){return t instanceof G||t&&"closed"in t&&H(t.remove)&&H(t.add)&&H(t.unsubscribe)}function Q(t){H(t)?t():t.unsubscribe()}var tt={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1},te={setTimeout:function(t,e){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];var i=te.delegate;return(null==i?void 0:i.setTimeout)?i.setTimeout.apply(i,z([t,e],N(r))):setTimeout.apply(void 0,z([t,e],N(r)))},clearTimeout:function(t){var e=te.delegate;return((null==e?void 0:e.clearTimeout)||clearTimeout)(t)},delegate:void 0};function tr(t){te.setTimeout(function(){var e=tt.onUnhandledError;if(e)e(t);else throw t})}function tn(){}var ti=ts("C",void 0,void 0);function ts(t,e,r){return{kind:t,value:e,error:r}}var to=null;function ta(t){if(tt.useDeprecatedSynchronousErrorHandling){var e=!to;if(e&&(to={errorThrown:!1,error:null}),t(),e){var r=to,n=r.errorThrown,i=r.error;if(to=null,n)throw i}}else t()}var tu=function(t){function e(e){var r=t.call(this)||this;return r.isStopped=!1,e?(r.destination=e,J(e)&&e.add(r)):r.destination=tm,r}return B(e,t),e.create=function(t,e,r){return new tf(t,e,r)},e.prototype.next=function(t){this.isStopped?td(ts("N",t,void 0),this):this._next(t)},e.prototype.error=function(t){this.isStopped?td(ts("E",void 0,t),this):(this.isStopped=!0,this._error(t))},e.prototype.complete=function(){this.isStopped?td(ti,this):(this.isStopped=!0,this._complete())},e.prototype.unsubscribe=function(){this.closed||(this.isStopped=!0,t.prototype.unsubscribe.call(this),this.destination=null)},e.prototype._next=function(t){this.destination.next(t)},e.prototype._error=function(t){try{this.destination.error(t)}finally{this.unsubscribe()}},e.prototype._complete=function(){try{this.destination.complete()}finally{this.unsubscribe()}},e}(G),tl=Function.prototype.bind;function th(t,e){return tl.call(t,e)}var tc=function(){function t(t){this.partialObserver=t}return t.prototype.next=function(t){var e=this.partialObserver;if(e.next)try{e.next(t)}catch(t){tp(t)}},t.prototype.error=function(t){var e=this.partialObserver;if(e.error)try{e.error(t)}catch(t){tp(t)}else tp(t)},t.prototype.complete=function(){var t=this.partialObserver;if(t.complete)try{t.complete()}catch(t){tp(t)}},t}(),tf=function(t){function e(e,r,n){var i,s,o=t.call(this)||this;return H(e)||!e?i={next:null!=e?e:void 0,error:null!=r?r:void 0,complete:null!=n?n:void 0}:o&&tt.useDeprecatedNextContext?((s=Object.create(e)).unsubscribe=function(){return o.unsubscribe()},i={next:e.next&&th(e.next,s),error:e.error&&th(e.error,s),complete:e.complete&&th(e.complete,s)}):i=e,o.destination=new tc(i),o}return B(e,t),e}(tu);function tp(t){if(tt.useDeprecatedSynchronousErrorHandling)tt.useDeprecatedSynchronousErrorHandling&&to&&(to.errorThrown=!0,to.error=t);else tr(t)}function td(t,e){var r=tt.onStoppedNotification;r&&te.setTimeout(function(){return r(t,e)})}var tm={closed:!0,next:tn,error:function(t){throw t},complete:tn},ty="function"==typeof Symbol&&Symbol.observable||"@@observable";function tg(t){return t}function tv(t){return 0===t.length?tg:1===t.length?t[0]:function(e){return t.reduce(function(t,e){return e(t)},e)}}var tb=function(){function t(t){t&&(this._subscribe=t)}return t.prototype.lift=function(e){var r=new t;return r.source=this,r.operator=e,r},t.prototype.subscribe=function(t,e,r){var n=this,i=!function(t){return t&&t instanceof tu||t&&H(t.next)&&H(t.error)&&H(t.complete)&&J(t)}(t)?new tf(t,e,r):t;return ta(function(){var t=n.operator,e=n.source;i.add(t?t.call(i,e):e?n._subscribe(i):n._trySubscribe(i))}),i},t.prototype._trySubscribe=function(t){try{return this._subscribe(t)}catch(e){t.error(e)}},t.prototype.forEach=function(t,e){var r=this;return new(e=tw(e))(function(e,n){var i=new tf({next:function(e){try{t(e)}catch(t){n(t),i.unsubscribe()}},error:n,complete:e});r.subscribe(i)})},t.prototype._subscribe=function(t){var e;return null==(e=this.source)?void 0:e.subscribe(t)},t.prototype[ty]=function(){return this},t.prototype.pipe=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return tv(t)(this)},t.prototype.toPromise=function(t){var e=this;return new(t=tw(t))(function(t,r){var n;e.subscribe(function(t){return n=t},function(t){return r(t)},function(){return t(n)})})},t.create=function(e){return new t(e)},t}();function tw(t){var e;return null!=(e=null!=t?t:tt.Promise)?e:Promise}var tx=function(t){return t&&"number"==typeof t.length&&"function"!=typeof t};function tE(t){return H(null==t?void 0:t.then)}function tT(t){return Symbol.asyncIterator&&H(null==t?void 0:t[Symbol.asyncIterator])}function tC(t){return TypeError("You provided "+(null!==t&&"object"==typeof t?"an invalid object":"'"+t+"'")+" where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.")}var tA="function"==typeof Symbol&&Symbol.iterator?Symbol.iterator:"@@iterator";function tS(t){return H(null==t?void 0:t[tA])}function tP(t){return function(t,e,r){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var n,i=r.apply(t,e||[]),s=[];return n=Object.create(("function"==typeof AsyncIterator?AsyncIterator:Object).prototype),o("next"),o("throw"),o("return",function(t){return function(e){return Promise.resolve(e).then(t,l)}}),n[Symbol.asyncIterator]=function(){return this},n;function o(t,e){i[t]&&(n[t]=function(e){return new Promise(function(r,n){s.push([t,e,r,n])>1||a(t,e)})},e&&(n[t]=e(n[t])))}function a(t,e){try{var r;(r=i[t](e)).value instanceof W?Promise.resolve(r.value.v).then(u,l):h(s[0][2],r)}catch(t){h(s[0][3],t)}}function u(t){a("next",t)}function l(t){a("throw",t)}function h(t,e){t(e),s.shift(),s.length&&a(s[0][0],s[0][1])}}(this,arguments,function(){var e,r,n;return U(this,function(i){switch(i.label){case 0:e=t.getReader(),i.label=1;case 1:i.trys.push([1,,9,10]),i.label=2;case 2:return[4,W(e.read())];case 3:if(n=(r=i.sent()).value,!r.done)return[3,5];return[4,W(void 0)];case 4:return[2,i.sent()];case 5:return[4,W(n)];case 6:return[4,i.sent()];case 7:return i.sent(),[3,2];case 8:return[3,10];case 9:return e.releaseLock(),[7];case 10:return[2]}})})}function tR(t){return H(null==t?void 0:t.getReader)}function tM(t){if(t instanceof tb)return t;if(null!=t){var e,r,n,i;if(H(t[ty])){return e=t,new tb(function(t){var r=e[ty]();if(H(r.subscribe))return r.subscribe(t);throw TypeError("Provided object does not correctly implement Symbol.observable")})}if(tx(t)){return r=t,new tb(function(t){for(var e=0;e<r.length&&!t.closed;e++)t.next(r[e]);t.complete()})}if(tE(t)){return n=t,new tb(function(t){n.then(function(e){t.closed||(t.next(e),t.complete())},function(e){return t.error(e)}).then(null,tr)})}if(tT(t))return tj(t);if(tS(t)){return i=t,new tb(function(t){var e,r;try{for(var n=$(i),s=n.next();!s.done;s=n.next()){var o=s.value;if(t.next(o),t.closed)return}}catch(t){e={error:t}}finally{try{s&&!s.done&&(r=n.return)&&r.call(n)}finally{if(e)throw e.error}}t.complete()})}if(tR(t))return tj(tP(t))}throw tC(t)}function tj(t){return new tb(function(e){(function(t,e){var r,n,i,s,o,a,u,l;return o=this,a=void 0,u=void 0,l=function(){var o;return U(this,function(a){switch(a.label){case 0:a.trys.push([0,5,6,11]),r=function(t){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var e,r=t[Symbol.asyncIterator];return r?r.call(t):(t=$(t),e={},n("next"),n("throw"),n("return"),e[Symbol.asyncIterator]=function(){return this},e);function n(r){e[r]=t[r]&&function(e){return new Promise(function(n,i){var s,o,a;s=n,o=i,a=(e=t[r](e)).done,Promise.resolve(e.value).then(function(t){s({value:t,done:a})},o)})}}}(t),a.label=1;case 1:return[4,r.next()];case 2:if((n=a.sent()).done)return[3,4];if(o=n.value,e.next(o),e.closed)return[2];a.label=3;case 3:return[3,1];case 4:return[3,11];case 5:return i={error:a.sent()},[3,11];case 6:if(a.trys.push([6,,9,10]),!(n&&!n.done&&(s=r.return)))return[3,8];return[4,s.call(r)];case 7:a.sent(),a.label=8;case 8:return[3,10];case 9:if(i)throw i.error;return[7];case 10:return[7];case 11:return e.complete(),[2]}})},new(u||(u=Promise))(function(t,e){function r(t){try{i(l.next(t))}catch(t){e(t)}}function n(t){try{i(l.throw(t))}catch(t){e(t)}}function i(e){var i;e.done?t(e.value):((i=e.value)instanceof u?i:new u(function(t){t(i)})).then(r,n)}i((l=l.apply(o,a||[])).next())})})(t,e).catch(function(t){return e.error(t)})})}function tO(t){return new tb(function(e){tM(t()).subscribe(e)})}function tI(t){return t[t.length-1]}function tk(t){var e;return(e=tI(t))&&H(e.schedule)?t.pop():void 0}function tD(t,e,r,n,i){void 0===n&&(n=0),void 0===i&&(i=!1);var s=e.schedule(function(){r(),i?t.add(this.schedule(null,n)):this.unsubscribe()},n);if(t.add(s),!i)return s}function tV(t){return function(e){if(H(null==e?void 0:e.lift))return e.lift(function(e){try{return t(e,this)}catch(t){this.error(t)}});throw TypeError("Unable to lift unknown Observable type")}}function tL(t,e,r,n,i){return new t_(t,e,r,n,i)}var t_=function(t){function e(e,r,n,i,s,o){var a=t.call(this,e)||this;return a.onFinalize=s,a.shouldUnsubscribe=o,a._next=r?function(t){try{r(t)}catch(t){e.error(t)}}:t.prototype._next,a._error=i?function(t){try{i(t)}catch(t){e.error(t)}finally{this.unsubscribe()}}:t.prototype._error,a._complete=n?function(){try{n()}catch(t){e.error(t)}finally{this.unsubscribe()}}:t.prototype._complete,a}return B(e,t),e.prototype.unsubscribe=function(){var e;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){var r=this.closed;t.prototype.unsubscribe.call(this),r||null==(e=this.onFinalize)||e.call(this)}},e}(tu);function tq(t,e){return void 0===e&&(e=0),tV(function(r,n){r.subscribe(tL(n,function(r){return tD(n,t,function(){return n.next(r)},e)},function(){return tD(n,t,function(){return n.complete()},e)},function(r){return tD(n,t,function(){return n.error(r)},e)}))})}function tF(t,e){return void 0===e&&(e=0),tV(function(r,n){n.add(t.schedule(function(){return r.subscribe(n)},e))})}function tB(t,e){if(!t)throw Error("Iterable cannot be null");return new tb(function(r){tD(r,e,function(){var n=t[Symbol.asyncIterator]();tD(r,e,function(){n.next().then(function(t){t.done?r.complete():r.next(t.value)})},0,!0)})})}function tU(t,e){return e?function(t,e){if(null!=t){if(H(t[ty]))return tM(t).pipe(tF(e),tq(e));if(tx(t))return new tb(function(r){var n=0;return e.schedule(function(){n===t.length?r.complete():(r.next(t[n++]),r.closed||this.schedule())})});if(tE(t))return tM(t).pipe(tF(e),tq(e));if(tT(t))return tB(t,e);if(tS(t))return new tb(function(r){var n;return tD(r,e,function(){n=t[tA](),tD(r,e,function(){var t,e,i;try{e=(t=n.next()).value,i=t.done}catch(t){r.error(t);return}i?r.complete():r.next(e)},0,!0)}),function(){return H(null==n?void 0:n.return)&&n.return()}});if(tR(t))return tB(tP(t),e)}throw tC(t)}(t,e):tM(t)}function t$(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var r=tk(t);return tU(t,r)}function tN(t,e){return tV(function(r,n){var i=0;r.subscribe(tL(n,function(r){n.next(t.call(e,r,i++))}))})}function tz(t,e,r){return(void 0===r&&(r=1/0),H(e))?tz(function(r,n){return tN(function(t,i){return e(r,t,n,i)})(tM(t(r,n)))},r):("number"==typeof e&&(r=e),tV(function(e,n){var i,s,o,a,u,l,h,c,f;return i=r,o=[],a=0,u=0,l=!1,h=function(){!l||o.length||a||n.complete()},c=function(t){return a<i?f(t):o.push(t)},f=function(e){a++;var r=!1;tM(t(e,u++)).subscribe(tL(n,function(t){s?c(t):n.next(t)},function(){r=!0},void 0,function(){if(r)try{for(a--;o.length&&a<i;)!function(){var t=o.shift();f(t)}();h()}catch(t){n.error(t)}}))},e.subscribe(tL(n,c,function(){l=!0,h()})),function(){}}))}var tW=Y(function(t){return function(){t(this),this.name="EmptyError",this.message="no elements in sequence"}});function tH(t,e){var r="object"==typeof e;return new Promise(function(n,i){var s,o=!1;t.subscribe({next:function(t){s=t,o=!0},error:i,complete:function(){o?n(s):r?n(e.defaultValue):i(new tW)}})})}var tY=Y(function(t){return function(){t(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"}}),tX=function(t){function e(){var e=t.call(this)||this;return e.closed=!1,e.currentObservers=null,e.observers=[],e.isStopped=!1,e.hasError=!1,e.thrownError=null,e}return B(e,t),e.prototype.lift=function(t){var e=new tK(this,this);return e.operator=t,e},e.prototype._throwIfClosed=function(){if(this.closed)throw new tY},e.prototype.next=function(t){var e=this;ta(function(){var r,n;if(e._throwIfClosed(),!e.isStopped){e.currentObservers||(e.currentObservers=Array.from(e.observers));try{for(var i=$(e.currentObservers),s=i.next();!s.done;s=i.next())s.value.next(t)}catch(t){r={error:t}}finally{try{s&&!s.done&&(n=i.return)&&n.call(i)}finally{if(r)throw r.error}}}})},e.prototype.error=function(t){var e=this;ta(function(){if(e._throwIfClosed(),!e.isStopped){e.hasError=e.isStopped=!0,e.thrownError=t;for(var r=e.observers;r.length;)r.shift().error(t)}})},e.prototype.complete=function(){var t=this;ta(function(){if(t._throwIfClosed(),!t.isStopped){t.isStopped=!0;for(var e=t.observers;e.length;)e.shift().complete()}})},e.prototype.unsubscribe=function(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null},Object.defineProperty(e.prototype,"observed",{get:function(){var t;return(null==(t=this.observers)?void 0:t.length)>0},enumerable:!1,configurable:!0}),e.prototype._trySubscribe=function(e){return this._throwIfClosed(),t.prototype._trySubscribe.call(this,e)},e.prototype._subscribe=function(t){return this._throwIfClosed(),this._checkFinalizedStatuses(t),this._innerSubscribe(t)},e.prototype._innerSubscribe=function(t){var e=this,r=this.hasError,n=this.isStopped,i=this.observers;return r||n?Z:(this.currentObservers=null,i.push(t),new G(function(){e.currentObservers=null,K(i,t)}))},e.prototype._checkFinalizedStatuses=function(t){var e=this.hasError,r=this.thrownError,n=this.isStopped;e?t.error(r):n&&t.complete()},e.prototype.asObservable=function(){var t=new tb;return t.source=this,t},e.create=function(t,e){return new tK(t,e)},e}(tb),tK=function(t){function e(e,r){var n=t.call(this)||this;return n.destination=e,n.source=r,n}return B(e,t),e.prototype.next=function(t){var e,r;null==(r=null==(e=this.destination)?void 0:e.next)||r.call(e,t)},e.prototype.error=function(t){var e,r;null==(r=null==(e=this.destination)?void 0:e.error)||r.call(e,t)},e.prototype.complete=function(){var t,e;null==(e=null==(t=this.destination)?void 0:t.complete)||e.call(t)},e.prototype._subscribe=function(t){var e,r;return null!=(r=null==(e=this.source)?void 0:e.subscribe(t))?r:Z},e}(tX),tG={now:function(){return(tG.delegate||Date).now()},delegate:void 0},tZ=function(t){function e(e,r,n){void 0===e&&(e=1/0),void 0===r&&(r=1/0),void 0===n&&(n=tG);var i=t.call(this)||this;return i._bufferSize=e,i._windowTime=r,i._timestampProvider=n,i._buffer=[],i._infiniteTimeWindow=!0,i._infiniteTimeWindow=r===1/0,i._bufferSize=Math.max(1,e),i._windowTime=Math.max(1,r),i}return B(e,t),e.prototype.next=function(e){var r=this.isStopped,n=this._buffer,i=this._infiniteTimeWindow,s=this._timestampProvider,o=this._windowTime;!r&&(n.push(e),i||n.push(s.now()+o)),this._trimBuffer(),t.prototype.next.call(this,e)},e.prototype._subscribe=function(t){this._throwIfClosed(),this._trimBuffer();for(var e=this._innerSubscribe(t),r=this._infiniteTimeWindow,n=this._buffer.slice(),i=0;i<n.length&&!t.closed;i+=r?1:2)t.next(n[i]);return this._checkFinalizedStatuses(t),e},e.prototype._trimBuffer=function(){var t=this._bufferSize,e=this._timestampProvider,r=this._buffer,n=this._infiniteTimeWindow,i=(n?1:2)*t;if(t<1/0&&i<r.length&&r.splice(0,r.length-i),!n){for(var s=e.now(),o=0,a=1;a<r.length&&r[a]<=s;a+=2)o=a;o&&r.splice(0,o+1)}},e}(tX);function tJ(t){void 0===t&&(t={});var e=t.connector,r=void 0===e?function(){return new tX}:e,n=t.resetOnError,i=void 0===n||n,s=t.resetOnComplete,o=void 0===s||s,a=t.resetOnRefCountZero,u=void 0===a||a;return function(t){var e,n,s,a=0,l=!1,h=!1,c=function(){null==n||n.unsubscribe(),n=void 0},f=function(){c(),e=s=void 0,l=h=!1},p=function(){var t=e;f(),null==t||t.unsubscribe()};return tV(function(t,d){a++,h||l||c();var m=s=null!=s?s:r();d.add(function(){0!=--a||h||l||(n=tQ(p,u))}),m.subscribe(d),!e&&a>0&&(e=new tf({next:function(t){return m.next(t)},error:function(t){h=!0,c(),n=tQ(f,i,t),m.error(t)},complete:function(){l=!0,c(),n=tQ(f,o),m.complete()}}),tM(t).subscribe(e))})(t)}}function tQ(t,e){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];if(!0===e)return void t();if(!1!==e){var i=new tf({next:function(){i.unsubscribe(),t()}});return tM(e.apply(void 0,z([],N(r)))).subscribe(i)}}function t0(t){return tV(function(e,r){var n,i=null,s=!1;i=e.subscribe(tL(r,void 0,void 0,function(o){n=tM(t(o,t0(t)(e))),i?(i.unsubscribe(),i=null,n.subscribe(r)):s=!0})),s&&(i.unsubscribe(),i=null,n.subscribe(r))})}function t1(t){return void 0===t&&(t=1/0),tz(tg,t)}function t2(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return t1(1)(tU(t,tk(t)))}var t3=function(t){function e(e,r){return t.call(this)||this}return B(e,t),e.prototype.schedule=function(t,e){return void 0===e&&(e=0),this},e}(G),t5={setInterval:function(t,e){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];var i=t5.delegate;return(null==i?void 0:i.setInterval)?i.setInterval.apply(i,z([t,e],N(r))):setInterval.apply(void 0,z([t,e],N(r)))},clearInterval:function(t){var e=t5.delegate;return((null==e?void 0:e.clearInterval)||clearInterval)(t)},delegate:void 0},t6=function(t){function e(e,r){var n=t.call(this,e,r)||this;return n.scheduler=e,n.work=r,n.pending=!1,n}return B(e,t),e.prototype.schedule=function(t,e){if(void 0===e&&(e=0),this.closed)return this;this.state=t;var r,n=this.id,i=this.scheduler;return null!=n&&(this.id=this.recycleAsyncId(i,n,e)),this.pending=!0,this.delay=e,this.id=null!=(r=this.id)?r:this.requestAsyncId(i,this.id,e),this},e.prototype.requestAsyncId=function(t,e,r){return void 0===r&&(r=0),t5.setInterval(t.flush.bind(t,this),r)},e.prototype.recycleAsyncId=function(t,e,r){if(void 0===r&&(r=0),null!=r&&this.delay===r&&!1===this.pending)return e;null!=e&&t5.clearInterval(e)},e.prototype.execute=function(t,e){if(this.closed)return Error("executing a cancelled action");this.pending=!1;var r=this._execute(t,e);if(r)return r;!1===this.pending&&null!=this.id&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))},e.prototype._execute=function(t,e){var r,n=!1;try{this.work(t)}catch(t){n=!0,r=t||Error("Scheduled action threw falsy error")}if(n)return this.unsubscribe(),r},e.prototype.unsubscribe=function(){if(!this.closed){var e=this.id,r=this.scheduler,n=r.actions;this.work=this.state=this.scheduler=null,this.pending=!1,K(n,this),null!=e&&(this.id=this.recycleAsyncId(r,e,null)),this.delay=null,t.prototype.unsubscribe.call(this)}},e}(t3),t8=function(){function t(e,r){void 0===r&&(r=t.now),this.schedulerActionCtor=e,this.now=r}return t.prototype.schedule=function(t,e,r){return void 0===e&&(e=0),new this.schedulerActionCtor(this,t).schedule(r,e)},t.now=tG.now,t}(),t4=new(function(t){function e(e,r){void 0===r&&(r=t8.now);var n=t.call(this,e,r)||this;return n.actions=[],n._active=!1,n}return B(e,t),e.prototype.flush=function(t){var e,r=this.actions;if(this._active)return void r.push(t);this._active=!0;do if(e=t.execute(t.state,t.delay))break;while(t=r.shift());if(this._active=!1,e){for(;t=r.shift();)t.unsubscribe();throw e}},e}(t8))(t6);function t9(t,e){var r=H(t)?t:function(){return t},n=function(t){return t.error(r())};return new tb(e?function(t){return e.schedule(n,0,t)}:n)}function t7(t){return tV(function(e,r){try{e.subscribe(r)}finally{r.add(t)}})}var et=new tb(function(t){return t.complete()});function ee(t,e){var r="object"==typeof e;return new Promise(function(n,i){var s=new tf({next:function(t){n(t),s.unsubscribe()},error:i,complete:function(){r?n(e.defaultValue):i(new tW)}});t.subscribe(s)})}var er=r(1501),en=Array.isArray,ei=Array.isArray;function es(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var r=H(tI(t))?t.pop():void 0;return r?function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return tv(t)}(es.apply(void 0,z([],N(t))),tN(function(t){return ei(t)?r.apply(void 0,z([],N(t))):r(t)})):tV(function(e,r){var n,i,s;(n=z([e],N(1===t.length&&en(t[0])?t[0]:t)),void 0===s&&(s=tg),function(t){var e,r,o;e=void 0,r=function(){for(var e=n.length,r=Array(e),o=e,a=e,u=function(e){var u,l,h;u=i,l=function(){var u=tU(n[e],i),l=!1;u.subscribe(tL(t,function(n){r[e]=n,!l&&(l=!0,a--),a||t.next(s(r.slice()))},function(){--o||t.complete()}))},h=t,u?tD(h,u,l):l()},l=0;l<e;l++)u(l)},o=t,e?tD(o,e,r):r()})(r)})}function eo(t,e){return tV(function(r,n){var i=0;r.subscribe(tL(n,function(r){return t.call(e,r,i++)&&n.next(r)}))})}let ea=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,eu=/_key\s*==\s*['"](.*)['"]/,el=/^\d*:\d*$/;function eh(t){return"string"==typeof t?eu.test(t.trim()):"object"==typeof t&&"_key"in t}function ec(t){var e;return"number"==typeof(e=t)||"string"==typeof e&&/^\[\d+\]$/.test(e)?Number(t.replace(/[^\d]/g,"")):eh(t)?{_key:t.match(eu)[1]}:!function(t){if("string"==typeof t&&el.test(t))return!0;if(!Array.isArray(t)||2!==t.length)return!1;let[e,r]=t;return("number"==typeof e||""===e)&&("number"==typeof r||""===r)}(t)?t:function(t){let[e,r]=t.split(":").map(t=>""===t?t:Number(t));return[e,r]}(t)}let ef="drafts.",ep="versions.";function ed(t){return t.startsWith(ef)}function em(t){return t.startsWith(ep)}function ey(t,e){if("drafts"===e||"published"===e)throw Error('Version can not be "published" or "drafts"');return`${ep}${e}.${ev(t)}`}function eg(t){if(!em(t))return;let[e,r,...n]=t.split(".");return r}function ev(t){return em(t)?t.split(".").slice(2).join("."):ed(t)?t.slice(ef.length):t}let eb=t=>crypto.getRandomValues(new Uint8Array(t)),ew=(t,e,r)=>{let n=(2<<Math.log(t.length-1)/Math.LN2)-1,i=-~(1.6*n*e/t.length);return (s=e)=>{let o="";for(;;){let e=r(i),a=0|i;for(;a--;)if((o+=t[e[a]&n]||"").length===s)return o}}},ex=/\r\n|[\n\r\u2028\u2029]/;function eE(t,e){let r=0;for(let n=0;n<e.length;n++){let i=e[n].length+1;if(r+i>t)return{line:n+1,column:t-r};r+=i}return{line:e.length,column:e[e.length-1]?.length??0}}class eT extends Error{response;statusCode=400;responseBody;details;constructor(t,e){let r=eA(t,e);super(r.message),Object.assign(this,r)}}class eC extends Error{response;statusCode=500;responseBody;details;constructor(t){let e=eA(t);super(e.message),Object.assign(this,e)}}function eA(t,e){var r,n,i;let s=t.body,o={response:t,statusCode:t.statusCode,responseBody:(r=s,-1!==(t.headers["content-type"]||"").toLowerCase().indexOf("application/json")?JSON.stringify(r,null,2):r),message:"",details:void 0};if(!(0,er.u4)(s))return o.message=eR(t,s),o;let a=s.error;if("string"==typeof a&&"string"==typeof s.message)return o.message=`${a} - ${s.message}`,o;if("object"!=typeof a||null===a)return"string"==typeof a?o.message=a:"string"==typeof s.message?o.message=s.message:o.message=eR(t,s),o;if("type"in(n=a)&&"mutationError"===n.type&&"description"in n&&"string"==typeof n.description||"type"in(i=a)&&"actionError"===i.type&&"description"in i&&"string"==typeof i.description){let t=a.items||[],e=t.slice(0,5).map(t=>t.error?.description).filter(Boolean),r=e.length?`:
- ${e.join(`
- `)}`:"";return t.length>5&&(r+=`
...and ${t.length-5} more`),o.message=`${a.description}${r}`,o.details=s.error,o}return eS(a)?(o.message=eP(a,e?.options?.query?.tag),o.details=s.error):"description"in a&&"string"==typeof a.description?(o.message=a.description,o.details=a):o.message=eR(t,s),o}function eS(t){return(0,er.u4)(t)&&"queryParseError"===t.type&&"string"==typeof t.query&&"number"==typeof t.start&&"number"==typeof t.end}function eP(t,e){let{query:r,start:n,end:i,description:s}=t;if(!r||typeof n>"u")return`GROQ query parse error: ${s}`;let o=e?`

Tag: ${e}`:"";return`GROQ query parse error:
${function(t,e,r){let n=t.split(ex),{start:i,end:s,markerLines:o}=function(t,e){let r={...t.start},n={...r,...t.end},i=r.line??-1,s=r.column??0,o=n.line,a=n.column,u=Math.max(i-3,0),l=Math.min(e.length,o+3);-1===i&&(u=0),-1===o&&(l=e.length);let h=o-i,c={};if(h)for(let t=0;t<=h;t++){let r=t+i;if(s)if(0===t){let t=e[r-1].length;c[r]=[s,t-s+1]}else if(t===h)c[r]=[0,a];else{let n=e[r-t].length;c[r]=[0,n]}else c[r]=!0}else s===a?s?c[i]=[s,0]:c[i]=!0:c[i]=[s,a-s];return{start:u,end:l,markerLines:c}}({start:eE(e.start,n),end:e.end?eE(e.end,n):void 0},n),a=`${s}`.length;return t.split(ex,s).slice(i,s).map((t,e)=>{let n=i+1+e,s=` ${` ${n}`.slice(-a)} |`,u=o[n],l=!o[n+1];if(!u)return` ${s}${t.length>0?` ${t}`:""}`;let h="";if(Array.isArray(u)){let e=t.slice(0,Math.max(u[0]-1,0)).replace(/[^\t]/g," "),n=u[1]||1;h=[`
 `,s.replace(/\d/g," ")," ",e,"^".repeat(n)].join(""),l&&r&&(h+=" "+r)}return[">",s,t.length>0?` ${t}`:"",h].join("")}).join(`
`)}(r,{start:n,end:i},s)}${o}`}function eR(t,e){var r,n;let i="string"==typeof e?` (${n=100,(r=e).length>100?`${r.slice(0,n)}\u2026`:r})`:"",s=t.statusMessage?` ${t.statusMessage}`:"";return`${t.method}-request to ${t.url} resulted in HTTP ${t.statusCode}${s}${i}`}class eM extends Error{projectId;addOriginUrl;constructor({projectId:t}){super("CorsOriginError"),this.name="CorsOriginError",this.projectId=t;let e=new URL(`https://sanity.io/manage/project/${t}/api`);if("u">typeof location){let{origin:t}=location;e.searchParams.set("cors","add"),e.searchParams.set("origin",t),this.addOriginUrl=e,this.message=`The current origin is not allowed to connect to the Live Content API. Add it here: ${e}`}else this.message=`The current origin is not allowed to connect to the Live Content API. Change your configuration here: ${e}`}}let ej={onResponse:(t,e)=>{if(t.statusCode>=500)throw new eC(t);if(t.statusCode>=400)throw new eT(t,e);return t}};function eO(t){return b([q({shouldRetry:eI}),...t,function(){let t={};return{onResponse:e=>{let r=e.headers["x-sanity-warning"];for(let e of Array.isArray(r)?r:[r])!e||t[e]||(t[e]=!0,console.warn(e));return e}}}(),{processOptions:t=>{let e=t.body;return!e||"function"==typeof e.pipe||R(e)||-1===j.indexOf(typeof e)&&!Array.isArray(e)&&!function(t){if(!1===M(t))return!1;let e=t.constructor;if(void 0===e)return!0;let r=e.prototype;return!1!==M(r)&&!1!==r.hasOwnProperty("isPrototypeOf")}(e)?t:Object.assign({},t,{body:JSON.stringify(t.body),headers:Object.assign({},t.headers,{"Content-Type":"application/json"})})}},{onResponse:t=>{let e=t.headers["content-type"]||"",r=-1!==e.indexOf("application/json");return t.body&&e&&r?Object.assign({},t,{body:function(t){try{return JSON.parse(t)}catch(t){throw t.message=`Failed to parsed response body as JSON: ${t.message}`,t}}(t.body)}):t},processOptions:t=>Object.assign({},t,{headers:Object.assign({Accept:"application/json"},t.headers)})},{onRequest:t=>{if("xhr"!==t.adapter)return;let e=t.request,r=t.context;function n(t){return e=>{let n=e.lengthComputable?e.loaded/e.total*100:-1;r.channels.progress.publish({stage:t,percent:n,total:e.total,loaded:e.loaded,lengthComputable:e.lengthComputable})}}"upload"in e&&"onprogress"in e.upload&&(e.upload.onprogress=n("upload")),"onprogress"in e&&(e.onprogress=n("download"))}},ej,function(t={}){let e=t.implementation||I.Observable;if(!e)throw Error("`Observable` is not available in global scope, and no implementation was passed");return{onReturn:(t,r)=>new e(e=>(t.error.subscribe(t=>e.error(t)),t.progress.subscribe(t=>e.next(Object.assign({type:"progress"},t))),t.response.subscribe(t=>{e.next(Object.assign({type:"response"},t)),e.complete()}),t.request.publish(r),()=>t.abort.publish()))}}({implementation:tb})])}function eI(t,e,r){if(0===r.maxRetries)return!1;let n="GET"===r.method||"HEAD"===r.method,i=(r.uri||r.url).startsWith("/data/query"),s=t.response&&(429===t.response.statusCode||502===t.response.statusCode||503===t.response.statusCode);return(!!n||!!i)&&!!s||q.shouldRetry(t,e,r)}function ek(t){return"https://www.sanity.io/help/"+t}let eD=["image","file"],eV=["before","after","replace"],eL=t=>{if(!/^(~[a-z0-9]{1}[-\w]{0,63}|[a-z0-9]{1}[-\w]{0,63})$/.test(t))throw Error("Datasets can only contain lowercase characters, numbers, underscores and dashes, and start with tilde, and be maximum 64 characters")},e_=t=>{if(!/^[-a-z0-9]+$/i.test(t))throw Error("`projectId` can only contain only a-z, 0-9 and dashes")},eq=t=>{if(-1===eD.indexOf(t))throw Error(`Invalid asset type: ${t}. Must be one of ${eD.join(", ")}`)},eF=(t,e)=>{if(null===e||"object"!=typeof e||Array.isArray(e))throw Error(`${t}() takes an object of properties`)},eB=(t,e)=>{if("string"!=typeof e||!/^[a-z0-9_][a-z0-9_.-]{0,127}$/i.test(e)||e.includes(".."))throw Error(`${t}(): "${e}" is not a valid document ID`)},eU=(t,e)=>{if(!e._id)throw Error(`${t}() requires that the document contains an ID ("_id" property)`);eB(t,e._id)},e$=(t,e)=>{if("string"!=typeof e)throw Error(`\`${t}()\`: \`${e}\` is not a valid document type`)},eN=(t,e)=>{if(!e._type)throw Error(`\`${t}()\` requires that the document contains a type (\`_type\` property)`);e$(t,e._type)},ez=(t,e)=>{if(e._id&&e._id!==t)throw Error(`The provided document ID (\`${e._id}\`) does not match the generated version ID (\`${t}\`)`)},eW=(t,e,r)=>{let n="insert(at, selector, items)";if(-1===eV.indexOf(t)){let t=eV.map(t=>`"${t}"`).join(", ");throw Error(`${n} takes an "at"-argument which is one of: ${t}`)}if("string"!=typeof e)throw Error(`${n} takes a "selector"-argument which must be a string`);if(!Array.isArray(r))throw Error(`${n} takes an "items"-argument which must be an array`)},eH=t=>{if(!t.dataset)throw Error("`dataset` must be provided to perform queries");return t.dataset||""},eY=t=>{if("string"!=typeof t||!/^[a-z0-9._-]{1,75}$/i.test(t))throw Error("Tag can only contain alphanumeric characters, underscores, dashes and dots, and be between one and 75 characters long.");return t},eX=t=>{if(!t["~experimental_resource"])throw Error("`resource` must be provided to perform resource queries");let{type:e,id:r}=t["~experimental_resource"];switch(e){case"dataset":if(2!==r.split(".").length)throw Error('Dataset resource ID must be in the format "project.dataset"');return;case"dashboard":case"media-library":case"canvas":return;default:throw Error(`Unsupported resource type: ${e.toString()}`)}},eK=(t,e)=>{if(e["~experimental_resource"])throw Error(`\`${t}\` does not support resource-based operations`)},eG=t=>(function(t){let e=!1,r;return(...n)=>(e||(r=t(...n),e=!0),r)})((...e)=>console.warn(t.join(" "),...e)),eZ=eG(["Because you set `withCredentials` to true, we will override your `useCdn`","setting to be false since (cookie-based) credentials are never set on the CDN"]),eJ=eG(["Since you haven't set a value for `useCdn`, we will deliver content using our","global, edge-cached API-CDN. If you wish to have content delivered faster, set","`useCdn: false` to use the Live API. Note: You may incur higher costs using the live API."]),eQ=eG(["The Sanity client is configured with the `perspective` set to `drafts` or `previewDrafts`, which doesn't support the API-CDN.","The Live API will be used instead. Set `useCdn: false` in your configuration to hide this warning."]),e0=eG(["The `previewDrafts` perspective has been renamed to  `drafts` and will be removed in a future API version"]),e1=eG(["You have configured Sanity client to use a token in the browser. This may cause unintentional security issues.",`See ${ek("js-client-browser-token")} for more information and how to hide this warning.`]),e2=eG(["You have configured Sanity client to use a token, but also provided `withCredentials: true`.","This is no longer supported - only token will be used - remove `withCredentials: true`."]),e3=eG(["Using the Sanity client without specifying an API version is deprecated.",`See ${ek("js-client-api-version")}`]),e5=(eG(["The default export of @sanity/client has been deprecated. Use the named export `createClient` instead."]),{apiHost:"https://api.sanity.io",apiVersion:"1",useProjectHostname:!0,stega:{enabled:!1}}),e6=["localhost","127.0.0.1","0.0.0.0"],e8=t=>-1!==e6.indexOf(t);function e4(t){if(Array.isArray(t)&&t.length>1&&t.includes("raw"))throw TypeError('Invalid API perspective value: "raw". The raw-perspective can not be combined with other perspectives')}let e9=(t,e)=>{let r={...e,...t,stega:{..."boolean"==typeof e.stega?{enabled:e.stega}:e.stega||e5.stega,..."boolean"==typeof t.stega?{enabled:t.stega}:t.stega||{}}};r.apiVersion||e3();let n={...e5,...r},i=n.useProjectHostname&&!n["~experimental_resource"];if(typeof Promise>"u"){let t=ek("js-client-promise-polyfill");throw Error(`No native Promise-implementation found, polyfill needed - see ${t}`)}if(i&&!n.projectId)throw Error("Configuration must contain `projectId`");if(n["~experimental_resource"]&&eX(n),"u">typeof n.perspective&&e4(n.perspective),"encodeSourceMap"in n)throw Error("It looks like you're using options meant for '@sanity/preview-kit/client'. 'encodeSourceMap' is not supported in '@sanity/client'. Did you mean 'stega.enabled'?");if("encodeSourceMapAtPath"in n)throw Error("It looks like you're using options meant for '@sanity/preview-kit/client'. 'encodeSourceMapAtPath' is not supported in '@sanity/client'. Did you mean 'stega.filter'?");if("boolean"!=typeof n.stega.enabled)throw Error(`stega.enabled must be a boolean, received ${n.stega.enabled}`);if(n.stega.enabled&&void 0===n.stega.studioUrl)throw Error("stega.studioUrl must be defined when stega.enabled is true");if(n.stega.enabled&&"string"!=typeof n.stega.studioUrl&&"function"!=typeof n.stega.studioUrl)throw Error(`stega.studioUrl must be a string or a function, received ${n.stega.studioUrl}`);let s="u">typeof window&&window.location&&window.location.hostname,o=s&&e8(window.location.hostname),a=!!n.token;n.withCredentials&&a&&(e2(),n.withCredentials=!1),s&&o&&a&&!0!==n.ignoreBrowserTokenWarning?e1():typeof n.useCdn>"u"&&eJ(),i&&e_(n.projectId),n.dataset&&eL(n.dataset),"requestTagPrefix"in n&&(n.requestTagPrefix=n.requestTagPrefix?eY(n.requestTagPrefix).replace(/\.+$/,""):void 0),n.apiVersion=`${n.apiVersion}`.replace(/^v/,""),n.isDefaultApi=n.apiHost===e5.apiHost,!0===n.useCdn&&n.withCredentials&&eZ(),n.useCdn=!1!==n.useCdn&&!n.withCredentials,function(t){if("1"===t||"X"===t)return;let e=new Date(t);if(!(/^\d{4}-\d{2}-\d{2}$/.test(t)&&e instanceof Date&&e.getTime()>0))throw Error("Invalid API version string, expected `1` or date in format `YYYY-MM-DD`")}(n.apiVersion);let u=n.apiHost.split("://",2),l=u[0],h=u[1],c=n.isDefaultApi?"apicdn.sanity.io":h;return i?(n.url=`${l}://${n.projectId}.${h}/v${n.apiVersion}`,n.cdnUrl=`${l}://${n.projectId}.${c}/v${n.apiVersion}`):(n.url=`${n.apiHost}/v${n.apiVersion}`,n.cdnUrl=n.url),n};class e7 extends Error{name="ConnectionFailedError"}class rt extends Error{name="DisconnectError";reason;constructor(t,e,r={}){super(t,r),this.reason=e}}class re extends Error{name="ChannelError";data;constructor(t,e){super(t),this.data=e}}class rr extends Error{name="MessageError";data;constructor(t,e,r={}){super(t,r),this.data=e}}class rn extends Error{name="MessageParseError"}let ri=["channelError","disconnect"];function rs(t,e){return tO(()=>{let e=t();return e&&(e instanceof tb||H(e.lift)&&H(e.subscribe))?e:t$(e)}).pipe(tz(t=>{var r,n;return r=t,n=e,new tb(t=>{let e=n.includes("open"),i=n.includes("reconnect");function s(e){if("data"in e){let[r,n]=ro(e);t.error(r?new rn("Unable to parse EventSource error message",{cause:n}):new rr((n?.data).message,n));return}r.readyState===r.CLOSED?t.error(new e7("EventSource connection failed")):i&&t.next({type:"reconnect"})}function o(){t.next({type:"open"})}function a(e){let[n,i]=ro(e);if(n)return void t.error(new rn("Unable to parse EventSource message",{cause:n}));if("channelError"===e.type){let e=new URL(r.url).searchParams.get("tag");t.error(new re(function(t,e){let r=t.error;return r?eS(r)?eP(r,e):r.description?r.description:"string"==typeof r?r:JSON.stringify(r,null,2):t.message||"Unknown listener error"}(i?.data,e),i.data));return}if("disconnect"===e.type)return void t.error(new rt(`Server disconnected client: ${i.data?.reason||"unknown error"}`));t.next({type:e.type,id:e.lastEventId,...i.data?{data:i.data}:{}})}r.addEventListener("error",s),e&&r.addEventListener("open",o);let u=[...new Set([...ri,...n])].filter(t=>"error"!==t&&"open"!==t&&"reconnect"!==t);return u.forEach(t=>r.addEventListener(t,a)),()=>{r.removeEventListener("error",s),e&&r.removeEventListener("open",o),u.forEach(t=>r.removeEventListener(t,a)),r.close()}})}))}function ro(t){try{let e="string"==typeof t.data&&JSON.parse(t.data);return[null,{type:t.type,id:t.lastEventId,...!function(t){for(let e in t)return!1;return!0}(e)?{data:e}:{}}]}catch(t){return[t,null]}}function ra(t){if("string"==typeof t)return{id:t};if(Array.isArray(t))return{query:"*[_id in $ids]",params:{ids:t}};if("object"==typeof t&&null!==t&&"query"in t&&"string"==typeof t.query)return"params"in t&&"object"==typeof t.params&&null!==t.params?{query:t.query,params:t.params}:{query:t.query};let e=["* Document ID (<docId>)","* Array of document IDs","* Object containing `query`"].join(`
`);throw Error(`Unknown selection - must be one of:

${e}`)}class ru{selection;operations;constructor(t,e={}){this.selection=t,this.operations=e}set(t){return this._assign("set",t)}setIfMissing(t){return this._assign("setIfMissing",t)}diffMatchPatch(t){return eF("diffMatchPatch",t),this._assign("diffMatchPatch",t)}unset(t){if(!Array.isArray(t))throw Error("unset(attrs) takes an array of attributes to unset, non-array given");return this.operations=Object.assign({},this.operations,{unset:t}),this}inc(t){return this._assign("inc",t)}dec(t){return this._assign("dec",t)}insert(t,e,r){return eW(t,e,r),this._assign("insert",{[t]:e,items:r})}append(t,e){return this.insert("after",`${t}[-1]`,e)}prepend(t,e){return this.insert("before",`${t}[0]`,e)}splice(t,e,r,n){let i=e<0?e-1:e,s=typeof r>"u"||-1===r?-1:Math.max(0,e+r),o=`${t}[${i}:${i<0&&s>=0?"":s}]`;return this.insert("replace",o,n||[])}ifRevisionId(t){return this.operations.ifRevisionID=t,this}serialize(){return{...ra(this.selection),...this.operations}}toJSON(){return this.serialize()}reset(){return this.operations={},this}_assign(t,e,r=!0){return eF(t,e),this.operations=Object.assign({},this.operations,{[t]:Object.assign({},r&&this.operations[t]||{},e)}),this}_set(t,e){return this._assign(t,e,!1)}}class rl extends ru{#a;constructor(t,e,r){super(t,e),this.#a=r}clone(){return new rl(this.selection,{...this.operations},this.#a)}commit(t){if(!this.#a)throw Error("No `client` passed to patch, either provide one or pass the patch to a clients `mutate()` method");let e=Object.assign({returnFirst:"string"==typeof this.selection,returnDocuments:!0},t);return this.#a.mutate({patch:this.serialize()},e)}}class rh extends ru{#a;constructor(t,e,r){super(t,e),this.#a=r}clone(){return new rh(this.selection,{...this.operations},this.#a)}commit(t){if(!this.#a)throw Error("No `client` passed to patch, either provide one or pass the patch to a clients `mutate()` method");let e=Object.assign({returnFirst:"string"==typeof this.selection,returnDocuments:!0},t);return this.#a.mutate({patch:this.serialize()},e)}}let rc={returnDocuments:!1};class rf{operations;trxId;constructor(t=[],e){this.operations=t,this.trxId=e}create(t){return eF("create",t),this._add({create:t})}createIfNotExists(t){let e="createIfNotExists";return eF(e,t),eU(e,t),this._add({[e]:t})}createOrReplace(t){let e="createOrReplace";return eF(e,t),eU(e,t),this._add({[e]:t})}delete(t){return eB("delete",t),this._add({delete:{id:t}})}transactionId(t){return t?(this.trxId=t,this):this.trxId}serialize(){return[...this.operations]}toJSON(){return this.serialize()}reset(){return this.operations=[],this}_add(t){return this.operations.push(t),this}}class rp extends rf{#a;constructor(t,e,r){super(t,r),this.#a=e}clone(){return new rp([...this.operations],this.#a,this.trxId)}commit(t){if(!this.#a)throw Error("No `client` passed to transaction, either provide one or pass the transaction to a clients `mutate()` method");return this.#a.mutate(this.serialize(),Object.assign({transactionId:this.trxId},rc,t||{}))}patch(t,e){let r="function"==typeof e,n="string"!=typeof t&&t instanceof rh,i="object"==typeof t&&("query"in t||"id"in t);if(n)return this._add({patch:t.serialize()});if(r){let r=e(new rh(t,{},this.#a));if(!(r instanceof rh))throw Error("function passed to `patch()` must return the patch");return this._add({patch:r.serialize()})}if(i){let r=new rh(t,e||{},this.#a);return this._add({patch:r.serialize()})}return this._add({patch:{id:t,...e}})}}class rd extends rf{#a;constructor(t,e,r){super(t,r),this.#a=e}clone(){return new rd([...this.operations],this.#a,this.trxId)}commit(t){if(!this.#a)throw Error("No `client` passed to transaction, either provide one or pass the transaction to a clients `mutate()` method");return this.#a.mutate(this.serialize(),Object.assign({transactionId:this.trxId},rc,t||{}))}patch(t,e){let r="function"==typeof e;if("string"!=typeof t&&t instanceof rl)return this._add({patch:t.serialize()});if(r){let r=e(new rl(t,{},this.#a));if(!(r instanceof rl))throw Error("function passed to `patch()` must return the patch");return this._add({patch:r.serialize()})}return this._add({patch:{id:t,...e}})}}let rm=({query:t,params:e={},options:r={}})=>{let n=new URLSearchParams,{tag:i,includeMutations:s,returnQuery:o,...a}=r;for(let[r,s]of(i&&n.append("tag",i),n.append("query",t),Object.entries(e)))void 0!==s&&n.append(`$${r}`,JSON.stringify(s));for(let[t,e]of Object.entries(a))e&&n.append(t,`${e}`);return!1===o&&n.append("returnQuery","false"),!1===s&&n.append("includeMutations","false"),`?${n}`},ry=(t,e)=>!1===t?void 0:typeof t>"u"?e:t,rg=(t={})=>({dryRun:t.dryRun,returnIds:!0,returnDocuments:ry(t.returnDocuments,!0),visibility:t.visibility||"sync",autoGenerateArrayKeys:t.autoGenerateArrayKeys,skipCrossDatasetReferenceValidation:t.skipCrossDatasetReferenceValidation}),rv=t=>"response"===t.type,rb=t=>t.body,rw=(t,e)=>t.reduce((t,r)=>(t[e(r)]=r,t),Object.create(null));function rx(t,e,n,i,s={},o={}){let a="stega"in o?{...n||{},..."boolean"==typeof o.stega?{enabled:o.stega}:o.stega||{}}:n,u=a.enabled?(0,er.Q)(s):s,l=!1===o.filterResponse?t=>t:t=>t.result,{cache:h,next:c,...f}={useAbortSignal:"u">typeof o.signal,resultSourceMap:a.enabled?"withKeyArraySelector":o.resultSourceMap,...o,returnQuery:!1===o.filterResponse&&!1!==o.returnQuery},p=rD(t,e,"query",{query:i,params:u},"u">typeof h||"u">typeof c?{...f,fetch:{cache:h,next:c}}:f);return a.enabled?p.pipe(function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return es.apply(void 0,z([],N(t)))}(tU(r.e(195).then(r.bind(r,4195)).then(function(t){return t.stegaEncodeSourceMap$1}).then(({stegaEncodeSourceMap:t})=>t))),tN(([t,e])=>{let r=e(t.result,t.resultSourceMap,a);return l({...t,result:r})})):p.pipe(tN(l))}function rE(t,e,r,n={}){let i={uri:rW(t,"doc",(()=>{if(!n.releaseId)return r;let t=eg(r);if(!t){if(ed(r))throw Error(`The document ID (\`${r}\`) is a draft, but \`options.releaseId\` is set as \`${n.releaseId}\``);return ey(r,n.releaseId)}if(t!==n.releaseId)throw Error(`The document ID (\`${r}\`) is already a version of \`${t}\` release, but this does not match the provided \`options.releaseId\` (\`${n.releaseId}\`)`);return r})()),json:!0,tag:n.tag,signal:n.signal};return rN(t,e,i).pipe(eo(rv),tN(t=>t.body.documents&&t.body.documents[0]))}function rT(t,e,r,n={}){let i={uri:rW(t,"doc",r.join(",")),json:!0,tag:n.tag,signal:n.signal};return rN(t,e,i).pipe(eo(rv),tN(t=>{let e=rw(t.body.documents||[],t=>t._id);return r.map(t=>e[t]||null)}))}function rC(t,e,r,n={}){return rD(t,e,"query",{query:"*[sanity::partOfRelease($releaseId)]",params:{releaseId:r}},n)}function rA(t,e,r,n){return eU("createIfNotExists",r),rV(t,e,r,"createIfNotExists",n)}function rS(t,e,r,n){return eU("createOrReplace",r),rV(t,e,r,"createOrReplace",n)}function rP(t,e,r,n,i){return eU("createVersion",r),eN("createVersion",r),rk(t,e,{actionType:"sanity.action.document.version.create",publishedId:n,document:r},i)}function rR(t,e,r,n){return rD(t,e,"mutate",{mutations:[{delete:ra(r)}]},n)}function rM(t,e,r,n=!1,i){return rk(t,e,{actionType:"sanity.action.document.version.discard",versionId:r,purge:n},i)}function rj(t,e,r,n){return eU("replaceVersion",r),eN("replaceVersion",r),rk(t,e,{actionType:"sanity.action.document.version.replace",document:r},n)}function rO(t,e,r,n,i){return rk(t,e,{actionType:"sanity.action.document.version.unpublish",versionId:r,publishedId:n},i)}function rI(t,e,r,n){let i;return rD(t,e,"mutate",{mutations:Array.isArray(i=r instanceof rh||r instanceof rl?{patch:r.serialize()}:r instanceof rp||r instanceof rd?r.serialize():r)?i:[i],transactionId:n&&n.transactionId||void 0},n)}function rk(t,e,r,n){let i=Array.isArray(r)?r:[r],s=n&&n.transactionId||void 0;return rD(t,e,"actions",{actions:i,transactionId:s,skipCrossDatasetReferenceValidation:n&&n.skipCrossDatasetReferenceValidation||void 0,dryRun:n&&n.dryRun||void 0},n)}function rD(t,e,r,n,i={}){let s="mutate"===r,o="actions"===r,a=s||o?"":rm(n),u=!s&&!o&&a.length<11264,l=u?a:"",h=i.returnFirst,{timeout:c,token:f,tag:p,headers:d,returnQuery:m,lastLiveEventId:y,cacheMode:g}=i,v={method:u?"GET":"POST",uri:rW(t,r,l),json:!0,body:u?void 0:n,query:s&&rg(i),timeout:c,headers:d,token:f,tag:p,returnQuery:m,perspective:i.perspective,resultSourceMap:i.resultSourceMap,lastLiveEventId:Array.isArray(y)?y[0]:y,cacheMode:g,canUseCdn:"query"===r,signal:i.signal,fetch:i.fetch,useAbortSignal:i.useAbortSignal,useCdn:i.useCdn};return rN(t,e,v).pipe(eo(rv),tN(rb),tN(t=>{if(!s)return t;let e=t.results||[];if(i.returnDocuments)return h?e[0]&&e[0].document:e.map(t=>t.document);let r=h?e[0]&&e[0].id:e.map(t=>t.id);return{transactionId:t.transactionId,results:e,[h?"documentId":"documentIds"]:r}}))}function rV(t,e,r,n,i={}){return rD(t,e,"mutate",{mutations:[{[n]:r}]},Object.assign({returnFirst:!0,returnDocuments:!0},i))}let rL=t=>void 0!==t.config().dataset&&void 0!==t.config().projectId||void 0!==t.config()["~experimental_resource"],r_=(t,e)=>rL(t)&&e.startsWith(rW(t,"query")),rq=(t,e)=>rL(t)&&e.startsWith(rW(t,"mutate")),rF=(t,e)=>rL(t)&&e.startsWith(rW(t,"doc","")),rB=(t,e)=>rL(t)&&e.startsWith(rW(t,"listen")),rU=(t,e)=>rL(t)&&e.startsWith(rW(t,"history","")),r$=(t,e)=>e.startsWith("/data/")||r_(t,e)||rq(t,e)||rF(t,e)||rB(t,e)||rU(t,e);function rN(t,e,r){var n;let i=r.url||r.uri,s=t.config(),o=typeof r.canUseCdn>"u"?["GET","HEAD"].indexOf(r.method||"GET")>=0&&r$(t,i):r.canUseCdn,a=(r.useCdn??s.useCdn)&&o,u=r.tag&&s.requestTagPrefix?[s.requestTagPrefix,r.tag].join("."):r.tag||s.requestTagPrefix;if(u&&null!==r.tag&&(r.query={tag:eY(u),...r.query}),["GET","HEAD","POST"].indexOf(r.method||"GET")>=0&&r_(t,i)){let t=r.resultSourceMap??s.resultSourceMap;void 0!==t&&!1!==t&&(r.query={resultSourceMap:t,...r.query});let e=r.perspective||s.perspective;"u">typeof e&&("previewDrafts"===e&&e0(),e4(e),r.query={perspective:Array.isArray(e)?e.join(","):e,...r.query},(Array.isArray(e)&&e.length>0||"previewDrafts"===e||"drafts"===e)&&a&&(a=!1,eQ())),r.lastLiveEventId&&(r.query={...r.query,lastLiveEventId:r.lastLiveEventId}),!1===r.returnQuery&&(r.query={returnQuery:"false",...r.query}),a&&"noStale"==r.cacheMode&&(r.query={cacheMode:"noStale",...r.query})}let l=function(t,e={}){let r={};t.headers&&Object.assign(r,t.headers);let n=e.token||t.token;n&&(r.Authorization=`Bearer ${n}`),e.useGlobalApi||t.useProjectHostname||!t.projectId||(r["X-Sanity-Project-ID"]=t.projectId);let i=!!(typeof e.withCredentials>"u"?t.withCredentials:e.withCredentials),s=typeof e.timeout>"u"?t.timeout:e.timeout;return Object.assign({},e,{headers:Object.assign({},r,e.headers||{}),timeout:typeof s>"u"?3e5:s,proxy:e.proxy||t.proxy,json:!0,withCredentials:i,fetch:"object"==typeof e.fetch&&"object"==typeof t.fetch?{...t.fetch,...e.fetch}:e.fetch||t.fetch})}(s,Object.assign({},r,{url:rH(t,i,a)})),h=new tb(t=>e(l,s.requester).subscribe(t));return r.signal?h.pipe((n=r.signal,t=>new tb(e=>{let r=()=>e.error(function(t){if(rY)return new DOMException(t?.reason??"The operation was aborted.","AbortError");let e=Error(t?.reason??"The operation was aborted.");return e.name="AbortError",e}(n));if(n&&n.aborted)return void r();let i=t.subscribe(e);return n.addEventListener("abort",r),()=>{n.removeEventListener("abort",r),i.unsubscribe()}}))):h}function rz(t,e,r){return rN(t,e,r).pipe(eo(t=>"response"===t.type),tN(t=>t.body))}function rW(t,e,r){let n=t.config();if(n["~experimental_resource"]){eX(n);let t=rX(n),i=void 0!==r?`${e}/${r}`:e;return`${t}/${i}`.replace(/\/($|\?)/,"$1")}let i=eH(n),s=`/${e}/${i}`;return`/data${void 0!==r?`${s}/${r}`:s}`.replace(/\/($|\?)/,"$1")}function rH(t,e,r=!1){let{url:n,cdnUrl:i}=t.config();return`${r?i:n}/${e.replace(/^\//,"")}`}let rY=!!globalThis.DOMException,rX=t=>{if(!t["~experimental_resource"])throw Error("`resource` must be provided to perform resource queries");let{type:e,id:r}=t["~experimental_resource"];switch(e){case"dataset":{let t=r.split(".");if(2!==t.length)throw Error('Dataset ID must be in the format "project.dataset"');return`/projects/${t[0]}/datasets/${t[1]}`}case"canvas":return`/canvases/${r}`;case"media-library":return`/media-libraries/${r}`;case"dashboard":return`/dashboards/${r}`;default:throw Error(`Unsupported resource type: ${e.toString()}`)}};function rK(t,e,r){let n=eH(t.config());return rz(t,e,{method:"POST",uri:`/agent/action/generate/${n}`,body:r})}function rG(t,e,r){let n=eH(t.config());return rz(t,e,{method:"POST",uri:`/agent/action/transform/${n}`,body:r})}function rZ(t,e,r){let n=eH(t.config());return rz(t,e,{method:"POST",uri:`/agent/action/translate/${n}`,body:r})}class rJ{#a;#u;constructor(t,e){this.#a=t,this.#u=e}generate(t){return rK(this.#a,this.#u,t)}transform(t){return rG(this.#a,this.#u,t)}translate(t){return rZ(this.#a,this.#u,t)}}class rQ{#a;#u;constructor(t,e){this.#a=t,this.#u=e}generate(t){return tH(rK(this.#a,this.#u,t))}transform(t){return tH(rG(this.#a,this.#u,t))}translate(t){return tH(rZ(this.#a,this.#u,t))}prompt(t){return tH(function(t,e,r){let n=eH(t.config());return rz(t,e,{method:"POST",uri:`/agent/action/prompt/${n}`,body:r})}(this.#a,this.#u,t))}patch(t){return tH(function(t,e,r){let n=eH(t.config());return rz(t,e,{method:"POST",uri:`/agent/action/patch/${n}`,body:r})}(this.#a,this.#u,t))}}class r0{#a;#u;constructor(t,e){this.#a=t,this.#u=e}upload(t,e,r){return r2(this.#a,this.#u,t,e,r)}}class r1{#a;#u;constructor(t,e){this.#a=t,this.#u=e}upload(t,e,r){return tH(r2(this.#a,this.#u,t,e,r).pipe(eo(t=>"response"===t.type),tN(t=>t.body.document)))}}function r2(t,e,r,n,i={}){var s,o;eq(r);let a=i.extract||void 0;a&&!a.length&&(a=["none"]);let u=t.config(),l=(s=i,o=n,!(typeof File>"u")&&o instanceof File?Object.assign({filename:!1===s.preserveFilename?void 0:o.name,contentType:o.type},s):s),{tag:h,label:c,title:f,description:p,creditLine:d,filename:m,source:y}=l,g={label:c,title:f,description:p,filename:m,meta:a,creditLine:d};return y&&(g.sourceId=y.id,g.sourceName=y.name,g.sourceUrl=y.url),rN(t,e,{tag:h,method:"POST",timeout:l.timeout||0,uri:function(t,e){let r="image"===e?"images":"files";if(t["~experimental_resource"]){let{type:e,id:n}=t["~experimental_resource"];switch(e){case"dataset":throw Error("Assets are not supported for dataset resources, yet. Configure the client with `{projectId: <projectId>, dataset: <datasetId>}` instead.");case"canvas":return`/canvases/${n}/assets/${r}`;case"media-library":return`/media-libraries/${n}/upload`;case"dashboard":return`/dashboards/${n}/assets/${r}`;default:throw Error(`Unsupported resource type: ${e.toString()}`)}}let n=eH(t);return`assets/${r}/${n}`}(u,r),headers:l.contentType?{"Content-Type":l.contentType}:{},query:g,body:n})}var r3=(t,e)=>Object.keys(e).concat(Object.keys(t)).reduce((r,n)=>(r[n]=typeof t[n]>"u"?e[n]:t[n],r),{});let r5=(t,e)=>e.reduce((e,r)=>(typeof t[r]>"u"||(e[r]=t[r]),e),{}),r6=tO(()=>r.e(406).then(r.t.bind(r,4406,19))).pipe(tN(({default:t})=>t),function(t,e,r){var n,i,s,o,a=!1;return o=null!=t?t:1/0,tJ({connector:function(){return new tZ(o,e,r)},resetOnError:!0,resetOnComplete:!1,resetOnRefCountZero:a})}(1));function r8(){return function(t){return t.pipe(t0((t,e)=>{var r;return t instanceof e7?t2(t$({type:"reconnect"}),(void 0===r&&(r=t4),new tb(function(t){var e=1e3;e<0&&(e=0);var n=0;return r.schedule(function(){t.closed||(t.next(n++),t.complete())},e)})).pipe(tz(()=>e))):t9(()=>t)}))}}let r4=["includePreviousRevision","includeResult","includeMutations","includeAllVersions","visibility","effectFormat","tag"],r9={includeResult:!0};function r7(t,e,r={}){let{url:n,token:i,withCredentials:s,requestTagPrefix:o,headers:a}=this.config(),u=r.tag&&o?[o,r.tag].join("."):r.tag,l={...r3(r,r9),tag:u},h=rm({query:t,params:e,options:{tag:u,...r5(l,r4)}}),c=`${n}${rW(this,"listen",h)}`;if(c.length>14800)return t9(()=>Error("Query too large for listener"));let f=l.events?l.events:["mutation"],p={};return s&&(p.withCredentials=!0),(i||a)&&(p.headers={},i&&(p.headers.Authorization=`Bearer ${i}`),a&&Object.assign(p.headers,a)),rs(()=>(typeof EventSource>"u"||p.headers?r6:t$(EventSource)).pipe(tN(t=>new t(c,p))),f).pipe(r8(),eo(t=>f.includes(t.type)),tN(t=>({type:t.type,..."data"in t?t.data:{}})))}let nt="2021-03-25";class ne{#a;constructor(t){this.#a=t}events({includeDrafts:t=!1,tag:e}={}){var r,n,i,s;eK("live",this.#a.config());let{projectId:o,apiVersion:a,token:u,withCredentials:l,requestTagPrefix:h,headers:c}=this.#a.config(),f=a.replace(/^v/,"");if("X"!==f&&f<nt)throw Error(`The live events API requires API version ${nt} or later. The current API version is ${f}. Please update your API version to use this feature.`);if(t&&!u&&!l)throw Error("The live events API requires a token or withCredentials when 'includeDrafts: true'. Please update your client configuration. The token should have the lowest possible access role.");let p=rW(this.#a,"live/events"),d=new URL(this.#a.getUrl(p,!1)),m=e&&h?[h,e].join("."):e;m&&d.searchParams.set("tag",m),t&&d.searchParams.set("includeDrafts","true");let y={};t&&l&&(y.withCredentials=!0),(t&&u||c)&&(y.headers={},t&&u&&(y.headers.Authorization=`Bearer ${u}`),c&&Object.assign(y.headers,c));let g=`${d.href}::${JSON.stringify(y)}`,v=nr.get(g);if(v)return v;let b=rs(()=>(typeof EventSource>"u"||y.headers?r6:t$(EventSource)).pipe(tN(t=>new t(d.href,y))),["message","restart","welcome","reconnect","goaway"]).pipe(r8(),tN(t=>{if("message"===t.type){let{data:e,...r}=t;return{...r,tags:e.tags}}return t})),w=t2((n=d,i={method:"OPTIONS",mode:"cors",credentials:y.withCredentials?"include":"omit",headers:y.headers},new tb(t=>{let e=new AbortController,r=e.signal;return fetch(n,{...i,signal:e.signal}).then(e=>{t.next(e),t.complete()},e=>{r.aborted||t.error(e)}),()=>e.abort()})).pipe(tz(()=>et),t0(()=>{throw new eM({projectId:o})})),b).pipe(t7(()=>nr.delete(g)),(s="function"==typeof(r={predicate:t=>"welcome"===t.type})?{predicate:r,...void 0}:r,t=>{var e,r,n,i;let o,a=!1,{predicate:u,...l}=s;return function(){for(var t,e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var n=tk(e),i=(t=1/0,"number"==typeof tI(e)?e.pop():t);return e.length?1===e.length?tM(e[0]):t1(i)(tU(e,n)):et}(t.pipe((i=H(e=t=>{s.predicate(t)&&(a=!0,o=t)})?{next:e,error:r,complete:n}:e)?tV(function(t,e){null==(r=i.subscribe)||r.call(i);var r,n=!0;t.subscribe(tL(e,function(t){var r;null==(r=i.next)||r.call(i,t),e.next(t)},function(){var t;n=!1,null==(t=i.complete)||t.call(i),e.complete()},function(t){var r;n=!1,null==(r=i.error)||r.call(i,t),e.error(t)},function(){var t,e;n&&(null==(t=i.unsubscribe)||t.call(i)),null==(e=i.finalize)||e.call(i)}))}):tg,t7(()=>{a=!1,o=void 0}),tJ(l)),new tb(t=>{a&&t.next(o),t.complete()}))}));return nr.set(g,w),w}}let nr=new Map;class nn{#a;#u;constructor(t,e){this.#a=t,this.#u=e}create(t,e){return ns(this.#a,this.#u,"PUT",t,e)}edit(t,e){return ns(this.#a,this.#u,"PATCH",t,e)}delete(t){return ns(this.#a,this.#u,"DELETE",t)}list(){return rz(this.#a,this.#u,{uri:"/datasets",tag:null})}}class ni{#a;#u;constructor(t,e){this.#a=t,this.#u=e}create(t,e){return eK("dataset",this.#a.config()),tH(ns(this.#a,this.#u,"PUT",t,e))}edit(t,e){return eK("dataset",this.#a.config()),tH(ns(this.#a,this.#u,"PATCH",t,e))}delete(t){return eK("dataset",this.#a.config()),tH(ns(this.#a,this.#u,"DELETE",t))}list(){return eK("dataset",this.#a.config()),tH(rz(this.#a,this.#u,{uri:"/datasets",tag:null}))}}function ns(t,e,r,n,i){return eK("dataset",t.config()),eL(n),rz(t,e,{method:r,uri:`/datasets/${n}`,body:i,tag:null})}class no{#a;#u;constructor(t,e){this.#a=t,this.#u=e}list(t){eK("projects",this.#a.config());let e=t?.includeMembers===!1?"/projects?includeMembers=false":"/projects";return rz(this.#a,this.#u,{uri:e})}getById(t){return eK("projects",this.#a.config()),rz(this.#a,this.#u,{uri:`/projects/${t}`})}}class na{#a;#u;constructor(t,e){this.#a=t,this.#u=e}list(t){eK("projects",this.#a.config());let e=t?.includeMembers===!1?"/projects?includeMembers=false":"/projects";return tH(rz(this.#a,this.#u,{uri:e}))}getById(t){return eK("projects",this.#a.config()),tH(rz(this.#a,this.#u,{uri:`/projects/${t}`}))}}let nu=((t,e=21)=>ew(t,e,eb))("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",8),nl=(t,e)=>e?ey(t,e):function(t){return em(t)?ef+ev(t):ed(t)?t:ef+t}(t);function nh(t,{releaseId:e,publishedId:r,document:n}){if(r&&n._id){let t=nl(r,e);return ez(t,n),t}if(n._id){let r=ed(n._id),i=em(n._id);if(!r&&!i)throw Error(`\`${t}()\` requires a document with an \`_id\` that is a version or draft ID`);if(e){if(r)throw Error(`\`${t}()\` was called with a document ID (\`${n._id}\`) that is a draft ID, but a release ID (\`${e}\`) was also provided.`);let i=eg(n._id);if(i!==e)throw Error(`\`${t}()\` was called with a document ID (\`${n._id}\`) that is a version ID, but the release ID (\`${e}\`) does not match the document's version ID (\`${i}\`).`)}return n._id}if(r)return nl(r,e);throw Error(`\`${t}()\` requires either a publishedId or a document with an \`_id\``)}let nc=(t,e)=>{if("object"==typeof t&&null!==t&&("releaseId"in t||"metadata"in t)){let{releaseId:r=nu(),metadata:n={}}=t;return[r,n,e]}return[nu(),{},t]},nf=(t,e)=>{let[r,n,i]=nc(t,e);return{action:{actionType:"sanity.action.release.create",releaseId:r,metadata:{...n,releaseType:n.releaseType||"undecided"}},options:i}};class np{#a;#u;constructor(t,e){this.#a=t,this.#u=e}get({releaseId:t},e){return rE(this.#a,this.#u,`_.releases.${t}`,e)}create(t,e){let{action:r,options:n}=nf(t,e),{releaseId:i,metadata:s}=r;return rk(this.#a,this.#u,r,n).pipe(tN(t=>({...t,releaseId:i,metadata:s})))}edit({releaseId:t,patch:e},r){return rk(this.#a,this.#u,{actionType:"sanity.action.release.edit",releaseId:t,patch:e},r)}publish({releaseId:t},e){return rk(this.#a,this.#u,{actionType:"sanity.action.release.publish",releaseId:t},e)}archive({releaseId:t},e){return rk(this.#a,this.#u,{actionType:"sanity.action.release.archive",releaseId:t},e)}unarchive({releaseId:t},e){return rk(this.#a,this.#u,{actionType:"sanity.action.release.unarchive",releaseId:t},e)}schedule({releaseId:t,publishAt:e},r){return rk(this.#a,this.#u,{actionType:"sanity.action.release.schedule",releaseId:t,publishAt:e},r)}unschedule({releaseId:t},e){return rk(this.#a,this.#u,{actionType:"sanity.action.release.unschedule",releaseId:t},e)}delete({releaseId:t},e){return rk(this.#a,this.#u,{actionType:"sanity.action.release.delete",releaseId:t},e)}fetchDocuments({releaseId:t},e){return rC(this.#a,this.#u,t,e)}}class nd{#a;#u;constructor(t,e){this.#a=t,this.#u=e}get({releaseId:t},e){return tH(rE(this.#a,this.#u,`_.releases.${t}`,e))}async create(t,e){let{action:r,options:n}=nf(t,e),{releaseId:i,metadata:s}=r;return{...await tH(rk(this.#a,this.#u,r,n)),releaseId:i,metadata:s}}edit({releaseId:t,patch:e},r){return tH(rk(this.#a,this.#u,{actionType:"sanity.action.release.edit",releaseId:t,patch:e},r))}publish({releaseId:t},e){return tH(rk(this.#a,this.#u,{actionType:"sanity.action.release.publish",releaseId:t},e))}archive({releaseId:t},e){return tH(rk(this.#a,this.#u,{actionType:"sanity.action.release.archive",releaseId:t},e))}unarchive({releaseId:t},e){return tH(rk(this.#a,this.#u,{actionType:"sanity.action.release.unarchive",releaseId:t},e))}schedule({releaseId:t,publishAt:e},r){return tH(rk(this.#a,this.#u,{actionType:"sanity.action.release.schedule",releaseId:t,publishAt:e},r))}unschedule({releaseId:t},e){return tH(rk(this.#a,this.#u,{actionType:"sanity.action.release.unschedule",releaseId:t},e))}delete({releaseId:t},e){return tH(rk(this.#a,this.#u,{actionType:"sanity.action.release.delete",releaseId:t},e))}fetchDocuments({releaseId:t},e){return tH(rC(this.#a,this.#u,t,e))}}class nm{#a;#u;constructor(t,e){this.#a=t,this.#u=e}getById(t){return rz(this.#a,this.#u,{uri:`/users/${t}`})}}class ny{#a;#u;constructor(t,e){this.#a=t,this.#u=e}getById(t){return tH(rz(this.#a,this.#u,{uri:`/users/${t}`}))}}class ng{assets;datasets;live;projects;users;agent;releases;#l;#u;listen=r7;constructor(t,e=e5){this.config(e),this.#u=t,this.assets=new r0(this,this.#u),this.datasets=new nn(this,this.#u),this.live=new ne(this),this.projects=new no(this,this.#u),this.users=new nm(this,this.#u),this.agent={action:new rJ(this,this.#u)},this.releases=new np(this,this.#u)}clone(){return new ng(this.#u,this.config())}config(t){if(void 0===t)return{...this.#l};if(this.#l&&!1===this.#l.allowReconfigure)throw Error("Existing client instance cannot be reconfigured - use `withConfig(newConfig)` to return a new client");return this.#l=e9(t,this.#l||{}),this}withConfig(t){let e=this.config();return new ng(this.#u,{...e,...t,stega:{...e.stega||{},..."boolean"==typeof t?.stega?{enabled:t.stega}:t?.stega||{}}})}fetch(t,e,r){return rx(this,this.#u,this.#l.stega,t,e,r)}getDocument(t,e){return rE(this,this.#u,t,e)}getDocuments(t,e){return rT(this,this.#u,t,e)}create(t,e){return rV(this,this.#u,t,"create",e)}createIfNotExists(t,e){return rA(this,this.#u,t,e)}createOrReplace(t,e){return rS(this,this.#u,t,e)}createVersion({document:t,publishedId:e,releaseId:r},n){let i=nh("createVersion",{document:t,publishedId:e,releaseId:r}),s={...t,_id:i},o=e||ev(t._id);return rP(this,this.#u,s,o,n)}delete(t,e){return rR(this,this.#u,t,e)}discardVersion({releaseId:t,publishedId:e},r,n){let i=nl(e,t);return rM(this,this.#u,i,r,n)}replaceVersion({document:t,publishedId:e,releaseId:r},n){let i=nh("replaceVersion",{document:t,publishedId:e,releaseId:r}),s={...t,_id:i};return rj(this,this.#u,s,n)}unpublishVersion({releaseId:t,publishedId:e},r){let n=ey(e,t);return rO(this,this.#u,n,e,r)}mutate(t,e){return rI(this,this.#u,t,e)}patch(t,e){return new rl(t,e,this)}transaction(t){return new rd(t,this)}action(t,e){return rk(this,this.#u,t,e)}request(t){return rz(this,this.#u,t)}getUrl(t,e){return rH(this,t,e)}getDataUrl(t,e){return rW(this,t,e)}}class nv{assets;datasets;live;projects;users;agent;releases;observable;#l;#u;listen=r7;constructor(t,e=e5){this.config(e),this.#u=t,this.assets=new r1(this,this.#u),this.datasets=new ni(this,this.#u),this.live=new ne(this),this.projects=new na(this,this.#u),this.users=new ny(this,this.#u),this.agent={action:new rQ(this,this.#u)},this.releases=new nd(this,this.#u),this.observable=new ng(t,e)}clone(){return new nv(this.#u,this.config())}config(t){if(void 0===t)return{...this.#l};if(this.#l&&!1===this.#l.allowReconfigure)throw Error("Existing client instance cannot be reconfigured - use `withConfig(newConfig)` to return a new client");return this.observable&&this.observable.config(t),this.#l=e9(t,this.#l||{}),this}withConfig(t){let e=this.config();return new nv(this.#u,{...e,...t,stega:{...e.stega||{},..."boolean"==typeof t?.stega?{enabled:t.stega}:t?.stega||{}}})}fetch(t,e,r){return tH(rx(this,this.#u,this.#l.stega,t,e,r))}getDocument(t,e){return tH(rE(this,this.#u,t,e))}getDocuments(t,e){return tH(rT(this,this.#u,t,e))}create(t,e){return tH(rV(this,this.#u,t,"create",e))}createIfNotExists(t,e){return tH(rA(this,this.#u,t,e))}createOrReplace(t,e){return tH(rS(this,this.#u,t,e))}createVersion({document:t,publishedId:e,releaseId:r},n){let i=nh("createVersion",{document:t,publishedId:e,releaseId:r}),s={...t,_id:i},o=e||ev(t._id);return ee(rP(this,this.#u,s,o,n))}delete(t,e){return tH(rR(this,this.#u,t,e))}discardVersion({releaseId:t,publishedId:e},r,n){let i=nl(e,t);return tH(rM(this,this.#u,i,r,n))}replaceVersion({document:t,publishedId:e,releaseId:r},n){let i=nh("replaceVersion",{document:t,publishedId:e,releaseId:r}),s={...t,_id:i};return ee(rj(this,this.#u,s,n))}unpublishVersion({releaseId:t,publishedId:e},r){let n=ey(e,t);return tH(rO(this,this.#u,n,e,r))}mutate(t,e){return tH(rI(this,this.#u,t,e))}patch(t,e){return new rh(t,e,this)}transaction(t){return new rp(t,this)}action(t,e){return tH(rk(this,this.#u,t,e))}request(t){return tH(rz(this,this.#u,t))}dataRequest(t,e,r){return tH(rD(this,this.#u,t,e,r))}getUrl(t,e){return rH(this,t,e)}getDataUrl(t,e){return rW(this,t,e)}}let nb=function(t,e){return{requester:eO(t),createClient:r=>{let n=eO(t);return new e((t,e)=>(e||n)({maxRedirects:0,maxRetries:r.maxRetries,retryDelay:r.retryDelay,...t}),r)}}}([],nv),nw=(nb.requester,nb.createClient)},3509:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},4416:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4612:function(t){t.exports=function(){function t(){return(t=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function e(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var r="image-Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000-jpg";function n(t){return("image-"+t.split("/").slice(-1)[0]).replace(/\.([a-z]+)$/,"-$1")}var i=[["width","w"],["height","h"],["format","fm"],["download","dl"],["blur","blur"],["sharpen","sharp"],["invert","invert"],["orientation","or"],["minHeight","min-h"],["maxHeight","max-h"],["minWidth","min-w"],["maxWidth","max-w"],["quality","q"],["fit","fit"],["crop","crop"],["saturation","sat"],["auto","auto"],["dpr","dpr"],["pad","pad"],["frame","frame"]],s=["clip","crop","fill","fillmax","max","scale","min"],o=["top","bottom","left","right","center","focalpoint","entropy"],a=["format"],u=function(){function u(e,r){this.options=void 0,this.options=e?t({},e.options||{},r||{}):t({},r||{})}var l=u.prototype;return l.withOptions=function(r){var n=r.baseUrl||this.options.baseUrl,s={baseUrl:n};for(var o in r)r.hasOwnProperty(o)&&(s[function(t){for(var r,n=function(t,r){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(n)return(n=n.call(t)).next.bind(n);if(Array.isArray(t)||(n=function(t,r){if(t){if("string"==typeof t)return e(t,void 0);var n=Object.prototype.toString.call(t).slice(8,-1);if("Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return e(t,void 0)}}(t))){n&&(t=n);var i=0;return function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(i);!(r=n()).done;){var s=r.value,o=s[0],a=s[1];if(t===o||t===a)return o}return t}(o)]=r[o]);return new u(this,t({baseUrl:n},s))},l.image=function(t){return this.withOptions({source:t})},l.dataset=function(t){return this.withOptions({dataset:t})},l.projectId=function(t){return this.withOptions({projectId:t})},l.bg=function(t){return this.withOptions({bg:t})},l.dpr=function(t){return this.withOptions(t&&1!==t?{dpr:t}:{})},l.width=function(t){return this.withOptions({width:t})},l.height=function(t){return this.withOptions({height:t})},l.focalPoint=function(t,e){return this.withOptions({focalPoint:{x:t,y:e}})},l.maxWidth=function(t){return this.withOptions({maxWidth:t})},l.minWidth=function(t){return this.withOptions({minWidth:t})},l.maxHeight=function(t){return this.withOptions({maxHeight:t})},l.minHeight=function(t){return this.withOptions({minHeight:t})},l.size=function(t,e){return this.withOptions({width:t,height:e})},l.blur=function(t){return this.withOptions({blur:t})},l.sharpen=function(t){return this.withOptions({sharpen:t})},l.rect=function(t,e,r,n){return this.withOptions({rect:{left:t,top:e,width:r,height:n}})},l.format=function(t){return this.withOptions({format:t})},l.invert=function(t){return this.withOptions({invert:t})},l.orientation=function(t){return this.withOptions({orientation:t})},l.quality=function(t){return this.withOptions({quality:t})},l.forceDownload=function(t){return this.withOptions({download:t})},l.flipHorizontal=function(){return this.withOptions({flipHorizontal:!0})},l.flipVertical=function(){return this.withOptions({flipVertical:!0})},l.ignoreImageParams=function(){return this.withOptions({ignoreImageParams:!0})},l.fit=function(t){if(-1===s.indexOf(t))throw Error('Invalid fit mode "'+t+'"');return this.withOptions({fit:t})},l.crop=function(t){if(-1===o.indexOf(t))throw Error('Invalid crop mode "'+t+'"');return this.withOptions({crop:t})},l.saturation=function(t){return this.withOptions({saturation:t})},l.auto=function(t){if(-1===a.indexOf(t))throw Error('Invalid auto mode "'+t+'"');return this.withOptions({auto:t})},l.pad=function(t){return this.withOptions({pad:t})},l.vanityName=function(t){return this.withOptions({vanityName:t})},l.frame=function(t){if(1!==t)throw Error('Invalid frame value "'+t+'"');return this.withOptions({frame:t})},l.url=function(){return function(e){var s=t({},e||{}),o=s.source;delete s.source;var a=function(e){var r,i;if(!e)return null;if("string"==typeof e&&(i=e,/^https?:\/\//.test(""+i)))r={asset:{_ref:n(e)}};else if("string"==typeof e)r={asset:{_ref:e}};else if(e&&"string"==typeof e._ref)r={asset:e};else if(e&&"string"==typeof e._id)r={asset:{_ref:e._id||""}};else if(e&&e.asset&&"string"==typeof e.asset.url)r={asset:{_ref:n(e.asset.url)}};else{if("object"!=typeof e.asset)return null;r=t({},e)}return e.crop&&(r.crop=e.crop),e.hotspot&&(r.hotspot=e.hotspot),function(e){if(e.crop&&e.hotspot)return e;var r=t({},e);return r.crop||(r.crop={left:0,top:0,bottom:0,right:0}),r.hotspot||(r.hotspot={x:.5,y:.5,height:1,width:1}),r}(r)}(o);if(!a)throw Error("Unable to resolve image URL from source ("+JSON.stringify(o)+")");var u=function(t){var e=t.split("-"),n=e[1],i=e[2],s=e[3];if(!n||!i||!s)throw Error("Malformed asset _ref '"+t+"'. Expected an id like \""+r+'".');var o=i.split("x"),a=o[0],u=o[1],l=+a,h=+u;if(!(isFinite(l)&&isFinite(h)))throw Error("Malformed asset _ref '"+t+"'. Expected an id like \""+r+'".');return{id:n,width:l,height:h,format:s}}(a.asset._ref||a.asset._id||""),l=Math.round(a.crop.left*u.width),h=Math.round(a.crop.top*u.height),c={left:l,top:h,width:Math.round(u.width-a.crop.right*u.width-l),height:Math.round(u.height-a.crop.bottom*u.height-h)},f=a.hotspot.height*u.height/2,p=a.hotspot.width*u.width/2,d=a.hotspot.x*u.width,m=a.hotspot.y*u.height;return s.rect||s.focalPoint||s.ignoreImageParams||s.crop||(s=t({},s,function(t,e){var r,n=e.width,i=e.height;if(!(n&&i))return{width:n,height:i,rect:t.crop};var s=t.crop,o=t.hotspot,a=n/i;if(s.width/s.height>a){var u=Math.round(s.height),l=Math.round(u*a),h=Math.max(0,Math.round(s.top)),c=Math.max(0,Math.round(Math.round((o.right-o.left)/2+o.left)-l/2));c<s.left?c=s.left:c+l>s.left+s.width&&(c=s.left+s.width-l),r={left:c,top:h,width:l,height:u}}else{var f=s.width,p=Math.round(f/a),d=Math.max(0,Math.round(s.left)),m=Math.max(0,Math.round(Math.round((o.bottom-o.top)/2+o.top)-p/2));m<s.top?m=s.top:m+p>s.top+s.height&&(m=s.top+s.height-p),r={left:d,top:m,width:f,height:p}}return{width:n,height:i,rect:r}}({crop:c,hotspot:{left:d-p,top:m-f,right:d+p,bottom:m+f}},s))),function(t){var e=(t.baseUrl||"https://cdn.sanity.io").replace(/\/+$/,""),r=t.vanityName?"/"+t.vanityName:"",n=t.asset.id+"-"+t.asset.width+"x"+t.asset.height+"."+t.asset.format+r,s=e+"/images/"+t.projectId+"/"+t.dataset+"/"+n,o=[];if(t.rect){var a=t.rect,u=a.left,l=a.top,h=a.width,c=a.height;(0!==u||0!==l||c!==t.asset.height||h!==t.asset.width)&&o.push("rect="+u+","+l+","+h+","+c)}t.bg&&o.push("bg="+t.bg),t.focalPoint&&(o.push("fp-x="+t.focalPoint.x),o.push("fp-y="+t.focalPoint.y));var f=[t.flipHorizontal&&"h",t.flipVertical&&"v"].filter(Boolean).join("");return(f&&o.push("flip="+f),i.forEach(function(e){var r=e[0],n=e[1];void 0!==t[r]?o.push(n+"="+encodeURIComponent(t[r])):void 0!==t[n]&&o.push(n+"="+encodeURIComponent(t[n]))}),0===o.length)?s:s+"?"+o.join("&")}(t({},s,{asset:u}))}(this.options)},l.toString=function(){return this.url()},u}();return function(t){if(t&&"config"in t&&"function"==typeof t.config){var e=t.config(),r=e.apiHost,n=e.projectId,i=e.dataset;return new u(null,{baseUrl:(r||"https://api.sanity.io").replace(/^https:\/\/api\./,"https://cdn."),projectId:n,dataset:i})}if(t&&"clientConfig"in t&&"object"==typeof t.clientConfig){var s=t.clientConfig,o=s.apiHost,a=s.projectId,l=s.dataset;return new u(null,{baseUrl:(o||"https://api.sanity.io").replace(/^https:\/\/api\./,"https://cdn."),projectId:a,dataset:l})}return new u(null,t||{})}}()},4783:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},5339:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},6408:(t,e,r)=>{"use strict";let n;function i(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function s(t){let e=[{},{}];return null==t||t.values.forEach((t,r)=>{e[0][r]=t.get(),e[1][r]=t.getVelocity()}),e}function o(t,e,r,n){if("function"==typeof e){let[i,o]=s(n);e=e(void 0!==r?r:t.custom,i,o)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[i,o]=s(n);e=e(void 0!==r?r:t.custom,i,o)}return e}function a(t,e,r){let n=t.getProps();return o(n,e,void 0!==r?r:n.custom,t)}function u(t,e){return t?.[e]??t?.default??t}r.d(e,{P:()=>sP});let l=t=>t,h={},c=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],f={value:null,addProjectionMetrics:null};function p(t,e){let r=!1,n=!0,i={delta:0,timestamp:0,isProcessing:!1},s=()=>r=!0,o=c.reduce((t,r)=>(t[r]=function(t,e){let r=new Set,n=new Set,i=!1,s=!1,o=new WeakSet,a={delta:0,timestamp:0,isProcessing:!1},u=0;function l(e){o.has(e)&&(h.schedule(e),t()),u++,e(a)}let h={schedule:(t,e=!1,s=!1)=>{let a=s&&i?r:n;return e&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{n.delete(t),o.delete(t)},process:t=>{if(a=t,i){s=!0;return}i=!0,[r,n]=[n,r],r.forEach(l),e&&f.value&&f.value.frameloop[e].push(u),u=0,r.clear(),i=!1,s&&(s=!1,h.process(t))}};return h}(s,e?r:void 0),t),{}),{setup:a,read:u,resolveKeyframes:l,preUpdate:p,update:d,preRender:m,render:y,postRender:g}=o,v=()=>{let s=h.useManualTiming?i.timestamp:performance.now();r=!1,h.useManualTiming||(i.delta=n?1e3/60:Math.max(Math.min(s-i.timestamp,40),1)),i.timestamp=s,i.isProcessing=!0,a.process(i),u.process(i),l.process(i),p.process(i),d.process(i),m.process(i),y.process(i),g.process(i),i.isProcessing=!1,r&&e&&(n=!1,t(v))},b=()=>{r=!0,n=!0,i.isProcessing||t(v)};return{schedule:c.reduce((t,e)=>{let n=o[e];return t[e]=(t,e=!1,i=!1)=>(r||b(),n.schedule(t,e,i)),t},{}),cancel:t=>{for(let e=0;e<c.length;e++)o[c[e]].cancel(t)},state:i,steps:o}}let{schedule:d,cancel:m,state:y,steps:g}=p("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:l,!0),v=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],b=new Set(v),w=new Set(["width","height","top","left","right","bottom",...v]);function x(t,e){-1===t.indexOf(e)&&t.push(e)}function E(t,e){let r=t.indexOf(e);r>-1&&t.splice(r,1)}class T{constructor(){this.subscriptions=[]}add(t){return x(this.subscriptions,t),()=>E(this.subscriptions,t)}notify(t,e,r){let n=this.subscriptions.length;if(n)if(1===n)this.subscriptions[0](t,e,r);else for(let i=0;i<n;i++){let n=this.subscriptions[i];n&&n(t,e,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function C(){n=void 0}let A={now:()=>(void 0===n&&A.set(y.isProcessing||h.useManualTiming?y.timestamp:performance.now()),n),set:t=>{n=t,queueMicrotask(C)}},S=t=>!isNaN(parseFloat(t)),P={current:void 0};class R{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let r=A.now();if(this.updatedAt!==r&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=A.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=S(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new T);let r=this.events[t].add(e);return"change"===t?()=>{r(),d.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,r){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-r}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return P.current&&P.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=A.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let r=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),r?1e3/r*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function M(t,e){return new R(t,e)}let j=t=>Array.isArray(t),O=t=>!!(t&&t.getVelocity);function I(t,e){let r=t.getValue("willChange");if(O(r)&&r.add)return r.add(e);if(!r&&h.WillChange){let r=new h.WillChange("auto");t.addValue("willChange",r),r.add(e)}}let k=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),D="data-"+k("framerAppearId"),V=(t,e)=>r=>e(t(r)),L=(...t)=>t.reduce(V),_=(t,e,r)=>r>e?e:r<t?t:r,q=t=>1e3*t,F=t=>t/1e3,B={layout:0,mainThread:0,waapi:0},U=()=>{},$=()=>{},N=t=>e=>"string"==typeof e&&e.startsWith(t),z=N("--"),W=N("var(--"),H=t=>!!W(t)&&Y.test(t.split("/*")[0].trim()),Y=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,X={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},K={...X,transform:t=>_(0,1,t)},G={...X,default:1},Z=t=>Math.round(1e5*t)/1e5,J=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,Q=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tt=(t,e)=>r=>!!("string"==typeof r&&Q.test(r)&&r.startsWith(t)||e&&null!=r&&Object.prototype.hasOwnProperty.call(r,e)),te=(t,e,r)=>n=>{if("string"!=typeof n)return n;let[i,s,o,a]=n.match(J);return{[t]:parseFloat(i),[e]:parseFloat(s),[r]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},tr=t=>_(0,255,t),tn={...X,transform:t=>Math.round(tr(t))},ti={test:tt("rgb","red"),parse:te("red","green","blue"),transform:({red:t,green:e,blue:r,alpha:n=1})=>"rgba("+tn.transform(t)+", "+tn.transform(e)+", "+tn.transform(r)+", "+Z(K.transform(n))+")"},ts={test:tt("#"),parse:function(t){let e="",r="",n="",i="";return t.length>5?(e=t.substring(1,3),r=t.substring(3,5),n=t.substring(5,7),i=t.substring(7,9)):(e=t.substring(1,2),r=t.substring(2,3),n=t.substring(3,4),i=t.substring(4,5),e+=e,r+=r,n+=n,i+=i),{red:parseInt(e,16),green:parseInt(r,16),blue:parseInt(n,16),alpha:i?parseInt(i,16)/255:1}},transform:ti.transform},to=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),ta=to("deg"),tu=to("%"),tl=to("px"),th=to("vh"),tc=to("vw"),tf={...tu,parse:t=>tu.parse(t)/100,transform:t=>tu.transform(100*t)},tp={test:tt("hsl","hue"),parse:te("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:r,alpha:n=1})=>"hsla("+Math.round(t)+", "+tu.transform(Z(e))+", "+tu.transform(Z(r))+", "+Z(K.transform(n))+")"},td={test:t=>ti.test(t)||ts.test(t)||tp.test(t),parse:t=>ti.test(t)?ti.parse(t):tp.test(t)?tp.parse(t):ts.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?ti.transform(t):tp.transform(t),getAnimatableNone:t=>{let e=td.parse(t);return e.alpha=0,td.transform(e)}},tm=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,ty="number",tg="color",tv=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tb(t){let e=t.toString(),r=[],n={color:[],number:[],var:[]},i=[],s=0,o=e.replace(tv,t=>(td.test(t)?(n.color.push(s),i.push(tg),r.push(td.parse(t))):t.startsWith("var(")?(n.var.push(s),i.push("var"),r.push(t)):(n.number.push(s),i.push(ty),r.push(parseFloat(t))),++s,"${}")).split("${}");return{values:r,split:o,indexes:n,types:i}}function tw(t){return tb(t).values}function tx(t){let{split:e,types:r}=tb(t),n=e.length;return t=>{let i="";for(let s=0;s<n;s++)if(i+=e[s],void 0!==t[s]){let e=r[s];e===ty?i+=Z(t[s]):e===tg?i+=td.transform(t[s]):i+=t[s]}return i}}let tE=t=>"number"==typeof t?0:td.test(t)?td.getAnimatableNone(t):t,tT={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(J)?.length||0)+(t.match(tm)?.length||0)>0},parse:tw,createTransformer:tx,getAnimatableNone:function(t){let e=tw(t);return tx(t)(e.map(tE))}};function tC(t,e,r){return(r<0&&(r+=1),r>1&&(r-=1),r<1/6)?t+(e-t)*6*r:r<.5?e:r<2/3?t+(e-t)*(2/3-r)*6:t}function tA(t,e){return r=>r>0?e:t}let tS=(t,e,r)=>t+(e-t)*r,tP=(t,e,r)=>{let n=t*t,i=r*(e*e-n)+n;return i<0?0:Math.sqrt(i)},tR=[ts,ti,tp],tM=t=>tR.find(e=>e.test(t));function tj(t){let e=tM(t);if(U(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let r=e.parse(t);return e===tp&&(r=function({hue:t,saturation:e,lightness:r,alpha:n}){t/=360,r/=100;let i=0,s=0,o=0;if(e/=100){let n=r<.5?r*(1+e):r+e-r*e,a=2*r-n;i=tC(a,n,t+1/3),s=tC(a,n,t),o=tC(a,n,t-1/3)}else i=s=o=r;return{red:Math.round(255*i),green:Math.round(255*s),blue:Math.round(255*o),alpha:n}}(r)),r}let tO=(t,e)=>{let r=tj(t),n=tj(e);if(!r||!n)return tA(t,e);let i={...r};return t=>(i.red=tP(r.red,n.red,t),i.green=tP(r.green,n.green,t),i.blue=tP(r.blue,n.blue,t),i.alpha=tS(r.alpha,n.alpha,t),ti.transform(i))},tI=new Set(["none","hidden"]);function tk(t,e){return r=>tS(t,e,r)}function tD(t){return"number"==typeof t?tk:"string"==typeof t?H(t)?tA:td.test(t)?tO:t_:Array.isArray(t)?tV:"object"==typeof t?td.test(t)?tO:tL:tA}function tV(t,e){let r=[...t],n=r.length,i=t.map((t,r)=>tD(t)(t,e[r]));return t=>{for(let e=0;e<n;e++)r[e]=i[e](t);return r}}function tL(t,e){let r={...t,...e},n={};for(let i in r)void 0!==t[i]&&void 0!==e[i]&&(n[i]=tD(t[i])(t[i],e[i]));return t=>{for(let e in n)r[e]=n[e](t);return r}}let t_=(t,e)=>{let r=tT.createTransformer(e),n=tb(t),i=tb(e);return n.indexes.var.length===i.indexes.var.length&&n.indexes.color.length===i.indexes.color.length&&n.indexes.number.length>=i.indexes.number.length?tI.has(t)&&!i.values.length||tI.has(e)&&!n.values.length?function(t,e){return tI.has(t)?r=>r<=0?t:e:r=>r>=1?e:t}(t,e):L(tV(function(t,e){let r=[],n={color:0,var:0,number:0};for(let i=0;i<e.values.length;i++){let s=e.types[i],o=t.indexes[s][n[s]],a=t.values[o]??0;r[i]=a,n[s]++}return r}(n,i),i.values),r):(U(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),tA(t,e))};function tq(t,e,r){return"number"==typeof t&&"number"==typeof e&&"number"==typeof r?tS(t,e,r):tD(t)(t,e)}let tF=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>d.update(e,t),stop:()=>m(e),now:()=>y.isProcessing?y.timestamp:A.now()}},tB=(t,e,r=10)=>{let n="",i=Math.max(Math.round(e/r),2);for(let e=0;e<i;e++)n+=Math.round(1e4*t(e/(i-1)))/1e4+", ";return`linear(${n.substring(0,n.length-2)})`};function tU(t){let e=0,r=t.next(e);for(;!r.done&&e<2e4;)e+=50,r=t.next(e);return e>=2e4?1/0:e}function t$(t,e,r){var n,i;let s=Math.max(e-5,0);return n=r-t(s),(i=e-s)?1e3/i*n:0}let tN={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function tz(t,e){return t*Math.sqrt(1-e*e)}let tW=["duration","bounce"],tH=["stiffness","damping","mass"];function tY(t,e){return e.some(e=>void 0!==t[e])}function tX(t=tN.visualDuration,e=tN.bounce){let r,n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:i,restDelta:s}=n,o=n.keyframes[0],a=n.keyframes[n.keyframes.length-1],u={done:!1,value:o},{stiffness:l,damping:h,mass:c,duration:f,velocity:p,isResolvedFromDuration:d}=function(t){let e={velocity:tN.velocity,stiffness:tN.stiffness,damping:tN.damping,mass:tN.mass,isResolvedFromDuration:!1,...t};if(!tY(t,tH)&&tY(t,tW))if(t.visualDuration){let r=2*Math.PI/(1.2*t.visualDuration),n=r*r,i=2*_(.05,1,1-(t.bounce||0))*Math.sqrt(n);e={...e,mass:tN.mass,stiffness:n,damping:i}}else{let r=function({duration:t=tN.duration,bounce:e=tN.bounce,velocity:r=tN.velocity,mass:n=tN.mass}){let i,s;U(t<=q(tN.maxDuration),"Spring duration must be 10 seconds or less");let o=1-e;o=_(tN.minDamping,tN.maxDamping,o),t=_(tN.minDuration,tN.maxDuration,F(t)),o<1?(i=e=>{let n=e*o,i=n*t;return .001-(n-r)/tz(e,o)*Math.exp(-i)},s=e=>{let n=e*o*t,s=Math.pow(o,2)*Math.pow(e,2)*t,a=Math.exp(-n),u=tz(Math.pow(e,2),o);return(n*r+r-s)*a*(-i(e)+.001>0?-1:1)/u}):(i=e=>-.001+Math.exp(-e*t)*((e-r)*t+1),s=e=>t*t*(r-e)*Math.exp(-e*t));let a=function(t,e,r){let n=r;for(let r=1;r<12;r++)n-=t(n)/e(n);return n}(i,s,5/t);if(t=q(t),isNaN(a))return{stiffness:tN.stiffness,damping:tN.damping,duration:t};{let e=Math.pow(a,2)*n;return{stiffness:e,damping:2*o*Math.sqrt(n*e),duration:t}}}(t);(e={...e,...r,mass:tN.mass}).isResolvedFromDuration=!0}return e}({...n,velocity:-F(n.velocity||0)}),m=p||0,y=h/(2*Math.sqrt(l*c)),g=a-o,v=F(Math.sqrt(l/c)),b=5>Math.abs(g);if(i||(i=b?tN.restSpeed.granular:tN.restSpeed.default),s||(s=b?tN.restDelta.granular:tN.restDelta.default),y<1){let t=tz(v,y);r=e=>a-Math.exp(-y*v*e)*((m+y*v*g)/t*Math.sin(t*e)+g*Math.cos(t*e))}else if(1===y)r=t=>a-Math.exp(-v*t)*(g+(m+v*g)*t);else{let t=v*Math.sqrt(y*y-1);r=e=>{let r=Math.exp(-y*v*e),n=Math.min(t*e,300);return a-r*((m+y*v*g)*Math.sinh(n)+t*g*Math.cosh(n))/t}}let w={calculatedDuration:d&&f||null,next:t=>{let e=r(t);if(d)u.done=t>=f;else{let n=0===t?m:0;y<1&&(n=0===t?q(m):t$(r,t,e));let o=Math.abs(a-e)<=s;u.done=Math.abs(n)<=i&&o}return u.value=u.done?a:e,u},toString:()=>{let t=Math.min(tU(w),2e4),e=tB(e=>w.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return w}function tK({keyframes:t,velocity:e=0,power:r=.8,timeConstant:n=325,bounceDamping:i=10,bounceStiffness:s=500,modifyTarget:o,min:a,max:u,restDelta:l=.5,restSpeed:h}){let c,f,p=t[0],d={done:!1,value:p},m=t=>void 0!==a&&t<a||void 0!==u&&t>u,y=t=>void 0===a?u:void 0===u||Math.abs(a-t)<Math.abs(u-t)?a:u,g=r*e,v=p+g,b=void 0===o?v:o(v);b!==v&&(g=b-p);let w=t=>-g*Math.exp(-t/n),x=t=>b+w(t),E=t=>{let e=w(t),r=x(t);d.done=Math.abs(e)<=l,d.value=d.done?b:r},T=t=>{m(d.value)&&(c=t,f=tX({keyframes:[d.value,y(d.value)],velocity:t$(x,t,d.value),damping:i,stiffness:s,restDelta:l,restSpeed:h}))};return T(0),{calculatedDuration:null,next:t=>{let e=!1;return(f||void 0!==c||(e=!0,E(t),T(t)),void 0!==c&&t>=c)?f.next(t-c):(e||E(t),d)}}}tX.applyToOptions=t=>{let e=function(t,e=100,r){let n=r({...t,keyframes:[0,e]}),i=Math.min(tU(n),2e4);return{type:"keyframes",ease:t=>n.next(i*t).value/e,duration:F(i)}}(t,100,tX);return t.ease=e.ease,t.duration=q(e.duration),t.type="keyframes",t};let tG=(t,e,r)=>(((1-3*r+3*e)*t+(3*r-6*e))*t+3*e)*t;function tZ(t,e,r,n){if(t===e&&r===n)return l;let i=e=>(function(t,e,r,n,i){let s,o,a=0;do(s=tG(o=e+(r-e)/2,n,i)-t)>0?r=o:e=o;while(Math.abs(s)>1e-7&&++a<12);return o})(e,0,1,t,r);return t=>0===t||1===t?t:tG(i(t),e,n)}let tJ=tZ(.42,0,1,1),tQ=tZ(0,0,.58,1),t0=tZ(.42,0,.58,1),t1=t=>Array.isArray(t)&&"number"!=typeof t[0],t2=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,t3=t=>e=>1-t(1-e),t5=tZ(.33,1.53,.69,.99),t6=t3(t5),t8=t2(t6),t4=t=>(t*=2)<1?.5*t6(t):.5*(2-Math.pow(2,-10*(t-1))),t9=t=>1-Math.sin(Math.acos(t)),t7=t3(t9),et=t2(t9),ee=t=>Array.isArray(t)&&"number"==typeof t[0],er={linear:l,easeIn:tJ,easeInOut:t0,easeOut:tQ,circIn:t9,circInOut:et,circOut:t7,backIn:t6,backInOut:t8,backOut:t5,anticipate:t4},en=t=>"string"==typeof t,ei=t=>{if(ee(t)){$(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,r,n,i]=t;return tZ(e,r,n,i)}return en(t)?($(void 0!==er[t],`Invalid easing type '${t}'`),er[t]):t},es=(t,e,r)=>{let n=e-t;return 0===n?1:(r-t)/n};function eo({duration:t=300,keyframes:e,times:r,ease:n="easeInOut"}){var i;let s=t1(n)?n.map(ei):ei(n),o={done:!1,value:e[0]},a=function(t,e,{clamp:r=!0,ease:n,mixer:i}={}){let s=t.length;if($(s===e.length,"Both input and output ranges must be the same length"),1===s)return()=>e[0];if(2===s&&e[0]===e[1])return()=>e[1];let o=t[0]===t[1];t[0]>t[s-1]&&(t=[...t].reverse(),e=[...e].reverse());let a=function(t,e,r){let n=[],i=r||h.mix||tq,s=t.length-1;for(let r=0;r<s;r++){let s=i(t[r],t[r+1]);e&&(s=L(Array.isArray(e)?e[r]||l:e,s)),n.push(s)}return n}(e,n,i),u=a.length,c=r=>{if(o&&r<t[0])return e[0];let n=0;if(u>1)for(;n<t.length-2&&!(r<t[n+1]);n++);let i=es(t[n],t[n+1],r);return a[n](i)};return r?e=>c(_(t[0],t[s-1],e)):c}((i=r&&r.length===e.length?r:function(t){let e=[0];return!function(t,e){let r=t[t.length-1];for(let n=1;n<=e;n++){let i=es(0,e,n);t.push(tS(r,1,i))}}(e,t.length-1),e}(e),i.map(e=>e*t)),e,{ease:Array.isArray(s)?s:e.map(()=>s||t0).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(o.value=a(e),o.done=e>=t,o)}}let ea=t=>null!==t;function eu(t,{repeat:e,repeatType:r="loop"},n,i=1){let s=t.filter(ea),o=i<0||e&&"loop"!==r&&e%2==1?0:s.length-1;return o&&void 0!==n?n:s[o]}let el={decay:tK,inertia:tK,tween:eo,keyframes:eo,spring:tX};function eh(t){"string"==typeof t.type&&(t.type=el[t.type])}class ec{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}let ef=t=>t/100;class ep extends ec{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;t&&t.updatedAt!==A.now()&&this.tick(A.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},B.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;eh(t);let{type:e=eo,repeat:r=0,repeatDelay:n=0,repeatType:i,velocity:s=0}=t,{keyframes:o}=t,a=e||eo;a!==eo&&"number"!=typeof o[0]&&(this.mixKeyframes=L(ef,tq(o[0],o[1])),o=[0,100]);let u=a({...t,keyframes:o});"mirror"===i&&(this.mirroredGenerator=a({...t,keyframes:[...o].reverse(),velocity:-s})),null===u.calculatedDuration&&(u.calculatedDuration=tU(u));let{calculatedDuration:l}=u;this.calculatedDuration=l,this.resolvedDuration=l+n,this.totalDuration=this.resolvedDuration*(r+1)-n,this.generator=u}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:r,totalDuration:n,mixKeyframes:i,mirroredGenerator:s,resolvedDuration:o,calculatedDuration:a}=this;if(null===this.startTime)return r.next(0);let{delay:u=0,keyframes:l,repeat:h,repeatType:c,repeatDelay:f,type:p,onUpdate:d,finalKeyframe:m}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-n/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let y=this.currentTime-u*(this.playbackSpeed>=0?1:-1),g=this.playbackSpeed>=0?y<0:y>n;this.currentTime=Math.max(y,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=n);let v=this.currentTime,b=r;if(h){let t=Math.min(this.currentTime,n)/o,e=Math.floor(t),r=t%1;!r&&t>=1&&(r=1),1===r&&e--,(e=Math.min(e,h+1))%2&&("reverse"===c?(r=1-r,f&&(r-=f/o)):"mirror"===c&&(b=s)),v=_(0,1,r)*o}let w=g?{done:!1,value:l[0]}:b.next(v);i&&(w.value=i(w.value));let{done:x}=w;g||null===a||(x=this.playbackSpeed>=0?this.currentTime>=n:this.currentTime<=0);let E=null===this.holdTime&&("finished"===this.state||"running"===this.state&&x);return E&&p!==tK&&(w.value=eu(l,this.options,m,this.speed)),d&&d(w.value),E&&this.finish(),w}then(t,e){return this.finished.then(t,e)}get duration(){return F(this.calculatedDuration)}get time(){return F(this.currentTime)}set time(t){t=q(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(A.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=F(this.currentTime))}play(){if(this.isStopped)return;let{driver:t=tF,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let r=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=r):null!==this.holdTime?this.startTime=r-this.holdTime:this.startTime||(this.startTime=e??r),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(A.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,B.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}let ed=t=>180*t/Math.PI,em=t=>eg(ed(Math.atan2(t[1],t[0]))),ey={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:em,rotateZ:em,skewX:t=>ed(Math.atan(t[1])),skewY:t=>ed(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},eg=t=>((t%=360)<0&&(t+=360),t),ev=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),eb=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),ew={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:ev,scaleY:eb,scale:t=>(ev(t)+eb(t))/2,rotateX:t=>eg(ed(Math.atan2(t[6],t[5]))),rotateY:t=>eg(ed(Math.atan2(-t[2],t[0]))),rotateZ:em,rotate:em,skewX:t=>ed(Math.atan(t[4])),skewY:t=>ed(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function ex(t){return+!!t.includes("scale")}function eE(t,e){let r,n;if(!t||"none"===t)return ex(e);let i=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(i)r=ew,n=i;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);r=ey,n=e}if(!n)return ex(e);let s=r[e],o=n[1].split(",").map(eC);return"function"==typeof s?s(o):o[s]}let eT=(t,e)=>{let{transform:r="none"}=getComputedStyle(t);return eE(r,e)};function eC(t){return parseFloat(t.trim())}let eA=t=>t===X||t===tl,eS=new Set(["x","y","z"]),eP=v.filter(t=>!eS.has(t)),eR={width:({x:t},{paddingLeft:e="0",paddingRight:r="0"})=>t.max-t.min-parseFloat(e)-parseFloat(r),height:({y:t},{paddingTop:e="0",paddingBottom:r="0"})=>t.max-t.min-parseFloat(e)-parseFloat(r),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>eE(e,"x"),y:(t,{transform:e})=>eE(e,"y")};eR.translateX=eR.x,eR.translateY=eR.y;let eM=new Set,ej=!1,eO=!1,eI=!1;function ek(){if(eO){let t=Array.from(eM).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),r=new Map;e.forEach(t=>{let e=function(t){let e=[];return eP.forEach(r=>{let n=t.getValue(r);void 0!==n&&(e.push([r,n.get()]),n.set(+!!r.startsWith("scale")))}),e}(t);e.length&&(r.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=r.get(t);e&&e.forEach(([e,r])=>{t.getValue(e)?.set(r)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}eO=!1,ej=!1,eM.forEach(t=>t.complete(eI)),eM.clear()}function eD(){eM.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(eO=!0)})}class eV{constructor(t,e,r,n,i,s=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=r,this.motionValue=n,this.element=i,this.isAsync=s}scheduleResolve(){this.state="scheduled",this.isAsync?(eM.add(this),ej||(ej=!0,d.read(eD),d.resolveKeyframes(ek))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:r,motionValue:n}=this;if(null===t[0]){let i=n?.get(),s=t[t.length-1];if(void 0!==i)t[0]=i;else if(r&&e){let n=r.readValue(e,s);null!=n&&(t[0]=n)}void 0===t[0]&&(t[0]=s),n&&void 0===i&&n.set(t[0])}for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),eM.delete(this)}cancel(){"scheduled"===this.state&&(eM.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let eL=t=>t.startsWith("--");function e_(t){let e;return()=>(void 0===e&&(e=t()),e)}let eq=e_(()=>void 0!==window.ScrollTimeline),eF={},eB=function(t,e){let r=e_(t);return()=>eF[e]??r()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),eU=([t,e,r,n])=>`cubic-bezier(${t}, ${e}, ${r}, ${n})`,e$={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:eU([0,.65,.55,1]),circOut:eU([.55,0,1,.45]),backIn:eU([.31,.01,.66,-.59]),backOut:eU([.33,1.53,.69,.99])};function eN(t){return"function"==typeof t&&"applyToOptions"in t}class ez extends ec{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:r,keyframes:n,pseudoElement:i,allowFlatten:s=!1,finalKeyframe:o,onComplete:a}=t;this.isPseudoElement=!!i,this.allowFlatten=s,this.options=t,$("string"!=typeof t.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let u=function({type:t,...e}){return eN(t)&&eB()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,r,{delay:n=0,duration:i=300,repeat:s=0,repeatType:o="loop",ease:a="easeOut",times:u}={},l){let h={[e]:r};u&&(h.offset=u);let c=function t(e,r){if(e)return"function"==typeof e?eB()?tB(e,r):"ease-out":ee(e)?eU(e):Array.isArray(e)?e.map(e=>t(e,r)||e$.easeOut):e$[e]}(a,i);Array.isArray(c)&&(h.easing=c),f.value&&B.waapi++;let p={delay:n,duration:i,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:s+1,direction:"reverse"===o?"alternate":"normal"};l&&(p.pseudoElement=l);let d=t.animate(h,p);return f.value&&d.finished.finally(()=>{B.waapi--}),d}(e,r,n,u,i),!1===u.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!i){let t=eu(n,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,r){eL(e)?t.style.setProperty(e,r):t.style[e]=r}(e,r,t),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return F(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return F(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=q(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&eq())?(this.animation.timeline=t,l):e(this)}}let eW={anticipate:t4,backInOut:t8,circInOut:et};class eH extends ez{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in eW&&(t.ease=eW[t.ease])}(t),eh(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:r,onComplete:n,element:i,...s}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);let o=new ep({...s,autoplay:!1}),a=q(this.finishedTime??this.time);e.setWithVelocity(o.sample(a-10).value,o.sample(a).value,10),o.stop()}}let eY=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tT.test(t)||"0"===t)&&!t.startsWith("url("));var eX,eK,eG=r(7351);let eZ=new Set(["opacity","clipPath","filter","transform"]),eJ=e_(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class eQ extends ec{constructor({autoplay:t=!0,delay:e=0,type:r="keyframes",repeat:n=0,repeatDelay:i=0,repeatType:s="loop",keyframes:o,name:a,motionValue:u,element:l,...h}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=A.now();let c={autoplay:t,delay:e,type:r,repeat:n,repeatDelay:i,repeatType:s,name:a,motionValue:u,element:l,...h},f=l?.KeyframeResolver||eV;this.keyframeResolver=new f(o,(t,e,r)=>this.onKeyframesResolved(t,e,c,!r),a,u,l),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,r,n){this.keyframeResolver=void 0;let{name:i,type:s,velocity:o,delay:a,isHandoff:u,onUpdate:c}=r;this.resolvedAt=A.now(),!function(t,e,r,n){let i=t[0];if(null===i)return!1;if("display"===e||"visibility"===e)return!0;let s=t[t.length-1],o=eY(i,e),a=eY(s,e);return U(o===a,`You are trying to animate ${e} from "${i}" to "${s}". ${i} is not an animatable value - to enable this animation set ${i} to a value animatable to ${s} via the \`style\` property.`),!!o&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let r=0;r<t.length;r++)if(t[r]!==e)return!0}(t)||("spring"===r||eN(r))&&n)}(t,i,s,o)&&((h.instantAnimations||!a)&&c?.(eu(t,r,e)),t[0]=t[t.length-1],r.duration=0,r.repeat=0);let f={startTime:n?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...r,keyframes:t},p=!u&&function(t){let{motionValue:e,name:r,repeatDelay:n,repeatType:i,damping:s,type:o}=t;if(!(0,eG.s)(e?.owner?.current))return!1;let{onUpdate:a,transformTemplate:u}=e.owner.getProps();return eJ()&&r&&eZ.has(r)&&("transform"!==r||!u)&&!a&&!n&&"mirror"!==i&&0!==s&&"inertia"!==o}(f)?new eH({...f,element:f.motionValue.owner.current}):new ep(f);p.finished.then(()=>this.notifyFinished()).catch(l),this.pendingTimeline&&(this.stopTimeline=p.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=p}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),eI=!0,eD(),ek(),eI=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let e0=t=>null!==t,e1={type:"spring",stiffness:500,damping:25,restSpeed:10},e2=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),e3={type:"keyframes",duration:.8},e5={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},e6=(t,e)=>{let{keyframes:r}=e;return r.length>2?e3:b.has(t)?t.startsWith("scale")?e2(r[1]):e1:e5},e8=function(t,e,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=arguments.length>4?arguments[4]:void 0,s=arguments.length>5?arguments[5]:void 0;return o=>{let a=u(n,t)||{},l=a.delay||n.delay||0,{elapsed:c=0}=n;c-=q(l);let f={keyframes:Array.isArray(r)?r:[null,r],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-c,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:s?void 0:i};!function(t){let{when:e,delay:r,delayChildren:n,staggerChildren:i,staggerDirection:s,repeat:o,repeatType:a,repeatDelay:u,from:l,elapsed:h,...c}=t;return!!Object.keys(c).length}(a)&&Object.assign(f,e6(t,f)),f.duration&&(f.duration=q(f.duration)),f.repeatDelay&&(f.repeatDelay=q(f.repeatDelay)),void 0!==f.from&&(f.keyframes[0]=f.from);let p=!1;if(!1!==f.type&&(0!==f.duration||f.repeatDelay)||(f.duration=0,0===f.delay&&(p=!0)),(h.instantAnimations||h.skipAnimations)&&(p=!0,f.duration=0,f.delay=0),f.allowFlatten=!a.type&&!a.ease,p&&!s&&void 0!==e.get()){let t=function(t,e,r){let{repeat:n,repeatType:i="loop"}=e,s=t.filter(e0),o=n&&"loop"!==i&&n%2==1?0:s.length-1;return s[o]}(f.keyframes,a);if(void 0!==t)return void d.update(()=>{f.onUpdate(t),f.onComplete()})}return a.isSync?new ep(f):new eQ(f)}};function e4(t,e){let{delay:r=0,transitionOverride:n,type:i}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{transition:s=t.getDefaultTransition(),transitionEnd:o,...l}=e;n&&(s=n);let h=[],c=i&&t.animationState&&t.animationState.getState()[i];for(let e in l){var f;let n=t.getValue(e,null!=(f=t.latestValues[e])?f:null),i=l[e];if(void 0===i||c&&function(t,e){let{protectedKeys:r,needsAnimating:n}=t,i=r.hasOwnProperty(e)&&!0!==n[e];return n[e]=!1,i}(c,e))continue;let o={delay:r,...u(s||{},e)},a=n.get();if(void 0!==a&&!n.isAnimating&&!Array.isArray(i)&&i===a&&!o.velocity)continue;let p=!1;if(window.MotionHandoffAnimation){let r=t.props[D];if(r){let t=window.MotionHandoffAnimation(r,e,d);null!==t&&(o.startTime=t,p=!0)}}I(t,e),n.start(e8(e,n,i,t.shouldReduceMotion&&w.has(e)?{type:!1}:o,t,p));let m=n.animation;m&&h.push(m)}return o&&Promise.all(h).then(()=>{d.update(()=>{o&&function(t,e){let{transitionEnd:r={},transition:n={},...i}=a(t,e)||{};for(let e in i={...i,...r}){var s;let r=j(s=i[e])?s[s.length-1]||0:s;t.hasValue(e)?t.getValue(e).set(r):t.addValue(e,M(r))}}(t,o)})}),h}function e9(t,e){var r;let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=a(t,e,"exit"===n.type?null==(r=t.presenceContext)?void 0:r.custom:void 0),{transition:s=t.getDefaultTransition()||{}}=i||{};n.transitionOverride&&(s=n.transitionOverride);let o=i?()=>Promise.all(e4(t,i,n)):()=>Promise.resolve(),u=t.variantChildren&&t.variantChildren.size?function(){let r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,{delayChildren:i=0,staggerChildren:o,staggerDirection:a}=s;return function(t,e){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,s=arguments.length>5&&void 0!==arguments[5]?arguments[5]:1,o=arguments.length>6?arguments[6]:void 0,a=[],u=t.variantChildren.size,l=(u-1)*i,h="function"==typeof n,c=h?t=>n(t,u):1===s?function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return t*i}:function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return l-t*i};return Array.from(t.variantChildren).sort(e7).forEach((t,i)=>{t.notify("AnimationStart",e),a.push(e9(t,e,{...o,delay:r+(h?0:n)+c(i)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(a)}(t,e,r,i,o,a,n)}:()=>Promise.resolve(),{when:l}=s;if(!l)return Promise.all([o(),u(n.delay)]);{let[t,e]="beforeChildren"===l?[o,u]:[u,o];return t().then(()=>e())}}function e7(t,e){return t.sortNodePosition(e)}function rt(t,e){if(!Array.isArray(e))return!1;let r=e.length;if(r!==t.length)return!1;for(let n=0;n<r;n++)if(e[n]!==t[n])return!1;return!0}function re(t){return"string"==typeof t||Array.isArray(t)}let rr=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],rn=["initial",...rr],ri=rn.length,rs=[...rr].reverse(),ro=rr.length;function ra(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function ru(){return{animate:ra(!0),whileInView:ra(),whileHover:ra(),whileTap:ra(),whileDrag:ra(),whileFocus:ra(),exit:ra()}}class rl{update(){}constructor(t){this.isMounted=!1,this.node=t}}class rh extends rl{updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();i(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),null==(t=this.unmountControls)||t.call(this)}constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(e=>{let{animation:r,options:n}=e;return function(t,e){let r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(t.notify("AnimationStart",e),Array.isArray(e))r=Promise.all(e.map(e=>e9(t,e,n)));else if("string"==typeof e)r=e9(t,e,n);else{let i="function"==typeof e?a(t,e,n.custom):e;r=Promise.all(e4(t,i,n))}return r.then(()=>{t.notify("AnimationComplete",e)})}(t,r,n)})),r=ru(),n=!0,s=e=>(r,n)=>{var i;let s=a(t,n,"exit"===e?null==(i=t.presenceContext)?void 0:i.custom:void 0);if(s){let{transition:t,transitionEnd:e,...n}=s;r={...r,...n,...e}}return r};function o(o){let{props:u}=t,l=function t(e){if(!e)return;if(!e.isControllingVariants){let r=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(r.initial=e.props.initial),r}let r={};for(let t=0;t<ri;t++){let n=rn[t],i=e.props[n];(re(i)||!1===i)&&(r[n]=i)}return r}(t.parent)||{},h=[],c=new Set,f={},p=1/0;for(let e=0;e<ro;e++){var d,m;let a=rs[e],y=r[a],g=void 0!==u[a]?u[a]:l[a],v=re(g),b=a===o?y.isActive:null;!1===b&&(p=e);let w=g===l[a]&&g!==u[a]&&v;if(w&&n&&t.manuallyAnimateOnMount&&(w=!1),y.protectedKeys={...f},!y.isActive&&null===b||!g&&!y.prevProp||i(g)||"boolean"==typeof g)continue;let x=(d=y.prevProp,"string"==typeof(m=g)?m!==d:!!Array.isArray(m)&&!rt(m,d)),E=x||a===o&&y.isActive&&!w&&v||e>p&&v,T=!1,C=Array.isArray(g)?g:[g],A=C.reduce(s(a),{});!1===b&&(A={});let{prevResolvedValues:S={}}=y,P={...S,...A},R=e=>{E=!0,c.has(e)&&(T=!0,c.delete(e)),y.needsAnimating[e]=!0;let r=t.getValue(e);r&&(r.liveStyle=!1)};for(let t in P){let e=A[t],r=S[t];if(f.hasOwnProperty(t))continue;let n=!1;(j(e)&&j(r)?rt(e,r):e===r)?void 0!==e&&c.has(t)?R(t):y.protectedKeys[t]=!0:null!=e?R(t):c.add(t)}y.prevProp=g,y.prevResolvedValues=A,y.isActive&&(f={...f,...A}),n&&t.blockInitialAnimation&&(E=!1);let M=!(w&&x)||T;E&&M&&h.push(...C.map(t=>({animation:t,options:{type:a}})))}if(c.size){let e={};if("boolean"!=typeof u.initial){let r=a(t,Array.isArray(u.initial)?u.initial[0]:u.initial);r&&r.transition&&(e.transition=r.transition)}c.forEach(r=>{let n=t.getBaseTarget(r),i=t.getValue(r);i&&(i.liveStyle=!0),e[r]=null!=n?n:null}),h.push({animation:e})}let y=!!h.length;return n&&(!1===u.initial||u.initial===u.animate)&&!t.manuallyAnimateOnMount&&(y=!1),n=!1,y?e(h):Promise.resolve()}return{animateChanges:o,setActive:function(e,n){var i;if(r[e].isActive===n)return Promise.resolve();null==(i=t.variantChildren)||i.forEach(t=>{var r;return null==(r=t.animationState)?void 0:r.setActive(e,n)}),r[e].isActive=n;let s=o(e);for(let t in r)r[t].protectedKeys={};return s},setAnimateFunction:function(r){e=r(t)},getState:()=>r,reset:()=>{r=ru(),n=!0}}}(t))}}let rc=0;class rf extends rl{update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===r)return;let n=this.node.animationState.setActive("exit",!t);e&&!t&&n.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}constructor(){super(...arguments),this.id=rc++}}let rp={x:!1,y:!1};function rd(t,e,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{passive:!0};return t.addEventListener(e,r,n),()=>t.removeEventListener(e,r)}let rm=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function ry(t){return{point:{x:t.pageX,y:t.pageY}}}let rg=t=>e=>rm(e)&&t(e,ry(e));function rv(t,e,r,n){return rd(t,e,rg(r),n)}function rb(t){let{top:e,left:r,right:n,bottom:i}=t;return{x:{min:r,max:n},y:{min:e,max:i}}}function rw(t){return t.max-t.min}function rx(t,e,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.5;t.origin=n,t.originPoint=tS(e.min,e.max,t.origin),t.scale=rw(r)/rw(e),t.translate=tS(r.min,r.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function rE(t,e,r,n){rx(t.x,e.x,r.x,n?n.originX:void 0),rx(t.y,e.y,r.y,n?n.originY:void 0)}function rT(t,e,r){t.min=r.min+e.min,t.max=t.min+rw(e)}function rC(t,e,r){t.min=e.min-r.min,t.max=t.min+rw(e)}function rA(t,e,r){rC(t.x,e.x,r.x),rC(t.y,e.y,r.y)}let rS=()=>({translate:0,scale:1,origin:0,originPoint:0}),rP=()=>({x:rS(),y:rS()}),rR=()=>({min:0,max:0}),rM=()=>({x:rR(),y:rR()});function rj(t){return[t("x"),t("y")]}function rO(t){return void 0===t||1===t}function rI(t){let{scale:e,scaleX:r,scaleY:n}=t;return!rO(e)||!rO(r)||!rO(n)}function rk(t){return rI(t)||rD(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function rD(t){var e,r;return(e=t.x)&&"0%"!==e||(r=t.y)&&"0%"!==r}function rV(t,e,r,n,i){return void 0!==i&&(t=n+i*(t-n)),n+r*(t-n)+e}function rL(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=arguments.length>3?arguments[3]:void 0,i=arguments.length>4?arguments[4]:void 0;t.min=rV(t.min,e,r,n,i),t.max=rV(t.max,e,r,n,i)}function r_(t,e){let{x:r,y:n}=e;rL(t.x,r.translate,r.scale,r.originPoint),rL(t.y,n.translate,n.scale,n.originPoint)}function rq(t,e){t.min=t.min+e,t.max=t.max+e}function rF(t,e,r,n){let i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:.5,s=tS(t.min,t.max,i);rL(t,e,r,s,n)}function rB(t,e){rF(t.x,e.x,e.scaleX,e.scale,e.originX),rF(t.y,e.y,e.scaleY,e.scale,e.originY)}function rU(t,e){return rb(function(t,e){if(!e)return t;let r=e({x:t.left,y:t.top}),n=e({x:t.right,y:t.bottom});return{top:r.y,left:r.x,bottom:n.y,right:n.x}}(t.getBoundingClientRect(),e))}let r$=t=>{let{current:e}=t;return e?e.ownerDocument.defaultView:null};function rN(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let rz=(t,e)=>Math.abs(t-e);class rW{updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),m(this.updatePoint)}constructor(t,e,{transformPagePoint:r,contextWindow:n=window,dragSnapToOrigin:i=!1,distanceThreshold:s=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=rX(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,r=function(t,e){return Math.sqrt(rz(t.x,e.x)**2+rz(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=this.distanceThreshold;if(!e&&!r)return;let{point:n}=t,{timestamp:i}=y;this.history.push({...n,timestamp:i});let{onStart:s,onMove:o}=this.handlers;e||(s&&s(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=rH(e,this.transformPagePoint),d.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:r,onSessionEnd:n,resumeAnimation:i}=this.handlers;if(this.dragSnapToOrigin&&i&&i(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=rX("pointercancel"===t.type?this.lastMoveEventInfo:rH(e,this.transformPagePoint),this.history);this.startEvent&&r&&r(t,s),n&&n(t,s)},!rm(t))return;this.dragSnapToOrigin=i,this.handlers=e,this.transformPagePoint=r,this.distanceThreshold=s,this.contextWindow=n||window;let o=rH(ry(t),this.transformPagePoint),{point:a}=o,{timestamp:u}=y;this.history=[{...a,timestamp:u}];let{onSessionStart:l}=e;l&&l(t,rX(o,this.history)),this.removeListeners=L(rv(this.contextWindow,"pointermove",this.handlePointerMove),rv(this.contextWindow,"pointerup",this.handlePointerUp),rv(this.contextWindow,"pointercancel",this.handlePointerUp))}}function rH(t,e){return e?{point:e(t.point)}:t}function rY(t,e){return{x:t.x-e.x,y:t.y-e.y}}function rX(t,e){let{point:r}=t;return{point:r,delta:rY(r,rK(e)),offset:rY(r,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let r=t.length-1,n=null,i=rK(t);for(;r>=0&&(n=t[r],!(i.timestamp-n.timestamp>q(.1)));)r--;if(!n)return{x:0,y:0};let s=F(i.timestamp-n.timestamp);if(0===s)return{x:0,y:0};let o={x:(i.x-n.x)/s,y:(i.y-n.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,.1)}}function rK(t){return t[t.length-1]}function rG(t,e,r){return{min:void 0!==e?t.min+e:void 0,max:void 0!==r?t.max+r-(t.max-t.min):void 0}}function rZ(t,e){let r=e.min-t.min,n=e.max-t.max;return e.max-e.min<t.max-t.min&&([r,n]=[n,r]),{min:r,max:n}}function rJ(t,e,r){return{min:rQ(t,e),max:rQ(t,r)}}function rQ(t,e){return"number"==typeof t?t:t[e]||0}let r0=new WeakMap;class r1{start(t){let{snapToCursor:e=!1,distanceThreshold:r}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;let{dragSnapToOrigin:i}=this.getProps();this.panSession=new rW(t,{onSessionStart:t=>{let{dragSnapToOrigin:r}=this.getProps();r?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(ry(t).point)},onStart:(t,e)=>{let{drag:r,dragPropagation:n,onDragStart:i}=this.getProps();if(r&&!n&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(rp[t])return null;else return rp[t]=!0,()=>{rp[t]=!1};return rp.x||rp.y?null:(rp.x=rp.y=!0,()=>{rp.x=rp.y=!1})}(r),!this.openDragLock))return;this.latestPointerEvent=t,this.latestPanInfo=e,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),rj(t=>{let e=this.getAxisMotionValue(t).get()||0;if(tu.test(e)){let{projection:r}=this.visualElement;if(r&&r.layout){let n=r.layout.layoutBox[t];n&&(e=rw(n)*(parseFloat(e)/100))}}this.originPoint[t]=e}),i&&d.postRender(()=>i(t,e)),I(this.visualElement,"transform");let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},onMove:(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e;let{dragPropagation:r,dragDirectionLock:n,onDirectionLock:i,onDrag:s}=this.getProps();if(!r&&!this.openDragLock)return;let{offset:o}=e;if(n&&null===this.currentDirection){this.currentDirection=function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,r=null;return Math.abs(t.y)>e?r="y":Math.abs(t.x)>e&&(r="x"),r}(o),null!==this.currentDirection&&i&&i(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),s&&s(t,e)},onSessionEnd:(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e,this.stop(t,e),this.latestPointerEvent=null,this.latestPanInfo=null},resumeAnimation:()=>rj(t=>{var e;return"paused"===this.getAnimationState(t)&&(null==(e=this.getAxisMotionValue(t).animation)?void 0:e.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:i,distanceThreshold:r,contextWindow:r$(this.visualElement)})}stop(t,e){let r=t||this.latestPointerEvent,n=e||this.latestPanInfo,i=this.isDragging;if(this.cancel(),!i||!n||!r)return;let{velocity:s}=n;this.startAnimation(s);let{onDragEnd:o}=this.getProps();o&&d.postRender(()=>o(r,n))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:r}=this.getProps();!r&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,r){let{drag:n}=this.getProps();if(!r||!r2(t,n,this.currentDirection))return;let i=this.getAxisMotionValue(t),s=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(s=function(t,e,r){let{min:n,max:i}=e;return void 0!==n&&t<n?t=r?tS(n,t,r.min):Math.max(t,n):void 0!==i&&t>i&&(t=r?tS(i,t,r.max):Math.min(t,i)),t}(s,this.constraints[t],this.elastic[t])),i.set(s)}resolveConstraints(){var t;let{dragConstraints:e,dragElastic:r}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null==(t=this.visualElement.projection)?void 0:t.layout,i=this.constraints;e&&rN(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&n?this.constraints=function(t,e){let{top:r,left:n,bottom:i,right:s}=e;return{x:rG(t.x,n,s),y:rG(t.y,r,i)}}(n.layoutBox,e):this.constraints=!1,this.elastic=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:.35;return!1===t?t=0:!0===t&&(t=.35),{x:rJ(t,"left","right"),y:rJ(t,"top","bottom")}}(r),i!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&rj(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let r={};return void 0!==e.min&&(r.min=e.min-t.min),void 0!==e.max&&(r.max=e.max-t.min),r}(n.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:r}=this.getProps();if(!e||!rN(e))return!1;let n=e.current;$(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:i}=this.visualElement;if(!i||!i.layout)return!1;let s=function(t,e,r){let n=rU(t,r),{scroll:i}=e;return i&&(rq(n.x,i.offset.x),rq(n.y,i.offset.y)),n}(n,i.root,this.visualElement.getTransformPagePoint()),o=(t=i.layout.layoutBox,{x:rZ(t.x,s.x),y:rZ(t.y,s.y)});if(r){let t=r(function(t){let{x:e,y:r}=t;return{top:r.min,right:e.max,bottom:r.max,left:e.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=rb(t))}return o}startAnimation(t){let{drag:e,dragMomentum:r,dragElastic:n,dragTransition:i,dragSnapToOrigin:s,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(rj(o=>{if(!r2(o,e,this.currentDirection))return;let u=a&&a[o]||{};s&&(u={min:0,max:0});let l={type:"inertia",velocity:r?t[o]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...i,...u};return this.startAxisValueAnimation(o,l)})).then(o)}startAxisValueAnimation(t,e){let r=this.getAxisMotionValue(t);return I(this.visualElement,t),r.start(e8(t,r,0,e,this.visualElement,!1))}stopAnimation(){rj(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){rj(t=>{var e;return null==(e=this.getAxisMotionValue(t).animation)?void 0:e.pause()})}getAnimationState(t){var e;return null==(e=this.getAxisMotionValue(t).animation)?void 0:e.state}getAxisMotionValue(t){let e="_drag".concat(t.toUpperCase()),r=this.visualElement.getProps();return r[e]||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){rj(e=>{let{drag:r}=this.getProps();if(!r2(e,r,this.currentDirection))return;let{projection:n}=this.visualElement,i=this.getAxisMotionValue(e);if(n&&n.layout){let{min:r,max:s}=n.layout.layoutBox[e];i.set(t[e]-tS(r,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:r}=this.visualElement;if(!rN(e)||!r||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};rj(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let r=e.get();n[t]=function(t,e){let r=.5,n=rw(t),i=rw(e);return i>n?r=es(e.min,e.max-n,t.min):n>i&&(r=es(t.min,t.max-i,e.min)),_(0,1,r)}({min:r,max:r},this.constraints[t])}});let{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),rj(e=>{if(!r2(e,t,null))return;let r=this.getAxisMotionValue(e),{min:i,max:s}=this.constraints[e];r.set(tS(i,s,n[e]))})}addListeners(){if(!this.visualElement.current)return;r0.set(this.visualElement,this);let t=rv(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:r=!0}=this.getProps();e&&r&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();rN(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:r}=this.visualElement,n=r.addEventListener("measure",e);r&&!r.layout&&(r.root&&r.root.updateScroll(),r.updateLayout()),d.read(e);let i=rd(window,"resize",()=>this.scalePositionWithinConstraints()),s=r.addEventListener("didUpdate",t=>{let{delta:e,hasLayoutChanged:r}=t;this.isDragging&&r&&(rj(t=>{let r=this.getAxisMotionValue(t);r&&(this.originPoint[t]+=e[t].translate,r.set(r.get()+e[t].translate))}),this.visualElement.render())});return()=>{i(),t(),n(),s&&s()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:r=!1,dragPropagation:n=!1,dragConstraints:i=!1,dragElastic:s=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:r,dragPropagation:n,dragConstraints:i,dragElastic:s,dragMomentum:o}}constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=rM(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=t}}function r2(t,e,r){return(!0===e||e===t)&&(null===r||r===t)}class r3 extends rl{mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||l}unmount(){this.removeGroupControls(),this.removeListeners()}constructor(t){super(t),this.removeGroupControls=l,this.removeListeners=l,this.controls=new r1(t)}}let r5=t=>(e,r)=>{t&&d.postRender(()=>t(e,r))};class r6 extends rl{onPointerDown(t){this.session=new rW(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:r$(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:r,onPanEnd:n}=this.node.getProps();return{onSessionStart:r5(t),onStart:r5(e),onMove:r,onEnd:(t,e)=>{delete this.session,n&&d.postRender(()=>n(t,e))}}}mount(){this.removePointerDownListener=rv(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}constructor(){super(...arguments),this.removePointerDownListener=l}}var r8=r(5155);let{schedule:r4}=p(queueMicrotask,!1);var r9=r(2115),r7=r(2082),nt=r(869);let ne=(0,r9.createContext)({}),nr={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function nn(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let ni={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!tl.test(t))return t;else t=parseFloat(t);let r=nn(t,e.target.x),n=nn(t,e.target.y);return"".concat(r,"% ").concat(n,"%")}},ns={},no=!1;class na extends r9.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:r,layoutId:n}=this.props,{projection:i}=t;for(let t in nl)ns[t]=nl[t],z(t)&&(ns[t].isCSSVariable=!0);i&&(e.group&&e.group.add(i),r&&r.register&&n&&r.register(i),no&&i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),nr.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:r,drag:n,isPresent:i}=this.props,{projection:s}=r;return s&&(s.isPresent=i,no=!0,n||t.layoutDependency!==e||void 0===e||t.isPresent!==i?s.willUpdate():this.safeToRemove(),t.isPresent!==i&&(i?s.promote():s.relegate()||d.postRender(()=>{let t=s.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),r4.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:r}=this.props,{projection:n}=t;n&&(n.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(n),r&&r.deregister&&r.deregister(n))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function nu(t){let[e,r]=(0,r7.xQ)(),n=(0,r9.useContext)(nt.L);return(0,r8.jsx)(na,{...t,layoutGroup:n,switchLayoutGroup:(0,r9.useContext)(ne),isPresent:e,safeToRemove:r})}let nl={borderRadius:{...ni,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ni,borderTopRightRadius:ni,borderBottomLeftRadius:ni,borderBottomRightRadius:ni,boxShadow:{correct:(t,e)=>{let{treeScale:r,projectionDelta:n}=e,i=tT.parse(t);if(i.length>5)return t;let s=tT.createTransformer(t),o=+("number"!=typeof i[0]),a=n.x.scale*r.x,u=n.y.scale*r.y;i[0+o]/=a,i[1+o]/=u;let l=tS(a,u,.5);return"number"==typeof i[2+o]&&(i[2+o]/=l),"number"==typeof i[3+o]&&(i[3+o]/=l),s(i)}}};var nh=r(6983);function nc(t){return(0,nh.G)(t)&&"ownerSVGElement"in t}let nf=(t,e)=>t.depth-e.depth;class np{add(t){x(this.children,t),this.isDirty=!0}remove(t){E(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(nf),this.isDirty=!1,this.children.forEach(t)}constructor(){this.children=[],this.isDirty=!1}}function nd(t){return O(t)?t.get():t}let nm=["TopLeft","TopRight","BottomLeft","BottomRight"],ny=nm.length,ng=t=>"string"==typeof t?parseFloat(t):t,nv=t=>"number"==typeof t||tl.test(t);function nb(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let nw=nE(0,.5,t7),nx=nE(.5,.95,l);function nE(t,e,r){return n=>n<t?0:n>e?1:r(es(t,e,n))}function nT(t,e){t.min=e.min,t.max=e.max}function nC(t,e){nT(t.x,e.x),nT(t.y,e.y)}function nA(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function nS(t,e,r,n,i){return t-=e,t=n+1/r*(t-n),void 0!==i&&(t=n+1/i*(t-n)),t}function nP(t,e,r,n,i){let[s,o,a]=r;!function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.5,i=arguments.length>4?arguments[4]:void 0,s=arguments.length>5&&void 0!==arguments[5]?arguments[5]:t,o=arguments.length>6&&void 0!==arguments[6]?arguments[6]:t;if(tu.test(e)&&(e=parseFloat(e),e=tS(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=tS(s.min,s.max,n);t===s&&(a-=e),t.min=nS(t.min,e,r,a,i),t.max=nS(t.max,e,r,a,i)}(t,e[s],e[o],e[a],e.scale,n,i)}let nR=["x","scaleX","originX"],nM=["y","scaleY","originY"];function nj(t,e,r,n){nP(t.x,e,nR,r?r.x:void 0,n?n.x:void 0),nP(t.y,e,nM,r?r.y:void 0,n?n.y:void 0)}function nO(t){return 0===t.translate&&1===t.scale}function nI(t){return nO(t.x)&&nO(t.y)}function nk(t,e){return t.min===e.min&&t.max===e.max}function nD(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function nV(t,e){return nD(t.x,e.x)&&nD(t.y,e.y)}function nL(t){return rw(t.x)/rw(t.y)}function n_(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class nq{add(t){x(this.members,t),t.scheduleRender()}remove(t){if(E(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,r=this.members.findIndex(e=>t===e);if(0===r)return!1;for(let t=r;t>=0;t--){let r=this.members[t];if(!1!==r.isPresent){e=r;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,e&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:n}=t.options;!1===n&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:r}=t;e.onExitComplete&&e.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}constructor(){this.members=[]}}let nF={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},nB=["","X","Y","Z"],nU=0;function n$(t,e,r,n){let{latestValues:i}=e;i[t]&&(r[t]=i[t],e.setStaticValue(t,0),n&&(n[t]=0))}function nN(t){let{attachResizeListener:e,defaultParent:r,measureScroll:n,checkIsScrollRoot:i,resetTransform:s}=t;return class{addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new T),this.eventHandlers.get(t).add(e)}notifyListeners(t){for(var e=arguments.length,r=Array(e>1?e-1:0),n=1;n<e;n++)r[n-1]=arguments[n];let i=this.eventHandlers.get(t);i&&i.notify(...r)}hasListeners(t){return this.eventHandlers.has(t)}mount(t){if(this.instance)return;this.isSVG=nc(t)&&!(nc(t)&&"svg"===t.tagName),this.instance=t;let{layoutId:r,layout:n,visualElement:i}=this.options;if(i&&!i.current&&i.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(n||r)&&(this.isLayoutDirty=!0),e){let r,n=0,i=()=>this.root.updateBlockedByResize=!1;d.read(()=>{n=window.innerWidth}),e(t,()=>{let t=window.innerWidth;t!==n&&(n=t,this.root.updateBlockedByResize=!0,r&&r(),r=function(t,e){let r=A.now(),n=i=>{let{timestamp:s}=i,o=s-r;o>=250&&(m(n),t(o-e))};return d.setup(n,!0),()=>m(n)}(i,250),nr.hasAnimatedSinceResize&&(nr.hasAnimatedSinceResize=!1,this.nodes.forEach(nJ)))})}r&&this.root.registerSharedNode(r,this),!1!==this.options.animate&&i&&(r||n)&&this.addEventListener("didUpdate",t=>{let{delta:e,hasLayoutChanged:r,hasRelativeLayoutChanged:n,layout:s}=t;if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let o=this.options.transition||i.getDefaultTransition()||n8,{onLayoutAnimationStart:a,onLayoutAnimationComplete:l}=i.getProps(),h=!this.targetLayout||!nV(this.targetLayout,s),c=!r&&n;if(this.options.layoutRoot||this.resumeFrom||c||r&&(h||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let t={...u(o,"layout"),onPlay:a,onComplete:l};(i.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t),this.setAnimationOrigin(e,c)}else r||nJ(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=s})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),m(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(n1),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(){let t=!(arguments.length>0)||void 0===arguments[0]||arguments[0];if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:r}=e.options;if(!r)return;let n=r.props[D];if(window.MotionHasOptimisedAnimation(n,"transform")){let{layout:t,layoutId:r}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",d,!(t||r))}let{parent:i}=e;i&&!i.hasCheckedOptimisedAppear&&t(i)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:r}=this.options;if(void 0===e&&!r)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(nK);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(nG);this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(nZ),this.nodes.forEach(nz),this.nodes.forEach(nW)):this.nodes.forEach(nG),this.clearAllSnapshots();let t=A.now();y.delta=_(0,1e3/60,t-y.timestamp),y.timestamp=t,y.isProcessing=!0,g.update.process(y),g.preRender.process(y),g.render.process(y),y.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,r4.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(nX),this.sharedNodes.forEach(n2)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,d.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){d.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||rw(this.snapshot.measuredBox.x)||rw(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=rM(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"measure",e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=i(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!s)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!nI(this.projectionDelta),r=this.getTransformTemplate(),n=r?r(this.latestValues,""):void 0,i=n!==this.prevTransformTemplateValue;t&&this.instance&&(e||rk(this.latestValues)||i)&&(s(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(){var t;let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0],r=this.measurePageBox(),n=this.removeElementScroll(r);return e&&(n=this.removeTransform(n)),n7((t=n).x),n7(t.y),{animationId:this.root.animationId,measuredBox:r,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){var t;let{visualElement:e}=this.options;if(!e)return rM();let r=e.measureViewportBox();if(!((null==(t=this.scroll)?void 0:t.wasRoot)||this.path.some(ie))){let{scroll:t}=this.root;t&&(rq(r.x,t.offset.x),rq(r.y,t.offset.y))}return r}removeElementScroll(t){var e;let r=rM();if(nC(r,t),null==(e=this.scroll)?void 0:e.wasRoot)return r;for(let e=0;e<this.path.length;e++){let n=this.path[e],{scroll:i,options:s}=n;n!==this.root&&i&&s.layoutScroll&&(i.wasRoot&&nC(r,t),rq(r.x,i.offset.x),rq(r.y,i.offset.y))}return r}applyTransform(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=rM();nC(r,t);for(let t=0;t<this.path.length;t++){let n=this.path[t];!e&&n.options.layoutScroll&&n.scroll&&n!==n.root&&rB(r,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),rk(n.latestValues)&&rB(r,n.latestValues)}return rk(this.latestValues)&&rB(r,this.latestValues),r}removeTransform(t){let e=rM();nC(e,t);for(let t=0;t<this.path.length;t++){let r=this.path[t];if(!r.instance||!rk(r.latestValues))continue;rI(r.latestValues)&&r.updateSnapshot();let n=rM();nC(n,r.measurePageBox()),nj(e,r.latestValues,r.snapshot?r.snapshot.layoutBox:void 0,n)}return rk(this.latestValues)&&nj(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==y.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(){var t,e,r,n;let i=arguments.length>0&&void 0!==arguments[0]&&arguments[0],s=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=s.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=s.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=s.isSharedProjectionDirty);let o=!!this.resumingFrom||this!==s;if(!(i||o&&this.isSharedProjectionDirty||this.isProjectionDirty||(null==(t=this.parent)?void 0:t.isProjectionDirty)||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:a,layoutId:u}=this.options;if(this.layout&&(a||u)){if(this.resolvedRelativeTargetAt=y.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=rM(),this.relativeTargetOrigin=rM(),rA(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),nC(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=rM(),this.targetWithTransforms=rM()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),e=this.target,r=this.relativeTarget,n=this.relativeParent.target,rT(e.x,r.x,n.x),rT(e.y,r.y,n.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):nC(this.target,this.layout.layoutBox),r_(this.target,this.targetDelta)):nC(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=rM(),this.relativeTargetOrigin=rM(),rA(this.relativeTargetOrigin,this.target,t.target),nC(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}f.value&&nF.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||rI(this.parent.latestValues)||rD(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var t;let e=this.getLead(),r=!!this.resumingFrom||this!==e,n=!0;if((this.isProjectionDirty||(null==(t=this.parent)?void 0:t.isProjectionDirty))&&(n=!1),r&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(n=!1),this.resolvedRelativeTargetAt===y.timestamp&&(n=!1),n)return;let{layout:i,layoutId:s}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(i||s))return;nC(this.layoutCorrected,this.layout.layoutBox);let o=this.treeScale.x,a=this.treeScale.y;!function(t,e,r){let n,i,s=arguments.length>3&&void 0!==arguments[3]&&arguments[3],o=r.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){i=(n=r[a]).projectionDelta;let{visualElement:o}=n.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(s&&n.options.layoutScroll&&n.scroll&&n!==n.root&&rB(t,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),i&&(e.x*=i.x.scale,e.y*=i.y.scale,r_(t,i)),s&&rk(n.latestValues)&&rB(t,n.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,r),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=rM());let{target:u}=e;if(!u){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(nA(this.prevProjectionDelta.x,this.projectionDelta.x),nA(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),rE(this.projectionDelta,this.layoutCorrected,u,this.latestValues),this.treeScale.x===o&&this.treeScale.y===a&&n_(this.projectionDelta.x,this.prevProjectionDelta.x)&&n_(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",u)),f.value&&nF.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(){var t;let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0];if(null==(t=this.options.visualElement)||t.scheduleRender(),e){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=rP(),this.projectionDelta=rP(),this.projectionDeltaWithTransform=rP()}setAnimationOrigin(t){let e,r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=this.snapshot,i=n?n.latestValues:{},s={...this.latestValues},o=rP();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!r;let a=rM(),u=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),l=this.getStack(),h=!l||l.members.length<=1,c=!!(u&&!h&&!0===this.options.crossfade&&!this.path.some(n6));this.animationProgress=0,this.mixTargetDelta=r=>{let n=r/1e3;if(n3(o.x,t.x,n),n3(o.y,t.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var l,f,p,d,m,y;rA(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,d=this.relativeTargetOrigin,m=a,y=n,n5(p.x,d.x,m.x,y),n5(p.y,d.y,m.y,y),e&&(l=this.relativeTarget,f=e,nk(l.x,f.x)&&nk(l.y,f.y))&&(this.isProjectionDirty=!1),e||(e=rM()),nC(e,this.relativeTarget)}u&&(this.animationValues=s,function(t,e,r,n,i,s){var o,a,u,l;i?(t.opacity=tS(0,null!=(o=r.opacity)?o:1,nw(n)),t.opacityExit=tS(null!=(a=e.opacity)?a:1,0,nx(n))):s&&(t.opacity=tS(null!=(u=e.opacity)?u:1,null!=(l=r.opacity)?l:1,n));for(let i=0;i<ny;i++){let s="border".concat(nm[i],"Radius"),o=nb(e,s),a=nb(r,s);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||nv(o)===nv(a)?(t[s]=Math.max(tS(ng(o),ng(a),n),0),(tu.test(a)||tu.test(o))&&(t[s]+="%")):t[s]=a)}(e.rotate||r.rotate)&&(t.rotate=tS(e.rotate||0,r.rotate||0,n))}(s,i,this.latestValues,n,c,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){var e,r,n;this.notifyListeners("animationStart"),null==(e=this.currentAnimation)||e.stop(),null==(n=this.resumingFrom)||null==(r=n.currentAnimation)||r.stop(),this.pendingAnimation&&(m(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=d.update(()=>{nr.hasAnimatedSinceResize=!0,B.layout++,this.motionValue||(this.motionValue=M(0)),this.currentAnimation=function(t,e,r){let n=O(t)?t:M(t);return n.start(e8("",n,e,r)),n.animation}(this.motionValue,[0,1e3],{...t,velocity:0,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{B.layout--},onComplete:()=>{B.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:r,layout:n,latestValues:i}=t;if(e&&r&&n){if(this!==t&&this.layout&&n&&it(this.options.animationType,this.layout.layoutBox,n.layoutBox)){r=this.target||rM();let e=rw(this.layout.layoutBox.x);r.x.min=t.target.x.min,r.x.max=r.x.min+e;let n=rw(this.layout.layoutBox.y);r.y.min=t.target.y.min,r.y.max=r.y.min+n}nC(e,r),rB(e,i),rE(this.projectionDeltaWithTransform,this.layoutCorrected,e,i)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new nq),this.sharedNodes.get(t).add(e);let r=e.options.initialPromotionConfig;e.promote({transition:r?r.transition:void 0,preserveFollowOpacity:r&&r.shouldPreserveFollowOpacity?r.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){var t;let{layoutId:e}=this.options;return e&&(null==(t=this.getStack())?void 0:t.lead)||this}getPrevLead(){var t;let{layoutId:e}=this.options;return e?null==(t=this.getStack())?void 0:t.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote(){let{needsReset:t,transition:e,preserveFollowOpacity:r}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=this.getStack();n&&n.promote(this,r),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:r}=t;if((r.z||r.rotate||r.rotateX||r.rotateY||r.rotateZ||r.skewX||r.skewY)&&(e=!0),!e)return;let n={};r.z&&n$("z",t,n,this.animationValues);for(let e=0;e<nB.length;e++)n$("rotate".concat(nB[e]),t,n,this.animationValues),n$("skew".concat(nB[e]),t,n,this.animationValues);for(let e in t.render(),n)t.setStaticValue(e,n[e]),this.animationValues&&(this.animationValues[e]=n[e]);t.scheduleRender()}applyProjectionStyles(t,e){if(!this.instance||this.isSVG)return;if(!this.isVisible){t.visibility="hidden";return}let r=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,t.visibility="",t.opacity="",t.pointerEvents=nd(null==e?void 0:e.pointerEvents)||"",t.transform=r?r(this.latestValues,""):"none";return}let n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target){this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=nd(null==e?void 0:e.pointerEvents)||""),this.hasProjected&&!rk(this.latestValues)&&(t.transform=r?r({},""):"none",this.hasProjected=!1);return}t.visibility="";let i=n.animationValues||n.latestValues;this.applyTransformsToTarget();let s=function(t,e,r){let n="",i=t.x.translate/e.x,s=t.y.translate/e.y,o=(null==r?void 0:r.z)||0;if((i||s||o)&&(n="translate3d(".concat(i,"px, ").concat(s,"px, ").concat(o,"px) ")),(1!==e.x||1!==e.y)&&(n+="scale(".concat(1/e.x,", ").concat(1/e.y,") ")),r){let{transformPerspective:t,rotate:e,rotateX:i,rotateY:s,skewX:o,skewY:a}=r;t&&(n="perspective(".concat(t,"px) ").concat(n)),e&&(n+="rotate(".concat(e,"deg) ")),i&&(n+="rotateX(".concat(i,"deg) ")),s&&(n+="rotateY(".concat(s,"deg) ")),o&&(n+="skewX(".concat(o,"deg) ")),a&&(n+="skewY(".concat(a,"deg) "))}let a=t.x.scale*e.x,u=t.y.scale*e.y;return(1!==a||1!==u)&&(n+="scale(".concat(a,", ").concat(u,")")),n||"none"}(this.projectionDeltaWithTransform,this.treeScale,i);r&&(s=r(i,s)),t.transform=s;let{x:o,y:a}=this.projectionDelta;if(t.transformOrigin="".concat(100*o.origin,"% ").concat(100*a.origin,"% 0"),n.animationValues){var u,l;t.opacity=n===this?null!=(l=null!=(u=i.opacity)?u:this.latestValues.opacity)?l:1:this.preserveOpacity?this.latestValues.opacity:i.opacityExit}else t.opacity=n===this?void 0!==i.opacity?i.opacity:"":void 0!==i.opacityExit?i.opacityExit:0;for(let e in ns){if(void 0===i[e])continue;let{correct:r,applyTo:o,isCSSVariable:a}=ns[e],u="none"===s?i[e]:r(i[e],n);if(o){let e=o.length;for(let r=0;r<e;r++)t[o[r]]=u}else a?this.options.visualElement.renderState.vars[e]=u:t[e]=u}this.options.layoutId&&(t.pointerEvents=n===this?nd(null==e?void 0:e.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>{var e;return null==(e=t.currentAnimation)?void 0:e.stop()}),this.root.nodes.forEach(nK),this.root.sharedNodes.clear()}constructor(t={},e=null==r?void 0:r()){this.id=nU++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,f.value&&(nF.nodes=nF.calculatedTargetDeltas=nF.calculatedProjections=0),this.nodes.forEach(nH),this.nodes.forEach(nQ),this.nodes.forEach(n0),this.nodes.forEach(nY),f.addProjectionMetrics&&f.addProjectionMetrics(nF)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=e?e.root||e:this,this.path=e?[...e.path,e]:[],this.parent=e,this.depth=e?e.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new np)}}}function nz(t){t.updateLayout()}function nW(t){var e;let r=(null==(e=t.resumeFrom)?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&r&&t.hasListeners("didUpdate")){let{layoutBox:e,measuredBox:n}=t.layout,{animationType:i}=t.options,s=r.source!==t.layout.source;"size"===i?rj(t=>{let n=s?r.measuredBox[t]:r.layoutBox[t],i=rw(n);n.min=e[t].min,n.max=n.min+i}):it(i,r.layoutBox,e)&&rj(n=>{let i=s?r.measuredBox[n]:r.layoutBox[n],o=rw(e[n]);i.max=i.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[n].max=t.relativeTarget[n].min+o)});let o=rP();rE(o,e,r.layoutBox);let a=rP();s?rE(a,t.applyTransform(n,!0),r.measuredBox):rE(a,e,r.layoutBox);let u=!nI(o),l=!1;if(!t.resumeFrom){let n=t.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:i,layout:s}=n;if(i&&s){let o=rM();rA(o,r.layoutBox,i.layoutBox);let a=rM();rA(a,e,s.layoutBox),nV(o,a)||(l=!0),n.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=n)}}}t.notifyListeners("didUpdate",{layout:e,snapshot:r,delta:a,layoutDelta:o,hasLayoutChanged:u,hasRelativeLayoutChanged:l})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function nH(t){f.value&&nF.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function nY(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function nX(t){t.clearSnapshot()}function nK(t){t.clearMeasurements()}function nG(t){t.isLayoutDirty=!1}function nZ(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function nJ(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function nQ(t){t.resolveTargetDelta()}function n0(t){t.calcProjection()}function n1(t){t.resetSkewAndRotation()}function n2(t){t.removeLeadSnapshot()}function n3(t,e,r){t.translate=tS(e.translate,0,r),t.scale=tS(e.scale,1,r),t.origin=e.origin,t.originPoint=e.originPoint}function n5(t,e,r,n){t.min=tS(e.min,r.min,n),t.max=tS(e.max,r.max,n)}function n6(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let n8={duration:.45,ease:[.4,0,.1,1]},n4=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),n9=n4("applewebkit/")&&!n4("chrome/")?Math.round:l;function n7(t){t.min=n9(t.min),t.max=n9(t.max)}function it(t,e,r){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(nL(e)-nL(r)))}function ie(t){var e;return t!==t.root&&(null==(e=t.scroll)?void 0:e.wasRoot)}let ir=nN({attachResizeListener:(t,e)=>rd(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),ii={current:void 0},is=nN({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!ii.current){let t=new ir({});t.mount(window),t.setOptions({layoutScroll:!0}),ii.current=t}return ii.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function io(t,e){let r=function(t,e,r){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document,r=(void 0)??e.querySelectorAll(t);return r?Array.from(r):[]}return Array.from(t)}(t),n=new AbortController;return[r,{passive:!0,...e,signal:n.signal},()=>n.abort()]}function ia(t){return!("touch"===t.pointerType||rp.x||rp.y)}function iu(t,e,r){let{props:n}=t;t.animationState&&n.whileHover&&t.animationState.setActive("whileHover","Start"===r);let i=n["onHover"+r];i&&d.postRender(()=>i(e,ry(e)))}class il extends rl{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,r={}){let[n,i,s]=io(t,r),o=t=>{if(!ia(t))return;let{target:r}=t,n=e(r,t);if("function"!=typeof n||!r)return;let s=t=>{ia(t)&&(n(t),r.removeEventListener("pointerleave",s))};r.addEventListener("pointerleave",s,i)};return n.forEach(t=>{t.addEventListener("pointerenter",o,i)}),s}(t,(t,e)=>(iu(this.node,e,"Start"),t=>iu(this.node,t,"End"))))}unmount(){}}class ih extends rl{onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=L(rd(this.node.current,"focus",()=>this.onFocus()),rd(this.node.current,"blur",()=>this.onBlur()))}unmount(){}constructor(){super(...arguments),this.isActive=!1}}let ic=(t,e)=>!!e&&(t===e||ic(t,e.parentElement)),ip=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),id=new WeakSet;function im(t){return e=>{"Enter"===e.key&&t(e)}}function iy(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let ig=(t,e)=>{let r=t.currentTarget;if(!r)return;let n=im(()=>{if(id.has(r))return;iy(r,"down");let t=im(()=>{iy(r,"up")});r.addEventListener("keyup",t,e),r.addEventListener("blur",()=>iy(r,"cancel"),e)});r.addEventListener("keydown",n,e),r.addEventListener("blur",()=>r.removeEventListener("keydown",n),e)};function iv(t){return rm(t)&&!(rp.x||rp.y)}function ib(t,e,r){let{props:n}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&n.whileTap&&t.animationState.setActive("whileTap","Start"===r);let i=n["onTap"+("End"===r?"":r)];i&&d.postRender(()=>i(e,ry(e)))}class iw extends rl{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,r={}){let[n,i,s]=io(t,r),o=t=>{let n=t.currentTarget;if(!iv(t))return;id.add(n);let s=e(n,t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",u),id.has(n)&&id.delete(n),iv(t)&&"function"==typeof s&&s(t,{success:e})},a=t=>{o(t,n===window||n===document||r.useGlobalTarget||ic(n,t.target))},u=t=>{o(t,!1)};window.addEventListener("pointerup",a,i),window.addEventListener("pointercancel",u,i)};return n.forEach(t=>{((r.useGlobalTarget?window:t).addEventListener("pointerdown",o,i),(0,eG.s)(t))&&(t.addEventListener("focus",t=>ig(t,i)),ip.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),s}(t,(t,e)=>(ib(this.node,e,"Start"),(t,e)=>{let{success:r}=e;return ib(this.node,t,r?"End":"Cancel")}),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let ix=new WeakMap,iE=new WeakMap,iT=t=>{let e=ix.get(t.target);e&&e(t)},iC=t=>{t.forEach(iT)},iA={some:0,all:1};class iS extends rl{startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:r,amount:n="some",once:i}=t,s={root:e?e.current:void 0,rootMargin:r,threshold:"number"==typeof n?n:iA[n]};return function(t,e,r){let n=function(t){let{root:e,...r}=t,n=e||document;iE.has(n)||iE.set(n,{});let i=iE.get(n),s=JSON.stringify(r);return i[s]||(i[s]=new IntersectionObserver(iC,{root:e,...r})),i[s]}(e);return ix.set(t,r),n.observe(t),()=>{ix.delete(t),n.unobserve(t)}}(this.node.current,s,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,i&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:r,onViewportLeave:n}=this.node.getProps(),s=e?r:n;s&&s(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function(t){let{viewport:e={}}=t,{viewport:r={}}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return t=>e[t]!==r[t]}(t,e))&&this.startObserver()}unmount(){}constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}}let iP=(0,r9.createContext)({strict:!1});var iR=r(1508);let iM=(0,r9.createContext)({});function ij(t){return i(t.animate)||rn.some(e=>re(t[e]))}function iO(t){return!!(ij(t)||t.variants)}function iI(t){return Array.isArray(t)?t.join(" "):t}var ik=r(8972);let iD={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},iV={};for(let t in iD)iV[t]={isEnabled:e=>iD[t].some(t=>!!e[t])};let iL=Symbol.for("motionComponentSymbol");var i_=r(845),iq=r(7494);function iF(t,e){let{layout:r,layoutId:n}=e;return b.has(t)||t.startsWith("origin")||(r||void 0!==n)&&(!!ns[t]||"opacity"===t)}let iB=(t,e)=>e&&"number"==typeof t?e.transform(t):t,iU={...X,transform:Math.round},i$={borderWidth:tl,borderTopWidth:tl,borderRightWidth:tl,borderBottomWidth:tl,borderLeftWidth:tl,borderRadius:tl,radius:tl,borderTopLeftRadius:tl,borderTopRightRadius:tl,borderBottomRightRadius:tl,borderBottomLeftRadius:tl,width:tl,maxWidth:tl,height:tl,maxHeight:tl,top:tl,right:tl,bottom:tl,left:tl,padding:tl,paddingTop:tl,paddingRight:tl,paddingBottom:tl,paddingLeft:tl,margin:tl,marginTop:tl,marginRight:tl,marginBottom:tl,marginLeft:tl,backgroundPositionX:tl,backgroundPositionY:tl,rotate:ta,rotateX:ta,rotateY:ta,rotateZ:ta,scale:G,scaleX:G,scaleY:G,scaleZ:G,skew:ta,skewX:ta,skewY:ta,distance:tl,translateX:tl,translateY:tl,translateZ:tl,x:tl,y:tl,z:tl,perspective:tl,transformPerspective:tl,opacity:K,originX:tf,originY:tf,originZ:tl,zIndex:iU,fillOpacity:K,strokeOpacity:K,numOctaves:iU},iN={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},iz=v.length;function iW(t,e,r){let{style:n,vars:i,transformOrigin:s}=t,o=!1,a=!1;for(let t in e){let r=e[t];if(b.has(t)){o=!0;continue}if(z(t)){i[t]=r;continue}{let e=iB(r,i$[t]);t.startsWith("origin")?(a=!0,s[t]=e):n[t]=e}}if(!e.transform&&(o||r?n.transform=function(t,e,r){let n="",i=!0;for(let s=0;s<iz;s++){let o=v[s],a=t[o];if(void 0===a)continue;let u=!0;if(!(u="number"==typeof a?a===+!!o.startsWith("scale"):0===parseFloat(a))||r){let t=iB(a,i$[o]);if(!u){i=!1;let e=iN[o]||o;n+="".concat(e,"(").concat(t,") ")}r&&(e[o]=t)}}return n=n.trim(),r?n=r(e,i?"":n):i&&(n="none"),n}(e,t.transform,r):n.transform&&(n.transform="none")),a){let{originX:t="50%",originY:e="50%",originZ:r=0}=s;n.transformOrigin="".concat(t," ").concat(e," ").concat(r)}}let iH=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function iY(t,e,r){for(let n in e)O(e[n])||iF(n,r)||(t[n]=e[n])}let iX={offset:"stroke-dashoffset",array:"stroke-dasharray"},iK={offset:"strokeDashoffset",array:"strokeDasharray"};function iG(t,e,r,n,i){var s,o;let{attrX:a,attrY:u,attrScale:l,pathLength:h,pathSpacing:c=1,pathOffset:f=0,...p}=e;if(iW(t,p,n),r){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:d,style:m}=t;d.transform&&(m.transform=d.transform,delete d.transform),(m.transform||d.transformOrigin)&&(m.transformOrigin=null!=(s=d.transformOrigin)?s:"50% 50%",delete d.transformOrigin),m.transform&&(m.transformBox=null!=(o=null==i?void 0:i.transformBox)?o:"fill-box",delete d.transformBox),void 0!==a&&(d.x=a),void 0!==u&&(d.y=u),void 0!==l&&(d.scale=l),void 0!==h&&function(t,e){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,i=!(arguments.length>4)||void 0===arguments[4]||arguments[4];t.pathLength=1;let s=i?iX:iK;t[s.offset]=tl.transform(-n);let o=tl.transform(e),a=tl.transform(r);t[s.array]="".concat(o," ").concat(a)}(d,h,c,f,!1)}let iZ=()=>({...iH(),attrs:{}}),iJ=t=>"string"==typeof t&&"svg"===t.toLowerCase(),iQ=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function i0(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||iQ.has(t)}let i1=t=>!i0(t);try{!function(t){"function"==typeof t&&(i1=e=>e.startsWith("on")?!i0(e):t(e))}(require("@emotion/is-prop-valid").default)}catch(t){}let i2=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function i3(t){if("string"!=typeof t||t.includes("-"));else if(i2.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var i5=r(2885);let i6=t=>(e,r)=>{let n=(0,r9.useContext)(iM),s=(0,r9.useContext)(i_.t),a=()=>(function(t,e,r,n){let{scrapeMotionValuesFromProps:s,createRenderState:a}=t;return{latestValues:function(t,e,r,n){let s={},a=n(t,{});for(let t in a)s[t]=nd(a[t]);let{initial:u,animate:l}=t,h=ij(t),c=iO(t);e&&c&&!h&&!1!==t.inherit&&(void 0===u&&(u=e.initial),void 0===l&&(l=e.animate));let f=!!r&&!1===r.initial,p=(f=f||!1===u)?l:u;if(p&&"boolean"!=typeof p&&!i(p)){let e=Array.isArray(p)?p:[p];for(let r=0;r<e.length;r++){let n=o(t,e[r]);if(n){let{transitionEnd:t,transition:e,...r}=n;for(let t in r){let e=r[t];if(Array.isArray(e)){let t=f?e.length-1:0;e=e[t]}null!==e&&(s[t]=e)}for(let e in t)s[e]=t[e]}}}return s}(e,r,n,s),renderState:a()}})(t,e,n,s);return r?a():(0,i5.M)(a)};function i8(t,e,r){let{style:n}=t,i={};for(let o in n){var s;(O(n[o])||e.style&&O(e.style[o])||iF(o,t)||(null==r||null==(s=r.getValue(o))?void 0:s.liveStyle)!==void 0)&&(i[o]=n[o])}return i}let i4={useVisualState:i6({scrapeMotionValuesFromProps:i8,createRenderState:iH})};function i9(t,e,r){let n=i8(t,e,r);for(let r in t)(O(t[r])||O(e[r]))&&(n[-1!==v.indexOf(r)?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r]=t[r]);return n}let i7={useVisualState:i6({scrapeMotionValuesFromProps:i9,createRenderState:iZ})},st=t=>e=>e.test(t),se=[X,tl,tu,ta,tc,th,{test:t=>"auto"===t,parse:t=>t}],sr=t=>se.find(st(t)),sn=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),si=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,ss=t=>/^0[^.\s]+$/u.test(t),so=new Set(["brightness","contrast","saturate","opacity"]);function sa(t){let[e,r]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[n]=r.match(J)||[];if(!n)return t;let i=r.replace(n,""),s=+!!so.has(e);return n!==r&&(s*=100),e+"("+s+i+")"}let su=/\b([a-z-]*)\(.*?\)/gu,sl={...tT,getAnimatableNone:t=>{let e=t.match(su);return e?e.map(sa).join(" "):t}},sh={...i$,color:td,backgroundColor:td,outlineColor:td,fill:td,stroke:td,borderColor:td,borderTopColor:td,borderRightColor:td,borderBottomColor:td,borderLeftColor:td,filter:sl,WebkitFilter:sl},sc=t=>sh[t];function sf(t,e){let r=sc(t);return r!==sl&&(r=tT),r.getAnimatableNone?r.getAnimatableNone(e):void 0}let sp=new Set(["auto","none","0"]);class sd extends eV{constructor(t,e,r,n,i){super(t,e,r,n,i,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:r}=this;if(!e||!e.current)return;super.readKeyframes();for(let r=0;r<t.length;r++){let n=t[r];if("string"==typeof n&&H(n=n.trim())){let i=function t(e,r,n=1){$(n<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[i,s]=function(t){let e=si.exec(t);if(!e)return[,];let[,r,n,i]=e;return[`--${r??n}`,i]}(e);if(!i)return;let o=window.getComputedStyle(r).getPropertyValue(i);if(o){let t=o.trim();return sn(t)?parseFloat(t):t}return H(s)?t(s,r,n+1):s}(n,e.current);void 0!==i&&(t[r]=i),r===t.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!w.has(r)||2!==t.length)return;let[n,i]=t,s=sr(n),o=sr(i);if(s!==o)if(eA(s)&&eA(o))for(let e=0;e<t.length;e++){let r=t[e];"string"==typeof r&&(t[e]=parseFloat(r))}else eR[r]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,r=[];for(let e=0;e<t.length;e++){var n;(null===t[e]||("number"==typeof(n=t[e])?0===n:null===n||"none"===n||"0"===n||ss(n)))&&r.push(e)}r.length&&function(t,e,r){let n,i=0;for(;i<t.length&&!n;){let e=t[i];"string"==typeof e&&!sp.has(e)&&tb(e).values.length&&(n=t[i]),i++}if(n&&r)for(let i of e)t[i]=sf(r,n)}(t,r,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:r}=this;if(!t||!t.current)return;"height"===r&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=eR[r](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let n=e[e.length-1];void 0!==n&&t.getValue(r,n).jump(n,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:r}=this;if(!t||!t.current)return;let n=t.getValue(e);n&&n.jump(this.measuredOrigin,!1);let i=r.length-1,s=r[i];r[i]=eR[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==s&&void 0===this.finalKeyframe&&(this.finalKeyframe=s),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,r])=>{t.getValue(e).set(r)}),this.resolveNoneKeyframes()}}let sm=[...se,td,tT],sy=t=>sm.find(st(t)),sg={current:null},sv={current:!1},sb=new WeakMap,sw=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class sx{scrapeMotionValuesFromProps(t,e,r){return{}}mount(t){this.current=t,sb.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),sv.current||function(){if(sv.current=!0,ik.B)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>sg.current=t.matches;t.addEventListener("change",e),e()}else sg.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||sg.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),m(this.notifyUpdate),m(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let r;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let n=b.has(t);n&&this.onBindTransform&&this.onBindTransform();let i=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&d.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),s=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(r=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{i(),s(),r&&r(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in iV){let e=iV[t];if(!e)continue;let{isEnabled:r,Feature:n}=e;if(!this.features[t]&&n&&r(this.props)&&(this.features[t]=new n(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):rM()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<sw.length;e++){let r=sw[e];this.propEventSubscriptions[r]&&(this.propEventSubscriptions[r](),delete this.propEventSubscriptions[r]);let n=t["on"+r];n&&(this.propEventSubscriptions[r]=this.on(r,n))}this.prevMotionValues=function(t,e,r){for(let n in e){let i=e[n],s=r[n];if(O(i))t.addValue(n,i);else if(O(s))t.addValue(n,M(i,{owner:t}));else if(s!==i)if(t.hasValue(n)){let e=t.getValue(n);!0===e.liveStyle?e.jump(i):e.hasAnimated||e.set(i)}else{let e=t.getStaticValue(n);t.addValue(n,M(void 0!==e?e:i,{owner:t}))}}for(let n in r)void 0===e[n]&&t.removeValue(n);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let r=this.values.get(t);e!==r&&(r&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return void 0===r&&void 0!==e&&(r=M(null===e?void 0:e,{owner:this}),this.addValue(t,r)),r}readValue(t,e){var r;let n=void 0===this.latestValues[t]&&this.current?null!=(r=this.getBaseTargetFromProps(this.props,t))?r:this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=n&&("string"==typeof n&&(sn(n)||ss(n))?n=parseFloat(n):!sy(n)&&tT.test(e)&&(n=sf(t,e)),this.setBaseTarget(t,O(n)?n.get():n)),O(n)?n.get():n}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:r}=this.props;if("string"==typeof r||"object"==typeof r){var n;let i=o(this.props,r,null==(n=this.presenceContext)?void 0:n.custom);i&&(e=i[t])}if(r&&void 0!==e)return e;let i=this.getBaseTargetFromProps(this.props,t);return void 0===i||O(i)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:i}on(t,e){return this.events[t]||(this.events[t]=new T),this.events[t].add(e)}notify(t){for(var e=arguments.length,r=Array(e>1?e-1:0),n=1;n<e;n++)r[n-1]=arguments[n];this.events[t]&&this.events[t].notify(...r)}constructor({parent:t,props:e,presenceContext:r,reducedMotionConfig:n,blockInitialAnimation:i,visualState:s},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=eV,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=A.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,d.render(this.render,!1,!0))};let{latestValues:a,renderState:u}=s;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=u,this.parent=t,this.props=e,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=n,this.options=o,this.blockInitialAnimation=!!i,this.isControllingVariants=ij(e),this.isVariantNode=iO(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:l,...h}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in h){let e=h[t];void 0!==a[t]&&O(e)&&e.set(a[t],!1)}}}class sE extends sx{sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,e){let{vars:r,style:n}=e;delete r[t],delete n[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;O(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent="".concat(t))}))}constructor(){super(...arguments),this.KeyframeResolver=sd}}function sT(t,e,r,n){let i,{style:s,vars:o}=e,a=t.style;for(i in s)a[i]=s[i];for(i in null==n||n.applyProjectionStyles(a,r),o)a.setProperty(i,o[i])}class sC extends sE{readValueFromInstance(t,e){var r;if(b.has(e))return(null==(r=this.projection)?void 0:r.isProjecting)?ex(e):eT(t,e);{let r=window.getComputedStyle(t),n=(z(e)?r.getPropertyValue(e):r[e])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(t,e){let{transformPagePoint:r}=e;return rU(t,r)}build(t,e,r){iW(t,e,r.transformTemplate)}scrapeMotionValuesFromProps(t,e,r){return i8(t,e,r)}constructor(){super(...arguments),this.type="html",this.renderInstance=sT}}let sA=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class sS extends sE{getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(b.has(e)){let t=sc(e);return t&&t.default||0}return e=sA.has(e)?e:k(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,r){return i9(t,e,r)}build(t,e,r){iG(t,e,this.isSVGTag,r.transformTemplate,r.style)}renderInstance(t,e,r,n){for(let r in sT(t,e,void 0,n),e.attrs)t.setAttribute(sA.has(r)?r:k(r),e.attrs[r])}mount(t){this.isSVGTag=iJ(t.tagName),super.mount(t)}constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=rM}}let sP=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy(function(){for(var e=arguments.length,r=Array(e),n=0;n<e;n++)r[n]=arguments[n];return t(...r)},{get:(r,n)=>"create"===n?t:(e.has(n)||e.set(n,t(n)),e.get(n))})}((eX={animation:{Feature:rh},exit:{Feature:rf},inView:{Feature:iS},tap:{Feature:iw},focus:{Feature:ih},hover:{Feature:il},pan:{Feature:r6},drag:{Feature:r3,ProjectionNode:is,MeasureLayout:nu},layout:{ProjectionNode:is,MeasureLayout:nu}},eK=(t,e)=>i3(t)?new sS(e):new sC(e,{allowProjection:t!==r9.Fragment}),function(t){let{forwardMotionProps:e}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{forwardMotionProps:!1};return function(t){var e,r;let{preloadedFeatures:n,createVisualElement:i,useRender:s,useVisualState:o,Component:a}=t;function u(t,e){var r,n,u;let l,h={...(0,r9.useContext)(iR.Q),...t,layoutId:function(t){let{layoutId:e}=t,r=(0,r9.useContext)(nt.L).id;return r&&void 0!==e?r+"-"+e:e}(t)},{isStatic:c}=h,f=function(t){let{initial:e,animate:r}=function(t,e){if(ij(t)){let{initial:e,animate:r}=t;return{initial:!1===e||re(e)?e:void 0,animate:re(r)?r:void 0}}return!1!==t.inherit?e:{}}(t,(0,r9.useContext)(iM));return(0,r9.useMemo)(()=>({initial:e,animate:r}),[iI(e),iI(r)])}(t),p=o(t,c);if(!c&&ik.B){n=0,u=0,(0,r9.useContext)(iP).strict;let t=function(t){let{drag:e,layout:r}=iV;if(!e&&!r)return{};let n={...e,...r};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==r?void 0:r.isEnabled(t))?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(h);l=t.MeasureLayout,f.visualElement=function(t,e,r,n,i){var s,o,a,u;let{visualElement:l}=(0,r9.useContext)(iM),h=(0,r9.useContext)(iP),c=(0,r9.useContext)(i_.t),f=(0,r9.useContext)(iR.Q).reducedMotion,p=(0,r9.useRef)(null);n=n||h.renderer,!p.current&&n&&(p.current=n(t,{visualState:e,parent:l,props:r,presenceContext:c,blockInitialAnimation:!!c&&!1===c.initial,reducedMotionConfig:f}));let d=p.current,m=(0,r9.useContext)(ne);d&&!d.projection&&i&&("html"===d.type||"svg"===d.type)&&function(t,e,r,n){let{layoutId:i,layout:s,drag:o,dragConstraints:a,layoutScroll:u,layoutRoot:l,layoutCrossfade:h}=e;t.projection=new r(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:i,layout:s,alwaysMeasureLayout:!!o||a&&rN(a),visualElement:t,animationType:"string"==typeof s?s:"both",initialPromotionConfig:n,crossfade:h,layoutScroll:u,layoutRoot:l})}(p.current,r,i,m);let y=(0,r9.useRef)(!1);(0,r9.useInsertionEffect)(()=>{d&&y.current&&d.update(r,c)});let g=r[D],v=(0,r9.useRef)(!!g&&!(null==(s=(o=window).MotionHandoffIsComplete)?void 0:s.call(o,g))&&(null==(a=(u=window).MotionHasOptimisedAnimation)?void 0:a.call(u,g)));return(0,iq.E)(()=>{d&&(y.current=!0,window.MotionIsMounted=!0,d.updateFeatures(),r4.render(d.render),v.current&&d.animationState&&d.animationState.animateChanges())}),(0,r9.useEffect)(()=>{d&&(!v.current&&d.animationState&&d.animationState.animateChanges(),v.current&&(queueMicrotask(()=>{var t,e;null==(t=(e=window).MotionHandoffMarkAsComplete)||t.call(e,g)}),v.current=!1))}),d}(a,p,h,i,t.ProjectionNode)}return(0,r8.jsxs)(iM.Provider,{value:f,children:[l&&f.visualElement?(0,r8.jsx)(l,{visualElement:f.visualElement,...h}):null,s(a,t,(r=f.visualElement,(0,r9.useCallback)(t=>{t&&p.onMount&&p.onMount(t),r&&(t?r.mount(t):r.unmount()),e&&("function"==typeof e?e(t):rN(e)&&(e.current=t))},[r])),p,c,f.visualElement)]})}n&&function(t){for(let e in t)iV[e]={...iV[e],...t[e]}}(n),u.displayName="motion.".concat("string"==typeof a?a:"create(".concat(null!=(r=null!=(e=a.displayName)?e:a.name)?r:"",")"));let l=(0,r9.forwardRef)(u);return l[iL]=a,l}({...i3(t)?i7:i4,preloadedFeatures:eX,useRender:function(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return(e,r,n,i,s)=>{let{latestValues:o}=i,a=(i3(e)?function(t,e,r,n){let i=(0,r9.useMemo)(()=>{let r=iZ();return iG(r,e,iJ(n),t.transformTemplate,t.style),{...r.attrs,style:{...r.style}}},[e]);if(t.style){let e={};iY(e,t.style,t),i.style={...e,...i.style}}return i}:function(t,e){let r={},n=function(t,e){let r=t.style||{},n={};return iY(n,r,t),Object.assign(n,function(t,e){let{transformTemplate:r}=t;return(0,r9.useMemo)(()=>{let t=iH();return iW(t,e,r),Object.assign({},t.vars,t.style)},[e])}(t,e)),n}(t,e);return t.drag&&!1!==t.dragListener&&(r.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===t.drag?"none":"pan-".concat("x"===t.drag?"y":"x")),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(r.tabIndex=0),r.style=n,r})(r,o,s,e),u=function(t,e,r){let n={};for(let i in t)("values"!==i||"object"!=typeof t.values)&&(i1(i)||!0===r&&i0(i)||!e&&!i0(i)||t.draggable&&i.startsWith("onDrag"))&&(n[i]=t[i]);return n}(r,"string"==typeof e,t),l=e!==r9.Fragment?{...u,...a,ref:n}:{},{children:h}=r,c=(0,r9.useMemo)(()=>O(h)?h.get():h,[h]);return(0,r9.createElement)(e,{...l,children:c})}}(e),createVisualElement:eK,Component:t})}))},6654:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"useMergedRef",{enumerable:!0,get:function(){return i}});let n=r(2115);function i(t,e){let r=(0,n.useRef)(null),i=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let t=r.current;t&&(r.current=null,t());let e=i.current;e&&(i.current=null,e())}else t&&(r.current=s(t,n)),e&&(i.current=s(e,n))},[t,e])}function s(t,e){if("function"!=typeof t)return t.current=e,()=>{t.current=null};{let r=t(e);return"function"==typeof r?r:()=>t(null)}}("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},6874:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var r in e)Object.defineProperty(t,r,{enumerable:!0,get:e[r]})}(e,{default:function(){return y},useLinkStatus:function(){return v}});let n=r(6966),i=r(5155),s=n._(r(2115)),o=r(2757),a=r(5227),u=r(9818),l=r(6654),h=r(9991),c=r(5929);r(3230);let f=r(4930),p=r(2664),d=r(6634);function m(t){return"string"==typeof t?t:(0,o.formatUrl)(t)}function y(t){let e,r,n,[o,y]=(0,s.useOptimistic)(f.IDLE_LINK_STATUS),v=(0,s.useRef)(null),{href:b,as:w,children:x,prefetch:E=null,passHref:T,replace:C,shallow:A,scroll:S,onClick:P,onMouseEnter:R,onTouchStart:M,legacyBehavior:j=!1,onNavigate:O,ref:I,unstable_dynamicOnHover:k,...D}=t;e=x,j&&("string"==typeof e||"number"==typeof e)&&(e=(0,i.jsx)("a",{children:e}));let V=s.default.useContext(a.AppRouterContext),L=!1!==E,_=null===E?u.PrefetchKind.AUTO:u.PrefetchKind.FULL,{href:q,as:F}=s.default.useMemo(()=>{let t=m(b);return{href:t,as:w?m(w):t}},[b,w]);j&&(r=s.default.Children.only(e));let B=j?r&&"object"==typeof r&&r.ref:I,U=s.default.useCallback(t=>(null!==V&&(v.current=(0,f.mountLinkInstance)(t,q,V,_,L,y)),()=>{v.current&&((0,f.unmountLinkForCurrentNavigation)(v.current),v.current=null),(0,f.unmountPrefetchableInstance)(t)}),[L,q,V,_,y]),$={ref:(0,l.useMergedRef)(U,B),onClick(t){j||"function"!=typeof P||P(t),j&&r.props&&"function"==typeof r.props.onClick&&r.props.onClick(t),V&&(t.defaultPrevented||function(t,e,r,n,i,o,a){let{nodeName:u}=t.currentTarget;if(!("A"===u.toUpperCase()&&function(t){let e=t.currentTarget.getAttribute("target");return e&&"_self"!==e||t.metaKey||t.ctrlKey||t.shiftKey||t.altKey||t.nativeEvent&&2===t.nativeEvent.which}(t)||t.currentTarget.hasAttribute("download"))){if(!(0,p.isLocalURL)(e)){i&&(t.preventDefault(),location.replace(e));return}t.preventDefault(),s.default.startTransition(()=>{if(a){let t=!1;if(a({preventDefault:()=>{t=!0}}),t)return}(0,d.dispatchNavigateAction)(r||e,i?"replace":"push",null==o||o,n.current)})}}(t,q,F,v,C,S,O))},onMouseEnter(t){j||"function"!=typeof R||R(t),j&&r.props&&"function"==typeof r.props.onMouseEnter&&r.props.onMouseEnter(t),V&&L&&(0,f.onNavigationIntent)(t.currentTarget,!0===k)},onTouchStart:function(t){j||"function"!=typeof M||M(t),j&&r.props&&"function"==typeof r.props.onTouchStart&&r.props.onTouchStart(t),V&&L&&(0,f.onNavigationIntent)(t.currentTarget,!0===k)}};return(0,h.isAbsoluteUrl)(F)?$.href=F:j&&!T&&("a"!==r.type||"href"in r.props)||($.href=(0,c.addBasePath)(F)),n=j?s.default.cloneElement(r,$):(0,i.jsx)("a",{...D,...$,children:e}),(0,i.jsx)(g.Provider,{value:o,children:n})}r(3180);let g=(0,s.createContext)(f.IDLE_LINK_STATUS),v=()=>(0,s.useContext)(g);("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},6983:(t,e,r)=>{"use strict";function n(t){return"object"==typeof t&&null!==t}r.d(e,{G:()=>n})},7351:(t,e,r)=>{"use strict";r.d(e,{s:()=>i});var n=r(6983);function i(t){return(0,n.G)(t)&&"offsetHeight"in t}},7494:(t,e,r)=>{"use strict";r.d(e,{E:()=>i});var n=r(2115);let i=r(8972).B?n.useLayoutEffect:n.useEffect},8859:(t,e)=>{"use strict";function r(t){let e={};for(let[r,n]of t.entries()){let t=e[r];void 0===t?e[r]=n:Array.isArray(t)?t.push(n):e[r]=[t,n]}return e}function n(t){return"string"==typeof t?t:("number"!=typeof t||isNaN(t))&&"boolean"!=typeof t?"":String(t)}function i(t){let e=new URLSearchParams;for(let[r,i]of Object.entries(t))if(Array.isArray(i))for(let t of i)e.append(r,n(t));else e.set(r,n(i));return e}function s(t){for(var e=arguments.length,r=Array(e>1?e-1:0),n=1;n<e;n++)r[n-1]=arguments[n];for(let e of r){for(let r of e.keys())t.delete(r);for(let[r,n]of e.entries())t.append(r,n)}return t}Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var r in e)Object.defineProperty(t,r,{enumerable:!0,get:e[r]})}(e,{assign:function(){return s},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return i}})},8883:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},8972:(t,e,r)=>{"use strict";r.d(e,{B:()=>n});let n="undefined"!=typeof window},9641:t=>{!function(){var e={675:function(t,e){"use strict";e.byteLength=function(t){var e=u(t),r=e[0],n=e[1];return(r+n)*3/4-n},e.toByteArray=function(t){var e,r,s=u(t),o=s[0],a=s[1],l=new i((o+a)*3/4-a),h=0,c=a>0?o-4:o;for(r=0;r<c;r+=4)e=n[t.charCodeAt(r)]<<18|n[t.charCodeAt(r+1)]<<12|n[t.charCodeAt(r+2)]<<6|n[t.charCodeAt(r+3)],l[h++]=e>>16&255,l[h++]=e>>8&255,l[h++]=255&e;return 2===a&&(e=n[t.charCodeAt(r)]<<2|n[t.charCodeAt(r+1)]>>4,l[h++]=255&e),1===a&&(e=n[t.charCodeAt(r)]<<10|n[t.charCodeAt(r+1)]<<4|n[t.charCodeAt(r+2)]>>2,l[h++]=e>>8&255,l[h++]=255&e),l},e.fromByteArray=function(t){for(var e,n=t.length,i=n%3,s=[],o=0,a=n-i;o<a;o+=16383)s.push(function(t,e,n){for(var i,s=[],o=e;o<n;o+=3)i=(t[o]<<16&0xff0000)+(t[o+1]<<8&65280)+(255&t[o+2]),s.push(r[i>>18&63]+r[i>>12&63]+r[i>>6&63]+r[63&i]);return s.join("")}(t,o,o+16383>a?a:o+16383));return 1===i?s.push(r[(e=t[n-1])>>2]+r[e<<4&63]+"=="):2===i&&s.push(r[(e=(t[n-2]<<8)+t[n-1])>>10]+r[e>>4&63]+r[e<<2&63]+"="),s.join("")};for(var r=[],n=[],i="undefined"!=typeof Uint8Array?Uint8Array:Array,s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",o=0,a=s.length;o<a;++o)r[o]=s[o],n[s.charCodeAt(o)]=o;function u(t){var e=t.length;if(e%4>0)throw Error("Invalid string. Length must be a multiple of 4");var r=t.indexOf("=");-1===r&&(r=e);var n=r===e?0:4-r%4;return[r,n]}n[45]=62,n[95]=63},72:function(t,e,r){"use strict";var n=r(675),i=r(783),s="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;function o(t){if(t>0x7fffffff)throw RangeError('The value "'+t+'" is invalid for option "size"');var e=new Uint8Array(t);return Object.setPrototypeOf(e,a.prototype),e}function a(t,e,r){if("number"==typeof t){if("string"==typeof e)throw TypeError('The "string" argument must be of type string. Received type number');return h(t)}return u(t,e,r)}function u(t,e,r){if("string"==typeof t){var n=t,i=e;if(("string"!=typeof i||""===i)&&(i="utf8"),!a.isEncoding(i))throw TypeError("Unknown encoding: "+i);var s=0|p(n,i),u=o(s),l=u.write(n,i);return l!==s&&(u=u.slice(0,l)),u}if(ArrayBuffer.isView(t))return c(t);if(null==t)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t);if(M(t,ArrayBuffer)||t&&M(t.buffer,ArrayBuffer)||"undefined"!=typeof SharedArrayBuffer&&(M(t,SharedArrayBuffer)||t&&M(t.buffer,SharedArrayBuffer)))return function(t,e,r){var n;if(e<0||t.byteLength<e)throw RangeError('"offset" is outside of buffer bounds');if(t.byteLength<e+(r||0))throw RangeError('"length" is outside of buffer bounds');return Object.setPrototypeOf(n=void 0===e&&void 0===r?new Uint8Array(t):void 0===r?new Uint8Array(t,e):new Uint8Array(t,e,r),a.prototype),n}(t,e,r);if("number"==typeof t)throw TypeError('The "value" argument must not be of type number. Received type number');var h=t.valueOf&&t.valueOf();if(null!=h&&h!==t)return a.from(h,e,r);var d=function(t){if(a.isBuffer(t)){var e=0|f(t.length),r=o(e);return 0===r.length||t.copy(r,0,0,e),r}return void 0!==t.length?"number"!=typeof t.length||function(t){return t!=t}(t.length)?o(0):c(t):"Buffer"===t.type&&Array.isArray(t.data)?c(t.data):void 0}(t);if(d)return d;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof t[Symbol.toPrimitive])return a.from(t[Symbol.toPrimitive]("string"),e,r);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t)}function l(t){if("number"!=typeof t)throw TypeError('"size" argument must be of type number');if(t<0)throw RangeError('The value "'+t+'" is invalid for option "size"')}function h(t){return l(t),o(t<0?0:0|f(t))}function c(t){for(var e=t.length<0?0:0|f(t.length),r=o(e),n=0;n<e;n+=1)r[n]=255&t[n];return r}e.Buffer=a,e.SlowBuffer=function(t){return+t!=t&&(t=0),a.alloc(+t)},e.INSPECT_MAX_BYTES=50,e.kMaxLength=0x7fffffff,a.TYPED_ARRAY_SUPPORT=function(){try{var t=new Uint8Array(1),e={foo:function(){return 42}};return Object.setPrototypeOf(e,Uint8Array.prototype),Object.setPrototypeOf(t,e),42===t.foo()}catch(t){return!1}}(),a.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(a.prototype,"parent",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.buffer}}),Object.defineProperty(a.prototype,"offset",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.byteOffset}}),a.poolSize=8192,a.from=function(t,e,r){return u(t,e,r)},Object.setPrototypeOf(a.prototype,Uint8Array.prototype),Object.setPrototypeOf(a,Uint8Array),a.alloc=function(t,e,r){return(l(t),t<=0)?o(t):void 0!==e?"string"==typeof r?o(t).fill(e,r):o(t).fill(e):o(t)},a.allocUnsafe=function(t){return h(t)},a.allocUnsafeSlow=function(t){return h(t)};function f(t){if(t>=0x7fffffff)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x7fffffff bytes");return 0|t}function p(t,e){if(a.isBuffer(t))return t.length;if(ArrayBuffer.isView(t)||M(t,ArrayBuffer))return t.byteLength;if("string"!=typeof t)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof t);var r=t.length,n=arguments.length>2&&!0===arguments[2];if(!n&&0===r)return 0;for(var i=!1;;)switch(e){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return A(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return P(t).length;default:if(i)return n?-1:A(t).length;e=(""+e).toLowerCase(),i=!0}}function d(t,e,r){var i,s,o,a=!1;if((void 0===e||e<0)&&(e=0),e>this.length||((void 0===r||r>this.length)&&(r=this.length),r<=0||(r>>>=0)<=(e>>>=0)))return"";for(t||(t="utf8");;)switch(t){case"hex":return function(t,e,r){var n=t.length;(!e||e<0)&&(e=0),(!r||r<0||r>n)&&(r=n);for(var i="",s=e;s<r;++s)i+=j[t[s]];return i}(this,e,r);case"utf8":case"utf-8":return v(this,e,r);case"ascii":return function(t,e,r){var n="";r=Math.min(t.length,r);for(var i=e;i<r;++i)n+=String.fromCharCode(127&t[i]);return n}(this,e,r);case"latin1":case"binary":return function(t,e,r){var n="";r=Math.min(t.length,r);for(var i=e;i<r;++i)n+=String.fromCharCode(t[i]);return n}(this,e,r);case"base64":return i=this,s=e,o=r,0===s&&o===i.length?n.fromByteArray(i):n.fromByteArray(i.slice(s,o));case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return function(t,e,r){for(var n=t.slice(e,r),i="",s=0;s<n.length;s+=2)i+=String.fromCharCode(n[s]+256*n[s+1]);return i}(this,e,r);default:if(a)throw TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),a=!0}}function m(t,e,r){var n=t[e];t[e]=t[r],t[r]=n}function y(t,e,r,n,i){var s;if(0===t.length)return -1;if("string"==typeof r?(n=r,r=0):r>0x7fffffff?r=0x7fffffff:r<-0x80000000&&(r=-0x80000000),(s=r*=1)!=s&&(r=i?0:t.length-1),r<0&&(r=t.length+r),r>=t.length)if(i)return -1;else r=t.length-1;else if(r<0)if(!i)return -1;else r=0;if("string"==typeof e&&(e=a.from(e,n)),a.isBuffer(e))return 0===e.length?-1:g(t,e,r,n,i);if("number"==typeof e){if(e&=255,"function"==typeof Uint8Array.prototype.indexOf)if(i)return Uint8Array.prototype.indexOf.call(t,e,r);else return Uint8Array.prototype.lastIndexOf.call(t,e,r);return g(t,[e],r,n,i)}throw TypeError("val must be string, number or Buffer")}function g(t,e,r,n,i){var s,o=1,a=t.length,u=e.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(t.length<2||e.length<2)return -1;o=2,a/=2,u/=2,r/=2}function l(t,e){return 1===o?t[e]:t.readUInt16BE(e*o)}if(i){var h=-1;for(s=r;s<a;s++)if(l(t,s)===l(e,-1===h?0:s-h)){if(-1===h&&(h=s),s-h+1===u)return h*o}else -1!==h&&(s-=s-h),h=-1}else for(r+u>a&&(r=a-u),s=r;s>=0;s--){for(var c=!0,f=0;f<u;f++)if(l(t,s+f)!==l(e,f)){c=!1;break}if(c)return s}return -1}a.isBuffer=function(t){return null!=t&&!0===t._isBuffer&&t!==a.prototype},a.compare=function(t,e){if(M(t,Uint8Array)&&(t=a.from(t,t.offset,t.byteLength)),M(e,Uint8Array)&&(e=a.from(e,e.offset,e.byteLength)),!a.isBuffer(t)||!a.isBuffer(e))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(t===e)return 0;for(var r=t.length,n=e.length,i=0,s=Math.min(r,n);i<s;++i)if(t[i]!==e[i]){r=t[i],n=e[i];break}return r<n?-1:+(n<r)},a.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},a.concat=function(t,e){if(!Array.isArray(t))throw TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return a.alloc(0);if(void 0===e)for(r=0,e=0;r<t.length;++r)e+=t[r].length;var r,n=a.allocUnsafe(e),i=0;for(r=0;r<t.length;++r){var s=t[r];if(M(s,Uint8Array)&&(s=a.from(s)),!a.isBuffer(s))throw TypeError('"list" argument must be an Array of Buffers');s.copy(n,i),i+=s.length}return n},a.byteLength=p,a.prototype._isBuffer=!0,a.prototype.swap16=function(){var t=this.length;if(t%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(var e=0;e<t;e+=2)m(this,e,e+1);return this},a.prototype.swap32=function(){var t=this.length;if(t%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(var e=0;e<t;e+=4)m(this,e,e+3),m(this,e+1,e+2);return this},a.prototype.swap64=function(){var t=this.length;if(t%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(var e=0;e<t;e+=8)m(this,e,e+7),m(this,e+1,e+6),m(this,e+2,e+5),m(this,e+3,e+4);return this},a.prototype.toString=function(){var t=this.length;return 0===t?"":0==arguments.length?v(this,0,t):d.apply(this,arguments)},a.prototype.toLocaleString=a.prototype.toString,a.prototype.equals=function(t){if(!a.isBuffer(t))throw TypeError("Argument must be a Buffer");return this===t||0===a.compare(this,t)},a.prototype.inspect=function(){var t="",r=e.INSPECT_MAX_BYTES;return t=this.toString("hex",0,r).replace(/(.{2})/g,"$1 ").trim(),this.length>r&&(t+=" ... "),"<Buffer "+t+">"},s&&(a.prototype[s]=a.prototype.inspect),a.prototype.compare=function(t,e,r,n,i){if(M(t,Uint8Array)&&(t=a.from(t,t.offset,t.byteLength)),!a.isBuffer(t))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof t);if(void 0===e&&(e=0),void 0===r&&(r=t?t.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),e<0||r>t.length||n<0||i>this.length)throw RangeError("out of range index");if(n>=i&&e>=r)return 0;if(n>=i)return -1;if(e>=r)return 1;if(e>>>=0,r>>>=0,n>>>=0,i>>>=0,this===t)return 0;for(var s=i-n,o=r-e,u=Math.min(s,o),l=this.slice(n,i),h=t.slice(e,r),c=0;c<u;++c)if(l[c]!==h[c]){s=l[c],o=h[c];break}return s<o?-1:+(o<s)},a.prototype.includes=function(t,e,r){return -1!==this.indexOf(t,e,r)},a.prototype.indexOf=function(t,e,r){return y(this,t,e,r,!0)},a.prototype.lastIndexOf=function(t,e,r){return y(this,t,e,r,!1)};function v(t,e,r){r=Math.min(t.length,r);for(var n=[],i=e;i<r;){var s,o,a,u,l=t[i],h=null,c=l>239?4:l>223?3:l>191?2:1;if(i+c<=r)switch(c){case 1:l<128&&(h=l);break;case 2:(192&(s=t[i+1]))==128&&(u=(31&l)<<6|63&s)>127&&(h=u);break;case 3:s=t[i+1],o=t[i+2],(192&s)==128&&(192&o)==128&&(u=(15&l)<<12|(63&s)<<6|63&o)>2047&&(u<55296||u>57343)&&(h=u);break;case 4:s=t[i+1],o=t[i+2],a=t[i+3],(192&s)==128&&(192&o)==128&&(192&a)==128&&(u=(15&l)<<18|(63&s)<<12|(63&o)<<6|63&a)>65535&&u<1114112&&(h=u)}null===h?(h=65533,c=1):h>65535&&(h-=65536,n.push(h>>>10&1023|55296),h=56320|1023&h),n.push(h),i+=c}var f=n,p=f.length;if(p<=4096)return String.fromCharCode.apply(String,f);for(var d="",m=0;m<p;)d+=String.fromCharCode.apply(String,f.slice(m,m+=4096));return d}function b(t,e,r){if(t%1!=0||t<0)throw RangeError("offset is not uint");if(t+e>r)throw RangeError("Trying to access beyond buffer length")}function w(t,e,r,n,i,s){if(!a.isBuffer(t))throw TypeError('"buffer" argument must be a Buffer instance');if(e>i||e<s)throw RangeError('"value" argument is out of bounds');if(r+n>t.length)throw RangeError("Index out of range")}function x(t,e,r,n,i,s){if(r+n>t.length||r<0)throw RangeError("Index out of range")}function E(t,e,r,n,s){return e*=1,r>>>=0,s||x(t,e,r,4,34028234663852886e22,-34028234663852886e22),i.write(t,e,r,n,23,4),r+4}function T(t,e,r,n,s){return e*=1,r>>>=0,s||x(t,e,r,8,17976931348623157e292,-17976931348623157e292),i.write(t,e,r,n,52,8),r+8}a.prototype.write=function(t,e,r,n){if(void 0===e)n="utf8",r=this.length,e=0;else if(void 0===r&&"string"==typeof e)n=e,r=this.length,e=0;else if(isFinite(e))e>>>=0,isFinite(r)?(r>>>=0,void 0===n&&(n="utf8")):(n=r,r=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var i,s,o,a,u,l,h,c,f=this.length-e;if((void 0===r||r>f)&&(r=f),t.length>0&&(r<0||e<0)||e>this.length)throw RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var p=!1;;)switch(n){case"hex":return function(t,e,r,n){r=Number(r)||0;var i=t.length-r;n?(n=Number(n))>i&&(n=i):n=i;var s=e.length;n>s/2&&(n=s/2);for(var o=0;o<n;++o){var a,u=parseInt(e.substr(2*o,2),16);if((a=u)!=a)break;t[r+o]=u}return o}(this,t,e,r);case"utf8":case"utf-8":return i=e,s=r,R(A(t,this.length-i),this,i,s);case"ascii":return o=e,a=r,R(S(t),this,o,a);case"latin1":case"binary":return function(t,e,r,n){return R(S(e),t,r,n)}(this,t,e,r);case"base64":return u=e,l=r,R(P(t),this,u,l);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return h=e,c=r,R(function(t,e){for(var r,n,i=[],s=0;s<t.length&&!((e-=2)<0);++s)n=(r=t.charCodeAt(s))>>8,i.push(r%256),i.push(n);return i}(t,this.length-h),this,h,c);default:if(p)throw TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),p=!0}},a.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},a.prototype.slice=function(t,e){var r=this.length;t=~~t,e=void 0===e?r:~~e,t<0?(t+=r)<0&&(t=0):t>r&&(t=r),e<0?(e+=r)<0&&(e=0):e>r&&(e=r),e<t&&(e=t);var n=this.subarray(t,e);return Object.setPrototypeOf(n,a.prototype),n},a.prototype.readUIntLE=function(t,e,r){t>>>=0,e>>>=0,r||b(t,e,this.length);for(var n=this[t],i=1,s=0;++s<e&&(i*=256);)n+=this[t+s]*i;return n},a.prototype.readUIntBE=function(t,e,r){t>>>=0,e>>>=0,r||b(t,e,this.length);for(var n=this[t+--e],i=1;e>0&&(i*=256);)n+=this[t+--e]*i;return n},a.prototype.readUInt8=function(t,e){return t>>>=0,e||b(t,1,this.length),this[t]},a.prototype.readUInt16LE=function(t,e){return t>>>=0,e||b(t,2,this.length),this[t]|this[t+1]<<8},a.prototype.readUInt16BE=function(t,e){return t>>>=0,e||b(t,2,this.length),this[t]<<8|this[t+1]},a.prototype.readUInt32LE=function(t,e){return t>>>=0,e||b(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+0x1000000*this[t+3]},a.prototype.readUInt32BE=function(t,e){return t>>>=0,e||b(t,4,this.length),0x1000000*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},a.prototype.readIntLE=function(t,e,r){t>>>=0,e>>>=0,r||b(t,e,this.length);for(var n=this[t],i=1,s=0;++s<e&&(i*=256);)n+=this[t+s]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*e)),n},a.prototype.readIntBE=function(t,e,r){t>>>=0,e>>>=0,r||b(t,e,this.length);for(var n=e,i=1,s=this[t+--n];n>0&&(i*=256);)s+=this[t+--n]*i;return s>=(i*=128)&&(s-=Math.pow(2,8*e)),s},a.prototype.readInt8=function(t,e){return(t>>>=0,e||b(t,1,this.length),128&this[t])?-((255-this[t]+1)*1):this[t]},a.prototype.readInt16LE=function(t,e){t>>>=0,e||b(t,2,this.length);var r=this[t]|this[t+1]<<8;return 32768&r?0xffff0000|r:r},a.prototype.readInt16BE=function(t,e){t>>>=0,e||b(t,2,this.length);var r=this[t+1]|this[t]<<8;return 32768&r?0xffff0000|r:r},a.prototype.readInt32LE=function(t,e){return t>>>=0,e||b(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},a.prototype.readInt32BE=function(t,e){return t>>>=0,e||b(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},a.prototype.readFloatLE=function(t,e){return t>>>=0,e||b(t,4,this.length),i.read(this,t,!0,23,4)},a.prototype.readFloatBE=function(t,e){return t>>>=0,e||b(t,4,this.length),i.read(this,t,!1,23,4)},a.prototype.readDoubleLE=function(t,e){return t>>>=0,e||b(t,8,this.length),i.read(this,t,!0,52,8)},a.prototype.readDoubleBE=function(t,e){return t>>>=0,e||b(t,8,this.length),i.read(this,t,!1,52,8)},a.prototype.writeUIntLE=function(t,e,r,n){if(t*=1,e>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;w(this,t,e,r,i,0)}var s=1,o=0;for(this[e]=255&t;++o<r&&(s*=256);)this[e+o]=t/s&255;return e+r},a.prototype.writeUIntBE=function(t,e,r,n){if(t*=1,e>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;w(this,t,e,r,i,0)}var s=r-1,o=1;for(this[e+s]=255&t;--s>=0&&(o*=256);)this[e+s]=t/o&255;return e+r},a.prototype.writeUInt8=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,1,255,0),this[e]=255&t,e+1},a.prototype.writeUInt16LE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,2,65535,0),this[e]=255&t,this[e+1]=t>>>8,e+2},a.prototype.writeUInt16BE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,2,65535,0),this[e]=t>>>8,this[e+1]=255&t,e+2},a.prototype.writeUInt32LE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,4,0xffffffff,0),this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t,e+4},a.prototype.writeUInt32BE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,4,0xffffffff,0),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},a.prototype.writeIntLE=function(t,e,r,n){if(t*=1,e>>>=0,!n){var i=Math.pow(2,8*r-1);w(this,t,e,r,i-1,-i)}var s=0,o=1,a=0;for(this[e]=255&t;++s<r&&(o*=256);)t<0&&0===a&&0!==this[e+s-1]&&(a=1),this[e+s]=(t/o|0)-a&255;return e+r},a.prototype.writeIntBE=function(t,e,r,n){if(t*=1,e>>>=0,!n){var i=Math.pow(2,8*r-1);w(this,t,e,r,i-1,-i)}var s=r-1,o=1,a=0;for(this[e+s]=255&t;--s>=0&&(o*=256);)t<0&&0===a&&0!==this[e+s+1]&&(a=1),this[e+s]=(t/o|0)-a&255;return e+r},a.prototype.writeInt8=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,1,127,-128),t<0&&(t=255+t+1),this[e]=255&t,e+1},a.prototype.writeInt16LE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,2,32767,-32768),this[e]=255&t,this[e+1]=t>>>8,e+2},a.prototype.writeInt16BE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,2,32767,-32768),this[e]=t>>>8,this[e+1]=255&t,e+2},a.prototype.writeInt32LE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,4,0x7fffffff,-0x80000000),this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24,e+4},a.prototype.writeInt32BE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,4,0x7fffffff,-0x80000000),t<0&&(t=0xffffffff+t+1),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},a.prototype.writeFloatLE=function(t,e,r){return E(this,t,e,!0,r)},a.prototype.writeFloatBE=function(t,e,r){return E(this,t,e,!1,r)},a.prototype.writeDoubleLE=function(t,e,r){return T(this,t,e,!0,r)},a.prototype.writeDoubleBE=function(t,e,r){return T(this,t,e,!1,r)},a.prototype.copy=function(t,e,r,n){if(!a.isBuffer(t))throw TypeError("argument should be a Buffer");if(r||(r=0),n||0===n||(n=this.length),e>=t.length&&(e=t.length),e||(e=0),n>0&&n<r&&(n=r),n===r||0===t.length||0===this.length)return 0;if(e<0)throw RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw RangeError("Index out of range");if(n<0)throw RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),t.length-e<n-r&&(n=t.length-e+r);var i=n-r;if(this===t&&"function"==typeof Uint8Array.prototype.copyWithin)this.copyWithin(e,r,n);else if(this===t&&r<e&&e<n)for(var s=i-1;s>=0;--s)t[s+e]=this[s+r];else Uint8Array.prototype.set.call(t,this.subarray(r,n),e);return i},a.prototype.fill=function(t,e,r,n){if("string"==typeof t){if("string"==typeof e?(n=e,e=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),void 0!==n&&"string"!=typeof n)throw TypeError("encoding must be a string");if("string"==typeof n&&!a.isEncoding(n))throw TypeError("Unknown encoding: "+n);if(1===t.length){var i,s=t.charCodeAt(0);("utf8"===n&&s<128||"latin1"===n)&&(t=s)}}else"number"==typeof t?t&=255:"boolean"==typeof t&&(t=Number(t));if(e<0||this.length<e||this.length<r)throw RangeError("Out of range index");if(r<=e)return this;if(e>>>=0,r=void 0===r?this.length:r>>>0,t||(t=0),"number"==typeof t)for(i=e;i<r;++i)this[i]=t;else{var o=a.isBuffer(t)?t:a.from(t,n),u=o.length;if(0===u)throw TypeError('The value "'+t+'" is invalid for argument "value"');for(i=0;i<r-e;++i)this[i+e]=o[i%u]}return this};var C=/[^+/0-9A-Za-z-_]/g;function A(t,e){e=e||1/0;for(var r,n=t.length,i=null,s=[],o=0;o<n;++o){if((r=t.charCodeAt(o))>55295&&r<57344){if(!i){if(r>56319||o+1===n){(e-=3)>-1&&s.push(239,191,189);continue}i=r;continue}if(r<56320){(e-=3)>-1&&s.push(239,191,189),i=r;continue}r=(i-55296<<10|r-56320)+65536}else i&&(e-=3)>-1&&s.push(239,191,189);if(i=null,r<128){if((e-=1)<0)break;s.push(r)}else if(r<2048){if((e-=2)<0)break;s.push(r>>6|192,63&r|128)}else if(r<65536){if((e-=3)<0)break;s.push(r>>12|224,r>>6&63|128,63&r|128)}else if(r<1114112){if((e-=4)<0)break;s.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}else throw Error("Invalid code point")}return s}function S(t){for(var e=[],r=0;r<t.length;++r)e.push(255&t.charCodeAt(r));return e}function P(t){return n.toByteArray(function(t){if((t=(t=t.split("=")[0]).trim().replace(C,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function R(t,e,r,n){for(var i=0;i<n&&!(i+r>=e.length)&&!(i>=t.length);++i)e[i+r]=t[i];return i}function M(t,e){return t instanceof e||null!=t&&null!=t.constructor&&null!=t.constructor.name&&t.constructor.name===e.name}var j=function(){for(var t="0123456789abcdef",e=Array(256),r=0;r<16;++r)for(var n=16*r,i=0;i<16;++i)e[n+i]=t[r]+t[i];return e}()},783:function(t,e){e.read=function(t,e,r,n,i){var s,o,a=8*i-n-1,u=(1<<a)-1,l=u>>1,h=-7,c=r?i-1:0,f=r?-1:1,p=t[e+c];for(c+=f,s=p&(1<<-h)-1,p>>=-h,h+=a;h>0;s=256*s+t[e+c],c+=f,h-=8);for(o=s&(1<<-h)-1,s>>=-h,h+=n;h>0;o=256*o+t[e+c],c+=f,h-=8);if(0===s)s=1-l;else{if(s===u)return o?NaN:1/0*(p?-1:1);o+=Math.pow(2,n),s-=l}return(p?-1:1)*o*Math.pow(2,s-n)},e.write=function(t,e,r,n,i,s){var o,a,u,l=8*s-i-1,h=(1<<l)-1,c=h>>1,f=5960464477539062e-23*(23===i),p=n?0:s-1,d=n?1:-1,m=+(e<0||0===e&&1/e<0);for(isNaN(e=Math.abs(e))||e===1/0?(a=+!!isNaN(e),o=h):(o=Math.floor(Math.log(e)/Math.LN2),e*(u=Math.pow(2,-o))<1&&(o--,u*=2),o+c>=1?e+=f/u:e+=f*Math.pow(2,1-c),e*u>=2&&(o++,u/=2),o+c>=h?(a=0,o=h):o+c>=1?(a=(e*u-1)*Math.pow(2,i),o+=c):(a=e*Math.pow(2,c-1)*Math.pow(2,i),o=0));i>=8;t[r+p]=255&a,p+=d,a/=256,i-=8);for(o=o<<i|a,l+=i;l>0;t[r+p]=255&o,p+=d,o/=256,l-=8);t[r+p-d]|=128*m}}},r={};function n(t){var i=r[t];if(void 0!==i)return i.exports;var s=r[t]={exports:{}},o=!0;try{e[t](s,s.exports,n),o=!1}finally{o&&delete r[t]}return s.exports}n.ab="//",t.exports=n(72)}()},9946:(t,e,r)=>{"use strict";r.d(e,{A:()=>c});var n=r(2115);let i=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=t=>t.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,e,r)=>r?r.toUpperCase():e.toLowerCase()),o=t=>{let e=s(t);return e.charAt(0).toUpperCase()+e.slice(1)},a=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];return e.filter((t,e,r)=>!!t&&""!==t.trim()&&r.indexOf(t)===e).join(" ").trim()},u=t=>{for(let e in t)if(e.startsWith("aria-")||"role"===e||"title"===e)return!0};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let h=(0,n.forwardRef)((t,e)=>{let{color:r="currentColor",size:i=24,strokeWidth:s=2,absoluteStrokeWidth:o,className:h="",children:c,iconNode:f,...p}=t;return(0,n.createElement)("svg",{ref:e,...l,width:i,height:i,stroke:r,strokeWidth:o?24*Number(s)/Number(i):s,className:a("lucide",h),...!c&&!u(p)&&{"aria-hidden":"true"},...p},[...f.map(t=>{let[e,r]=t;return(0,n.createElement)(e,r)}),...Array.isArray(c)?c:[c]])}),c=(t,e)=>{let r=(0,n.forwardRef)((r,s)=>{let{className:u,...l}=r;return(0,n.createElement)(h,{ref:s,iconNode:e,className:a("lucide-".concat(i(o(t))),"lucide-".concat(t),u),...l})});return r.displayName=o(t),r}},9991:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var r in e)Object.defineProperty(t,r,{enumerable:!0,get:e[r]})}(e,{DecodeError:function(){return d},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return g},NormalizeError:function(){return m},PageNotFoundError:function(){return y},SP:function(){return f},ST:function(){return p},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return u},getLocationOrigin:function(){return o},getURL:function(){return a},isAbsoluteUrl:function(){return s},isResSent:function(){return l},loadGetInitialProps:function(){return c},normalizeRepeatedSlashes:function(){return h},stringifyError:function(){return b}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(t){let e,r=!1;return function(){for(var n=arguments.length,i=Array(n),s=0;s<n;s++)i[s]=arguments[s];return r||(r=!0,e=t(...i)),e}}let i=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,s=t=>i.test(t);function o(){let{protocol:t,hostname:e,port:r}=window.location;return t+"//"+e+(r?":"+r:"")}function a(){let{href:t}=window.location,e=o();return t.substring(e.length)}function u(t){return"string"==typeof t?t:t.displayName||t.name||"Unknown"}function l(t){return t.finished||t.headersSent}function h(t){let e=t.split("?");return e[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(e[1]?"?"+e.slice(1).join("?"):"")}async function c(t,e){let r=e.res||e.ctx&&e.ctx.res;if(!t.getInitialProps)return e.ctx&&e.Component?{pageProps:await c(e.Component,e.ctx)}:{};let n=await t.getInitialProps(e);if(r&&l(r))return n;if(!n)throw Object.defineProperty(Error('"'+u(t)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let f="undefined"!=typeof performance,p=f&&["mark","measure","getEntriesByName"].every(t=>"function"==typeof performance[t]);class d extends Error{}class m extends Error{}class y extends Error{constructor(t){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+t}}class g extends Error{constructor(t,e){super(),this.message="Failed to load static file for page: "+t+" "+e}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(t){return JSON.stringify({message:t.message,stack:t.stack})}}}]);