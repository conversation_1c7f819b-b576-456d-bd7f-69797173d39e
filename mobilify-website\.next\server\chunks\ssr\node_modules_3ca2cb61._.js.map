{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/eventsource/lib/eventsource.js"], "sourcesContent": ["var parse = require('url').parse\nvar events = require('events')\nvar https = require('https')\nvar http = require('http')\nvar util = require('util')\n\nvar httpsOptions = [\n  'pfx', 'key', 'passphrase', 'cert', 'ca', 'ciphers',\n  'rejectUnauthorized', 'secureProtocol', 'servername', 'checkServerIdentity'\n]\n\nvar bom = [239, 187, 191]\nvar colon = 58\nvar space = 32\nvar lineFeed = 10\nvar carriageReturn = 13\n// Beyond 256KB we could not observe any gain in performance\nvar maxBufferAheadAllocation = 1024 * 256\n// Headers matching the pattern should be removed when redirecting to different origin\nvar reUnsafeHeader = /^(cookie|authorization)$/i\n\nfunction hasBom (buf) {\n  return bom.every(function (charCode, index) {\n    return buf[index] === charCode\n  })\n}\n\n/**\n * Creates a new EventSource object\n *\n * @param {String} url the URL to which to connect\n * @param {Object} [eventSourceInitDict] extra init params. See README for details.\n * @api public\n **/\nfunction EventSource (url, eventSourceInitDict) {\n  var readyState = EventSource.CONNECTING\n  var headers = eventSourceInitDict && eventSourceInitDict.headers\n  var hasNewOrigin = false\n  Object.defineProperty(this, 'readyState', {\n    get: function () {\n      return readyState\n    }\n  })\n\n  Object.defineProperty(this, 'url', {\n    get: function () {\n      return url\n    }\n  })\n\n  var self = this\n  self.reconnectInterval = 1000\n  self.connectionInProgress = false\n\n  function onConnectionClosed (message) {\n    if (readyState === EventSource.CLOSED) return\n    readyState = EventSource.CONNECTING\n    _emit('error', new Event('error', {message: message}))\n\n    // The url may have been changed by a temporary redirect. If that's the case,\n    // revert it now, and flag that we are no longer pointing to a new origin\n    if (reconnectUrl) {\n      url = reconnectUrl\n      reconnectUrl = null\n      hasNewOrigin = false\n    }\n    setTimeout(function () {\n      if (readyState !== EventSource.CONNECTING || self.connectionInProgress) {\n        return\n      }\n      self.connectionInProgress = true\n      connect()\n    }, self.reconnectInterval)\n  }\n\n  var req\n  var lastEventId = ''\n  if (headers && headers['Last-Event-ID']) {\n    lastEventId = headers['Last-Event-ID']\n    delete headers['Last-Event-ID']\n  }\n\n  var discardTrailingNewline = false\n  var data = ''\n  var eventName = ''\n\n  var reconnectUrl = null\n\n  function connect () {\n    var options = parse(url)\n    var isSecure = options.protocol === 'https:'\n    options.headers = { 'Cache-Control': 'no-cache', 'Accept': 'text/event-stream' }\n    if (lastEventId) options.headers['Last-Event-ID'] = lastEventId\n    if (headers) {\n      var reqHeaders = hasNewOrigin ? removeUnsafeHeaders(headers) : headers\n      for (var i in reqHeaders) {\n        var header = reqHeaders[i]\n        if (header) {\n          options.headers[i] = header\n        }\n      }\n    }\n\n    // Legacy: this should be specified as `eventSourceInitDict.https.rejectUnauthorized`,\n    // but for now exists as a backwards-compatibility layer\n    options.rejectUnauthorized = !(eventSourceInitDict && !eventSourceInitDict.rejectUnauthorized)\n\n    if (eventSourceInitDict && eventSourceInitDict.createConnection !== undefined) {\n      options.createConnection = eventSourceInitDict.createConnection\n    }\n\n    // If specify http proxy, make the request to sent to the proxy server,\n    // and include the original url in path and Host headers\n    var useProxy = eventSourceInitDict && eventSourceInitDict.proxy\n    if (useProxy) {\n      var proxy = parse(eventSourceInitDict.proxy)\n      isSecure = proxy.protocol === 'https:'\n\n      options.protocol = isSecure ? 'https:' : 'http:'\n      options.path = url\n      options.headers.Host = options.host\n      options.hostname = proxy.hostname\n      options.host = proxy.host\n      options.port = proxy.port\n    }\n\n    // If https options are specified, merge them into the request options\n    if (eventSourceInitDict && eventSourceInitDict.https) {\n      for (var optName in eventSourceInitDict.https) {\n        if (httpsOptions.indexOf(optName) === -1) {\n          continue\n        }\n\n        var option = eventSourceInitDict.https[optName]\n        if (option !== undefined) {\n          options[optName] = option\n        }\n      }\n    }\n\n    // Pass this on to the XHR\n    if (eventSourceInitDict && eventSourceInitDict.withCredentials !== undefined) {\n      options.withCredentials = eventSourceInitDict.withCredentials\n    }\n\n    req = (isSecure ? https : http).request(options, function (res) {\n      self.connectionInProgress = false\n      // Handle HTTP errors\n      if (res.statusCode === 500 || res.statusCode === 502 || res.statusCode === 503 || res.statusCode === 504) {\n        _emit('error', new Event('error', {status: res.statusCode, message: res.statusMessage}))\n        onConnectionClosed()\n        return\n      }\n\n      // Handle HTTP redirects\n      if (res.statusCode === 301 || res.statusCode === 302 || res.statusCode === 307) {\n        var location = res.headers.location\n        if (!location) {\n          // Server sent redirect response without Location header.\n          _emit('error', new Event('error', {status: res.statusCode, message: res.statusMessage}))\n          return\n        }\n        var prevOrigin = new URL(url).origin\n        var nextOrigin = new URL(location).origin\n        hasNewOrigin = prevOrigin !== nextOrigin\n        if (res.statusCode === 307) reconnectUrl = url\n        url = location\n        process.nextTick(connect)\n        return\n      }\n\n      if (res.statusCode !== 200) {\n        _emit('error', new Event('error', {status: res.statusCode, message: res.statusMessage}))\n        return self.close()\n      }\n\n      readyState = EventSource.OPEN\n      res.on('close', function () {\n        res.removeAllListeners('close')\n        res.removeAllListeners('end')\n        onConnectionClosed()\n      })\n\n      res.on('end', function () {\n        res.removeAllListeners('close')\n        res.removeAllListeners('end')\n        onConnectionClosed()\n      })\n      _emit('open', new Event('open'))\n\n      // text/event-stream parser adapted from webkit's\n      // Source/WebCore/page/EventSource.cpp\n      var buf\n      var newBuffer\n      var startingPos = 0\n      var startingFieldLength = -1\n      var newBufferSize = 0\n      var bytesUsed = 0\n\n      res.on('data', function (chunk) {\n        if (!buf) {\n          buf = chunk\n          if (hasBom(buf)) {\n            buf = buf.slice(bom.length)\n          }\n          bytesUsed = buf.length\n        } else {\n          if (chunk.length > buf.length - bytesUsed) {\n            newBufferSize = (buf.length * 2) + chunk.length\n            if (newBufferSize > maxBufferAheadAllocation) {\n              newBufferSize = buf.length + chunk.length + maxBufferAheadAllocation\n            }\n            newBuffer = Buffer.alloc(newBufferSize)\n            buf.copy(newBuffer, 0, 0, bytesUsed)\n            buf = newBuffer\n          }\n          chunk.copy(buf, bytesUsed)\n          bytesUsed += chunk.length\n        }\n\n        var pos = 0\n        var length = bytesUsed\n\n        while (pos < length) {\n          if (discardTrailingNewline) {\n            if (buf[pos] === lineFeed) {\n              ++pos\n            }\n            discardTrailingNewline = false\n          }\n\n          var lineLength = -1\n          var fieldLength = startingFieldLength\n          var c\n\n          for (var i = startingPos; lineLength < 0 && i < length; ++i) {\n            c = buf[i]\n            if (c === colon) {\n              if (fieldLength < 0) {\n                fieldLength = i - pos\n              }\n            } else if (c === carriageReturn) {\n              discardTrailingNewline = true\n              lineLength = i - pos\n            } else if (c === lineFeed) {\n              lineLength = i - pos\n            }\n          }\n\n          if (lineLength < 0) {\n            startingPos = length - pos\n            startingFieldLength = fieldLength\n            break\n          } else {\n            startingPos = 0\n            startingFieldLength = -1\n          }\n\n          parseEventStreamLine(buf, pos, fieldLength, lineLength)\n\n          pos += lineLength + 1\n        }\n\n        if (pos === length) {\n          buf = void 0\n          bytesUsed = 0\n        } else if (pos > 0) {\n          buf = buf.slice(pos, bytesUsed)\n          bytesUsed = buf.length\n        }\n      })\n    })\n\n    req.on('error', function (err) {\n      self.connectionInProgress = false\n      onConnectionClosed(err.message)\n    })\n\n    if (req.setNoDelay) req.setNoDelay(true)\n    req.end()\n  }\n\n  connect()\n\n  function _emit () {\n    if (self.listeners(arguments[0]).length > 0) {\n      self.emit.apply(self, arguments)\n    }\n  }\n\n  this._close = function () {\n    if (readyState === EventSource.CLOSED) return\n    readyState = EventSource.CLOSED\n    if (req.abort) req.abort()\n    if (req.xhr && req.xhr.abort) req.xhr.abort()\n  }\n\n  function parseEventStreamLine (buf, pos, fieldLength, lineLength) {\n    if (lineLength === 0) {\n      if (data.length > 0) {\n        var type = eventName || 'message'\n        _emit(type, new MessageEvent(type, {\n          data: data.slice(0, -1), // remove trailing newline\n          lastEventId: lastEventId,\n          origin: new URL(url).origin\n        }))\n        data = ''\n      }\n      eventName = void 0\n    } else if (fieldLength > 0) {\n      var noValue = fieldLength < 0\n      var step = 0\n      var field = buf.slice(pos, pos + (noValue ? lineLength : fieldLength)).toString()\n\n      if (noValue) {\n        step = lineLength\n      } else if (buf[pos + fieldLength + 1] !== space) {\n        step = fieldLength + 1\n      } else {\n        step = fieldLength + 2\n      }\n      pos += step\n\n      var valueLength = lineLength - step\n      var value = buf.slice(pos, pos + valueLength).toString()\n\n      if (field === 'data') {\n        data += value + '\\n'\n      } else if (field === 'event') {\n        eventName = value\n      } else if (field === 'id') {\n        lastEventId = value\n      } else if (field === 'retry') {\n        var retry = parseInt(value, 10)\n        if (!Number.isNaN(retry)) {\n          self.reconnectInterval = retry\n        }\n      }\n    }\n  }\n}\n\nmodule.exports = EventSource\n\nutil.inherits(EventSource, events.EventEmitter)\nEventSource.prototype.constructor = EventSource; // make stacktraces readable\n\n['open', 'error', 'message'].forEach(function (method) {\n  Object.defineProperty(EventSource.prototype, 'on' + method, {\n    /**\n     * Returns the current listener\n     *\n     * @return {Mixed} the set function or undefined\n     * @api private\n     */\n    get: function get () {\n      var listener = this.listeners(method)[0]\n      return listener ? (listener._listener ? listener._listener : listener) : undefined\n    },\n\n    /**\n     * Start listening for events\n     *\n     * @param {Function} listener the listener\n     * @return {Mixed} the set function or undefined\n     * @api private\n     */\n    set: function set (listener) {\n      this.removeAllListeners(method)\n      this.addEventListener(method, listener)\n    }\n  })\n})\n\n/**\n * Ready states\n */\nObject.defineProperty(EventSource, 'CONNECTING', {enumerable: true, value: 0})\nObject.defineProperty(EventSource, 'OPEN', {enumerable: true, value: 1})\nObject.defineProperty(EventSource, 'CLOSED', {enumerable: true, value: 2})\n\nEventSource.prototype.CONNECTING = 0\nEventSource.prototype.OPEN = 1\nEventSource.prototype.CLOSED = 2\n\n/**\n * Closes the connection, if one is made, and sets the readyState attribute to 2 (closed)\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/EventSource/close\n * @api public\n */\nEventSource.prototype.close = function () {\n  this._close()\n}\n\n/**\n * Emulates the W3C Browser based WebSocket interface using addEventListener.\n *\n * @param {String} type A string representing the event type to listen out for\n * @param {Function} listener callback\n * @see https://developer.mozilla.org/en/DOM/element.addEventListener\n * @see http://dev.w3.org/html5/websockets/#the-websocket-interface\n * @api public\n */\nEventSource.prototype.addEventListener = function addEventListener (type, listener) {\n  if (typeof listener === 'function') {\n    // store a reference so we can return the original function again\n    listener._listener = listener\n    this.on(type, listener)\n  }\n}\n\n/**\n * Emulates the W3C Browser based WebSocket interface using dispatchEvent.\n *\n * @param {Event} event An event to be dispatched\n * @see https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/dispatchEvent\n * @api public\n */\nEventSource.prototype.dispatchEvent = function dispatchEvent (event) {\n  if (!event.type) {\n    throw new Error('UNSPECIFIED_EVENT_TYPE_ERR')\n  }\n  // if event is instance of an CustomEvent (or has 'details' property),\n  // send the detail object as the payload for the event\n  this.emit(event.type, event.detail)\n}\n\n/**\n * Emulates the W3C Browser based WebSocket interface using removeEventListener.\n *\n * @param {String} type A string representing the event type to remove\n * @param {Function} listener callback\n * @see https://developer.mozilla.org/en/DOM/element.removeEventListener\n * @see http://dev.w3.org/html5/websockets/#the-websocket-interface\n * @api public\n */\nEventSource.prototype.removeEventListener = function removeEventListener (type, listener) {\n  if (typeof listener === 'function') {\n    listener._listener = undefined\n    this.removeListener(type, listener)\n  }\n}\n\n/**\n * W3C Event\n *\n * @see http://www.w3.org/TR/DOM-Level-3-Events/#interface-Event\n * @api private\n */\nfunction Event (type, optionalProperties) {\n  Object.defineProperty(this, 'type', { writable: false, value: type, enumerable: true })\n  if (optionalProperties) {\n    for (var f in optionalProperties) {\n      if (optionalProperties.hasOwnProperty(f)) {\n        Object.defineProperty(this, f, { writable: false, value: optionalProperties[f], enumerable: true })\n      }\n    }\n  }\n}\n\n/**\n * W3C MessageEvent\n *\n * @see http://www.w3.org/TR/webmessaging/#event-definitions\n * @api private\n */\nfunction MessageEvent (type, eventInitDict) {\n  Object.defineProperty(this, 'type', { writable: false, value: type, enumerable: true })\n  for (var f in eventInitDict) {\n    if (eventInitDict.hasOwnProperty(f)) {\n      Object.defineProperty(this, f, { writable: false, value: eventInitDict[f], enumerable: true })\n    }\n  }\n}\n\n/**\n * Returns a new object of headers that does not include any authorization and cookie headers\n *\n * @param {Object} headers An object of headers ({[headerName]: headerValue})\n * @return {Object} a new object of headers\n * @api private\n */\nfunction removeUnsafeHeaders (headers) {\n  var safe = {}\n  for (var key in headers) {\n    if (reUnsafeHeader.test(key)) {\n      continue\n    }\n\n    safe[key] = headers[key]\n  }\n\n  return safe\n}\n"], "names": [], "mappings": "AAAA,IAAI,QAAQ,iEAAe,KAAK;AAChC,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,IAAI,eAAe;IACjB;IAAO;IAAO;IAAc;IAAQ;IAAM;IAC1C;IAAsB;IAAkB;IAAc;CACvD;AAED,IAAI,MAAM;IAAC;IAAK;IAAK;CAAI;AACzB,IAAI,QAAQ;AACZ,IAAI,QAAQ;AACZ,IAAI,WAAW;AACf,IAAI,iBAAiB;AACrB,4DAA4D;AAC5D,IAAI,2BAA2B,OAAO;AACtC,sFAAsF;AACtF,IAAI,iBAAiB;AAErB,SAAS,OAAQ,GAAG;IAClB,OAAO,IAAI,KAAK,CAAC,SAAU,QAAQ,EAAE,KAAK;QACxC,OAAO,GAAG,CAAC,MAAM,KAAK;IACxB;AACF;AAEA;;;;;;EAME,GACF,SAAS,YAAa,GAAG,EAAE,mBAAmB;IAC5C,IAAI,aAAa,YAAY,UAAU;IACvC,IAAI,UAAU,uBAAuB,oBAAoB,OAAO;IAChE,IAAI,eAAe;IACnB,OAAO,cAAc,CAAC,IAAI,EAAE,cAAc;QACxC,KAAK;YACH,OAAO;QACT;IACF;IAEA,OAAO,cAAc,CAAC,IAAI,EAAE,OAAO;QACjC,KAAK;YACH,OAAO;QACT;IACF;IAEA,IAAI,OAAO,IAAI;IACf,KAAK,iBAAiB,GAAG;IACzB,KAAK,oBAAoB,GAAG;IAE5B,SAAS,mBAAoB,OAAO;QAClC,IAAI,eAAe,YAAY,MAAM,EAAE;QACvC,aAAa,YAAY,UAAU;QACnC,MAAM,SAAS,IAAI,MAAM,SAAS;YAAC,SAAS;QAAO;QAEnD,6EAA6E;QAC7E,yEAAyE;QACzE,IAAI,cAAc;YAChB,MAAM;YACN,eAAe;YACf,eAAe;QACjB;QACA,WAAW;YACT,IAAI,eAAe,YAAY,UAAU,IAAI,KAAK,oBAAoB,EAAE;gBACtE;YACF;YACA,KAAK,oBAAoB,GAAG;YAC5B;QACF,GAAG,KAAK,iBAAiB;IAC3B;IAEA,IAAI;IACJ,IAAI,cAAc;IAClB,IAAI,WAAW,OAAO,CAAC,gBAAgB,EAAE;QACvC,cAAc,OAAO,CAAC,gBAAgB;QACtC,OAAO,OAAO,CAAC,gBAAgB;IACjC;IAEA,IAAI,yBAAyB;IAC7B,IAAI,OAAO;IACX,IAAI,YAAY;IAEhB,IAAI,eAAe;IAEnB,SAAS;QACP,IAAI,UAAU,MAAM;QACpB,IAAI,WAAW,QAAQ,QAAQ,KAAK;QACpC,QAAQ,OAAO,GAAG;YAAE,iBAAiB;YAAY,UAAU;QAAoB;QAC/E,IAAI,aAAa,QAAQ,OAAO,CAAC,gBAAgB,GAAG;QACpD,IAAI,SAAS;YACX,IAAI,aAAa,eAAe,oBAAoB,WAAW;YAC/D,IAAK,IAAI,KAAK,WAAY;gBACxB,IAAI,SAAS,UAAU,CAAC,EAAE;gBAC1B,IAAI,QAAQ;oBACV,QAAQ,OAAO,CAAC,EAAE,GAAG;gBACvB;YACF;QACF;QAEA,sFAAsF;QACtF,wDAAwD;QACxD,QAAQ,kBAAkB,GAAG,CAAC,CAAC,uBAAuB,CAAC,oBAAoB,kBAAkB;QAE7F,IAAI,uBAAuB,oBAAoB,gBAAgB,KAAK,WAAW;YAC7E,QAAQ,gBAAgB,GAAG,oBAAoB,gBAAgB;QACjE;QAEA,uEAAuE;QACvE,wDAAwD;QACxD,IAAI,WAAW,uBAAuB,oBAAoB,KAAK;QAC/D,IAAI,UAAU;YACZ,IAAI,QAAQ,MAAM,oBAAoB,KAAK;YAC3C,WAAW,MAAM,QAAQ,KAAK;YAE9B,QAAQ,QAAQ,GAAG,WAAW,WAAW;YACzC,QAAQ,IAAI,GAAG;YACf,QAAQ,OAAO,CAAC,IAAI,GAAG,QAAQ,IAAI;YACnC,QAAQ,QAAQ,GAAG,MAAM,QAAQ;YACjC,QAAQ,IAAI,GAAG,MAAM,IAAI;YACzB,QAAQ,IAAI,GAAG,MAAM,IAAI;QAC3B;QAEA,sEAAsE;QACtE,IAAI,uBAAuB,oBAAoB,KAAK,EAAE;YACpD,IAAK,IAAI,WAAW,oBAAoB,KAAK,CAAE;gBAC7C,IAAI,aAAa,OAAO,CAAC,aAAa,CAAC,GAAG;oBACxC;gBACF;gBAEA,IAAI,SAAS,oBAAoB,KAAK,CAAC,QAAQ;gBAC/C,IAAI,WAAW,WAAW;oBACxB,OAAO,CAAC,QAAQ,GAAG;gBACrB;YACF;QACF;QAEA,0BAA0B;QAC1B,IAAI,uBAAuB,oBAAoB,eAAe,KAAK,WAAW;YAC5E,QAAQ,eAAe,GAAG,oBAAoB,eAAe;QAC/D;QAEA,MAAM,CAAC,WAAW,QAAQ,IAAI,EAAE,OAAO,CAAC,SAAS,SAAU,GAAG;YAC5D,KAAK,oBAAoB,GAAG;YAC5B,qBAAqB;YACrB,IAAI,IAAI,UAAU,KAAK,OAAO,IAAI,UAAU,KAAK,OAAO,IAAI,UAAU,KAAK,OAAO,IAAI,UAAU,KAAK,KAAK;gBACxG,MAAM,SAAS,IAAI,MAAM,SAAS;oBAAC,QAAQ,IAAI,UAAU;oBAAE,SAAS,IAAI,aAAa;gBAAA;gBACrF;gBACA;YACF;YAEA,wBAAwB;YACxB,IAAI,IAAI,UAAU,KAAK,OAAO,IAAI,UAAU,KAAK,OAAO,IAAI,UAAU,KAAK,KAAK;gBAC9E,IAAI,WAAW,IAAI,OAAO,CAAC,QAAQ;gBACnC,IAAI,CAAC,UAAU;oBACb,yDAAyD;oBACzD,MAAM,SAAS,IAAI,MAAM,SAAS;wBAAC,QAAQ,IAAI,UAAU;wBAAE,SAAS,IAAI,aAAa;oBAAA;oBACrF;gBACF;gBACA,IAAI,aAAa,IAAI,IAAI,KAAK,MAAM;gBACpC,IAAI,aAAa,IAAI,IAAI,UAAU,MAAM;gBACzC,eAAe,eAAe;gBAC9B,IAAI,IAAI,UAAU,KAAK,KAAK,eAAe;gBAC3C,MAAM;gBACN,QAAQ,QAAQ,CAAC;gBACjB;YACF;YAEA,IAAI,IAAI,UAAU,KAAK,KAAK;gBAC1B,MAAM,SAAS,IAAI,MAAM,SAAS;oBAAC,QAAQ,IAAI,UAAU;oBAAE,SAAS,IAAI,aAAa;gBAAA;gBACrF,OAAO,KAAK,KAAK;YACnB;YAEA,aAAa,YAAY,IAAI;YAC7B,IAAI,EAAE,CAAC,SAAS;gBACd,IAAI,kBAAkB,CAAC;gBACvB,IAAI,kBAAkB,CAAC;gBACvB;YACF;YAEA,IAAI,EAAE,CAAC,OAAO;gBACZ,IAAI,kBAAkB,CAAC;gBACvB,IAAI,kBAAkB,CAAC;gBACvB;YACF;YACA,MAAM,QAAQ,IAAI,MAAM;YAExB,iDAAiD;YACjD,sCAAsC;YACtC,IAAI;YACJ,IAAI;YACJ,IAAI,cAAc;YAClB,IAAI,sBAAsB,CAAC;YAC3B,IAAI,gBAAgB;YACpB,IAAI,YAAY;YAEhB,IAAI,EAAE,CAAC,QAAQ,SAAU,KAAK;gBAC5B,IAAI,CAAC,KAAK;oBACR,MAAM;oBACN,IAAI,OAAO,MAAM;wBACf,MAAM,IAAI,KAAK,CAAC,IAAI,MAAM;oBAC5B;oBACA,YAAY,IAAI,MAAM;gBACxB,OAAO;oBACL,IAAI,MAAM,MAAM,GAAG,IAAI,MAAM,GAAG,WAAW;wBACzC,gBAAgB,AAAC,IAAI,MAAM,GAAG,IAAK,MAAM,MAAM;wBAC/C,IAAI,gBAAgB,0BAA0B;4BAC5C,gBAAgB,IAAI,MAAM,GAAG,MAAM,MAAM,GAAG;wBAC9C;wBACA,YAAY,OAAO,KAAK,CAAC;wBACzB,IAAI,IAAI,CAAC,WAAW,GAAG,GAAG;wBAC1B,MAAM;oBACR;oBACA,MAAM,IAAI,CAAC,KAAK;oBAChB,aAAa,MAAM,MAAM;gBAC3B;gBAEA,IAAI,MAAM;gBACV,IAAI,SAAS;gBAEb,MAAO,MAAM,OAAQ;oBACnB,IAAI,wBAAwB;wBAC1B,IAAI,GAAG,CAAC,IAAI,KAAK,UAAU;4BACzB,EAAE;wBACJ;wBACA,yBAAyB;oBAC3B;oBAEA,IAAI,aAAa,CAAC;oBAClB,IAAI,cAAc;oBAClB,IAAI;oBAEJ,IAAK,IAAI,IAAI,aAAa,aAAa,KAAK,IAAI,QAAQ,EAAE,EAAG;wBAC3D,IAAI,GAAG,CAAC,EAAE;wBACV,IAAI,MAAM,OAAO;4BACf,IAAI,cAAc,GAAG;gCACnB,cAAc,IAAI;4BACpB;wBACF,OAAO,IAAI,MAAM,gBAAgB;4BAC/B,yBAAyB;4BACzB,aAAa,IAAI;wBACnB,OAAO,IAAI,MAAM,UAAU;4BACzB,aAAa,IAAI;wBACnB;oBACF;oBAEA,IAAI,aAAa,GAAG;wBAClB,cAAc,SAAS;wBACvB,sBAAsB;wBACtB;oBACF,OAAO;wBACL,cAAc;wBACd,sBAAsB,CAAC;oBACzB;oBAEA,qBAAqB,KAAK,KAAK,aAAa;oBAE5C,OAAO,aAAa;gBACtB;gBAEA,IAAI,QAAQ,QAAQ;oBAClB,MAAM,KAAK;oBACX,YAAY;gBACd,OAAO,IAAI,MAAM,GAAG;oBAClB,MAAM,IAAI,KAAK,CAAC,KAAK;oBACrB,YAAY,IAAI,MAAM;gBACxB;YACF;QACF;QAEA,IAAI,EAAE,CAAC,SAAS,SAAU,GAAG;YAC3B,KAAK,oBAAoB,GAAG;YAC5B,mBAAmB,IAAI,OAAO;QAChC;QAEA,IAAI,IAAI,UAAU,EAAE,IAAI,UAAU,CAAC;QACnC,IAAI,GAAG;IACT;IAEA;IAEA,SAAS;QACP,IAAI,KAAK,SAAS,CAAC,SAAS,CAAC,EAAE,EAAE,MAAM,GAAG,GAAG;YAC3C,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM;QACxB;IACF;IAEA,IAAI,CAAC,MAAM,GAAG;QACZ,IAAI,eAAe,YAAY,MAAM,EAAE;QACvC,aAAa,YAAY,MAAM;QAC/B,IAAI,IAAI,KAAK,EAAE,IAAI,KAAK;QACxB,IAAI,IAAI,GAAG,IAAI,IAAI,GAAG,CAAC,KAAK,EAAE,IAAI,GAAG,CAAC,KAAK;IAC7C;IAEA,SAAS,qBAAsB,GAAG,EAAE,GAAG,EAAE,WAAW,EAAE,UAAU;QAC9D,IAAI,eAAe,GAAG;YACpB,IAAI,KAAK,MAAM,GAAG,GAAG;gBACnB,IAAI,OAAO,aAAa;gBACxB,MAAM,MAAM,IAAI,aAAa,MAAM;oBACjC,MAAM,KAAK,KAAK,CAAC,GAAG,CAAC;oBACrB,aAAa;oBACb,QAAQ,IAAI,IAAI,KAAK,MAAM;gBAC7B;gBACA,OAAO;YACT;YACA,YAAY,KAAK;QACnB,OAAO,IAAI,cAAc,GAAG;YAC1B,IAAI,UAAU,cAAc;YAC5B,IAAI,OAAO;YACX,IAAI,QAAQ,IAAI,KAAK,CAAC,KAAK,MAAM,CAAC,UAAU,aAAa,WAAW,GAAG,QAAQ;YAE/E,IAAI,SAAS;gBACX,OAAO;YACT,OAAO,IAAI,GAAG,CAAC,MAAM,cAAc,EAAE,KAAK,OAAO;gBAC/C,OAAO,cAAc;YACvB,OAAO;gBACL,OAAO,cAAc;YACvB;YACA,OAAO;YAEP,IAAI,cAAc,aAAa;YAC/B,IAAI,QAAQ,IAAI,KAAK,CAAC,KAAK,MAAM,aAAa,QAAQ;YAEtD,IAAI,UAAU,QAAQ;gBACpB,QAAQ,QAAQ;YAClB,OAAO,IAAI,UAAU,SAAS;gBAC5B,YAAY;YACd,OAAO,IAAI,UAAU,MAAM;gBACzB,cAAc;YAChB,OAAO,IAAI,UAAU,SAAS;gBAC5B,IAAI,QAAQ,SAAS,OAAO;gBAC5B,IAAI,CAAC,OAAO,KAAK,CAAC,QAAQ;oBACxB,KAAK,iBAAiB,GAAG;gBAC3B;YACF;QACF;IACF;AACF;AAEA,OAAO,OAAO,GAAG;AAEjB,KAAK,QAAQ,CAAC,aAAa,OAAO,YAAY;AAC9C,YAAY,SAAS,CAAC,WAAW,GAAG,aAAa,4BAA4B;AAE7E;IAAC;IAAQ;IAAS;CAAU,CAAC,OAAO,CAAC,SAAU,MAAM;IACnD,OAAO,cAAc,CAAC,YAAY,SAAS,EAAE,OAAO,QAAQ;QAC1D;;;;;KAKC,GACD,KAAK,SAAS;YACZ,IAAI,WAAW,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;YACxC,OAAO,WAAY,SAAS,SAAS,GAAG,SAAS,SAAS,GAAG,WAAY;QAC3E;QAEA;;;;;;KAMC,GACD,KAAK,SAAS,IAAK,QAAQ;YACzB,IAAI,CAAC,kBAAkB,CAAC;YACxB,IAAI,CAAC,gBAAgB,CAAC,QAAQ;QAChC;IACF;AACF;AAEA;;CAEC,GACD,OAAO,cAAc,CAAC,aAAa,cAAc;IAAC,YAAY;IAAM,OAAO;AAAC;AAC5E,OAAO,cAAc,CAAC,aAAa,QAAQ;IAAC,YAAY;IAAM,OAAO;AAAC;AACtE,OAAO,cAAc,CAAC,aAAa,UAAU;IAAC,YAAY;IAAM,OAAO;AAAC;AAExE,YAAY,SAAS,CAAC,UAAU,GAAG;AACnC,YAAY,SAAS,CAAC,IAAI,GAAG;AAC7B,YAAY,SAAS,CAAC,MAAM,GAAG;AAE/B;;;;;CAKC,GACD,YAAY,SAAS,CAAC,KAAK,GAAG;IAC5B,IAAI,CAAC,MAAM;AACb;AAEA;;;;;;;;CAQC,GACD,YAAY,SAAS,CAAC,gBAAgB,GAAG,SAAS,iBAAkB,IAAI,EAAE,QAAQ;IAChF,IAAI,OAAO,aAAa,YAAY;QAClC,iEAAiE;QACjE,SAAS,SAAS,GAAG;QACrB,IAAI,CAAC,EAAE,CAAC,MAAM;IAChB;AACF;AAEA;;;;;;CAMC,GACD,YAAY,SAAS,CAAC,aAAa,GAAG,SAAS,cAAe,KAAK;IACjE,IAAI,CAAC,MAAM,IAAI,EAAE;QACf,MAAM,IAAI,MAAM;IAClB;IACA,sEAAsE;IACtE,sDAAsD;IACtD,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,EAAE,MAAM,MAAM;AACpC;AAEA;;;;;;;;CAQC,GACD,YAAY,SAAS,CAAC,mBAAmB,GAAG,SAAS,oBAAqB,IAAI,EAAE,QAAQ;IACtF,IAAI,OAAO,aAAa,YAAY;QAClC,SAAS,SAAS,GAAG;QACrB,IAAI,CAAC,cAAc,CAAC,MAAM;IAC5B;AACF;AAEA;;;;;CAKC,GACD,SAAS,MAAO,IAAI,EAAE,kBAAkB;IACtC,OAAO,cAAc,CAAC,IAAI,EAAE,QAAQ;QAAE,UAAU;QAAO,OAAO;QAAM,YAAY;IAAK;IACrF,IAAI,oBAAoB;QACtB,IAAK,IAAI,KAAK,mBAAoB;YAChC,IAAI,mBAAmB,cAAc,CAAC,IAAI;gBACxC,OAAO,cAAc,CAAC,IAAI,EAAE,GAAG;oBAAE,UAAU;oBAAO,OAAO,kBAAkB,CAAC,EAAE;oBAAE,YAAY;gBAAK;YACnG;QACF;IACF;AACF;AAEA;;;;;CAKC,GACD,SAAS,aAAc,IAAI,EAAE,aAAa;IACxC,OAAO,cAAc,CAAC,IAAI,EAAE,QAAQ;QAAE,UAAU;QAAO,OAAO;QAAM,YAAY;IAAK;IACrF,IAAK,IAAI,KAAK,cAAe;QAC3B,IAAI,cAAc,cAAc,CAAC,IAAI;YACnC,OAAO,cAAc,CAAC,IAAI,EAAE,GAAG;gBAAE,UAAU;gBAAO,OAAO,aAAa,CAAC,EAAE;gBAAE,YAAY;YAAK;QAC9F;IACF;AACF;AAEA;;;;;;CAMC,GACD,SAAS,oBAAqB,OAAO;IACnC,IAAI,OAAO,CAAC;IACZ,IAAK,IAAI,OAAO,QAAS;QACvB,IAAI,eAAe,IAAI,CAAC,MAAM;YAC5B;QACF;QAEA,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI;IAC1B;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 492, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/eventsource/node.js"], "sourcesContent": ["module.exports = require('eventsource')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}