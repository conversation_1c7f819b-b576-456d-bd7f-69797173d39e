{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/Logo.tsx"], "sourcesContent": ["import React from 'react';\n\nconst Logo = ({ className = \"\" }: { className?: string }) => {\n  return (\n    <div className={`inline-flex items-center justify-center w-10 h-10 bg-gray-900 rounded-lg ${className}`}>\n      <span className=\"text-white font-bold text-xl\">M</span>\n    </div>\n  );\n};\n\nexport default Logo;\n"], "names": [], "mappings": ";;;;;AAEA,MAAM,OAAO,CAAC,EAAE,YAAY,EAAE,EAA0B;IACtD,qBACE,6LAAC;QAAI,WAAW,CAAC,yEAAyE,EAAE,WAAW;kBACrG,cAAA,6LAAC;YAAK,WAAU;sBAA+B;;;;;;;;;;;AAGrD;KANM;uCAQS", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/SimpleHeaderChat.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { MessageCircle } from 'lucide-react';\n\nconst SimpleHeaderChat: React.FC = () => {\n  const handleChatOpen = () => {\n    // Try to open Crisp chat if available\n    if (typeof window !== 'undefined' && (window as any).$crisp) {\n      (window as any).$crisp.push(['do', 'chat:open']);\n    } else {\n      // Fallback to mailto if Crisp is not available\n      window.location.href = 'mailto:<EMAIL>?subject=Chat%20Request';\n    }\n\n    // Track chat interaction for analytics\n    if (typeof window !== 'undefined' && (window as any).gtag) {\n      (window as any).gtag('event', 'chat_opened', {\n        event_category: 'engagement',\n        event_label: 'header_chat'\n      });\n    }\n  };\n\n  return (\n    <button\n      onClick={handleChatOpen}\n      className=\"inline-flex items-center gap-2 text-gray-600 dark:text-gray-300 hover:text-dark-charcoal dark:hover:text-white transition-colors duration-200\"\n      aria-label=\"Open chat\"\n    >\n      <MessageCircle className=\"w-5 h-5\" />\n      <span className=\"hidden sm:inline\">Chat</span>\n    </button>\n  );\n};\n\nexport default SimpleHeaderChat;\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKA,MAAM,mBAA6B;IACjC,MAAM,iBAAiB;QACrB,sCAAsC;QACtC,IAAI,aAAkB,eAAe,AAAC,OAAe,MAAM,EAAE;YAC1D,OAAe,MAAM,CAAC,IAAI,CAAC;gBAAC;gBAAM;aAAY;QACjD,OAAO;YACL,+CAA+C;YAC/C,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;QAEA,uCAAuC;QACvC,IAAI,aAAkB,eAAe,AAAC,OAAe,IAAI,EAAE;YACxD,OAAe,IAAI,CAAC,SAAS,eAAe;gBAC3C,gBAAgB;gBAChB,aAAa;YACf;QACF;IACF;IAEA,qBACE,6LAAC;QACC,SAAS;QACT,WAAU;QACV,cAAW;;0BAEX,6LAAC,2NAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;0BACzB,6LAAC;gBAAK,WAAU;0BAAmB;;;;;;;;;;;;AAGzC;KA7BM;uCA+BS", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/SimpleDarkModeToggle.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Sun, Moon } from 'lucide-react';\nimport { useTheme } from '../contexts/ThemeContext';\n\ninterface SimpleDarkModeToggleProps {\n  className?: string;\n  size?: 'sm' | 'md' | 'lg';\n}\n\nconst SimpleDarkModeToggle: React.FC<SimpleDarkModeToggleProps> = ({\n  className = '',\n  size = 'md'\n}) => {\n  const { theme, toggleTheme } = useTheme();\n\n  const getSizeClasses = () => {\n    switch (size) {\n      case 'sm':\n        return 'w-8 h-8 text-sm';\n      case 'lg':\n        return 'w-12 h-12 text-lg';\n      default:\n        return 'w-10 h-10 text-base';\n    }\n  };\n\n  return (\n    <button\n      onClick={toggleTheme}\n      className={`${getSizeClasses()} flex items-center justify-center rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-200 ${className}`}\n      aria-label={`Switch to ${theme === 'dark' ? 'light' : 'dark'} mode`}\n    >\n      {theme === 'dark' ? (\n        <Sun className=\"w-5 h-5\" />\n      ) : (\n        <Moon className=\"w-5 h-5\" />\n      )}\n    </button>\n  );\n};\n\nexport default SimpleDarkModeToggle;\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;;;AAJA;;;AAWA,MAAM,uBAA4D,CAAC,EACjE,YAAY,EAAE,EACd,OAAO,IAAI,EACZ;;IACC,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IAEtC,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QACC,SAAS;QACT,WAAW,GAAG,iBAAiB,gLAAgL,EAAE,WAAW;QAC5N,cAAY,CAAC,UAAU,EAAE,UAAU,SAAS,UAAU,OAAO,KAAK,CAAC;kBAElE,UAAU,uBACT,6LAAC,mMAAA,CAAA,MAAG;YAAC,WAAU;;;;;iCAEf,6LAAC,qMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;;;;;;AAIxB;GA9BM;;QAI2B,mIAAA,CAAA,WAAQ;;;KAJnC;uCAgCS", "debugId": null}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/config/site.ts"], "sourcesContent": ["/**\n * Site Configuration\n * \n * Centralized configuration for static, developer-controlled values.\n * This file contains non-sensitive data that doesn't belong in the CMS.\n */\n\n// Site Identity\nexport const SITE_CONFIG = {\n  name: 'Mobilify',\n  tagline: 'Turn Your Website or Idea Into a Custom Mobile App',\n  description: 'Mobilify converts your existing website or new idea into a high-quality, native mobile app for iOS and Android. Get a beautiful, custom-designed app without the complexity.',\n  url: process.env.NEXT_PUBLIC_SITE_URL || 'https://mobilify.app',\n  author: 'Mobilify Team',\n} as const;\n\n// Contact Information\nexport const CONTACT_INFO = {\n  email: '<EMAIL>',\n  phone: '+****************', // Update with actual phone\n  address: {\n    street: '123 Tech Street',\n    city: 'San Francisco',\n    state: 'CA',\n    zip: '94105',\n    country: 'USA'\n  }\n} as const;\n\n// Social Media Links\nexport const SOCIAL_LINKS = {\n  twitter: 'https://twitter.com/mobilify',\n  linkedin: 'https://linkedin.com/company/mobilify',\n  github: 'https://github.com/mobilify',\n  facebook: 'https://facebook.com/mobilify',\n  instagram: 'https://instagram.com/mobilify'\n} as const;\n\n// Navigation Structure\nexport const NAVIGATION = {\n  main: [\n    { label: 'Services', href: '#services-overview', id: 'services-overview' },\n    { label: 'How It Works', href: '#process', id: 'process' },\n    { label: 'About Us', href: '#about', id: 'about' },\n  ],\n  footer: {\n    company: [\n      { label: 'About', href: '/about' },\n      { label: 'Services', href: '/services' },\n      { label: 'Blog', href: '/blog' },\n      { label: 'FAQ', href: '/faq' },\n    ],\n    legal: [\n      { label: 'Privacy Policy', href: '/privacy' },\n      { label: 'Terms of Service', href: '/terms' },\n      { label: 'Cookie Policy', href: '/cookies' },\n    ],\n    support: [\n      { label: 'Contact Us', href: '#contact' },\n      { label: 'Help Center', href: '/help' },\n      { label: 'Documentation', href: '/docs' },\n    ]\n  }\n} as const;\n\n// Default/Fallback Content (used when CMS is unavailable)\nexport const FALLBACK_CONTENT = {\n  hero: {\n    headline: 'Your Idea. Your App. Realized.',\n    subtext: 'Mobilify transforms your concepts and existing websites into stunning, high-performance mobile apps. We are the bridge from vision to launch.',\n    buttonText: 'See How It Works'\n  },\n  contact: {\n    headline: 'Ready to Build Your Mobile Future?',\n    subtext: \"Let's discuss your project. We're happy to provide a free, no-obligation consultation and quote.\",\n    buttonText: 'Send Message',\n    successMessage: 'Thank you! Your message has been sent successfully. We\\'ll get back to you within 24 hours.',\n    errorMessage: 'Sorry, there was an error sending your message. Please try again or contact us directly.'\n  },\n  services: {\n    headline: 'Our Services',\n    subtext: 'Choose the perfect solution for your mobile app needs'\n  },\n  process: {\n    headline: 'How It Works',\n    subtext: 'Our proven process to turn your idea into a successful mobile app'\n  },\n  about: {\n    headline: 'About Mobilify',\n    subtext: 'We are passionate about helping businesses and entrepreneurs bring their ideas to life through mobile technology.'\n  }\n} as const;\n\n// Service Offerings\nexport const SERVICES = {\n  starter: {\n    name: 'Starter App',\n    description: 'Perfect for converting existing websites into mobile apps.',\n    price: 'Starting at $5,000',\n    features: ['Website Conversion', 'iOS & Android', 'Basic Features'],\n    popular: false\n  },\n  custom: {\n    name: 'Custom App',\n    description: 'Turn your new ideas into reality with custom development.',\n    price: 'Starting at $15,000',\n    features: ['Idea to App', 'Custom UI/UX', 'Advanced Features'],\n    popular: true\n  },\n  enterprise: {\n    name: 'Enterprise Solution',\n    description: 'Comprehensive solutions for large-scale applications.',\n    price: 'Custom Pricing',\n    features: ['Full Development', 'Scalable Architecture', 'Ongoing Support'],\n    popular: false\n  }\n} as const;\n\n// External Service URLs\nexport const EXTERNAL_SERVICES = {\n  analytics: {\n    googleAnalytics: process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID,\n  },\n  forms: {\n    web3forms: process.env.NEXT_PUBLIC_WEB3FORMS_ACCESS_KEY,\n  },\n  chat: {\n    crisp: process.env.NEXT_PUBLIC_CRISP_WEBSITE_ID,\n    tawkTo: process.env.NEXT_PUBLIC_TAWK_TO_PROPERTY_ID,\n  },\n  newsletter: {\n    mailchimp: process.env.NEXT_PUBLIC_MAILCHIMP_API_KEY,\n    convertkit: process.env.CONVERTKIT_API_KEY,\n  }\n} as const;\n\n// SEO Configuration\nexport const SEO_CONFIG = {\n  defaultTitle: SITE_CONFIG.name + ' | ' + SITE_CONFIG.tagline,\n  titleTemplate: '%s | ' + SITE_CONFIG.name + ' - Custom Mobile App Development',\n  description: SITE_CONFIG.description,\n  keywords: [\n    'mobile app development',\n    'website to app conversion',\n    'custom mobile apps',\n    'iOS app development',\n    'Android app development',\n    'mobile app builder',\n    'app development services',\n    'native mobile apps',\n    'mobile app design',\n    'app development company'\n  ],\n  openGraph: {\n    type: 'website',\n    locale: 'en_US',\n    url: SITE_CONFIG.url,\n    siteName: SITE_CONFIG.name,\n    images: [\n      {\n        url: '/og-image.png',\n        width: 1200,\n        height: 630,\n        alt: SITE_CONFIG.name + ' - ' + SITE_CONFIG.tagline,\n      },\n    ],\n  },\n  twitter: {\n    handle: '@mobilify',\n    site: '@mobilify',\n    cardType: 'summary_large_image',\n  },\n} as const;\n\n// Animation Configuration\nexport const ANIMATION_CONFIG = {\n  duration: {\n    fast: 0.2,\n    normal: 0.4,\n    slow: 0.6,\n    verySlow: 1.0\n  },\n  easing: {\n    easeInOut: [0.4, 0, 0.2, 1],\n    easeOut: [0, 0, 0.2, 1],\n    easeIn: [0.4, 0, 1, 1]\n  }\n} as const;\n\n// Development Configuration\nexport const DEV_CONFIG = {\n  isDevelopment: process.env.NODE_ENV === 'development',\n  isProduction: process.env.NODE_ENV === 'production',\n  enableDebugLogs: process.env.NODE_ENV === 'development',\n  enableAnalytics: process.env.NODE_ENV === 'production',\n} as const;\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GAED,gBAAgB;;;;;;;;;;;;;AAKT;AAJA,MAAM,cAAc;IACzB,MAAM;IACN,SAAS;IACT,aAAa;IACb,KAAK,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI;IACzC,QAAQ;AACV;AAGO,MAAM,eAAe;IAC1B,OAAO;IACP,OAAO;IACP,SAAS;QACP,QAAQ;QACR,MAAM;QACN,OAAO;QACP,KAAK;QACL,SAAS;IACX;AACF;AAGO,MAAM,eAAe;IAC1B,SAAS;IACT,UAAU;IACV,QAAQ;IACR,UAAU;IACV,WAAW;AACb;AAGO,MAAM,aAAa;IACxB,MAAM;QACJ;YAAE,OAAO;YAAY,MAAM;YAAsB,IAAI;QAAoB;QACzE;YAAE,OAAO;YAAgB,MAAM;YAAY,IAAI;QAAU;QACzD;YAAE,OAAO;YAAY,MAAM;YAAU,IAAI;QAAQ;KAClD;IACD,QAAQ;QACN,SAAS;YACP;gBAAE,OAAO;gBAAS,MAAM;YAAS;YACjC;gBAAE,OAAO;gBAAY,MAAM;YAAY;YACvC;gBAAE,OAAO;gBAAQ,MAAM;YAAQ;YAC/B;gBAAE,OAAO;gBAAO,MAAM;YAAO;SAC9B;QACD,OAAO;YACL;gBAAE,OAAO;gBAAkB,MAAM;YAAW;YAC5C;gBAAE,OAAO;gBAAoB,MAAM;YAAS;YAC5C;gBAAE,OAAO;gBAAiB,MAAM;YAAW;SAC5C;QACD,SAAS;YACP;gBAAE,OAAO;gBAAc,MAAM;YAAW;YACxC;gBAAE,OAAO;gBAAe,MAAM;YAAQ;YACtC;gBAAE,OAAO;gBAAiB,MAAM;YAAQ;SACzC;IACH;AACF;AAGO,MAAM,mBAAmB;IAC9B,MAAM;QACJ,UAAU;QACV,SAAS;QACT,YAAY;IACd;IACA,SAAS;QACP,UAAU;QACV,SAAS;QACT,YAAY;QACZ,gBAAgB;QAChB,cAAc;IAChB;IACA,UAAU;QACR,UAAU;QACV,SAAS;IACX;IACA,SAAS;QACP,UAAU;QACV,SAAS;IACX;IACA,OAAO;QACL,UAAU;QACV,SAAS;IACX;AACF;AAGO,MAAM,WAAW;IACtB,SAAS;QACP,MAAM;QACN,aAAa;QACb,OAAO;QACP,UAAU;YAAC;YAAsB;YAAiB;SAAiB;QACnE,SAAS;IACX;IACA,QAAQ;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,UAAU;YAAC;YAAe;YAAgB;SAAoB;QAC9D,SAAS;IACX;IACA,YAAY;QACV,MAAM;QACN,aAAa;QACb,OAAO;QACP,UAAU;YAAC;YAAoB;YAAyB;SAAkB;QAC1E,SAAS;IACX;AACF;AAGO,MAAM,oBAAoB;IAC/B,WAAW;QACT,eAAe;IACjB;IACA,OAAO;QACL,SAAS;IACX;IACA,MAAM;QACJ,KAAK;QACL,MAAM;IACR;IACA,YAAY;QACV,SAAS;QACT,YAAY,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,kBAAkB;IAC5C;AACF;AAGO,MAAM,aAAa;IACxB,cAAc,YAAY,IAAI,GAAG,QAAQ,YAAY,OAAO;IAC5D,eAAe,UAAU,YAAY,IAAI,GAAG;IAC5C,aAAa,YAAY,WAAW;IACpC,UAAU;QACR;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,WAAW;QACT,MAAM;QACN,QAAQ;QACR,KAAK,YAAY,GAAG;QACpB,UAAU,YAAY,IAAI;QAC1B,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK,YAAY,IAAI,GAAG,QAAQ,YAAY,OAAO;YACrD;SACD;IACH;IACA,SAAS;QACP,QAAQ;QACR,MAAM;QACN,UAAU;IACZ;AACF;AAGO,MAAM,mBAAmB;IAC9B,UAAU;QACR,MAAM;QACN,QAAQ;QACR,MAAM;QACN,UAAU;IACZ;IACA,QAAQ;QACN,WAAW;YAAC;YAAK;YAAG;YAAK;SAAE;QAC3B,SAAS;YAAC;YAAG;YAAG;YAAK;SAAE;QACvB,QAAQ;YAAC;YAAK;YAAG;YAAG;SAAE;IACxB;AACF;AAGO,MAAM,aAAa;IACxB,eAAe,oDAAyB;IACxC,cAAc,oDAAyB;IACvC,iBAAiB,oDAAyB;IAC1C,iBAAiB,oDAAyB;AAC5C", "debugId": null}}, {"offset": {"line": 443, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/hooks/useAnalytics.ts"], "sourcesContent": ["/**\n * Analytics Hook\n * \n * Unified interface for all analytics tracking across the site.\n * Abstracts away the implementation details of Google Analytics.\n */\n\nimport { useCallback } from 'react';\nimport { DEV_CONFIG } from '@/config/site';\n\n// Analytics event types\nexport interface AnalyticsEvent {\n  name: string;\n  category?: string;\n  label?: string;\n  value?: number;\n  custom_parameters?: Record<string, any>;\n}\n\n// Predefined event types for type safety\nexport const ANALYTICS_EVENTS = {\n  // Navigation & CTA Events\n  HERO_CTA_CLICK: 'hero_cta_click',\n  CONTACT_CTA_CLICK: 'contact_cta_click',\n  SERVICES_CTA_CLICK: 'services_cta_click',\n  \n  // Demo Interaction Events\n  DEMO_INTERACTION: 'demo_interaction',\n  DEMO_TAB_SWITCH: 'demo_tab_switch',\n  DEMO_ANIMATION_COMPLETE: 'demo_animation_complete',\n  DEMO_PREVIEW_CLICK: 'demo_preview_click',\n  \n  // Form Events\n  FORM_SUBMIT: 'form_submit',\n  FORM_SUCCESS: 'form_success',\n  FORM_ERROR: 'form_error',\n  FORM_FIELD_FOCUS: 'form_field_focus',\n  \n  // Content Interaction Events\n  BLOG_POST_CLICK: 'blog_post_click',\n  FAQ_ITEM_EXPAND: 'faq_item_expand',\n  EXTERNAL_LINK_CLICK: 'external_link_click',\n  \n  // Chat & Support Events\n  CHAT_TRIGGER_CLICK: 'chat_trigger_click',\n  PHONE_CLICK: 'phone_click',\n  EMAIL_CLICK: 'email_click',\n  \n  // Page Events\n  PAGE_VIEW: 'page_view',\n  SCROLL_DEPTH: 'scroll_depth',\n  TIME_ON_PAGE: 'time_on_page',\n} as const;\n\n// Hook implementation\nexport function useAnalytics() {\n  const trackEvent = useCallback((\n    eventName: string,\n    eventData?: Partial<AnalyticsEvent>\n  ) => {\n    // Only track in production or when explicitly enabled\n    if (!DEV_CONFIG.enableAnalytics && !DEV_CONFIG.isDevelopment) {\n      return;\n    }\n\n    // Log in development for debugging\n    if (DEV_CONFIG.isDevelopment && DEV_CONFIG.enableDebugLogs) {\n      console.log('📊 Analytics Event:', {\n        event: eventName,\n        ...eventData\n      });\n    }\n\n    // Send to Google Analytics if available\n    if (typeof window !== 'undefined' && (window as any).gtag) {\n      try {\n        (window as any).gtag('event', eventName, {\n          event_category: eventData?.category || 'engagement',\n          event_label: eventData?.label,\n          value: eventData?.value,\n          ...eventData?.custom_parameters\n        });\n      } catch (error) {\n        console.warn('Failed to send analytics event:', error);\n      }\n    }\n  }, []);\n\n  // Convenience methods for common events\n  const trackNavigation = useCallback((destination: string, source?: string) => {\n    trackEvent(ANALYTICS_EVENTS.HERO_CTA_CLICK, {\n      category: 'navigation',\n      label: destination,\n      custom_parameters: { source }\n    });\n  }, [trackEvent]);\n\n  const trackFormInteraction = useCallback((\n    action: 'submit' | 'success' | 'error' | 'field_focus',\n    formName: string,\n    fieldName?: string\n  ) => {\n    const eventMap = {\n      submit: ANALYTICS_EVENTS.FORM_SUBMIT,\n      success: ANALYTICS_EVENTS.FORM_SUCCESS,\n      error: ANALYTICS_EVENTS.FORM_ERROR,\n      field_focus: ANALYTICS_EVENTS.FORM_FIELD_FOCUS\n    };\n\n    trackEvent(eventMap[action], {\n      category: 'form_interaction',\n      label: formName,\n      custom_parameters: { field_name: fieldName }\n    });\n  }, [trackEvent]);\n\n  const trackDemoInteraction = useCallback((\n    action: 'tab_switch' | 'preview_click' | 'animation_complete',\n    details?: string\n  ) => {\n    const eventMap = {\n      tab_switch: ANALYTICS_EVENTS.DEMO_TAB_SWITCH,\n      preview_click: ANALYTICS_EVENTS.DEMO_PREVIEW_CLICK,\n      animation_complete: ANALYTICS_EVENTS.DEMO_ANIMATION_COMPLETE\n    };\n\n    trackEvent(eventMap[action], {\n      category: 'demo_interaction',\n      label: details\n    });\n  }, [trackEvent]);\n\n  const trackContentInteraction = useCallback((\n    contentType: 'blog_post' | 'faq_item' | 'external_link',\n    contentId: string,\n    action?: string\n  ) => {\n    const eventMap = {\n      blog_post: ANALYTICS_EVENTS.BLOG_POST_CLICK,\n      faq_item: ANALYTICS_EVENTS.FAQ_ITEM_EXPAND,\n      external_link: ANALYTICS_EVENTS.EXTERNAL_LINK_CLICK\n    };\n\n    trackEvent(eventMap[contentType], {\n      category: 'content_interaction',\n      label: contentId,\n      custom_parameters: { action }\n    });\n  }, [trackEvent]);\n\n  const trackSupportInteraction = useCallback((\n    method: 'chat' | 'phone' | 'email',\n    source?: string\n  ) => {\n    const eventMap = {\n      chat: ANALYTICS_EVENTS.CHAT_TRIGGER_CLICK,\n      phone: ANALYTICS_EVENTS.PHONE_CLICK,\n      email: ANALYTICS_EVENTS.EMAIL_CLICK\n    };\n\n    trackEvent(eventMap[method], {\n      category: 'support_interaction',\n      label: method,\n      custom_parameters: { source }\n    });\n  }, [trackEvent]);\n\n  const trackPageView = useCallback((pagePath: string, pageTitle?: string) => {\n    trackEvent(ANALYTICS_EVENTS.PAGE_VIEW, {\n      category: 'page_interaction',\n      label: pagePath,\n      custom_parameters: {\n        page_title: pageTitle,\n        page_path: pagePath\n      }\n    });\n  }, [trackEvent]);\n\n  const trackFormSubmission = useCallback((formName: string, success: boolean, metadata?: Record<string, unknown>) => {\n    trackEvent(ANALYTICS_EVENTS.FORM_SUBMIT, {\n      category: 'form',\n      label: formName,\n      custom_parameters: {\n        success,\n        ...metadata\n      }\n    });\n  }, [trackEvent]);\n\n  return {\n    // Core tracking function\n    trackEvent,\n\n    // Convenience methods\n    trackNavigation,\n    trackFormInteraction,\n    trackFormSubmission,\n    trackDemoInteraction,\n    trackContentInteraction,\n    trackSupportInteraction,\n    trackPageView,\n\n    // Event constants for external use\n    EVENTS: ANALYTICS_EVENTS\n  };\n}\n\n\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;AAED;AACA;;;;AAYO,MAAM,mBAAmB;IAC9B,0BAA0B;IAC1B,gBAAgB;IAChB,mBAAmB;IACnB,oBAAoB;IAEpB,0BAA0B;IAC1B,kBAAkB;IAClB,iBAAiB;IACjB,yBAAyB;IACzB,oBAAoB;IAEpB,cAAc;IACd,aAAa;IACb,cAAc;IACd,YAAY;IACZ,kBAAkB;IAElB,6BAA6B;IAC7B,iBAAiB;IACjB,iBAAiB;IACjB,qBAAqB;IAErB,wBAAwB;IACxB,oBAAoB;IACpB,aAAa;IACb,aAAa;IAEb,cAAc;IACd,WAAW;IACX,cAAc;IACd,cAAc;AAChB;AAGO,SAAS;;IACd,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE,CAC7B,WACA;YAEA,sDAAsD;YACtD,IAAI,CAAC,wHAAA,CAAA,aAAU,CAAC,eAAe,IAAI,CAAC,wHAAA,CAAA,aAAU,CAAC,aAAa,EAAE;gBAC5D;YACF;YAEA,mCAAmC;YACnC,IAAI,wHAAA,CAAA,aAAU,CAAC,aAAa,IAAI,wHAAA,CAAA,aAAU,CAAC,eAAe,EAAE;gBAC1D,QAAQ,GAAG,CAAC,uBAAuB;oBACjC,OAAO;oBACP,GAAG,SAAS;gBACd;YACF;YAEA,wCAAwC;YACxC,IAAI,aAAkB,eAAe,AAAC,OAAe,IAAI,EAAE;gBACzD,IAAI;oBACD,OAAe,IAAI,CAAC,SAAS,WAAW;wBACvC,gBAAgB,WAAW,YAAY;wBACvC,aAAa,WAAW;wBACxB,OAAO,WAAW;wBAClB,GAAG,WAAW,iBAAiB;oBACjC;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,IAAI,CAAC,mCAAmC;gBAClD;YACF;QACF;+CAAG,EAAE;IAEL,wCAAwC;IACxC,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,CAAC,aAAqB;YACxD,WAAW,iBAAiB,cAAc,EAAE;gBAC1C,UAAU;gBACV,OAAO;gBACP,mBAAmB;oBAAE;gBAAO;YAC9B;QACF;oDAAG;QAAC;KAAW;IAEf,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE,CACvC,QACA,UACA;YAEA,MAAM,WAAW;gBACf,QAAQ,iBAAiB,WAAW;gBACpC,SAAS,iBAAiB,YAAY;gBACtC,OAAO,iBAAiB,UAAU;gBAClC,aAAa,iBAAiB,gBAAgB;YAChD;YAEA,WAAW,QAAQ,CAAC,OAAO,EAAE;gBAC3B,UAAU;gBACV,OAAO;gBACP,mBAAmB;oBAAE,YAAY;gBAAU;YAC7C;QACF;yDAAG;QAAC;KAAW;IAEf,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE,CACvC,QACA;YAEA,MAAM,WAAW;gBACf,YAAY,iBAAiB,eAAe;gBAC5C,eAAe,iBAAiB,kBAAkB;gBAClD,oBAAoB,iBAAiB,uBAAuB;YAC9D;YAEA,WAAW,QAAQ,CAAC,OAAO,EAAE;gBAC3B,UAAU;gBACV,OAAO;YACT;QACF;yDAAG;QAAC;KAAW;IAEf,MAAM,0BAA0B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE,CAC1C,aACA,WACA;YAEA,MAAM,WAAW;gBACf,WAAW,iBAAiB,eAAe;gBAC3C,UAAU,iBAAiB,eAAe;gBAC1C,eAAe,iBAAiB,mBAAmB;YACrD;YAEA,WAAW,QAAQ,CAAC,YAAY,EAAE;gBAChC,UAAU;gBACV,OAAO;gBACP,mBAAmB;oBAAE;gBAAO;YAC9B;QACF;4DAAG;QAAC;KAAW;IAEf,MAAM,0BAA0B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE,CAC1C,QACA;YAEA,MAAM,WAAW;gBACf,MAAM,iBAAiB,kBAAkB;gBACzC,OAAO,iBAAiB,WAAW;gBACnC,OAAO,iBAAiB,WAAW;YACrC;YAEA,WAAW,QAAQ,CAAC,OAAO,EAAE;gBAC3B,UAAU;gBACV,OAAO;gBACP,mBAAmB;oBAAE;gBAAO;YAC9B;QACF;4DAAG;QAAC;KAAW;IAEf,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,CAAC,UAAkB;YACnD,WAAW,iBAAiB,SAAS,EAAE;gBACrC,UAAU;gBACV,OAAO;gBACP,mBAAmB;oBACjB,YAAY;oBACZ,WAAW;gBACb;YACF;QACF;kDAAG;QAAC;KAAW;IAEf,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,CAAC,UAAkB,SAAkB;YAC3E,WAAW,iBAAiB,WAAW,EAAE;gBACvC,UAAU;gBACV,OAAO;gBACP,mBAAmB;oBACjB;oBACA,GAAG,QAAQ;gBACb;YACF;QACF;wDAAG;QAAC;KAAW;IAEf,OAAO;QACL,yBAAyB;QACzB;QAEA,sBAAsB;QACtB;QACA;QACA;QACA;QACA;QACA;QACA;QAEA,mCAAmC;QACnC,QAAQ;IACV;AACF;GAtJgB", "debugId": null}}, {"offset": {"line": 652, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/hooks/useContactForm.ts"], "sourcesContent": ["/**\n * Contact Form Hook\n * \n * Handles form state management, validation, and submission logic\n * for the contact form component.\n */\n\nimport { useState, useCallback } from 'react';\nimport { EXTERNAL_SERVICES } from '@/config/site';\nimport { useAnalytics } from './useAnalytics';\n\n// Form data interface\nexport interface ContactFormData {\n  name: string;\n  email: string;\n  company: string;\n  projectType: string;\n  message: string;\n}\n\n// Form validation errors\nexport interface FormErrors {\n  name?: string;\n  email?: string;\n  company?: string;\n  projectType?: string;\n  message?: string;\n  general?: string;\n}\n\n// Form state\nexport interface FormState {\n  data: ContactFormData;\n  errors: FormErrors;\n  isSubmitting: boolean;\n  isSubmitted: boolean;\n  submitSuccess: boolean;\n}\n\n// Initial form data\nconst initialFormData: ContactFormData = {\n  name: '',\n  email: '',\n  company: '',\n  projectType: '',\n  message: ''\n};\n\n// Initial form state\nconst initialFormState: FormState = {\n  data: initialFormData,\n  errors: {},\n  isSubmitting: false,\n  isSubmitted: false,\n  submitSuccess: false\n};\n\n// Validation rules\nconst validateField = (name: keyof ContactFormData, value: string): string | undefined => {\n  switch (name) {\n    case 'name':\n      if (!value.trim()) return 'Name is required';\n      if (value.trim().length < 2) return 'Name must be at least 2 characters';\n      return undefined;\n      \n    case 'email':\n      if (!value.trim()) return 'Email is required';\n      const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n      if (!emailRegex.test(value)) return 'Please enter a valid email address';\n      return undefined;\n      \n    case 'company':\n      // Company is optional, but if provided should be reasonable length\n      if (value.trim() && value.trim().length < 2) return 'Company name must be at least 2 characters';\n      return undefined;\n      \n    case 'projectType':\n      if (!value.trim()) return 'Please select a project type';\n      return undefined;\n      \n    case 'message':\n      if (!value.trim()) return 'Message is required';\n      if (value.trim().length < 10) return 'Message must be at least 10 characters';\n      if (value.trim().length > 1000) return 'Message must be less than 1000 characters';\n      return undefined;\n      \n    default:\n      return undefined;\n  }\n};\n\n// Validate entire form\nconst validateForm = (data: ContactFormData): FormErrors => {\n  const errors: FormErrors = {};\n  \n  (Object.keys(data) as Array<keyof ContactFormData>).forEach(field => {\n    const error = validateField(field, data[field]);\n    if (error) {\n      errors[field] = error;\n    }\n  });\n  \n  return errors;\n};\n\n// Hook implementation\nexport function useContactForm() {\n  const [formState, setFormState] = useState<FormState>(initialFormState);\n  const { trackFormInteraction } = useAnalytics();\n\n  // Update form data\n  const updateField = useCallback((field: keyof ContactFormData, value: string) => {\n    setFormState(prev => ({\n      ...prev,\n      data: {\n        ...prev.data,\n        [field]: value\n      },\n      // Clear field error when user starts typing\n      errors: {\n        ...prev.errors,\n        [field]: undefined,\n        general: undefined\n      }\n    }));\n  }, []);\n\n  // Handle field focus for analytics\n  const handleFieldFocus = useCallback((field: keyof ContactFormData) => {\n    trackFormInteraction('field_focus', 'contact_form', field);\n  }, [trackFormInteraction]);\n\n  // Validate single field\n  const validateSingleField = useCallback((field: keyof ContactFormData) => {\n    const error = validateField(field, formState.data[field]);\n    setFormState(prev => ({\n      ...prev,\n      errors: {\n        ...prev.errors,\n        [field]: error\n      }\n    }));\n    return !error;\n  }, [formState.data]);\n\n  // Reset form\n  const resetForm = useCallback(() => {\n    setFormState(initialFormState);\n  }, []);\n\n  // Submit form\n  const submitForm = useCallback(async () => {\n    // Validate form\n    const errors = validateForm(formState.data);\n    \n    if (Object.keys(errors).length > 0) {\n      setFormState(prev => ({\n        ...prev,\n        errors\n      }));\n      return false;\n    }\n\n    // Check if Web3Forms is configured\n    if (!EXTERNAL_SERVICES.forms.web3forms) {\n      setFormState(prev => ({\n        ...prev,\n        errors: {\n          general: 'Form service is not configured. Please contact us directly.'\n        }\n      }));\n      trackFormInteraction('error', 'contact_form');\n      return false;\n    }\n\n    setFormState(prev => ({\n      ...prev,\n      isSubmitting: true,\n      errors: {}\n    }));\n\n    trackFormInteraction('submit', 'contact_form');\n\n    try {\n      const formData = new FormData();\n      formData.append('access_key', EXTERNAL_SERVICES.forms.web3forms);\n      formData.append('name', formState.data.name);\n      formData.append('email', formState.data.email);\n      formData.append('company', formState.data.company);\n      formData.append('project_type', formState.data.projectType);\n      formData.append('message', formState.data.message);\n      formData.append('from_name', 'Mobilify Contact Form');\n      formData.append('subject', `New Contact Form Submission from ${formState.data.name}`);\n\n      const response = await fetch('https://api.web3forms.com/submit', {\n        method: 'POST',\n        body: formData\n      });\n\n      const result = await response.json();\n\n      if (result.success) {\n        setFormState(prev => ({\n          ...prev,\n          isSubmitting: false,\n          isSubmitted: true,\n          submitSuccess: true\n        }));\n        trackFormInteraction('success', 'contact_form');\n        return true;\n      } else {\n        throw new Error(result.message || 'Form submission failed');\n      }\n    } catch (error) {\n      console.error('Form submission error:', error);\n      setFormState(prev => ({\n        ...prev,\n        isSubmitting: false,\n        isSubmitted: true,\n        submitSuccess: false,\n        errors: {\n          general: 'There was an error sending your message. Please try again or contact us directly.'\n        }\n      }));\n      trackFormInteraction('error', 'contact_form');\n      return false;\n    }\n  }, [formState.data, trackFormInteraction]);\n\n  return {\n    // Form state\n    formData: formState.data,\n    errors: formState.errors,\n    isSubmitting: formState.isSubmitting,\n    isSubmitted: formState.isSubmitted,\n    submitSuccess: formState.submitSuccess,\n    \n    // Form actions\n    updateField,\n    handleFieldFocus,\n    validateSingleField,\n    submitForm,\n    resetForm,\n    \n    // Validation helpers\n    isValid: Object.keys(formState.errors).length === 0,\n    hasErrors: Object.keys(formState.errors).length > 0\n  };\n}\n\n\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;AAED;AACA;AACA;;;;;AA8BA,oBAAoB;AACpB,MAAM,kBAAmC;IACvC,MAAM;IACN,OAAO;IACP,SAAS;IACT,aAAa;IACb,SAAS;AACX;AAEA,qBAAqB;AACrB,MAAM,mBAA8B;IAClC,MAAM;IACN,QAAQ,CAAC;IACT,cAAc;IACd,aAAa;IACb,eAAe;AACjB;AAEA,mBAAmB;AACnB,MAAM,gBAAgB,CAAC,MAA6B;IAClD,OAAQ;QACN,KAAK;YACH,IAAI,CAAC,MAAM,IAAI,IAAI,OAAO;YAC1B,IAAI,MAAM,IAAI,GAAG,MAAM,GAAG,GAAG,OAAO;YACpC,OAAO;QAET,KAAK;YACH,IAAI,CAAC,MAAM,IAAI,IAAI,OAAO;YAC1B,MAAM,aAAa;YACnB,IAAI,CAAC,WAAW,IAAI,CAAC,QAAQ,OAAO;YACpC,OAAO;QAET,KAAK;YACH,mEAAmE;YACnE,IAAI,MAAM,IAAI,MAAM,MAAM,IAAI,GAAG,MAAM,GAAG,GAAG,OAAO;YACpD,OAAO;QAET,KAAK;YACH,IAAI,CAAC,MAAM,IAAI,IAAI,OAAO;YAC1B,OAAO;QAET,KAAK;YACH,IAAI,CAAC,MAAM,IAAI,IAAI,OAAO;YAC1B,IAAI,MAAM,IAAI,GAAG,MAAM,GAAG,IAAI,OAAO;YACrC,IAAI,MAAM,IAAI,GAAG,MAAM,GAAG,MAAM,OAAO;YACvC,OAAO;QAET;YACE,OAAO;IACX;AACF;AAEA,uBAAuB;AACvB,MAAM,eAAe,CAAC;IACpB,MAAM,SAAqB,CAAC;IAE3B,OAAO,IAAI,CAAC,MAAuC,OAAO,CAAC,CAAA;QAC1D,MAAM,QAAQ,cAAc,OAAO,IAAI,CAAC,MAAM;QAC9C,IAAI,OAAO;YACT,MAAM,CAAC,MAAM,GAAG;QAClB;IACF;IAEA,OAAO;AACT;AAGO,SAAS;;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;IACtD,MAAM,EAAE,oBAAoB,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD;IAE5C,mBAAmB;IACnB,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,CAAC,OAA8B;YAC7D;2DAAa,CAAA,OAAQ,CAAC;wBACpB,GAAG,IAAI;wBACP,MAAM;4BACJ,GAAG,KAAK,IAAI;4BACZ,CAAC,MAAM,EAAE;wBACX;wBACA,4CAA4C;wBAC5C,QAAQ;4BACN,GAAG,KAAK,MAAM;4BACd,CAAC,MAAM,EAAE;4BACT,SAAS;wBACX;oBACF,CAAC;;QACH;kDAAG,EAAE;IAEL,mCAAmC;IACnC,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,CAAC;YACpC,qBAAqB,eAAe,gBAAgB;QACtD;uDAAG;QAAC;KAAqB;IAEzB,wBAAwB;IACxB,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE,CAAC;YACvC,MAAM,QAAQ,cAAc,OAAO,UAAU,IAAI,CAAC,MAAM;YACxD;mEAAa,CAAA,OAAQ,CAAC;wBACpB,GAAG,IAAI;wBACP,QAAQ;4BACN,GAAG,KAAK,MAAM;4BACd,CAAC,MAAM,EAAE;wBACX;oBACF,CAAC;;YACD,OAAO,CAAC;QACV;0DAAG;QAAC,UAAU,IAAI;KAAC;IAEnB,aAAa;IACb,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE;YAC5B,aAAa;QACf;gDAAG,EAAE;IAEL,cAAc;IACd,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE;YAC7B,gBAAgB;YAChB,MAAM,SAAS,aAAa,UAAU,IAAI;YAE1C,IAAI,OAAO,IAAI,CAAC,QAAQ,MAAM,GAAG,GAAG;gBAClC;8DAAa,CAAA,OAAQ,CAAC;4BACpB,GAAG,IAAI;4BACP;wBACF,CAAC;;gBACD,OAAO;YACT;YAEA,mCAAmC;YACnC,IAAI,CAAC,wHAAA,CAAA,oBAAiB,CAAC,KAAK,CAAC,SAAS,EAAE;gBACtC;8DAAa,CAAA,OAAQ,CAAC;4BACpB,GAAG,IAAI;4BACP,QAAQ;gCACN,SAAS;4BACX;wBACF,CAAC;;gBACD,qBAAqB,SAAS;gBAC9B,OAAO;YACT;YAEA;0DAAa,CAAA,OAAQ,CAAC;wBACpB,GAAG,IAAI;wBACP,cAAc;wBACd,QAAQ,CAAC;oBACX,CAAC;;YAED,qBAAqB,UAAU;YAE/B,IAAI;gBACF,MAAM,WAAW,IAAI;gBACrB,SAAS,MAAM,CAAC,cAAc,wHAAA,CAAA,oBAAiB,CAAC,KAAK,CAAC,SAAS;gBAC/D,SAAS,MAAM,CAAC,QAAQ,UAAU,IAAI,CAAC,IAAI;gBAC3C,SAAS,MAAM,CAAC,SAAS,UAAU,IAAI,CAAC,KAAK;gBAC7C,SAAS,MAAM,CAAC,WAAW,UAAU,IAAI,CAAC,OAAO;gBACjD,SAAS,MAAM,CAAC,gBAAgB,UAAU,IAAI,CAAC,WAAW;gBAC1D,SAAS,MAAM,CAAC,WAAW,UAAU,IAAI,CAAC,OAAO;gBACjD,SAAS,MAAM,CAAC,aAAa;gBAC7B,SAAS,MAAM,CAAC,WAAW,CAAC,iCAAiC,EAAE,UAAU,IAAI,CAAC,IAAI,EAAE;gBAEpF,MAAM,WAAW,MAAM,MAAM,oCAAoC;oBAC/D,QAAQ;oBACR,MAAM;gBACR;gBAEA,MAAM,SAAS,MAAM,SAAS,IAAI;gBAElC,IAAI,OAAO,OAAO,EAAE;oBAClB;kEAAa,CAAA,OAAQ,CAAC;gCACpB,GAAG,IAAI;gCACP,cAAc;gCACd,aAAa;gCACb,eAAe;4BACjB,CAAC;;oBACD,qBAAqB,WAAW;oBAChC,OAAO;gBACT,OAAO;oBACL,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;gBACpC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0BAA0B;gBACxC;8DAAa,CAAA,OAAQ,CAAC;4BACpB,GAAG,IAAI;4BACP,cAAc;4BACd,aAAa;4BACb,eAAe;4BACf,QAAQ;gCACN,SAAS;4BACX;wBACF,CAAC;;gBACD,qBAAqB,SAAS;gBAC9B,OAAO;YACT;QACF;iDAAG;QAAC,UAAU,IAAI;QAAE;KAAqB;IAEzC,OAAO;QACL,aAAa;QACb,UAAU,UAAU,IAAI;QACxB,QAAQ,UAAU,MAAM;QACxB,cAAc,UAAU,YAAY;QACpC,aAAa,UAAU,WAAW;QAClC,eAAe,UAAU,aAAa;QAEtC,eAAe;QACf;QACA;QACA;QACA;QACA;QAEA,qBAAqB;QACrB,SAAS,OAAO,IAAI,CAAC,UAAU,MAAM,EAAE,MAAM,KAAK;QAClD,WAAW,OAAO,IAAI,CAAC,UAAU,MAAM,EAAE,MAAM,GAAG;IACpD;AACF;GA9IgB;;QAEmB,+HAAA,CAAA,eAAY", "debugId": null}}, {"offset": {"line": 895, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/lib/sanity.ts"], "sourcesContent": ["import { createClient } from '@sanity/client';\nimport imageUrlBuilder from '@sanity/image-url';\n\n// Check if <PERSON><PERSON> is configured with valid values\nconst isValidProjectId = (projectId: string | undefined): boolean => {\n  if (!projectId) return false;\n  // Sanity project IDs can only contain a-z, 0-9, and dashes\n  // Also exclude placeholder values\n  return /^[a-z0-9-]+$/.test(projectId) && !projectId.includes('your_sanity_project_id');\n};\n\nconst isConfigured = !!(\n  process.env.NEXT_PUBLIC_SANITY_PROJECT_ID &&\n  process.env.NEXT_PUBLIC_SANITY_DATASET &&\n  isValidProjectId(process.env.NEXT_PUBLIC_SANITY_PROJECT_ID) &&\n  !process.env.NEXT_PUBLIC_SANITY_DATASET.includes('your_sanity_dataset')\n);\n\n// Sanity client configuration (only if configured)\nexport const client = isConfigured ? createClient({\n  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID!,\n  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',\n  apiVersion: '2024-01-01',\n  useCdn: process.env.NODE_ENV === 'production',\n  token: process.env.SANITY_API_TOKEN,\n}) : null;\n\n// Image URL builder (only if configured)\nconst builder = isConfigured && client ? imageUrlBuilder(client) : null;\n\nexport function urlFor(source: any) {\n  if (!builder) {\n    console.warn('Sanity not configured, returning placeholder image URL');\n    return { url: () => '/placeholder-image.jpg' };\n  }\n  return builder.image(source);\n}\n\n// Type definitions for Sanity documents\nexport interface SanityImage {\n  _type: 'image';\n  asset: {\n    _ref: string;\n    _type: 'reference';\n  };\n  alt?: string;\n}\n\nexport interface Category {\n  _id: string;\n  _type: 'category';\n  title: string;\n  slug: {\n    current: string;\n  };\n  description?: string;\n}\n\nexport interface BlogPost {\n  _id: string;\n  _type: 'post';\n  title: string;\n  slug: {\n    current: string;\n  };\n  author: string;\n  mainImage?: SanityImage;\n  categories: Category[];\n  publishedAt: string;\n  excerpt?: string;\n  body: any[]; // Portable Text\n  _createdAt: string;\n  _updatedAt: string;\n}\n\nexport interface FAQTopic {\n  _id: string;\n  _type: 'faqTopic';\n  title: string;\n  slug: {\n    current: string;\n  };\n  description?: string;\n}\n\nexport interface SiteSettings {\n  _id: string;\n  _type: 'siteSettings';\n  // Hero Section\n  heroHeadline?: string;\n  heroSubtext?: string;\n  heroButtonText?: string;\n  // Contact Section\n  contactHeadline?: string;\n  contactSubtext?: string;\n  contactButtonText?: string;\n  // Form Messages\n  formSuccessMessage?: string;\n  formErrorMessage?: string;\n  // Services Section\n  servicesHeadline?: string;\n  servicesSubtext?: string;\n  // Process Section\n  processHeadline?: string;\n  processSubtext?: string;\n  // About Section\n  aboutHeadline?: string;\n  aboutSubtext?: string;\n  // Footer\n  footerTagline?: string;\n  footerCopyright?: string;\n}\n\nexport interface FAQItem {\n  _id: string;\n  _type: 'faqItem';\n  question: string;\n  answer: any[]; // Portable Text\n  topic: FAQTopic;\n  relatedPosts?: BlogPost[];\n  _createdAt: string;\n  _updatedAt: string;\n}\n\n// GROQ queries\nexport const POSTS_QUERY = `*[_type == \"post\" && defined(slug.current)] | order(publishedAt desc) {\n  _id,\n  title,\n  slug,\n  author,\n  mainImage,\n  categories[]-> {\n    _id,\n    title,\n    slug\n  },\n  publishedAt,\n  excerpt,\n  _createdAt\n}`;\n\nexport const POST_QUERY = `*[_type == \"post\" && slug.current == $slug][0] {\n  _id,\n  title,\n  slug,\n  author,\n  mainImage,\n  categories[]-> {\n    _id,\n    title,\n    slug,\n    description\n  },\n  publishedAt,\n  excerpt,\n  body,\n  _createdAt,\n  _updatedAt\n}`;\n\nexport const CATEGORIES_QUERY = `*[_type == \"category\"] | order(title asc) {\n  _id,\n  title,\n  slug,\n  description\n}`;\n\nexport const POSTS_BY_CATEGORY_QUERY = `*[_type == \"post\" && $categoryId in categories[]._ref] | order(publishedAt desc) {\n  _id,\n  title,\n  slug,\n  author,\n  mainImage,\n  categories[]-> {\n    _id,\n    title,\n    slug\n  },\n  publishedAt,\n  excerpt,\n  _createdAt\n}`;\n\nexport const FAQ_ITEMS_QUERY = `*[_type == \"faqItem\"] | order(topic->title asc, _createdAt asc) {\n  _id,\n  question,\n  answer,\n  topic-> {\n    _id,\n    title,\n    slug,\n    description\n  },\n  relatedPosts[]-> {\n    _id,\n    title,\n    slug\n  },\n  _createdAt\n}`;\n\nexport const FAQ_TOPICS_QUERY = `*[_type == \"faqTopic\"] | order(title asc) {\n  _id,\n  title,\n  slug,\n  description\n}`;\n\nexport const RELATED_POSTS_QUERY = `*[_type == \"post\" && _id != $postId && count(categories[@._ref in $categoryIds]) > 0] | order(publishedAt desc)[0...3] {\n  _id,\n  title,\n  slug,\n  mainImage,\n  publishedAt,\n  excerpt\n}`;\n\n// Site Settings Query (Singleton)\nexport const SITE_SETTINGS_QUERY = `*[_type == \"siteSettings\"][0] {\n  _id,\n  heroHeadline,\n  heroSubtext,\n  heroButtonText,\n  contactHeadline,\n  contactSubtext,\n  contactButtonText,\n  formSuccessMessage,\n  formErrorMessage,\n  servicesHeadline,\n  servicesSubtext,\n  processHeadline,\n  processSubtext,\n  aboutHeadline,\n  aboutSubtext,\n  footerTagline,\n  footerCopyright\n}`;\n\n// Utility functions\nexport async function getAllPosts(): Promise<BlogPost[]> {\n  if (!client) {\n    console.warn('Sanity not configured, returning empty posts array');\n    return [];\n  }\n  return await client.fetch(POSTS_QUERY);\n}\n\nexport async function getPostBySlug(slug: string): Promise<BlogPost | null> {\n  if (!client) {\n    console.warn('Sanity not configured, returning null for post');\n    return null;\n  }\n  return await client.fetch(POST_QUERY, { slug });\n}\n\nexport async function getAllCategories(): Promise<Category[]> {\n  if (!client) {\n    console.warn('Sanity not configured, returning empty categories array');\n    return [];\n  }\n  return await client.fetch(CATEGORIES_QUERY);\n}\n\nexport async function getPostsByCategory(categoryId: string): Promise<BlogPost[]> {\n  if (!client) {\n    console.warn('Sanity not configured, returning empty posts array');\n    return [];\n  }\n  return await client.fetch(POSTS_BY_CATEGORY_QUERY, { categoryId });\n}\n\nexport async function getAllFAQItems(): Promise<FAQItem[]> {\n  if (!client) {\n    console.warn('Sanity not configured, returning empty FAQ items array');\n    return [];\n  }\n  return await client.fetch(FAQ_ITEMS_QUERY);\n}\n\nexport async function getAllFAQTopics(): Promise<FAQTopic[]> {\n  if (!client) {\n    console.warn('Sanity not configured, returning empty FAQ topics array');\n    return [];\n  }\n  return await client.fetch(FAQ_TOPICS_QUERY);\n}\n\nexport async function getRelatedPosts(postId: string, categoryIds: string[]): Promise<BlogPost[]> {\n  if (!client) {\n    console.warn('Sanity not configured, returning empty related posts array');\n    return [];\n  }\n  return await client.fetch(RELATED_POSTS_QUERY, { postId, categoryIds });\n}\n\nexport async function getSiteSettings(): Promise<SiteSettings | null> {\n  if (!client) {\n    console.warn('Sanity not configured, returning null for site settings');\n    return null;\n  }\n  try {\n    return await client.fetch(SITE_SETTINGS_QUERY);\n  } catch (error) {\n    console.warn('Failed to fetch site settings from Sanity:', error);\n    return null;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAYE;AAZF;AACA;;;AAEA,kDAAkD;AAClD,MAAM,mBAAmB,CAAC;IACxB,IAAI,CAAC,WAAW,OAAO;IACvB,2DAA2D;IAC3D,kCAAkC;IAClC,OAAO,eAAe,IAAI,CAAC,cAAc,CAAC,UAAU,QAAQ,CAAC;AAC/D;AAEA,MAAM,eAAe,CAAC,CAAC,CACrB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,6BAA6B,IACzC,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,0BAA0B,IACtC,iBAAiB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,6BAA6B,KAC1D,CAAC,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,QAAQ,CAAC,sBACnD;AAGO,MAAM,SAAS,eAAe,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE;IAChD,WAAW,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,6BAA6B;IACpD,SAAS,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,0BAA0B,IAAI;IACnD,YAAY;IACZ,QAAQ,oDAAyB;IACjC,OAAO,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,gBAAgB;AACrC,KAAK;AAEL,yCAAyC;AACzC,MAAM,UAAU,gBAAgB,SAAS,CAAA,GAAA,oLAAA,CAAA,UAAe,AAAD,EAAE,UAAU;AAE5D,SAAS,OAAO,MAAW;IAChC,IAAI,CAAC,SAAS;QACZ,QAAQ,IAAI,CAAC;QACb,OAAO;YAAE,KAAK,IAAM;QAAyB;IAC/C;IACA,OAAO,QAAQ,KAAK,CAAC;AACvB;AAyFO,MAAM,cAAc,CAAC;;;;;;;;;;;;;;CAc3B,CAAC;AAEK,MAAM,aAAa,CAAC;;;;;;;;;;;;;;;;;CAiB1B,CAAC;AAEK,MAAM,mBAAmB,CAAC;;;;;CAKhC,CAAC;AAEK,MAAM,0BAA0B,CAAC;;;;;;;;;;;;;;CAcvC,CAAC;AAEK,MAAM,kBAAkB,CAAC;;;;;;;;;;;;;;;;CAgB/B,CAAC;AAEK,MAAM,mBAAmB,CAAC;;;;;CAKhC,CAAC;AAEK,MAAM,sBAAsB,CAAC;;;;;;;CAOnC,CAAC;AAGK,MAAM,sBAAsB,CAAC;;;;;;;;;;;;;;;;;;CAkBnC,CAAC;AAGK,eAAe;IACpB,IAAI,CAAC,QAAQ;QACX,QAAQ,IAAI,CAAC;QACb,OAAO,EAAE;IACX;IACA,OAAO,MAAM,OAAO,KAAK,CAAC;AAC5B;AAEO,eAAe,cAAc,IAAY;IAC9C,IAAI,CAAC,QAAQ;QACX,QAAQ,IAAI,CAAC;QACb,OAAO;IACT;IACA,OAAO,MAAM,OAAO,KAAK,CAAC,YAAY;QAAE;IAAK;AAC/C;AAEO,eAAe;IACpB,IAAI,CAAC,QAAQ;QACX,QAAQ,IAAI,CAAC;QACb,OAAO,EAAE;IACX;IACA,OAAO,MAAM,OAAO,KAAK,CAAC;AAC5B;AAEO,eAAe,mBAAmB,UAAkB;IACzD,IAAI,CAAC,QAAQ;QACX,QAAQ,IAAI,CAAC;QACb,OAAO,EAAE;IACX;IACA,OAAO,MAAM,OAAO,KAAK,CAAC,yBAAyB;QAAE;IAAW;AAClE;AAEO,eAAe;IACpB,IAAI,CAAC,QAAQ;QACX,QAAQ,IAAI,CAAC;QACb,OAAO,EAAE;IACX;IACA,OAAO,MAAM,OAAO,KAAK,CAAC;AAC5B;AAEO,eAAe;IACpB,IAAI,CAAC,QAAQ;QACX,QAAQ,IAAI,CAAC;QACb,OAAO,EAAE;IACX;IACA,OAAO,MAAM,OAAO,KAAK,CAAC;AAC5B;AAEO,eAAe,gBAAgB,MAAc,EAAE,WAAqB;IACzE,IAAI,CAAC,QAAQ;QACX,QAAQ,IAAI,CAAC;QACb,OAAO,EAAE;IACX;IACA,OAAO,MAAM,OAAO,KAAK,CAAC,qBAAqB;QAAE;QAAQ;IAAY;AACvE;AAEO,eAAe;IACpB,IAAI,CAAC,QAAQ;QACX,QAAQ,IAAI,CAAC;QACb,OAAO;IACT;IACA,IAAI;QACF,OAAO,MAAM,OAAO,KAAK,CAAC;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,8CAA8C;QAC3D,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 1127, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/hooks/useSiteSettings.ts"], "sourcesContent": ["/**\n * Site Settings Hook\n * \n * Fetches site settings from Sanity CMS with fallback to static configuration.\n * Provides a unified interface for accessing dynamic content across the site.\n */\n\nimport { useState, useEffect } from 'react';\nimport { getSiteSettings, type SiteSettings } from '@/lib/sanity';\nimport { FALLBACK_CONTENT } from '@/config/site';\n\n// Combined settings interface with fallbacks\nexport interface SiteSettingsWithFallbacks {\n  // Hero Section\n  heroHeadline: string;\n  heroSubtext: string;\n  heroButtonText: string;\n  \n  // Contact Section\n  contactHeadline: string;\n  contactSubtext: string;\n  contactButtonText: string;\n  \n  // Form Messages\n  formSuccessMessage: string;\n  formErrorMessage: string;\n  \n  // Services Section\n  servicesHeadline: string;\n  servicesSubtext: string;\n  \n  // Process Section\n  processHeadline: string;\n  processSubtext: string;\n  \n  // About Section\n  aboutHeadline: string;\n  aboutSubtext: string;\n  \n  // Footer\n  footerTagline: string;\n  footerCopyright: string;\n  \n  // Meta information\n  isLoading: boolean;\n  isFromCMS: boolean;\n  error: string | null;\n}\n\n// Create settings with fallbacks\nconst createSettingsWithFallbacks = (\n  cmsSettings: SiteSettings | null,\n  isLoading: boolean = false,\n  error: string | null = null\n): SiteSettingsWithFallbacks => {\n  const currentYear = new Date().getFullYear();\n  \n  return {\n    // Hero Section\n    heroHeadline: cmsSettings?.heroHeadline || FALLBACK_CONTENT.hero.headline,\n    heroSubtext: cmsSettings?.heroSubtext || FALLBACK_CONTENT.hero.subtext,\n    heroButtonText: cmsSettings?.heroButtonText || FALLBACK_CONTENT.hero.buttonText,\n    \n    // Contact Section\n    contactHeadline: cmsSettings?.contactHeadline || FALLBACK_CONTENT.contact.headline,\n    contactSubtext: cmsSettings?.contactSubtext || FALLBACK_CONTENT.contact.subtext,\n    contactButtonText: cmsSettings?.contactButtonText || FALLBACK_CONTENT.contact.buttonText,\n    \n    // Form Messages\n    formSuccessMessage: cmsSettings?.formSuccessMessage || FALLBACK_CONTENT.contact.successMessage,\n    formErrorMessage: cmsSettings?.formErrorMessage || FALLBACK_CONTENT.contact.errorMessage,\n    \n    // Services Section\n    servicesHeadline: cmsSettings?.servicesHeadline || FALLBACK_CONTENT.services.headline,\n    servicesSubtext: cmsSettings?.servicesSubtext || FALLBACK_CONTENT.services.subtext,\n    \n    // Process Section\n    processHeadline: cmsSettings?.processHeadline || FALLBACK_CONTENT.process.headline,\n    processSubtext: cmsSettings?.processSubtext || FALLBACK_CONTENT.process.subtext,\n    \n    // About Section\n    aboutHeadline: cmsSettings?.aboutHeadline || FALLBACK_CONTENT.about.headline,\n    aboutSubtext: cmsSettings?.aboutSubtext || FALLBACK_CONTENT.about.subtext,\n    \n    // Footer\n    footerTagline: cmsSettings?.footerTagline || 'Building the future of mobile apps',\n    footerCopyright: cmsSettings?.footerCopyright || `© ${currentYear} Mobilify. All rights reserved.`,\n    \n    // Meta information\n    isLoading,\n    isFromCMS: !!cmsSettings,\n    error\n  };\n};\n\n// Hook implementation\nexport function useSiteSettings(): SiteSettingsWithFallbacks {\n  const [cmsSettings, setCmsSettings] = useState<SiteSettings | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    let isMounted = true;\n\n    const fetchSettings = async () => {\n      try {\n        setIsLoading(true);\n        setError(null);\n        \n        const settings = await getSiteSettings();\n        \n        if (isMounted) {\n          setCmsSettings(settings);\n          setIsLoading(false);\n        }\n      } catch (err) {\n        if (isMounted) {\n          const errorMessage = err instanceof Error ? err.message : 'Failed to fetch site settings';\n          setError(errorMessage);\n          setIsLoading(false);\n          console.warn('Failed to fetch site settings, using fallbacks:', errorMessage);\n        }\n      }\n    };\n\n    fetchSettings();\n\n    return () => {\n      isMounted = false;\n    };\n  }, []);\n\n  return createSettingsWithFallbacks(cmsSettings, isLoading, error);\n}\n\n// Static version for server-side rendering or when you need immediate access\nexport function getStaticSiteSettings(): SiteSettingsWithFallbacks {\n  return createSettingsWithFallbacks(null, false, null);\n}\n\n// Utility function to get specific section settings\nexport function useSectionSettings(section: 'hero' | 'contact' | 'services' | 'process' | 'about') {\n  const settings = useSiteSettings();\n  \n  switch (section) {\n    case 'hero':\n      return {\n        headline: settings.heroHeadline,\n        subtext: settings.heroSubtext,\n        buttonText: settings.heroButtonText,\n        isLoading: settings.isLoading,\n        isFromCMS: settings.isFromCMS\n      };\n      \n    case 'contact':\n      return {\n        headline: settings.contactHeadline,\n        subtext: settings.contactSubtext,\n        buttonText: settings.contactButtonText,\n        successMessage: settings.formSuccessMessage,\n        errorMessage: settings.formErrorMessage,\n        isLoading: settings.isLoading,\n        isFromCMS: settings.isFromCMS\n      };\n      \n    case 'services':\n      return {\n        headline: settings.servicesHeadline,\n        subtext: settings.servicesSubtext,\n        isLoading: settings.isLoading,\n        isFromCMS: settings.isFromCMS\n      };\n      \n    case 'process':\n      return {\n        headline: settings.processHeadline,\n        subtext: settings.processSubtext,\n        isLoading: settings.isLoading,\n        isFromCMS: settings.isFromCMS\n      };\n      \n    case 'about':\n      return {\n        headline: settings.aboutHeadline,\n        subtext: settings.aboutSubtext,\n        isLoading: settings.isLoading,\n        isFromCMS: settings.isFromCMS\n      };\n      \n    default:\n      return {\n        isLoading: settings.isLoading,\n        isFromCMS: settings.isFromCMS\n      };\n  }\n}\n\n\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;AAED;AACA;AACA;;;;;AAwCA,iCAAiC;AACjC,MAAM,8BAA8B,CAClC,aACA,YAAqB,KAAK,EAC1B,QAAuB,IAAI;IAE3B,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,OAAO;QACL,eAAe;QACf,cAAc,aAAa,gBAAgB,wHAAA,CAAA,mBAAgB,CAAC,IAAI,CAAC,QAAQ;QACzE,aAAa,aAAa,eAAe,wHAAA,CAAA,mBAAgB,CAAC,IAAI,CAAC,OAAO;QACtE,gBAAgB,aAAa,kBAAkB,wHAAA,CAAA,mBAAgB,CAAC,IAAI,CAAC,UAAU;QAE/E,kBAAkB;QAClB,iBAAiB,aAAa,mBAAmB,wHAAA,CAAA,mBAAgB,CAAC,OAAO,CAAC,QAAQ;QAClF,gBAAgB,aAAa,kBAAkB,wHAAA,CAAA,mBAAgB,CAAC,OAAO,CAAC,OAAO;QAC/E,mBAAmB,aAAa,qBAAqB,wHAAA,CAAA,mBAAgB,CAAC,OAAO,CAAC,UAAU;QAExF,gBAAgB;QAChB,oBAAoB,aAAa,sBAAsB,wHAAA,CAAA,mBAAgB,CAAC,OAAO,CAAC,cAAc;QAC9F,kBAAkB,aAAa,oBAAoB,wHAAA,CAAA,mBAAgB,CAAC,OAAO,CAAC,YAAY;QAExF,mBAAmB;QACnB,kBAAkB,aAAa,oBAAoB,wHAAA,CAAA,mBAAgB,CAAC,QAAQ,CAAC,QAAQ;QACrF,iBAAiB,aAAa,mBAAmB,wHAAA,CAAA,mBAAgB,CAAC,QAAQ,CAAC,OAAO;QAElF,kBAAkB;QAClB,iBAAiB,aAAa,mBAAmB,wHAAA,CAAA,mBAAgB,CAAC,OAAO,CAAC,QAAQ;QAClF,gBAAgB,aAAa,kBAAkB,wHAAA,CAAA,mBAAgB,CAAC,OAAO,CAAC,OAAO;QAE/E,gBAAgB;QAChB,eAAe,aAAa,iBAAiB,wHAAA,CAAA,mBAAgB,CAAC,KAAK,CAAC,QAAQ;QAC5E,cAAc,aAAa,gBAAgB,wHAAA,CAAA,mBAAgB,CAAC,KAAK,CAAC,OAAO;QAEzE,SAAS;QACT,eAAe,aAAa,iBAAiB;QAC7C,iBAAiB,aAAa,mBAAmB,CAAC,EAAE,EAAE,YAAY,+BAA+B,CAAC;QAElG,mBAAmB;QACnB;QACA,WAAW,CAAC,CAAC;QACb;IACF;AACF;AAGO,SAAS;;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IACpE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,YAAY;YAEhB,MAAM;2DAAgB;oBACpB,IAAI;wBACF,aAAa;wBACb,SAAS;wBAET,MAAM,WAAW,MAAM,CAAA,GAAA,uHAAA,CAAA,kBAAe,AAAD;wBAErC,IAAI,WAAW;4BACb,eAAe;4BACf,aAAa;wBACf;oBACF,EAAE,OAAO,KAAK;wBACZ,IAAI,WAAW;4BACb,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;4BAC1D,SAAS;4BACT,aAAa;4BACb,QAAQ,IAAI,CAAC,mDAAmD;wBAClE;oBACF;gBACF;;YAEA;YAEA;6CAAO;oBACL,YAAY;gBACd;;QACF;oCAAG,EAAE;IAEL,OAAO,4BAA4B,aAAa,WAAW;AAC7D;GArCgB;AAwCT,SAAS;IACd,OAAO,4BAA4B,MAAM,OAAO;AAClD;AAGO,SAAS,mBAAmB,OAA8D;;IAC/F,MAAM,WAAW;IAEjB,OAAQ;QACN,KAAK;YACH,OAAO;gBACL,UAAU,SAAS,YAAY;gBAC/B,SAAS,SAAS,WAAW;gBAC7B,YAAY,SAAS,cAAc;gBACnC,WAAW,SAAS,SAAS;gBAC7B,WAAW,SAAS,SAAS;YAC/B;QAEF,KAAK;YACH,OAAO;gBACL,UAAU,SAAS,eAAe;gBAClC,SAAS,SAAS,cAAc;gBAChC,YAAY,SAAS,iBAAiB;gBACtC,gBAAgB,SAAS,kBAAkB;gBAC3C,cAAc,SAAS,gBAAgB;gBACvC,WAAW,SAAS,SAAS;gBAC7B,WAAW,SAAS,SAAS;YAC/B;QAEF,KAAK;YACH,OAAO;gBACL,UAAU,SAAS,gBAAgB;gBACnC,SAAS,SAAS,eAAe;gBACjC,WAAW,SAAS,SAAS;gBAC7B,WAAW,SAAS,SAAS;YAC/B;QAEF,KAAK;YACH,OAAO;gBACL,UAAU,SAAS,eAAe;gBAClC,SAAS,SAAS,cAAc;gBAChC,WAAW,SAAS,SAAS;gBAC7B,WAAW,SAAS,SAAS;YAC/B;QAEF,KAAK;YACH,OAAO;gBACL,UAAU,SAAS,aAAa;gBAChC,SAAS,SAAS,YAAY;gBAC9B,WAAW,SAAS,SAAS;gBAC7B,WAAW,SAAS,SAAS;YAC/B;QAEF;YACE,OAAO;gBACL,WAAW,SAAS,SAAS;gBAC7B,WAAW,SAAS,SAAS;YAC/B;IACJ;AACF;IAtDgB;;QACG", "debugId": null}}, {"offset": {"line": 1283, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/hooks/index.ts"], "sourcesContent": ["/**\n * Hooks Index\n * \n * Central export point for all custom hooks.\n */\n\n// Analytics hook\nexport { useAnalytics, ANALYTICS_EVENTS } from './useAnalytics';\nexport type { AnalyticsEvent } from './useAnalytics';\n\n// Contact form hook\nexport { useContactForm } from './useContactForm';\nexport type { ContactFormData, FormErrors, FormState } from './useContactForm';\n\n// Site settings hook\nexport { useSiteSettings, useSectionSettings, getStaticSiteSettings } from './useSiteSettings';\nexport type { SiteSettingsWithFallbacks } from './useSiteSettings';\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED,iBAAiB;;AACjB;AAGA,oBAAoB;AACpB;AAGA,qBAAqB;AACrB", "debugId": null}}, {"offset": {"line": 1317, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/layout/Navigation.tsx"], "sourcesContent": ["/**\n * Navigation Component\n *\n * Handles the main navigation menu for both desktop and mobile views.\n * Supports smooth scrolling to sections and analytics tracking.\n *\n * @component\n * @param {Object} props - Component props\n * @param {string} [props.className] - Additional CSS classes to apply\n *\n * @example\n * ```tsx\n * <Navigation className=\"hidden md:flex space-x-8\" />\n * ```\n *\n * Features:\n * - Smooth scrolling to page sections\n * - Analytics tracking for navigation events\n * - Configurable navigation items from site config\n * - Responsive design support\n */\n\n'use client';\n\nimport React from 'react';\nimport { NAVIGATION } from '@/config/site';\nimport { useAnalytics } from '@/hooks';\n\ninterface NavigationProps {\n  isMobile?: boolean;\n  onItemClick?: () => void;\n  className?: string;\n}\n\nconst Navigation: React.FC<NavigationProps> = ({ \n  isMobile = false, \n  onItemClick,\n  className = ''\n}) => {\n  const { trackNavigation } = useAnalytics();\n\n  const scrollToSection = (sectionId: string, label: string) => {\n    const element = document.getElementById(sectionId);\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' });\n      trackNavigation(label, isMobile ? 'mobile_nav' : 'desktop_nav');\n    }\n    onItemClick?.();\n  };\n\n  const navItems = NAVIGATION.main;\n\n  const baseClasses = isMobile \n    ? \"flex flex-col space-y-4\"\n    : \"hidden md:flex md:items-center md:space-x-8\";\n\n  return (\n    <nav className={`${baseClasses} ${className}`}>\n      {navItems.map((item) => (\n        <button\n          key={item.href}\n          onClick={() => scrollToSection(item.href.replace('#', ''), item.label)}\n          className={`\n            text-gray-700 dark:text-gray-300 hover:text-electric-blue dark:hover:text-electric-blue \n            transition-colors duration-200 font-medium\n            ${isMobile \n              ? 'text-lg py-2 text-left w-full hover:bg-gray-50 dark:hover:bg-gray-800 px-4 rounded-lg' \n              : 'text-sm'\n            }\n          `}\n          aria-label={`Navigate to ${item.label} section`}\n        >\n          {item.label}\n        </button>\n      ))}\n    </nav>\n  );\n};\n\nexport default Navigation;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;CAoBC;;;;AAKD;AACA;AAAA;;;AAJA;;;AAYA,MAAM,aAAwC,CAAC,EAC7C,WAAW,KAAK,EAChB,WAAW,EACX,YAAY,EAAE,EACf;;IACC,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD;IAEvC,MAAM,kBAAkB,CAAC,WAAmB;QAC1C,MAAM,UAAU,SAAS,cAAc,CAAC;QACxC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;YAC5C,gBAAgB,OAAO,WAAW,eAAe;QACnD;QACA;IACF;IAEA,MAAM,WAAW,wHAAA,CAAA,aAAU,CAAC,IAAI;IAEhC,MAAM,cAAc,WAChB,4BACA;IAEJ,qBACE,6LAAC;QAAI,WAAW,GAAG,YAAY,CAAC,EAAE,WAAW;kBAC1C,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC;gBAEC,SAAS,IAAM,gBAAgB,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,KAAK,KAAK;gBACrE,WAAW,CAAC;;;YAGV,EAAE,WACE,0FACA,UACH;UACH,CAAC;gBACD,cAAY,CAAC,YAAY,EAAE,KAAK,KAAK,CAAC,QAAQ,CAAC;0BAE9C,KAAK,KAAK;eAZN,KAAK,IAAI;;;;;;;;;;AAiBxB;GA3CM;;QAKwB,+HAAA,CAAA,eAAY;;;KALpC;uCA6CS", "debugId": null}}, {"offset": {"line": 1404, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/layout/MobileMenu.tsx"], "sourcesContent": ["/**\n * Mobile Menu Component\n *\n * Full-screen mobile navigation menu with slide-in animation from the right.\n * Includes navigation items, chat trigger, dark mode toggle, and close button.\n *\n * @component\n * @param {Object} props - Component props\n * @param {boolean} props.isOpen - Whether the mobile menu is currently open\n * @param {() => void} props.onClose - Callback function to close the menu\n *\n * @example\n * ```tsx\n * <MobileMenu\n *   isOpen={isMenuOpen}\n *   onClose={() => setIsMenuOpen(false)}\n * />\n * ```\n *\n * Features:\n * - Smooth slide-in/out animation using Framer Motion\n * - Full-screen overlay with backdrop blur\n * - Integrated navigation, chat, and dark mode toggle\n * - Keyboard and click-outside support for closing\n * - Mobile-optimized touch interactions\n */\n\n'use client';\n\nimport React from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { X } from 'lucide-react';\nimport Navigation from './Navigation';\nimport SimpleHeaderChat from '../SimpleHeaderChat';\nimport SimpleDarkModeToggle from '../SimpleDarkModeToggle';\nimport { ANIMATION_CONFIG } from '@/config/site';\n\ninterface MobileMenuProps {\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nconst MobileMenu: React.FC<MobileMenuProps> = ({ isOpen, onClose }) => {\n  return (\n    <AnimatePresence>\n      {isOpen && (\n        <>\n          {/* Backdrop */}\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            transition={{ duration: ANIMATION_CONFIG.duration.normal }}\n            className=\"fixed inset-0 bg-black/50 z-40 md:hidden\"\n            onClick={onClose}\n          />\n          \n          {/* Menu Panel */}\n          <motion.div\n            initial={{ x: '100%' }}\n            animate={{ x: 0 }}\n            exit={{ x: '100%' }}\n            transition={{ \n              duration: ANIMATION_CONFIG.duration.normal,\n              ease: ANIMATION_CONFIG.easing.easeInOut\n            }}\n            className=\"fixed top-0 right-0 h-full w-80 max-w-[90vw] bg-white dark:bg-gray-900 shadow-xl z-50 md:hidden\"\n          >\n            <div className=\"flex flex-col h-full\">\n              {/* Header */}\n              <div className=\"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700\">\n                <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                  Menu\n                </h2>\n                <button\n                  onClick={onClose}\n                  className=\"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors\"\n                  aria-label=\"Close menu\"\n                >\n                  <X className=\"h-5 w-5 text-gray-600 dark:text-gray-400\" />\n                </button>\n              </div>\n\n              {/* Navigation */}\n              <div className=\"flex-1 p-4\">\n                <Navigation \n                  isMobile={true} \n                  onItemClick={onClose}\n                  className=\"mb-8\"\n                />\n                \n                {/* Mobile-specific features */}\n                <div className=\"space-y-4 pt-8 border-t border-gray-200 dark:border-gray-700\">\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                      Dark Mode\n                    </span>\n                    <SimpleDarkModeToggle />\n                  </div>\n                  \n                  <div className=\"pt-4\">\n                    <SimpleHeaderChat />\n                  </div>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        </>\n      )}\n    </AnimatePresence>\n  );\n};\n\nexport default MobileMenu;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;CAyBC;;;;AAKD;AAAA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;AAeA,MAAM,aAAwC,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE;IAChE,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACb,wBACC;;8BAEE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,YAAY;wBAAE,UAAU,wHAAA,CAAA,mBAAgB,CAAC,QAAQ,CAAC,MAAM;oBAAC;oBACzD,WAAU;oBACV,SAAS;;;;;;8BAIX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,GAAG;oBAAO;oBACrB,SAAS;wBAAE,GAAG;oBAAE;oBAChB,MAAM;wBAAE,GAAG;oBAAO;oBAClB,YAAY;wBACV,UAAU,wHAAA,CAAA,mBAAgB,CAAC,QAAQ,CAAC,MAAM;wBAC1C,MAAM,wHAAA,CAAA,mBAAgB,CAAC,MAAM,CAAC,SAAS;oBACzC;oBACA,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAsD;;;;;;kDAGpE,6LAAC;wCACC,SAAS;wCACT,WAAU;wCACV,cAAW;kDAEX,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAKjB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6IAAA,CAAA,UAAU;wCACT,UAAU;wCACV,aAAa;wCACb,WAAU;;;;;;kDAIZ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAuD;;;;;;kEAGvE,6LAAC,6IAAA,CAAA,UAAoB;;;;;;;;;;;0DAGvB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,yIAAA,CAAA,UAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrC;KArEM;uCAuES", "debugId": null}}, {"offset": {"line": 1616, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/layout/Header.tsx"], "sourcesContent": ["/**\n * Header Component\n *\n * Main navigation header for the Mobilify website. Features responsive design\n * with desktop navigation and mobile hamburger menu. Includes logo, navigation\n * links, chat trigger, dark mode toggle, and CTA button.\n *\n * @component\n * @example\n * ```tsx\n * <Header />\n * ```\n *\n * Features:\n * - Responsive navigation (desktop/mobile)\n * - Mobile hamburger menu with slide-in animation\n * - Dark mode toggle\n * - Chat integration\n * - Analytics tracking for navigation events\n * - Fixed positioning with backdrop blur\n */\n\n'use client';\n\nimport React, { useState } from 'react';\nimport { Menu, X } from 'lucide-react';\nimport Logo from '../Logo';\nimport NoSSR from '../NoSSR';\nimport SimpleHeaderChat from '../SimpleHeaderChat';\nimport SimpleDarkModeToggle from '../SimpleDarkModeToggle';\nimport Navigation from './Navigation';\nimport MobileMenu from './MobileMenu';\nimport { useAnalytics } from '@/hooks';\n\n/**\n * Header component with responsive navigation and mobile menu\n */\nconst Header = () => {\n  // State for mobile menu visibility\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const { trackNavigation } = useAnalytics();\n\n  /**\n   * Handles mobile menu toggle and tracks the interaction\n   */\n  const handleMobileMenuToggle = () => {\n    setIsMenuOpen(!isMenuOpen);\n    trackNavigation('mobile_menu_toggle', 'header');\n  };\n\n  return (\n    <>\n      <header className=\"fixed top-0 left-0 right-0 w-full bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm border-b border-gray-100 dark:border-gray-800 z-50 transition-colors duration-300\">\n        <div className=\"w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between h-16 w-full\">\n            {/* Logo */}\n            <div className=\"flex items-center\">\n              <Logo />\n            </div>\n\n            {/* Desktop Navigation */}\n            <div className=\"hidden md:flex items-center space-x-8\">\n              <Navigation />\n              <NoSSR>\n                <SimpleHeaderChat />\n              </NoSSR>\n              <NoSSR>\n                <SimpleDarkModeToggle />\n              </NoSSR>\n            </div>\n\n            {/* Mobile menu button */}\n            <button\n              onClick={handleMobileMenuToggle}\n              className=\"md:hidden p-2 rounded-lg text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors\"\n              aria-label=\"Toggle mobile menu\"\n          >\n            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}\n          </button>\n          </div>\n        </div>\n      </header>\n\n      {/* Mobile Navigation */}\n      <MobileMenu\n        isOpen={isMenuOpen}\n        onClose={() => setIsMenuOpen(false)}\n      />\n    </>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;CAoBC;;;;AAID;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;AAVA;;;;;;;;;;AAYA;;CAEC,GACD,MAAM,SAAS;;IACb,mCAAmC;IACnC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD;IAEvC;;GAEC,GACD,MAAM,yBAAyB;QAC7B,cAAc,CAAC;QACf,gBAAgB,sBAAsB;IACxC;IAEA,qBACE;;0BACE,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,6HAAA,CAAA,UAAI;;;;;;;;;;0CAIP,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6IAAA,CAAA,UAAU;;;;;kDACX,6LAAC,8HAAA,CAAA,UAAK;kDACJ,cAAA,6LAAC,yIAAA,CAAA,UAAgB;;;;;;;;;;kDAEnB,6LAAC,8HAAA,CAAA,UAAK;kDACJ,cAAA,6LAAC,6IAAA,CAAA,UAAoB;;;;;;;;;;;;;;;;0CAKzB,6LAAC;gCACC,SAAS;gCACT,WAAU;gCACV,cAAW;0CAEZ,2BAAa,6LAAC,+LAAA,CAAA,IAAC;oCAAC,MAAM;;;;;yDAAS,6LAAC,qMAAA,CAAA,OAAI;oCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOlD,6LAAC,6IAAA,CAAA,UAAU;gBACT,QAAQ;gBACR,SAAS,IAAM,cAAc;;;;;;;;AAIrC;GArDM;;QAGwB,+HAAA,CAAA,eAAY;;;KAHpC;uCAuDS", "debugId": null}}, {"offset": {"line": 1800, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/layout/FooterNav.tsx"], "sourcesContent": ["/**\n * Footer Navigation Component\n * \n * Handles the navigation links in the footer section.\n * Organized into logical groups with proper accessibility.\n */\n\n'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { useAnalytics } from '@/hooks';\n\ninterface FooterNavProps {\n  className?: string;\n}\n\ninterface NavSection {\n  title: string;\n  links: Array<{\n    label: string;\n    href: string;\n    external?: boolean;\n  }>;\n}\n\nconst navSections: NavSection[] = [\n  {\n    title: 'Quick Links',\n    links: [\n      { label: 'Interactive Demo', href: '/#demo' },\n      { label: 'Our Services', href: '/#services' },\n      { label: 'How It Works', href: '/#process' },\n      { label: 'About Us', href: '/about' },\n      { label: 'Contact', href: '/#contact' }\n    ]\n  },\n  {\n    title: 'Services',\n    links: [\n      { label: 'Website to App', href: '/services#website-conversion' },\n      { label: 'Custom Mobile Apps', href: '/services#custom-apps' },\n      { label: 'App Maintenance', href: '/services#maintenance' },\n      { label: 'Consultation', href: '/services#consultation' }\n    ]\n  },\n  {\n    title: 'Resources',\n    links: [\n      { label: 'Blog', href: '/blog' },\n      { label: 'FAQ', href: '/faq' },\n      { label: 'Case Studies', href: '/blog?category=case-studies' },\n      { label: 'Pricing', href: '/services#pricing' }\n    ]\n  },\n  {\n    title: 'Legal',\n    links: [\n      { label: 'Privacy Policy', href: '/privacy' },\n      { label: 'Terms of Service', href: '/terms' },\n      { label: 'Cookie Policy', href: '/cookies' }\n    ]\n  }\n];\n\nconst FooterNav: React.FC<FooterNavProps> = ({ className = '' }) => {\n  const { trackNavigation } = useAnalytics();\n\n  const handleLinkClick = (label: string) => {\n    trackNavigation(label, 'footer_nav');\n  };\n\n  return (\n    <div className={`grid grid-cols-2 md:grid-cols-4 gap-8 ${className}`}>\n      {navSections.map((section) => (\n        <div key={section.title}>\n          <h3 className=\"text-lg font-semibold mb-4 text-white\">\n            {section.title}\n          </h3>\n          <ul className=\"space-y-2 text-sm\">\n            {section.links.map((link) => (\n              <li key={link.href}>\n                {link.external ? (\n                  <a\n                    href={link.href}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    onClick={() => handleLinkClick(link.label)}\n                    className=\"text-gray-400 dark:text-gray-300 hover:text-electric-blue transition-colors duration-200\"\n                  >\n                    {link.label}\n                  </a>\n                ) : (\n                  <Link\n                    href={link.href}\n                    onClick={() => handleLinkClick(link.label)}\n                    className=\"text-gray-400 dark:text-gray-300 hover:text-electric-blue transition-colors duration-200\"\n                  >\n                    {link.label}\n                  </Link>\n                )}\n              </li>\n            ))}\n          </ul>\n        </div>\n      ))}\n    </div>\n  );\n};\n\nexport default FooterNav;\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;AAKD;AACA;AAAA;;;AAJA;;;AAmBA,MAAM,cAA4B;IAChC;QACE,OAAO;QACP,OAAO;YACL;gBAAE,OAAO;gBAAoB,MAAM;YAAS;YAC5C;gBAAE,OAAO;gBAAgB,MAAM;YAAa;YAC5C;gBAAE,OAAO;gBAAgB,MAAM;YAAY;YAC3C;gBAAE,OAAO;gBAAY,MAAM;YAAS;YACpC;gBAAE,OAAO;gBAAW,MAAM;YAAY;SACvC;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,OAAO;gBAAkB,MAAM;YAA+B;YAChE;gBAAE,OAAO;gBAAsB,MAAM;YAAwB;YAC7D;gBAAE,OAAO;gBAAmB,MAAM;YAAwB;YAC1D;gBAAE,OAAO;gBAAgB,MAAM;YAAyB;SACzD;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,OAAO;gBAAQ,MAAM;YAAQ;YAC/B;gBAAE,OAAO;gBAAO,MAAM;YAAO;YAC7B;gBAAE,OAAO;gBAAgB,MAAM;YAA8B;YAC7D;gBAAE,OAAO;gBAAW,MAAM;YAAoB;SAC/C;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,OAAO;gBAAkB,MAAM;YAAW;YAC5C;gBAAE,OAAO;gBAAoB,MAAM;YAAS;YAC5C;gBAAE,OAAO;gBAAiB,MAAM;YAAW;SAC5C;IACH;CACD;AAED,MAAM,YAAsC,CAAC,EAAE,YAAY,EAAE,EAAE;;IAC7D,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD;IAEvC,MAAM,kBAAkB,CAAC;QACvB,gBAAgB,OAAO;IACzB;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,sCAAsC,EAAE,WAAW;kBACjE,YAAY,GAAG,CAAC,CAAC,wBAChB,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCACX,QAAQ,KAAK;;;;;;kCAEhB,6LAAC;wBAAG,WAAU;kCACX,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,qBAClB,6LAAC;0CACE,KAAK,QAAQ,iBACZ,6LAAC;oCACC,MAAM,KAAK,IAAI;oCACf,QAAO;oCACP,KAAI;oCACJ,SAAS,IAAM,gBAAgB,KAAK,KAAK;oCACzC,WAAU;8CAET,KAAK,KAAK;;;;;yDAGb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAM,KAAK,IAAI;oCACf,SAAS,IAAM,gBAAgB,KAAK,KAAK;oCACzC,WAAU;8CAET,KAAK,KAAK;;;;;;+BAjBR,KAAK,IAAI;;;;;;;;;;;eANd,QAAQ,KAAK;;;;;;;;;;AAiC/B;GA3CM;;QACwB,+HAAA,CAAA,eAAY;;;KADpC;uCA6CS", "debugId": null}}, {"offset": {"line": 1985, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/layout/FooterNewsletter.tsx"], "sourcesContent": ["/**\n * Footer Newsletter Component\n * \n * Handles newsletter signup in the footer section.\n * Includes form validation and submission tracking.\n */\n\n'use client';\n\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Mail, CheckCircle, AlertCircle } from 'lucide-react';\nimport { useAnalytics } from '@/hooks';\nimport { ANIMATION_CONFIG } from '@/config/site';\n\ninterface FooterNewsletterProps {\n  className?: string;\n}\n\ninterface NewsletterState {\n  email: string;\n  isSubmitting: boolean;\n  isSubmitted: boolean;\n  error: string | null;\n}\n\nconst FooterNewsletter: React.FC<FooterNewsletterProps> = ({ className = '' }) => {\n  const [state, setState] = useState<NewsletterState>({\n    email: '',\n    isSubmitting: false,\n    isSubmitted: false,\n    error: null\n  });\n\n  const { trackFormSubmission } = useAnalytics();\n\n  const validateEmail = (email: string): boolean => {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!validateEmail(state.email)) {\n      setState(prev => ({ ...prev, error: 'Please enter a valid email address' }));\n      return;\n    }\n\n    setState(prev => ({ ...prev, isSubmitting: true, error: null }));\n\n    try {\n      // Newsletter signup API call would go here\n      // For now, simulate success\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      setState(prev => ({ \n        ...prev, \n        isSubmitting: false, \n        isSubmitted: true,\n        email: ''\n      }));\n\n      trackFormSubmission('newsletter', true, {\n        source: 'footer',\n        email_domain: state.email.split('@')[1]\n      });\n\n      // Reset success state after 3 seconds\n      setTimeout(() => {\n        setState(prev => ({ ...prev, isSubmitted: false }));\n      }, 3000);\n\n    } catch {\n      setState(prev => ({ \n        ...prev, \n        isSubmitting: false, \n        error: 'Something went wrong. Please try again.' \n      }));\n\n      trackFormSubmission('newsletter', false, {\n        source: 'footer',\n        error: 'submission_failed'\n      });\n    }\n  };\n\n  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    setState(prev => ({ \n      ...prev, \n      email: e.target.value,\n      error: null \n    }));\n  };\n\n  return (\n    <div className={`${className}`}>\n      <h3 className=\"text-lg font-semibold mb-4 text-white\">\n        Stay Updated\n      </h3>\n      <p className=\"text-gray-400 dark:text-gray-300 text-sm mb-4\">\n        Get the latest updates on mobile app development trends and Mobilify news.\n      </p>\n\n      <form onSubmit={handleSubmit} className=\"space-y-3\">\n        <div className=\"relative\">\n          <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n            <Mail className=\"h-4 w-4 text-gray-400\" />\n          </div>\n          <input\n            type=\"email\"\n            value={state.email}\n            onChange={handleEmailChange}\n            placeholder=\"Enter your email\"\n            disabled={state.isSubmitting || state.isSubmitted}\n            className=\"\n              w-full pl-10 pr-4 py-2 text-sm\n              bg-gray-800 dark:bg-gray-700 \n              border border-gray-600 dark:border-gray-600\n              rounded-lg text-white placeholder-gray-400\n              focus:ring-2 focus:ring-electric-blue focus:border-electric-blue\n              disabled:opacity-50 disabled:cursor-not-allowed\n              transition-all duration-200\n            \"\n            aria-label=\"Email address for newsletter\"\n          />\n        </div>\n\n        {state.error && (\n          <motion.div\n            initial={{ opacity: 0, y: -10 }}\n            animate={{ opacity: 1, y: 0 }}\n            className=\"flex items-center gap-2 text-red-400 text-xs\"\n          >\n            <AlertCircle className=\"h-3 w-3\" />\n            {state.error}\n          </motion.div>\n        )}\n\n        <motion.button\n          type=\"submit\"\n          disabled={state.isSubmitting || state.isSubmitted || !state.email.trim()}\n          className=\"\n            w-full py-2 px-4 text-sm font-medium rounded-lg\n            bg-electric-blue hover:bg-electric-blue/90\n            text-white transition-all duration-200\n            disabled:opacity-50 disabled:cursor-not-allowed\n            focus:ring-2 focus:ring-electric-blue focus:ring-offset-2 focus:ring-offset-gray-800\n          \"\n          whileHover={{ scale: 1.02 }}\n          whileTap={{ scale: 0.98 }}\n        >\n          {state.isSubmitting ? (\n            <div className=\"flex items-center justify-center gap-2\">\n              <div className=\"w-3 h-3 border border-white border-t-transparent rounded-full animate-spin\" />\n              Subscribing...\n            </div>\n          ) : state.isSubmitted ? (\n            <div className=\"flex items-center justify-center gap-2\">\n              <CheckCircle className=\"h-4 w-4\" />\n              Subscribed!\n            </div>\n          ) : (\n            'Subscribe'\n          )}\n        </motion.button>\n      </form>\n\n      {state.isSubmitted && (\n        <motion.p\n          initial={{ opacity: 0, y: 10 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: ANIMATION_CONFIG.duration.normal }}\n          className=\"text-green-400 text-xs mt-2\"\n        >\n          Thank you for subscribing! Check your email for confirmation.\n        </motion.p>\n      )}\n    </div>\n  );\n};\n\nexport default FooterNewsletter;\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;AAID;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;;;AANA;;;;;;AAmBA,MAAM,mBAAoD,CAAC,EAAE,YAAY,EAAE,EAAE;;IAC3E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;QAClD,OAAO;QACP,cAAc;QACd,aAAa;QACb,OAAO;IACT;IAEA,MAAM,EAAE,mBAAmB,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD;IAE3C,MAAM,gBAAgB,CAAC;QACrB,MAAM,aAAa;QACnB,OAAO,WAAW,IAAI,CAAC;IACzB;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,cAAc,MAAM,KAAK,GAAG;YAC/B,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,OAAO;gBAAqC,CAAC;YAC1E;QACF;QAEA,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,cAAc;gBAAM,OAAO;YAAK,CAAC;QAE9D,IAAI;YACF,2CAA2C;YAC3C,4BAA4B;YAC5B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,cAAc;oBACd,aAAa;oBACb,OAAO;gBACT,CAAC;YAED,oBAAoB,cAAc,MAAM;gBACtC,QAAQ;gBACR,cAAc,MAAM,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;YACzC;YAEA,sCAAsC;YACtC,WAAW;gBACT,SAAS,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,aAAa;oBAAM,CAAC;YACnD,GAAG;QAEL,EAAE,OAAM;YACN,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,cAAc;oBACd,OAAO;gBACT,CAAC;YAED,oBAAoB,cAAc,OAAO;gBACvC,QAAQ;gBACR,OAAO;YACT;QACF;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,SAAS,CAAA,OAAQ,CAAC;gBAChB,GAAG,IAAI;gBACP,OAAO,EAAE,MAAM,CAAC,KAAK;gBACrB,OAAO;YACT,CAAC;IACH;IAEA,qBACE,6LAAC;QAAI,WAAW,GAAG,WAAW;;0BAC5B,6LAAC;gBAAG,WAAU;0BAAwC;;;;;;0BAGtD,6LAAC;gBAAE,WAAU;0BAAgD;;;;;;0BAI7D,6LAAC;gBAAK,UAAU;gBAAc,WAAU;;kCACtC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAElB,6LAAC;gCACC,MAAK;gCACL,OAAO,MAAM,KAAK;gCAClB,UAAU;gCACV,aAAY;gCACZ,UAAU,MAAM,YAAY,IAAI,MAAM,WAAW;gCACjD,WAAU;gCASV,cAAW;;;;;;;;;;;;oBAId,MAAM,KAAK,kBACV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,WAAU;;0CAEV,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;4BACtB,MAAM,KAAK;;;;;;;kCAIhB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,MAAK;wBACL,UAAU,MAAM,YAAY,IAAI,MAAM,WAAW,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI;wBACtE,WAAU;wBAOV,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;kCAEvB,MAAM,YAAY,iBACjB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;gCAA+E;;;;;;mCAG9F,MAAM,WAAW,iBACnB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,8NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;gCAAY;;;;;;mCAIrC;;;;;;;;;;;;YAKL,MAAM,WAAW,kBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gBACP,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU,wHAAA,CAAA,mBAAgB,CAAC,QAAQ,CAAC,MAAM;gBAAC;gBACzD,WAAU;0BACX;;;;;;;;;;;;AAMT;GA1JM;;QAQ4B,+HAAA,CAAA,eAAY;;;KARxC;uCA4JS", "debugId": null}}, {"offset": {"line": 2259, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/layout/Footer.tsx"], "sourcesContent": ["/**\n * Footer Component\n *\n * Main site footer with company information, navigation links, and newsletter signup.\n * Composed of smaller focused components for better maintainability.\n *\n * @component\n * @example\n * ```tsx\n * <Footer />\n * ```\n *\n * Features:\n * - Company branding and description\n * - Organized navigation links in multiple columns\n * - Newsletter signup with validation\n * - Dark mode toggle\n * - Copyright and legal information\n * - Responsive design with mobile-first approach\n *\n * Architecture:\n * - Uses composition with FooterNav and FooterNewsletter components\n * - CMS-driven content with fallbacks from site config\n * - Consistent theming with semantic color tokens\n */\n\n'use client';\n\nimport React from 'react';\nimport Logo from '../Logo';\nimport SimpleDarkModeToggle from '../SimpleDarkModeToggle';\nimport NoSSR from '../NoSSR';\nimport FooterNav from './FooterNav';\nimport FooterNewsletter from './FooterNewsletter';\nimport { SITE_CONFIG } from '@/config/site';\n\n/**\n * Site footer with navigation, newsletter, and company information\n */\nconst Footer = () => {\n  return (\n    <footer className=\"bg-dark-charcoal dark:bg-gray-950 text-white py-12 transition-colors duration-300\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-8 mb-8\">\n          {/* Company Info */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"flex items-center mb-4\">\n              <Logo />\n              <span className=\"ml-2 text-xl font-bold text-white\">{SITE_CONFIG.name}</span>\n            </div>\n            <p className=\"text-gray-400 dark:text-gray-300 text-sm leading-relaxed\">\n              {SITE_CONFIG.description}\n            </p>\n\n            {/* Newsletter Signup */}\n            <div className=\"mt-6\">\n              <FooterNewsletter />\n            </div>\n          </div>\n\n          {/* Navigation Links */}\n          <div className=\"lg:col-span-3\">\n            <FooterNav />\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"border-t border-gray-800 dark:border-gray-700 pt-8\">\n          <div className=\"flex flex-col md:flex-row items-center justify-between\">\n            <p className=\"text-gray-400 dark:text-gray-300 text-sm\">\n              © {new Date().getFullYear()} {SITE_CONFIG.name}. All rights reserved.\n            </p>\n            <div className=\"flex items-center space-x-6 mt-4 md:mt-0\">\n              <NoSSR>\n                <SimpleDarkModeToggle />\n              </NoSSR>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;CAwBC;;;;AAKD;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;AAUA;;CAEC,GACD,MAAM,SAAS;IACb,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6HAAA,CAAA,UAAI;;;;;sDACL,6LAAC;4CAAK,WAAU;sDAAqC,wHAAA,CAAA,cAAW,CAAC,IAAI;;;;;;;;;;;;8CAEvE,6LAAC;oCAAE,WAAU;8CACV,wHAAA,CAAA,cAAW,CAAC,WAAW;;;;;;8CAI1B,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,mJAAA,CAAA,UAAgB;;;;;;;;;;;;;;;;sCAKrB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,4IAAA,CAAA,UAAS;;;;;;;;;;;;;;;;8BAKd,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;;oCAA2C;oCACnD,IAAI,OAAO,WAAW;oCAAG;oCAAE,wHAAA,CAAA,cAAW,CAAC,IAAI;oCAAC;;;;;;;0CAEjD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,8HAAA,CAAA,UAAK;8CACJ,cAAA,6LAAC,6IAAA,CAAA,UAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrC;KA3CM;uCA6CS", "debugId": null}}, {"offset": {"line": 2454, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/PricingTable.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { Check, X } from 'lucide-react';\n\nconst PricingTable = () => {\n  const packages = [\n    {\n      name: 'Starter App',\n      price: '$5,000',\n      description: 'Perfect for converting existing websites',\n      popular: false,\n    },\n    {\n      name: 'Custom App',\n      price: '$15,000',\n      description: 'Turn your ideas into reality',\n      popular: true,\n    },\n    {\n      name: 'Enterprise',\n      price: 'Custom Pricing',\n      description: 'Bespoke solutions for complex needs',\n      popular: false,\n    },\n  ];\n\n  const features = [\n    {\n      name: 'Core Service',\n      starter: 'Website Conversion',\n      custom: 'Idea to App',\n      enterprise: 'Bespoke Solution',\n    },\n    {\n      name: 'Platform',\n      starter: 'iOS & Android',\n      custom: 'iOS & Android',\n      enterprise: 'iOS & Android',\n    },\n    {\n      name: 'UI/UX',\n      starter: 'Standardized Template',\n      custom: 'Custom Branded UI/UX',\n      enterprise: 'Fully Bespoke Design',\n    },\n    {\n      name: 'Push Notifications',\n      starter: true,\n      custom: true,\n      enterprise: true,\n    },\n    {\n      name: 'App Store Submission',\n      starter: 'Guided',\n      custom: 'Managed',\n      enterprise: 'Fully Managed',\n    },\n    {\n      name: 'Native Features (Camera, GPS)',\n      starter: false,\n      custom: true,\n      enterprise: true,\n    },\n    {\n      name: 'Offline Access',\n      starter: 'Basic Caching',\n      custom: 'Advanced Offline Mode',\n      enterprise: 'Custom Sync Engine',\n    },\n    {\n      name: '3rd Party Integrations',\n      starter: false,\n      custom: 'Up to 2 APIs',\n      enterprise: 'Unlimited APIs',\n    },\n    {\n      name: 'Dedicated Project Manager',\n      starter: false,\n      custom: false,\n      enterprise: true,\n    },\n    {\n      name: 'Support',\n      starter: 'Email Support',\n      custom: 'Priority Email & Chat',\n      enterprise: '24/7 Dedicated Support',\n    },\n  ];\n\n  const renderFeatureValue = (value: boolean | string | number) => {\n    if (typeof value === 'boolean') {\n      return value ? (\n        <Check className=\"w-5 h-5 text-green-500 mx-auto\" />\n      ) : (\n        <X className=\"w-5 h-5 text-gray-400 mx-auto\" />\n      );\n    }\n    return <span className=\"text-sm text-gray-600\">{value}</span>;\n  };\n\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"mb-12\"\n        >\n          <h2 className=\"text-3xl font-bold text-gray-900 text-center mb-8\">\n            Compare All Features & Pricing\n          </h2>\n          \n          {/* Mobile-friendly cards for small screens */}\n          <div className=\"md:hidden space-y-6\">\n            {packages.map((pkg, index) => (\n              <motion.div\n                key={pkg.name}\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                className={`bg-white rounded-xl p-6 shadow-lg ${\n                  pkg.popular ? 'ring-2 ring-indigo-500' : 'border border-gray-200'\n                }`}\n              >\n                {pkg.popular && (\n                  <div className=\"text-center mb-4\">\n                    <span className=\"bg-indigo-500 text-white px-3 py-1 rounded-full text-sm font-medium\">\n                      Most Popular\n                    </span>\n                  </div>\n                )}\n                \n                <div className=\"text-center mb-6\">\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-2\">{pkg.name}</h3>\n                  <div className=\"text-3xl font-bold text-gray-900 mb-2\">{pkg.price}</div>\n                  <p className=\"text-gray-600\">{pkg.description}</p>\n                </div>\n                \n                <div className=\"space-y-3\">\n                  {features.map((feature) => (\n                    <div key={feature.name} className=\"flex justify-between items-center py-2 border-b border-gray-100\">\n                      <span className=\"text-sm font-medium text-gray-900\">{feature.name}</span>\n                      <div className=\"text-right\">\n                        {renderFeatureValue(\n                          pkg.name === 'Starter App' ? feature.starter :\n                          pkg.name === 'Custom App' ? feature.custom :\n                          feature.enterprise\n                        )}\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </motion.div>\n            ))}\n          </div>\n\n          {/* Desktop table */}\n          <div className=\"hidden md:block overflow-x-auto\">\n            <table className=\"w-full\">\n              <thead>\n                <tr>\n                  <th className=\"text-left py-4 px-6 font-medium text-gray-900\">Features</th>\n                  {packages.map((pkg) => (\n                    <th key={pkg.name} className=\"text-center py-4 px-6\">\n                      <div className={`rounded-xl p-6 ${\n                        pkg.popular ? 'bg-indigo-50 ring-2 ring-indigo-500' : 'bg-gray-50'\n                      }`}>\n                        {pkg.popular && (\n                          <div className=\"mb-2\">\n                            <span className=\"bg-indigo-500 text-white px-3 py-1 rounded-full text-sm font-medium\">\n                              Most Popular\n                            </span>\n                          </div>\n                        )}\n                        <div className=\"font-bold text-xl text-gray-900 mb-2\">{pkg.name}</div>\n                        <div className=\"text-2xl font-bold text-gray-900 mb-2\">{pkg.price}</div>\n                        <div className=\"text-sm text-gray-600\">{pkg.description}</div>\n                      </div>\n                    </th>\n                  ))}\n                </tr>\n              </thead>\n              <tbody>\n                {features.map((feature, index) => (\n                  <tr key={feature.name} className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>\n                    <td className=\"py-4 px-6 font-medium text-gray-900\">{feature.name}</td>\n                    <td className=\"py-4 px-6 text-center\">{renderFeatureValue(feature.starter)}</td>\n                    <td className=\"py-4 px-6 text-center\">{renderFeatureValue(feature.custom)}</td>\n                    <td className=\"py-4 px-6 text-center\">{renderFeatureValue(feature.enterprise)}</td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        </motion.div>\n\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.3 }}\n          viewport={{ once: true }}\n          className=\"text-center\"\n        >\n          <button\n            onClick={() => {\n              const element = document.getElementById('contact');\n              if (element) {\n                element.scrollIntoView({ behavior: 'smooth' });\n              }\n            }}\n            className=\"bg-indigo-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-indigo-700 transition-colors duration-200 shadow-lg hover:shadow-xl\"\n          >\n            Get Your Custom Quote\n          </button>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default PricingTable;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAJA;;;;AAMA,MAAM,eAAe;IACnB,MAAM,WAAW;QACf;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,SAAS;QACX;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,SAAS;QACX;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,SAAS;QACX;KACD;IAED,MAAM,WAAW;QACf;YACE,MAAM;YACN,SAAS;YACT,QAAQ;YACR,YAAY;QACd;QACA;YACE,MAAM;YACN,SAAS;YACT,QAAQ;YACR,YAAY;QACd;QACA;YACE,MAAM;YACN,SAAS;YACT,QAAQ;YACR,YAAY;QACd;QACA;YACE,MAAM;YACN,SAAS;YACT,QAAQ;YACR,YAAY;QACd;QACA;YACE,MAAM;YACN,SAAS;YACT,QAAQ;YACR,YAAY;QACd;QACA;YACE,MAAM;YACN,SAAS;YACT,QAAQ;YACR,YAAY;QACd;QACA;YACE,MAAM;YACN,SAAS;YACT,QAAQ;YACR,YAAY;QACd;QACA;YACE,MAAM;YACN,SAAS;YACT,QAAQ;YACR,YAAY;QACd;QACA;YACE,MAAM;YACN,SAAS;YACT,QAAQ;YACR,YAAY;QACd;QACA;YACE,MAAM;YACN,SAAS;YACT,QAAQ;YACR,YAAY;QACd;KACD;IAED,MAAM,qBAAqB,CAAC;QAC1B,IAAI,OAAO,UAAU,WAAW;YAC9B,OAAO,sBACL,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;qCAEjB,6LAAC,+LAAA,CAAA,IAAC;gBAAC,WAAU;;;;;;QAEjB;QACA,qBAAO,6LAAC;YAAK,WAAU;sBAAyB;;;;;;IAClD;IAEA,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAKlE,6LAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,KAAK,sBAClB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAW,CAAC,kCAAkC,EAC5C,IAAI,OAAO,GAAG,2BAA2B,0BACzC;;wCAED,IAAI,OAAO,kBACV,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAsE;;;;;;;;;;;sDAM1F,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAwC,IAAI,IAAI;;;;;;8DAC9D,6LAAC;oDAAI,WAAU;8DAAyC,IAAI,KAAK;;;;;;8DACjE,6LAAC;oDAAE,WAAU;8DAAiB,IAAI,WAAW;;;;;;;;;;;;sDAG/C,6LAAC;4CAAI,WAAU;sDACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;oDAAuB,WAAU;;sEAChC,6LAAC;4DAAK,WAAU;sEAAqC,QAAQ,IAAI;;;;;;sEACjE,6LAAC;4DAAI,WAAU;sEACZ,mBACC,IAAI,IAAI,KAAK,gBAAgB,QAAQ,OAAO,GAC5C,IAAI,IAAI,KAAK,eAAe,QAAQ,MAAM,GAC1C,QAAQ,UAAU;;;;;;;mDANd,QAAQ,IAAI;;;;;;;;;;;mCAzBrB,IAAI,IAAI;;;;;;;;;;sCA0CnB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAM,WAAU;;kDACf,6LAAC;kDACC,cAAA,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAgD;;;;;;gDAC7D,SAAS,GAAG,CAAC,CAAC,oBACb,6LAAC;wDAAkB,WAAU;kEAC3B,cAAA,6LAAC;4DAAI,WAAW,CAAC,eAAe,EAC9B,IAAI,OAAO,GAAG,wCAAwC,cACtD;;gEACC,IAAI,OAAO,kBACV,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAK,WAAU;kFAAsE;;;;;;;;;;;8EAK1F,6LAAC;oEAAI,WAAU;8EAAwC,IAAI,IAAI;;;;;;8EAC/D,6LAAC;oEAAI,WAAU;8EAAyC,IAAI,KAAK;;;;;;8EACjE,6LAAC;oEAAI,WAAU;8EAAyB,IAAI,WAAW;;;;;;;;;;;;uDAblD,IAAI,IAAI;;;;;;;;;;;;;;;;kDAmBvB,6LAAC;kDACE,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;gDAAsB,WAAW,QAAQ,MAAM,IAAI,eAAe;;kEACjE,6LAAC;wDAAG,WAAU;kEAAuC,QAAQ,IAAI;;;;;;kEACjE,6LAAC;wDAAG,WAAU;kEAAyB,mBAAmB,QAAQ,OAAO;;;;;;kEACzE,6LAAC;wDAAG,WAAU;kEAAyB,mBAAmB,QAAQ,MAAM;;;;;;kEACxE,6LAAC;wDAAG,WAAU;kEAAyB,mBAAmB,QAAQ,UAAU;;;;;;;+CAJrE,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAY/B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,6LAAC;wBACC,SAAS;4BACP,MAAM,UAAU,SAAS,cAAc,CAAC;4BACxC,IAAI,SAAS;gCACX,QAAQ,cAAc,CAAC;oCAAE,UAAU;gCAAS;4BAC9C;wBACF;wBACA,WAAU;kCACX;;;;;;;;;;;;;;;;;;;;;;AAOX;KAzNM;uCA2NS", "debugId": null}}, {"offset": {"line": 2923, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/ServicesFAQ.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport <PERSON> from 'next/link';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { ChevronDown } from 'lucide-react';\n\nconst ServicesFAQ = () => {\n  const [openIndex, setOpenIndex] = useState<number | null>(null);\n\n  const faqs = [\n    {\n      question: 'What\\'s included in the Starter App package?',\n      answer: 'The Starter App package is perfect for converting existing websites into mobile apps. It includes iOS and Android development, standardized UI templates, push notifications, guided app store submission, and email support. This package focuses on getting your existing web presence onto mobile devices quickly and affordably.',\n    },\n    {\n      question: 'How is the Custom App package different?',\n      answer: 'The Custom App package is our most popular option for turning new ideas into reality. It includes everything in Starter, plus custom branded UI/UX design, native device features (camera, GPS, etc.), advanced offline capabilities, up to 2 third-party API integrations, managed app store submission, and priority email & chat support.',\n    },\n    {\n      question: 'When should I choose the Enterprise package?',\n      answer: 'The Enterprise package is designed for complex projects requiring bespoke solutions. It includes fully custom design, unlimited API integrations, custom sync engines, a dedicated project manager, 24/7 support, and fully managed app store processes. Perfect for large organizations or apps with complex technical requirements.',\n    },\n    {\n      question: 'How long does development typically take?',\n      answer: 'Development timelines vary by package: Starter Apps typically take 2-4 weeks, Custom Apps take 4-8 weeks, and Enterprise projects are scoped individually. We provide detailed timelines during our initial consultation and keep you updated throughout the process.',\n    },\n    {\n      question: 'Do you handle app store submissions?',\n      answer: 'Yes! All packages include app store submission support. Starter packages include guided submission (we help you through the process), Custom packages include managed submission (we handle most of it), and Enterprise packages include fully managed submission (we take care of everything).',\n    },\n    {\n      question: 'What ongoing support do you provide?',\n      answer: 'Support varies by package: Starter includes email support, Custom includes priority email & chat support, and Enterprise includes 24/7 dedicated support. All packages include bug fixes for the first 30 days after launch, and we offer ongoing maintenance plans for long-term support.',\n    },\n    {\n      question: 'Can I upgrade my package later?',\n      answer: 'Absolutely! You can upgrade your package at any time during development. We\\'ll adjust the pricing and timeline accordingly. Many clients start with a Starter package and upgrade to Custom as their needs become clearer.',\n    },\n    {\n      question: 'Do you provide the source code?',\n      answer: 'Yes, you own all the code we create for your project. Upon final payment, you receive the complete source code, documentation, and any necessary credentials. This ensures you\\'re never locked into working with us and can make changes independently if needed.',\n    },\n  ];\n\n  const toggleFAQ = (index: number) => {\n    setOpenIndex(openIndex === index ? null : index);\n  };\n\n  return (\n    <section className=\"py-20 bg-gray-50\">\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-12\"\n        >\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n            Frequently Asked Questions\n          </h2>\n          <p className=\"text-xl text-gray-600\">\n            Everything you need to know about our services and packages\n          </p>\n        </motion.div>\n\n        <div className=\"space-y-4\">\n          {faqs.map((faq, index) => (\n            <motion.div\n              key={index}\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5, delay: index * 0.1 }}\n              viewport={{ once: true }}\n              className=\"bg-white rounded-lg shadow-sm border border-gray-200\"\n            >\n              <button\n                onClick={() => toggleFAQ(index)}\n                className=\"w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors duration-200\"\n              >\n                <span className=\"font-medium text-gray-900 pr-4\">{faq.question}</span>\n                <ChevronDown\n                  className={`w-5 h-5 text-gray-500 transition-transform duration-200 ${\n                    openIndex === index ? 'transform rotate-180' : ''\n                  }`}\n                />\n              </button>\n              \n              <AnimatePresence>\n                {openIndex === index && (\n                  <motion.div\n                    initial={{ height: 0, opacity: 0 }}\n                    animate={{ height: 'auto', opacity: 1 }}\n                    exit={{ height: 0, opacity: 0 }}\n                    transition={{ duration: 0.3 }}\n                    className=\"overflow-hidden\"\n                  >\n                    <div className=\"px-6 pb-4 text-gray-600 leading-relaxed\">\n                      {faq.answer}\n                    </div>\n                  </motion.div>\n                )}\n              </AnimatePresence>\n            </motion.div>\n          ))}\n        </div>\n\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.5 }}\n          viewport={{ once: true }}\n          className=\"text-center mt-12\"\n        >\n          <p className=\"text-gray-600 mb-4\">\n            Still have questions? We&apos;d love to help!\n          </p>\n          <Link\n            href=\"/#contact\"\n            className=\"inline-flex items-center text-indigo-600 hover:text-indigo-700 font-semibold transition-colors duration-200\"\n          >\n            Get in touch with our team\n          </Link>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default ServicesFAQ;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;;;AALA;;;;;AAOA,MAAM,cAAc;;IAClB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAE1D,MAAM,OAAO;QACX;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;KACD;IAED,MAAM,YAAY,CAAC;QACjB,aAAa,cAAc,QAAQ,OAAO;IAC5C;IAEA,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAKvC,6LAAC;oBAAI,WAAU;8BACZ,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,6LAAC;oCACC,SAAS,IAAM,UAAU;oCACzB,WAAU;;sDAEV,6LAAC;4CAAK,WAAU;sDAAkC,IAAI,QAAQ;;;;;;sDAC9D,6LAAC,uNAAA,CAAA,cAAW;4CACV,WAAW,CAAC,wDAAwD,EAClE,cAAc,QAAQ,yBAAyB,IAC/C;;;;;;;;;;;;8CAIN,6LAAC,4LAAA,CAAA,kBAAe;8CACb,cAAc,uBACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,QAAQ;4CAAG,SAAS;wCAAE;wCACjC,SAAS;4CAAE,QAAQ;4CAAQ,SAAS;wCAAE;wCACtC,MAAM;4CAAE,QAAQ;4CAAG,SAAS;wCAAE;wCAC9B,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,WAAU;kDAEV,cAAA,6LAAC;4CAAI,WAAU;sDACZ,IAAI,MAAM;;;;;;;;;;;;;;;;;2BA7Bd;;;;;;;;;;8BAsCX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX;GAzHM;KAAA;uCA2HS", "debugId": null}}]}