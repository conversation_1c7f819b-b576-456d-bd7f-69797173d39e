{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "stegaEncodeSourceMap.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/csm/studioPath.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/csm/jsonPath.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/csm/resolveMapping.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/csm/isArray.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/csm/walkMap.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/stega/encodeIntoResult.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/csm/draftUtils.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/csm/createEditUrl.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/csm/resolveEditInfo.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/stega/filterDefault.ts", "file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/%40sanity/client/src/stega/stegaEncodeSourceMap.ts"], "sourcesContent": ["/** @alpha */\nexport type KeyedSegment = {_key: string}\n\n/** @alpha */\nexport type IndexTuple = [number | '', number | '']\n\n/** @alpha */\nexport type PathSegment = string | number | KeyedSegment | IndexTuple\n\n/** @alpha */\nexport type Path = PathSegment[]\n\nconst rePropName =\n  /[^.[\\]]+|\\[(?:(-?\\d+(?:\\.\\d+)?)|([\"'])((?:(?!\\2)[^\\\\]|\\\\.)*?)\\2)\\]|(?=(?:\\.|\\[\\])(?:\\.|\\[\\]|$))/g\n/** @internal */\nexport const reKeySegment = /_key\\s*==\\s*['\"](.*)['\"]/\nconst reIndexTuple = /^\\d*:\\d*$/\n\n/** @internal */\nexport function isIndexSegment(segment: PathSegment): segment is number {\n  return typeof segment === 'number' || (typeof segment === 'string' && /^\\[\\d+\\]$/.test(segment))\n}\n\n/** @internal */\nexport function isKeySegment(segment: PathSegment): segment is KeyedSegment {\n  if (typeof segment === 'string') {\n    return reKeySegment.test(segment.trim())\n  }\n\n  return typeof segment === 'object' && '_key' in segment\n}\n\n/** @internal */\nexport function isIndexTuple(segment: PathSegment): segment is IndexTuple {\n  if (typeof segment === 'string' && reIndexTuple.test(segment)) {\n    return true\n  }\n\n  if (!Array.isArray(segment) || segment.length !== 2) {\n    return false\n  }\n\n  const [from, to] = segment\n  return (typeof from === 'number' || from === '') && (typeof to === 'number' || to === '')\n}\n\n/** @internal */\nexport function get<Result = unknown, Fallback = unknown>(\n  obj: unknown,\n  path: Path | string,\n  defaultVal?: Fallback,\n): Result | typeof defaultVal {\n  const select = typeof path === 'string' ? fromString(path) : path\n  if (!Array.isArray(select)) {\n    throw new Error('Path must be an array or a string')\n  }\n\n  let acc: unknown | undefined = obj\n  for (let i = 0; i < select.length; i++) {\n    const segment = select[i]\n    if (isIndexSegment(segment)) {\n      if (!Array.isArray(acc)) {\n        return defaultVal\n      }\n\n      acc = acc[segment]\n    }\n\n    if (isKeySegment(segment)) {\n      if (!Array.isArray(acc)) {\n        return defaultVal\n      }\n\n      acc = acc.find((item) => item._key === segment._key)\n    }\n\n    if (typeof segment === 'string') {\n      acc =\n        typeof acc === 'object' && acc !== null\n          ? ((acc as Record<string, unknown>)[segment] as Result)\n          : undefined\n    }\n\n    if (typeof acc === 'undefined') {\n      return defaultVal\n    }\n  }\n\n  return acc as Result\n}\n\n/** @alpha */\nexport function toString(path: Path): string {\n  if (!Array.isArray(path)) {\n    throw new Error('Path is not an array')\n  }\n\n  return path.reduce<string>((target, segment, i) => {\n    const segmentType = typeof segment\n    if (segmentType === 'number') {\n      return `${target}[${segment}]`\n    }\n\n    if (segmentType === 'string') {\n      const separator = i === 0 ? '' : '.'\n      return `${target}${separator}${segment}`\n    }\n\n    if (isKeySegment(segment) && segment._key) {\n      return `${target}[_key==\"${segment._key}\"]`\n    }\n\n    if (Array.isArray(segment)) {\n      const [from, to] = segment\n      return `${target}[${from}:${to}]`\n    }\n\n    throw new Error(`Unsupported path segment \\`${JSON.stringify(segment)}\\``)\n  }, '')\n}\n\n/** @alpha */\nexport function fromString(path: string): Path {\n  if (typeof path !== 'string') {\n    throw new Error('Path is not a string')\n  }\n\n  const segments = path.match(rePropName)\n  if (!segments) {\n    throw new Error('Invalid path string')\n  }\n\n  return segments.map(parsePathSegment)\n}\n\nfunction parsePathSegment(segment: string): PathSegment {\n  if (isIndexSegment(segment)) {\n    return parseIndexSegment(segment)\n  }\n\n  if (isKeySegment(segment)) {\n    return parseKeySegment(segment)\n  }\n\n  if (isIndexTuple(segment)) {\n    return parseIndexTupleSegment(segment)\n  }\n\n  return segment\n}\n\nfunction parseIndexSegment(segment: string): PathSegment {\n  return Number(segment.replace(/[^\\d]/g, ''))\n}\n\nfunction parseKeySegment(segment: string): KeyedSegment {\n  const segments = segment.match(reKeySegment)\n  return {_key: segments![1]}\n}\n\nfunction parseIndexTupleSegment(segment: string): IndexTuple {\n  const [from, to] = segment.split(':').map((seg) => (seg === '' ? seg : Number(seg)))\n  return [from, to]\n}\n", "import * as studioPath from './studioPath'\nimport type {\n  ContentSourceMapParsedPath,\n  ContentSourceMapParsedPathKeyedSegment,\n  ContentSourceMapPaths,\n  Path,\n} from './types'\n\nconst ESCAPE: Record<string, string> = {\n  '\\f': '\\\\f',\n  '\\n': '\\\\n',\n  '\\r': '\\\\r',\n  '\\t': '\\\\t',\n  \"'\": \"\\\\'\",\n  '\\\\': '\\\\\\\\',\n}\n\nconst UNESCAPE: Record<string, string> = {\n  '\\\\f': '\\f',\n  '\\\\n': '\\n',\n  '\\\\r': '\\r',\n  '\\\\t': '\\t',\n  \"\\\\'\": \"'\",\n  '\\\\\\\\': '\\\\',\n}\n\n/**\n * @internal\n */\nexport function jsonPath(path: ContentSourceMapParsedPath): ContentSourceMapPaths[number] {\n  return `$${path\n    .map((segment) => {\n      if (typeof segment === 'string') {\n        const escapedKey = segment.replace(/[\\f\\n\\r\\t'\\\\]/g, (match) => {\n          return ESCAPE[match]\n        })\n        return `['${escapedKey}']`\n      }\n\n      if (typeof segment === 'number') {\n        return `[${segment}]`\n      }\n\n      if (segment._key !== '') {\n        const escapedKey = segment._key.replace(/['\\\\]/g, (match) => {\n          return ESCAPE[match]\n        })\n        return `[?(@._key=='${escapedKey}')]`\n      }\n\n      return `[${segment._index}]`\n    })\n    .join('')}`\n}\n\n/**\n * @internal\n */\nexport function parseJsonPath(path: ContentSourceMapPaths[number]): ContentSourceMapParsedPath {\n  const parsed: ContentSourceMapParsedPath = []\n\n  const parseRe = /\\['(.*?)'\\]|\\[(\\d+)\\]|\\[\\?\\(@\\._key=='(.*?)'\\)\\]/g\n  let match: RegExpExecArray | null\n\n  while ((match = parseRe.exec(path)) !== null) {\n    if (match[1] !== undefined) {\n      const key = match[1].replace(/\\\\(\\\\|f|n|r|t|')/g, (m) => {\n        return UNESCAPE[m]\n      })\n\n      parsed.push(key)\n      continue\n    }\n\n    if (match[2] !== undefined) {\n      parsed.push(parseInt(match[2], 10))\n      continue\n    }\n\n    if (match[3] !== undefined) {\n      const _key = match[3].replace(/\\\\(\\\\')/g, (m) => {\n        return UNESCAPE[m]\n      })\n\n      parsed.push({\n        _key,\n        _index: -1,\n      })\n      continue\n    }\n  }\n\n  return parsed\n}\n\n/**\n * @internal\n */\nexport function jsonPathToStudioPath(path: ContentSourceMapParsedPath): Path {\n  return path.map((segment) => {\n    if (typeof segment === 'string') {\n      return segment\n    }\n\n    if (typeof segment === 'number') {\n      return segment\n    }\n\n    if (segment._key !== '') {\n      return {_key: segment._key}\n    }\n\n    if (segment._index !== -1) {\n      return segment._index\n    }\n\n    throw new Error(`invalid segment:${JSON.stringify(segment)}`)\n  })\n}\n\n/**\n * @internal\n */\nexport function studioPathToJsonPath(path: Path | string): ContentSourceMapParsedPath {\n  const parsedPath = typeof path === 'string' ? studioPath.fromString(path) : path\n\n  return parsedPath.map((segment) => {\n    if (typeof segment === 'string') {\n      return segment\n    }\n\n    if (typeof segment === 'number') {\n      return segment\n    }\n\n    if (Array.isArray(segment)) {\n      throw new Error(`IndexTuple segments aren't supported:${JSON.stringify(segment)}`)\n    }\n\n    if (isContentSourceMapParsedPathKeyedSegment(segment)) {\n      return segment\n    }\n\n    if (segment._key) {\n      return {_key: segment._key, _index: -1}\n    }\n\n    throw new Error(`invalid segment:${JSON.stringify(segment)}`)\n  })\n}\n\nfunction isContentSourceMapParsedPathKeyedSegment(\n  segment: studioPath.PathSegment | ContentSourceMapParsedPath[number],\n): segment is ContentSourceMapParsedPathKeyedSegment {\n  return typeof segment === 'object' && '_key' in segment && '_index' in segment\n}\n\n/**\n * @internal\n */\nexport function jsonPathToMappingPath(path: ContentSourceMapParsedPath): (string | number)[] {\n  return path.map((segment) => {\n    if (typeof segment === 'string') {\n      return segment\n    }\n\n    if (typeof segment === 'number') {\n      return segment\n    }\n\n    if (segment._index !== -1) {\n      return segment._index\n    }\n\n    throw new Error(`invalid segment:${JSON.stringify(segment)}`)\n  })\n}\n", "import {jsonPath, jsonPathToMappingPath} from './jsonPath'\nimport type {ContentSourceMap, ContentSourceMapMapping, ContentSourceMapParsedPath} from './types'\n\n/**\n * @internal\n */\nexport function resolveMapping(\n  resultPath: ContentSourceMapParsedPath,\n  csm?: ContentSourceMap,\n):\n  | {\n      mapping: ContentSourceMapMapping\n      matchedPath: string\n      pathSuffix: string\n    }\n  | undefined {\n  if (!csm?.mappings) {\n    return undefined\n  }\n  const resultMappingPath = jsonPath(jsonPathToMappingPath(resultPath))\n\n  if (csm.mappings[resultMappingPath] !== undefined) {\n    return {\n      mapping: csm.mappings[resultMappingPath],\n      matchedPath: resultMappingPath,\n      pathSuffix: '',\n    }\n  }\n\n  const mappings = Object.entries(csm.mappings)\n    .filter(([key]) => resultMappingPath.startsWith(key))\n    .sort(([key1], [key2]) => key2.length - key1.length)\n\n  if (mappings.length == 0) {\n    return undefined\n  }\n\n  const [matchedPath, mapping] = mappings[0]\n  const pathSuffix = resultMappingPath.substring(matchedPath.length)\n  return {mapping, matchedPath, pathSuffix}\n}\n", "/** @internal */\nexport function isArray(value: unknown): value is Array<unknown> {\n  return value !== null && Array.isArray(value)\n}\n", "import {isRecord} from '../util/isRecord'\nimport {isArray} from './isArray'\nimport type {ContentSourceMapParsedPath, WalkMapFn} from './types'\n\n/**\n * generic way to walk a nested object or array and apply a mapping function to each value\n * @internal\n */\nexport function walkMap(\n  value: unknown,\n  mappingFn: WalkMapFn,\n  path: ContentSourceMapParsedPath = [],\n): unknown {\n  if (isArray(value)) {\n    return value.map((v, idx) => {\n      if (isRecord(v)) {\n        const _key = v['_key']\n        if (typeof _key === 'string') {\n          return walkMap(v, mappingFn, path.concat({_key, _index: idx}))\n        }\n      }\n\n      return walkMap(v, mappingFn, path.concat(idx))\n    })\n  }\n\n  if (isRecord(value)) {\n    // Handle Portable Text in a faster way\n    if (value._type === 'block' || value._type === 'span') {\n      const result = {...value}\n      if (value._type === 'block') {\n        result.children = walkMap(value.children, mappingFn, path.concat('children'))\n      } else if (value._type === 'span') {\n        result.text = walkMap(value.text, mappingFn, path.concat('text'))\n      }\n      return result\n    }\n\n    return Object.fromEntries(\n      Object.entries(value).map(([k, v]) => [k, walkMap(v, mappingFn, path.concat(k))]),\n    )\n  }\n\n  return mappingFn(value, path)\n}\n", "import type {ContentSourceMap} from '@sanity/client/csm'\n\nimport {parseJsonPath} from '../csm/jsonPath'\nimport {resolveMapping} from '../csm/resolveMapping'\nimport {walkMap} from '../csm/walkMap'\nimport type {Encoder} from './types'\n\n/**\n * @internal\n */\nexport function encodeIntoResult<Result>(\n  result: Result,\n  csm: ContentSourceMap,\n  encoder: Encoder,\n): Result {\n  return walkMap(result, (value, path) => {\n    // Only map strings, we could extend this in the future to support other types like integers...\n    if (typeof value !== 'string') {\n      return value\n    }\n\n    const resolveMappingResult = resolveMapping(path, csm)\n    if (!resolveMappingResult) {\n      return value\n    }\n\n    const {mapping, matchedPath} = resolveMappingResult\n    if (mapping.type !== 'value') {\n      return value\n    }\n\n    if (mapping.source.type !== 'documentValue') {\n      return value\n    }\n\n    const sourceDocument = csm.documents[mapping.source.document!]\n    const sourcePath = csm.paths[mapping.source.path]\n\n    const matchPathSegments = parseJsonPath(matchedPath)\n    const sourcePathSegments = parseJsonPath(sourcePath)\n    const fullSourceSegments = sourcePathSegments.concat(path.slice(matchPathSegments.length))\n\n    return encoder({\n      sourcePath: fullSourceSegments,\n      sourceDocument,\n      resultPath: path,\n      value,\n    })\n  }) as Result\n}\n", "// nominal/opaque type hack\ntype Opaque<T, K> = T & {__opaqueId__: K}\n\n/** @internal */\nexport type DraftId = Opaque<string, 'draftId'>\n\n/** @internal */\nexport type PublishedId = Opaque<string, 'publishedId'>\n\n/** @internal */\nexport const DRAFTS_FOLDER = 'drafts'\n\n/** @internal */\nexport const VERSION_FOLDER = 'versions'\n\nconst PATH_SEPARATOR = '.'\nconst DRAFTS_PREFIX = `${DRAFTS_FOLDER}${PATH_SEPARATOR}`\nconst VERSION_PREFIX = `${VERSION_FOLDER}${PATH_SEPARATOR}`\n\n/** @internal */\nexport function isDraftId(id: string): id is DraftId {\n  return id.startsWith(DRAFTS_PREFIX)\n}\n\n/** @internal */\nexport function isVersionId(id: string): boolean {\n  return id.startsWith(VERSION_PREFIX)\n}\n\n/** @internal */\nexport function isPublishedId(id: string): id is PublishedId {\n  return !isDraftId(id) && !isVersionId(id)\n}\n\n/** @internal */\nexport function getDraftId(id: string): DraftId {\n  if (isVersionId(id)) {\n    const publishedId = getPublishedId(id)\n    return (DRAFTS_PREFIX + publishedId) as DraftId\n  }\n\n  return isDraftId(id) ? id : ((DRAFTS_PREFIX + id) as DraftId)\n}\n\n/**  @internal */\nexport function getVersionId(id: string, version: string): string {\n  if (version === 'drafts' || version === 'published') {\n    throw new Error('Version can not be \"published\" or \"drafts\"')\n  }\n\n  return `${VERSION_PREFIX}${version}${PATH_SEPARATOR}${getPublishedId(id)}`\n}\n\n/**\n *  @internal\n *  Given an id, returns the versionId if it exists.\n *  e.g. `versions.summer-drop.foo` = `summer-drop`\n *  e.g. `drafts.foo` = `undefined`\n *  e.g. `foo` = `undefined`\n */\nexport function getVersionFromId(id: string): string | undefined {\n  if (!isVersionId(id)) return undefined\n  // eslint-disable-next-line unused-imports/no-unused-vars\n  const [_versionPrefix, versionId, ..._publishedId] = id.split(PATH_SEPARATOR)\n\n  return versionId\n}\n\n/** @internal */\nexport function getPublishedId(id: string): PublishedId {\n  if (isVersionId(id)) {\n    // make sure to only remove the versions prefix and the bundle name\n    return id.split(PATH_SEPARATOR).slice(2).join(PATH_SEPARATOR) as PublishedId as PublishedId\n  }\n\n  if (isDraftId(id)) {\n    return id.slice(DRAFTS_PREFIX.length) as PublishedId\n  }\n\n  return id as PublishedId\n}\n", "import {getPublishedId, getVersionFromId, isPublishedId, isVersionId} from './draftUtils'\nimport {jsonPathToStudioPath} from './jsonPath'\nimport * as studioPath from './studioPath'\nimport type {CreateEditUrlOptions, EditIntentUrl, StudioBaseUrl} from './types'\n\n/** @internal */\nexport function createEditUrl(options: CreateEditUrlOptions): `${StudioBaseUrl}${EditIntentUrl}` {\n  const {\n    baseUrl,\n    workspace: _workspace = 'default',\n    tool: _tool = 'default',\n    id: _id,\n    type,\n    path,\n    projectId,\n    dataset,\n  } = options\n\n  if (!baseUrl) {\n    throw new Error('baseUrl is required')\n  }\n  if (!path) {\n    throw new Error('path is required')\n  }\n  if (!_id) {\n    throw new Error('id is required')\n  }\n  if (baseUrl !== '/' && baseUrl.endsWith('/')) {\n    throw new Error('baseUrl must not end with a slash')\n  }\n\n  const workspace = _workspace === 'default' ? undefined : _workspace\n  const tool = _tool === 'default' ? undefined : _tool\n  const id = getPublishedId(_id)\n  const stringifiedPath = Array.isArray(path)\n    ? studioPath.toString(jsonPathToStudioPath(path))\n    : path\n\n  // eslint-disable-next-line no-warning-comments\n  // @TODO Using searchParams as a temporary workaround until `@sanity/overlays` can decode state from the path reliably\n  const searchParams = new URLSearchParams({\n    baseUrl,\n    id,\n    type,\n    path: stringifiedPath,\n  })\n  if (workspace) {\n    searchParams.set('workspace', workspace)\n  }\n  if (tool) {\n    searchParams.set('tool', tool)\n  }\n  if (projectId) {\n    searchParams.set('projectId', projectId)\n  }\n  if (dataset) {\n    searchParams.set('dataset', dataset)\n  }\n  if (isPublishedId(_id)) {\n    searchParams.set('perspective', 'published')\n  } else if (isVersionId(_id)) {\n    const versionId = getVersionFromId(_id)!\n    searchParams.set('perspective', versionId)\n  }\n\n  const segments = [baseUrl === '/' ? '' : baseUrl]\n  if (workspace) {\n    segments.push(workspace)\n  }\n  const routerParams = [\n    'mode=presentation',\n    `id=${id}`,\n    `type=${type}`,\n    `path=${encodeURIComponent(stringifiedPath)}`,\n  ]\n  if (tool) {\n    routerParams.push(`tool=${tool}`)\n  }\n  segments.push('intent', 'edit', `${routerParams.join(';')}?${searchParams}`)\n  return segments.join('/') as unknown as `${StudioBaseUrl}${EditIntentUrl}`\n}\n", "import {parseJsonPath} from './jsonPath'\nimport {resolveMapping} from './resolveMapping'\nimport type {\n  CreateEditUrlOptions,\n  ResolveEditInfoOptions,\n  StudioBaseRoute,\n  StudioBaseUrl,\n  StudioUrl,\n} from './types'\n\n/** @internal */\nexport function resolveEditInfo(options: ResolveEditInfoOptions): CreateEditUrlOptions | undefined {\n  const {resultSourceMap: csm, resultPath} = options\n  const {mapping, pathSuffix} = resolveMapping(resultPath, csm) || {}\n\n  if (!mapping) {\n    // console.warn('no mapping for path', { path: resultPath, sourceMap: csm })\n    return undefined\n  }\n\n  if (mapping.source.type === 'literal') {\n    return undefined\n  }\n\n  if (mapping.source.type === 'unknown') {\n    return undefined\n  }\n\n  const sourceDoc = csm.documents[mapping.source.document]\n  const sourcePath = csm.paths[mapping.source.path]\n\n  if (sourceDoc && sourcePath) {\n    const {baseUrl, workspace, tool} = resolveStudioBaseRoute(\n      typeof options.studioUrl === 'function' ? options.studioUrl(sourceDoc) : options.studioUrl,\n    )\n    if (!baseUrl) return undefined\n    const {_id, _type, _projectId, _dataset} = sourceDoc\n    return {\n      baseUrl,\n      workspace,\n      tool,\n      id: _id,\n      type: _type,\n      path: parseJsonPath(sourcePath + pathSuffix),\n      projectId: _projectId,\n      dataset: _dataset,\n    } satisfies CreateEditUrlOptions\n  }\n\n  return undefined\n}\n\n/** @internal */\nexport function resolveStudioBaseRoute(studioUrl: StudioUrl): StudioBaseRoute {\n  let baseUrl: StudioBaseUrl = typeof studioUrl === 'string' ? studioUrl : studioUrl.baseUrl\n  if (baseUrl !== '/') {\n    baseUrl = baseUrl.replace(/\\/$/, '')\n  }\n  if (typeof studioUrl === 'string') {\n    return {baseUrl}\n  }\n  return {...studioUrl, baseUrl}\n}\n", "import type {ContentSourceMapParsedPath, FilterDefault} from './types'\n\nexport const filterDefault: FilterDefault = ({sourcePath, resultPath, value}) => {\n  // Skips encoding on URL or Date strings, similar to the `skip: 'auto'` parameter in vercelStegaCombine()\n  if (isValidDate(value) || isValidURL(value)) {\n    return false\n  }\n\n  const endPath = sourcePath.at(-1)\n  // Never encode slugs\n  if (sourcePath.at(-2) === 'slug' && endPath === 'current') {\n    return false\n  }\n\n  // Skip underscored keys, and strings that end with `Id`, needs better heuristics but it works for now\n  if (typeof endPath === 'string' && (endPath.startsWith('_') || endPath.endsWith('Id'))) {\n    return false\n  }\n\n  // Don't encode into anything that is suggested it'll render for SEO in meta tags\n  if (\n    sourcePath.some(\n      (path) => path === 'meta' || path === 'metadata' || path === 'openGraph' || path === 'seo',\n    )\n  ) {\n    return false\n  }\n\n  // If the sourcePath or resultPath contains something that sounds like a type, like iconType, we skip encoding, as it's most\n  // of the time used for logic that breaks if it contains stega characters\n  if (hasTypeLike(sourcePath) || hasTypeLike(resultPath)) {\n    return false\n  }\n\n  // Finally, we ignore a bunch of paths that are typically used for page building\n  if (typeof endPath === 'string' && denylist.has(endPath)) {\n    return false\n  }\n\n  return true\n}\n\nconst denylist = new Set([\n  'color',\n  'colour',\n  'currency',\n  'email',\n  'format',\n  'gid',\n  'hex',\n  'href',\n  'hsl',\n  'hsla',\n  'icon',\n  'id',\n  'index',\n  'key',\n  'language',\n  'layout',\n  'link',\n  'linkAction',\n  'locale',\n  'lqip',\n  'page',\n  'path',\n  'ref',\n  'rgb',\n  'rgba',\n  'route',\n  'secret',\n  'slug',\n  'status',\n  'tag',\n  'template',\n  'theme',\n  'type',\n  'textTheme',\n  'unit',\n  'url',\n  'username',\n  'variant',\n  'website',\n])\n\nfunction isValidDate(dateString: string) {\n  return /^\\d{4}-\\d{2}-\\d{2}/.test(dateString) ? Boolean(Date.parse(dateString)) : false\n}\n\nfunction isValidURL(url: string) {\n  try {\n    new URL(url, url.startsWith('/') ? 'https://acme.com' : undefined)\n  } catch {\n    return false\n  }\n  return true\n}\n\nfunction hasTypeLike(path: ContentSourceMapParsedPath): boolean {\n  return path.some((segment) => typeof segment === 'string' && segment.match(/type/i) !== null)\n}\n", "import {vercelStegaCombine} from '@vercel/stega'\n\nimport {createEditUrl} from '../csm/createEditUrl'\nimport {jsonPathToStudioPath} from '../csm/jsonPath'\nimport {resolveStudioBaseRoute} from '../csm/resolveEditInfo'\nimport {reKeySegment, toString as studioPathToString} from '../csm/studioPath'\nimport {encodeIntoResult} from './encodeIntoResult'\nimport {filterDefault} from './filterDefault'\nimport {\n  type ContentSourceMap,\n  type ContentSourceMapParsedPath,\n  type InitializedStegaConfig,\n} from './types'\n\nconst TRUNCATE_LENGTH = 20\n\n/**\n * Uses `@vercel/stega` to embed edit info JSON into strings in your query result.\n * The JSON payloads are added using invisible characters so they don't show up visually.\n * The edit info is generated from the Content Source Map (CSM) that is returned from Sanity for the query.\n * @public\n */\nexport function stegaEncodeSourceMap<Result = unknown>(\n  result: Result,\n  resultSourceMap: ContentSourceMap | undefined,\n  config: InitializedStegaConfig,\n): Result {\n  const {filter, logger, enabled} = config\n  if (!enabled) {\n    const msg = \"config.enabled must be true, don't call this function otherwise\"\n    logger?.error?.(`[@sanity/client]: ${msg}`, {result, resultSourceMap, config})\n    throw new TypeError(msg)\n  }\n\n  if (!resultSourceMap) {\n    logger?.error?.('[@sanity/client]: Missing Content Source Map from response body', {\n      result,\n      resultSourceMap,\n      config,\n    })\n    return result\n  }\n\n  if (!config.studioUrl) {\n    const msg = 'config.studioUrl must be defined'\n    logger?.error?.(`[@sanity/client]: ${msg}`, {result, resultSourceMap, config})\n    throw new TypeError(msg)\n  }\n\n  const report: Record<'encoded' | 'skipped', {path: string; length: number; value: string}[]> = {\n    encoded: [],\n    skipped: [],\n  }\n\n  const resultWithStega = encodeIntoResult(\n    result,\n    resultSourceMap,\n    ({sourcePath, sourceDocument, resultPath, value}) => {\n      // Allow userland to control when to opt-out of encoding\n      if (\n        (typeof filter === 'function'\n          ? filter({sourcePath, resultPath, filterDefault, sourceDocument, value})\n          : filterDefault({sourcePath, resultPath, filterDefault, sourceDocument, value})) === false\n      ) {\n        if (logger) {\n          report.skipped.push({\n            path: prettyPathForLogging(sourcePath),\n            value: `${value.slice(0, TRUNCATE_LENGTH)}${\n              value.length > TRUNCATE_LENGTH ? '...' : ''\n            }`,\n            length: value.length,\n          })\n        }\n        return value\n      }\n\n      if (logger) {\n        report.encoded.push({\n          path: prettyPathForLogging(sourcePath),\n          value: `${value.slice(0, TRUNCATE_LENGTH)}${value.length > TRUNCATE_LENGTH ? '...' : ''}`,\n          length: value.length,\n        })\n      }\n\n      const {baseUrl, workspace, tool} = resolveStudioBaseRoute(\n        typeof config.studioUrl === 'function'\n          ? config.studioUrl(sourceDocument)\n          : config.studioUrl!,\n      )\n      if (!baseUrl) return value\n      const {_id: id, _type: type, _projectId: projectId, _dataset: dataset} = sourceDocument\n\n      return vercelStegaCombine(\n        value,\n        {\n          origin: 'sanity.io',\n          href: createEditUrl({\n            baseUrl,\n            workspace,\n            tool,\n            id,\n            type,\n            path: sourcePath,\n            ...(!config.omitCrossDatasetReferenceData && {dataset, projectId}),\n          }),\n        },\n        // We use custom logic to determine if we should skip encoding\n        false,\n      )\n    },\n  )\n\n  if (logger) {\n    const isSkipping = report.skipped.length\n    const isEncoding = report.encoded.length\n    if (isSkipping || isEncoding) {\n      ;(logger?.groupCollapsed || logger.log)?.('[@sanity/client]: Encoding source map into result')\n      logger.log?.(\n        `[@sanity/client]: Paths encoded: ${report.encoded.length}, skipped: ${report.skipped.length}`,\n      )\n    }\n    if (report.encoded.length > 0) {\n      logger?.log?.(`[@sanity/client]: Table of encoded paths`)\n      ;(logger?.table || logger.log)?.(report.encoded)\n    }\n    if (report.skipped.length > 0) {\n      const skipped = new Set<string>()\n      for (const {path} of report.skipped) {\n        skipped.add(path.replace(reKeySegment, '0').replace(/\\[\\d+\\]/g, '[]'))\n      }\n      logger?.log?.(`[@sanity/client]: List of skipped paths`, [...skipped.values()])\n    }\n\n    if (isSkipping || isEncoding) {\n      logger?.groupEnd?.()\n    }\n  }\n\n  return resultWithStega\n}\n\nfunction prettyPathForLogging(path: ContentSourceMapParsedPath): string {\n  return studioPathToString(jsonPathToStudioPath(path))\n}\n"], "names": ["studioPath.toString", "vercelStegaCombine", "studioPathToString"], "mappings": ";;;;;;;AAeO,MAAM,eAAe;AASrB,SAAS,aAAa,OAAA,EAA+C;IAC1E,OAAI,OAAO,WAAY,WACd,aAAa,IAAA,CAAK,QAAQ,IAAA,CAAK,CAAC,IAGlC,OAAO,WAAY,YAAY,UAAU;AAClD;AA8DO,SAAS,SAAS,IAAA,EAAoB;IACvC,IAAA,CAAC,MAAM,OAAA,CAAQ,IAAI,GACf,MAAA,IAAI,MAAM,sBAAsB;IAGxC,OAAO,KAAK,MAAA,CAAe,CAAC,QAAQ,SAAS,MAAM;QACjD,MAAM,cAAc,OAAO;QAC3B,IAAI,gBAAgB,UACX,OAAA,GAAG,MAAM,CAAA,CAAA,EAAI,OAAO,CAAA,CAAA,CAAA;QAG7B,IAAI,gBAAgB,UAEX,OAAA,GAAG,MAAM,GADE,MAAM,IAAI,KAAK,GACL,GAAG,OAAO,EAAA;QAGpC,IAAA,aAAa,OAAO,KAAK,QAAQ,IAAA,EACnC,OAAO,GAAG,MAAM,CAAA,QAAA,EAAW,QAAQ,IAAI,CAAA,EAAA,CAAA;QAGrC,IAAA,MAAM,OAAA,CAAQ,OAAO,GAAG;YACpB,MAAA,CAAC,MAAM,EAAE,CAAA,GAAI;YACnB,OAAO,GAAG,MAAM,CAAA,CAAA,EAAI,IAAI,CAAA,CAAA,EAAI,EAAE,CAAA,CAAA,CAAA;QAAA;QAGhC,MAAM,IAAI,MAAM,CAAA,2BAAA,EAA8B,KAAK,SAAA,CAAU,OAAO,CAAC,CAAA,EAAA,CAAI;IAAA,GACxE,EAAE;AACP;AC/GA,MAAM,SAAiC;IACrC,MAAM;IACN,MAAM;IACN,MAAM;IACN,KAAM;IACN,KAAK;IACL,MAAM;AACR,GAEM,WAAmC;IACvC,OAAO;IACP,OAAO,CAAA;AAAA,CAAA;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,QAAQ;AACV;AAKO,SAAS,SAAS,IAAA,EAAiE;IACjF,OAAA,CAAA,CAAA,EAAI,KACR,GAAA,CAAI,CAAC,UACA,OAAO,WAAY,WAId,CAAA,EAAA,EAHY,QAAQ,OAAA,CAAQ,kBAAkB,CAAC,QAC7C,MAAA,CAAO,KAAK,CACpB,CACqB,CAAA,EAAA,CAAA,GAGpB,OAAO,WAAY,WACd,CAAA,CAAA,EAAI,OAAO,CAAA,CAAA,CAAA,GAGhB,QAAQ,IAAA,KAAS,KAIZ,CAAA,YAAA,EAHY,QAAQ,IAAA,CAAK,OAAA,CAAQ,UAAU,CAAC,QAC1C,MAAA,CAAO,KAAK,CACpB,CAC+B,CAAA,GAAA,CAAA,GAG3B,CAAA,CAAA,EAAI,QAAQ,MAAM,CAAA,CAAA,CAC1B,EACA,IAAA,CAAK,EAAE,CAAC,EAAA;AACb;AAKO,SAAS,cAAc,IAAA,EAAiE;IACvF,MAAA,SAAqC,EAAA,EAErC,UAAU;IACZ,IAAA;IAEJ,MAAA,CAAQ,QAAQ,QAAQ,IAAA,CAAK,IAAI,CAAA,MAAO,MAAM;QACxC,IAAA,KAAA,CAAM,CAAC,CAAA,KAAM,KAAA,GAAW;YACpB,MAAA,MAAM,KAAA,CAAM,CAAC,CAAA,CAAE,OAAA,CAAQ,qBAAqB,CAAC,IAC1C,QAAA,CAAS,CAAC,CAClB;YAED,OAAO,IAAA,CAAK,GAAG;YACf;QAAA;QAGE,IAAA,KAAA,CAAM,CAAC,CAAA,KAAM,KAAA,GAAW;YAC1B,OAAO,IAAA,CAAK,SAAS,KAAA,CAAM,CAAC,CAAA,EAAG,EAAE,CAAC;YAClC;QAAA;QAGE,IAAA,KAAA,CAAM,CAAC,CAAA,KAAM,KAAA,GAAW;YACpB,MAAA,OAAO,KAAA,CAAM,CAAC,CAAA,CAAE,OAAA,CAAQ,YAAY,CAAC,IAClC,QAAA,CAAS,CAAC,CAClB;YAED,OAAO,IAAA,CAAK;gBACV;gBACA,QAAQ,CAAA;YAAA,CACT;YACD;QAAA;IACF;IAGK,OAAA;AACT;AAKO,SAAS,qBAAqB,IAAA,EAAwC;IACpE,OAAA,KAAK,GAAA,CAAI,CAAC,YAAY;QAK3B,IAJI,OAAO,WAAY,YAInB,OAAO,WAAY,UACd,OAAA;QAGT,IAAI,QAAQ,IAAA,KAAS,IACZ,OAAA;YAAC,MAAM,QAAQ,IAAA;QAAI;QAG5B,IAAI,QAAQ,MAAA,KAAW,CAAA,GACrB,OAAO,QAAQ,MAAA;QAGjB,MAAM,IAAI,MAAM,CAAA,gBAAA,EAAmB,KAAK,SAAA,CAAU,OAAO,CAAC,EAAE;IAAA,CAC7D;AACH;AA0CO,SAAS,sBAAsB,IAAA,EAAuD;IACpF,OAAA,KAAK,GAAA,CAAI,CAAC,YAAY;QAK3B,IAJI,OAAO,WAAY,YAInB,OAAO,WAAY,UACd,OAAA;QAGT,IAAI,QAAQ,MAAA,KAAW,CAAA,GACrB,OAAO,QAAQ,MAAA;QAGjB,MAAM,IAAI,MAAM,CAAA,gBAAA,EAAmB,KAAK,SAAA,CAAU,OAAO,CAAC,EAAE;IAAA,CAC7D;AACH;AC1KgB,SAAA,eACd,UAAA,EACA,GAAA,EAOY;IACZ,IAAI,CAAC,KAAK,UACR;IAEF,MAAM,oBAAoB,SAAS,sBAAsB,UAAU,CAAC;IAEhE,IAAA,IAAI,QAAA,CAAS,iBAAiB,CAAA,KAAM,KAAA,GAC/B,OAAA;QACL,SAAS,IAAI,QAAA,CAAS,iBAAiB,CAAA;QACvC,aAAa;QACb,YAAY;IACd;IAGI,MAAA,WAAW,OAAO,OAAA,CAAQ,IAAI,QAAQ,EACzC,MAAA,CAAO,CAAC,CAAC,GAAG,CAAA,GAAM,kBAAkB,UAAA,CAAW,GAAG,CAAC,EACnD,IAAA,CAAK,CAAC,CAAC,IAAI,CAAA,EAAG,CAAC,IAAI,CAAA,GAAM,KAAK,MAAA,GAAS,KAAK,MAAM;IAErD,IAAI,SAAS,MAAA,IAAU,GACrB;IAGI,MAAA,CAAC,aAAa,OAAO,CAAA,GAAI,QAAA,CAAS,CAAC,CAAA,EACnC,aAAa,kBAAkB,SAAA,CAAU,YAAY,MAAM;IAC1D,OAAA;QAAC;QAAS;QAAa;IAAU;AAC1C;ACvCO,SAAS,QAAQ,KAAA,EAAyC;IAC/D,OAAO,UAAU,QAAQ,MAAM,OAAA,CAAQ,KAAK;AAC9C;ACKO,SAAS,QACd,KAAA,EACA,SAAA,EACA,OAAmC,CAAA,CAAA,EAC1B;IACT,IAAI,QAAQ,KAAK,GACf,OAAO,MAAM,GAAA,CAAI,CAAC,GAAG,QAAQ;QACvB,qLAAA,WAAA,EAAS,CAAC,GAAG;YACf,MAAM,OAAO,EAAE,IAAA;YACf,IAAI,OAAO,QAAS,UACX,OAAA,QAAQ,GAAG,WAAW,KAAK,MAAA,CAAO;gBAAC;gBAAM,QAAQ;YAAG,CAAC,CAAC;QAAA;QAIjE,OAAO,QAAQ,GAAG,WAAW,KAAK,MAAA,CAAO,GAAG,CAAC;IAAA,CAC9C;IAGC,qLAAA,WAAA,EAAS,KAAK,GAAG;QAEnB,IAAI,MAAM,KAAA,KAAU,WAAW,MAAM,KAAA,KAAU,QAAQ;YAC/C,MAAA,SAAS;gBAAC,GAAG,KAAA;YAAK;YACpB,OAAA,MAAM,KAAA,KAAU,UAClB,OAAO,QAAA,GAAW,QAAQ,MAAM,QAAA,EAAU,WAAW,KAAK,MAAA,CAAO,UAAU,CAAC,IACnE,MAAM,KAAA,KAAU,UAAA,CACzB,OAAO,IAAA,GAAO,QAAQ,MAAM,IAAA,EAAM,WAAW,KAAK,MAAA,CAAO,MAAM,CAAC,CAAA,GAE3D;QAAA;QAGT,OAAO,OAAO,WAAA,CACZ,OAAO,OAAA,CAAQ,KAAK,EAAE,GAAA,CAAI,CAAC,CAAC,GAAG,CAAC,CAAA,GAAM;gBAAC;gBAAG,QAAQ,GAAG,WAAW,KAAK,MAAA,CAAO,CAAC,CAAC,CAAC;aAAC;IAClF;IAGK,OAAA,UAAU,OAAO,IAAI;AAC9B;AClCgB,SAAA,iBACd,MAAA,EACA,GAAA,EACA,OAAA,EACQ;IACR,OAAO,QAAQ,QAAQ,CAAC,OAAO,SAAS;QAEtC,IAAI,OAAO,SAAU,UACZ,OAAA;QAGH,MAAA,uBAAuB,eAAe,MAAM,GAAG;QACrD,IAAI,CAAC,sBACI,OAAA;QAGH,MAAA,EAAC,OAAA,EAAS,WAAA,CAAA,CAAA,GAAe;QAK/B,IAJI,QAAQ,IAAA,KAAS,WAIjB,QAAQ,MAAA,CAAO,IAAA,KAAS,iBACnB,OAAA;QAGH,MAAA,iBAAiB,IAAI,SAAA,CAAU,QAAQ,MAAA,CAAO,QAAS,CAAA,EACvD,aAAa,IAAI,KAAA,CAAM,QAAQ,MAAA,CAAO,IAAI,CAAA,EAE1C,oBAAoB,cAAc,WAAW,GAE7C,qBADqB,cAAc,UAAU,EACL,MAAA,CAAO,KAAK,KAAA,CAAM,kBAAkB,MAAM,CAAC;QAEzF,OAAO,QAAQ;YACb,YAAY;YACZ;YACA,YAAY;YACZ;QAAA,CACD;IAAA,CACF;AACH;ACvCa,MAAA,gBAAgB,UAGhB,iBAAiB,YAExB,iBAAiB,KACjB,gBAAgB,GAAG,aAAa,GAAG,cAAc,EAAA,EACjD,iBAAiB,GAAG,cAAc,GAAG,cAAc,EAAA;AAGlD,SAAS,UAAU,EAAA,EAA2B;IAC5C,OAAA,GAAG,UAAA,CAAW,aAAa;AACpC;AAGO,SAAS,YAAY,EAAA,EAAqB;IACxC,OAAA,GAAG,UAAA,CAAW,cAAc;AACrC;AAGO,SAAS,cAAc,EAAA,EAA+B;IAC3D,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,YAAY,EAAE;AAC1C;AA4BO,SAAS,iBAAiB,EAAA,EAAgC;IAC3D,IAAA,CAAC,YAAY,EAAE,EAAG,CAAA;IAEhB,MAAA,CAAC,gBAAgB,WAAW,GAAG,YAAY,CAAA,GAAI,GAAG,KAAA,CAAM,cAAc;IAErE,OAAA;AACT;AAGO,SAAS,eAAe,EAAA,EAAyB;IAClD,OAAA,YAAY,EAAE,IAET,GAAG,KAAA,CAAM,cAAc,EAAE,KAAA,CAAM,CAAC,EAAE,IAAA,CAAK,cAAc,IAG1D,UAAU,EAAE,IACP,GAAG,KAAA,CAAM,cAAc,MAAM,IAG/B;AACT;AC1EO,SAAS,cAAc,OAAA,EAAmE;IACzF,MAAA,EACJ,OAAA,EACA,WAAW,aAAa,SAAA,EACxB,MAAM,QAAQ,SAAA,EACd,IAAI,GAAA,EACJ,IAAA,EACA,IAAA,EACA,SAAA,EACA,OAAA,EAAA,GACE;IAEJ,IAAI,CAAC,SACG,MAAA,IAAI,MAAM,qBAAqB;IAEvC,IAAI,CAAC,MACG,MAAA,IAAI,MAAM,kBAAkB;IAEpC,IAAI,CAAC,KACG,MAAA,IAAI,MAAM,gBAAgB;IAElC,IAAI,YAAY,OAAO,QAAQ,QAAA,CAAS,GAAG,GACnC,MAAA,IAAI,MAAM,mCAAmC;IAGrD,MAAM,YAAY,eAAe,YAAY,KAAA,IAAY,YACnD,OAAO,UAAU,YAAY,KAAA,IAAY,OACzC,KAAK,eAAe,GAAG,GACvB,kBAAkB,MAAM,OAAA,CAAQ,IAAI,IACtCA,SAAoB,qBAAqB,IAAI,CAAC,IAC9C,MAIE,eAAe,IAAI,gBAAgB;QACvC;QACA;QACA;QACA,MAAM;IAAA,CACP;IACG,IAAA,aACF,aAAa,GAAA,CAAI,aAAa,SAAS,GAErC,QACF,aAAa,GAAA,CAAI,QAAQ,IAAI,GAE3B,aACF,aAAa,GAAA,CAAI,aAAa,SAAS,GAErC,WACF,aAAa,GAAA,CAAI,WAAW,OAAO,GAEjC,cAAc,GAAG,GACN,aAAA,GAAA,CAAI,eAAe,WAAW;SAAA,IAClC,YAAY,GAAG,GAAG;QACrB,MAAA,YAAY,iBAAiB,GAAG;QACzB,aAAA,GAAA,CAAI,eAAe,SAAS;IAAA;IAG3C,MAAM,WAAW;QAAC,YAAY,MAAM,KAAK,OAAO;KAAA;IAC5C,aACF,SAAS,IAAA,CAAK,SAAS;IAEzB,MAAM,eAAe;QACnB;QACA,CAAA,GAAA,EAAM,EAAE,EAAA;QACR,CAAA,KAAA,EAAQ,IAAI,EAAA;QACZ,CAAA,KAAA,EAAQ,mBAAmB,eAAe,CAAC,EAAA;KAC7C;IACI,OAAA,QACF,aAAa,IAAA,CAAK,CAAA,KAAA,EAAQ,IAAI,EAAE,GAElC,SAAS,IAAA,CAAK,UAAU,QAAQ,GAAG,aAAa,IAAA,CAAK,GAAG,CAAC,CAAA,CAAA,EAAI,YAAY,EAAE,GACpE,SAAS,IAAA,CAAK,GAAG;AAC1B;AC3BO,SAAS,uBAAuB,SAAA,EAAuC;IAC5E,IAAI,UAAyB,OAAO,aAAc,WAAW,YAAY,UAAU,OAAA;IAInF,OAHI,YAAY,OAAA,CACd,UAAU,QAAQ,OAAA,CAAQ,OAAO,EAAE,CAAA,GAEjC,OAAO,aAAc,WAChB;QAAC;IAAA,IAEH;QAAC,GAAG,SAAA;QAAW;IAAO;AAC/B;AC5DO,MAAM,gBAA+B,CAAC,EAAC,UAAA,EAAY,UAAA,EAAY,KAAA,EAAA,KAAW;IAE/E,IAAI,YAAY,KAAK,KAAK,WAAW,KAAK,GACjC,OAAA,CAAA;IAGH,MAAA,UAAU,WAAW,EAAA,CAAG,CAAA,CAAE;IA2BhC,OAzBI,CAAA,CAAA,WAAW,EAAA,CAAG,CAAA,CAAE,MAAM,UAAU,YAAY,aAK5C,OAAO,WAAY,YAAA,CAAa,QAAQ,UAAA,CAAW,GAAG,KAAK,QAAQ,QAAA,CAAS,IAAI,CAAA,KAMlF,WAAW,IAAA,CACT,CAAC,OAAS,SAAS,UAAU,SAAS,cAAc,SAAS,eAAe,SAAS,UAQrF,YAAY,UAAU,KAAK,YAAY,UAAU,KAKjD,OAAO,WAAY,YAAY,SAAS,GAAA,CAAI,OAAO,CAAA;AAKzD,GAEM,WAAA,aAAA,GAAA,IAAe,IAAI;IACvB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,SAAS,YAAY,UAAA,EAAoB;IAChC,OAAA,qBAAqB,IAAA,CAAK,UAAU,IAAI,CAAA,CAAQ,KAAK,KAAA,CAAM,UAAU,IAAK,CAAA;AACnF;AAEA,SAAS,WAAW,GAAA,EAAa;IAC3B,IAAA;QACF,IAAI,IAAI,KAAK,IAAI,UAAA,CAAW,GAAG,IAAI,qBAAqB,KAAA,CAAS;IAAA,EAAA,OAC3D;QACC,OAAA,CAAA;IAAA;IAEF,OAAA,CAAA;AACT;AAEA,SAAS,YAAY,IAAA,EAA2C;IACvD,OAAA,KAAK,IAAA,CAAK,CAAC,UAAY,OAAO,WAAY,YAAY,QAAQ,KAAA,CAAM,OAAO,MAAM,IAAI;AAC9F;ACrFA,MAAM,kBAAkB;AAQR,SAAA,qBACd,MAAA,EACA,eAAA,EACA,MAAA,EACQ;IACR,MAAM,EAAC,MAAA,EAAQ,MAAA,EAAQ,OAAA,CAAW,CAAA,GAAA;IAClC,IAAI,CAAC,SAAS;QACZ,MAAM,MAAM;QACZ,MAAA,QAAQ,QAAQ,CAAA,kBAAA,EAAqB,GAAG,EAAA,EAAI;YAAC;YAAQ;YAAiB;QAAA,CAAO,GACvE,IAAI,UAAU,GAAG;IAAA;IAGzB,IAAI,CAAC,iBACH,OAAA,QAAQ,QAAQ,mEAAmE;QACjF;QACA;QACA;IACD,CAAA,GACM;IAGL,IAAA,CAAC,OAAO,SAAA,EAAW;QACrB,MAAM,MAAM;QACZ,MAAA,QAAQ,QAAQ,CAAA,kBAAA,EAAqB,GAAG,EAAA,EAAI;YAAC;YAAQ;YAAiB;QAAA,CAAO,GACvE,IAAI,UAAU,GAAG;IAAA;IAGzB,MAAM,SAAyF;QAC7F,SAAS,CAAC,CAAA;QACV,SAAS,CAAA,CAAA;IAAA,GAGL,kBAAkB,iBACtB,QACA,iBACA,CAAC,EAAC,UAAA,EAAY,cAAA,EAAgB,UAAA,EAAY,KAAA,EAAA,KAAW;QAGhD,IAAA,CAAA,OAAO,UAAW,aACf,OAAO;YAAC;YAAY;YAAY;YAAe;YAAgB;QAAA,CAAM,IACrE,cAAc;YAAC;YAAY;YAA2C;QAAM,CAAA,CAAA,MAAO,CAAA,GAEnF,OAAA,UACF,OAAO,OAAA,CAAQ,IAAA,CAAK;YAClB,MAAM,qBAAqB,UAAU;YACrC,OAAO,GAAG,MAAM,KAAA,CAAM,GAAG,eAAe,CAAC,GACvC,MAAM,MAAA,GAAS,kBAAkB,QAAQ,EAC3C,EAAA;YACA,QAAQ,MAAM,MAAA;QACf,CAAA,GAEI;QAGL,UACF,OAAO,OAAA,CAAQ,IAAA,CAAK;YAClB,MAAM,qBAAqB,UAAU;YACrC,OAAO,GAAG,MAAM,KAAA,CAAM,GAAG,eAAe,CAAC,GAAG,MAAM,MAAA,GAAS,kBAAkB,QAAQ,EAAE,EAAA;YACvF,QAAQ,MAAM,MAAA;QAAA,CACf;QAGH,MAAM,EAAC,OAAA,EAAS,SAAA,EAAW,IAAA,CAAQ,CAAA,GAAA,uBACjC,OAAO,OAAO,SAAA,IAAc,aACxB,OAAO,SAAA,CAAU,cAAc,IAC/B,OAAO,SAAA;QAET,IAAA,CAAC,QAAgB,CAAA,OAAA;QACf,MAAA,EAAC,KAAK,EAAA,EAAI,OAAO,IAAA,EAAM,YAAY,SAAA,EAAW,UAAU,OAAA,CAAA,CAAA,GAAW;QAElE,OAAAC,qLAAAA,EACL,OACA;YACE,QAAQ;YACR,MAAM,cAAc;gBAClB;gBACA;gBACA;gBACA;gBACA;gBACA,MAAM;gBACN,GAAI,CAAC,OAAO,6BAAA,IAAiC;oBAAC;oBAAS;gBAAS,CAAA;YACjE,CAAA;QACH,GAAA,8DAAA;QAEA,CAAA;IACF;IAIJ,IAAI,QAAQ;QACV,MAAM,aAAa,OAAO,OAAA,CAAQ,MAAA,EAC5B,aAAa,OAAO,OAAA,CAAQ,MAAA;QAC9B,IAAA,CAAA,cAAc,UAAA,KAAA,CAAA,CACd,QAAQ,kBAAkB,OAAO,GAAA,IAAO,mDAAmD,GAC7F,OAAO,GAAA,GACL,CAAA,iCAAA,EAAoC,OAAO,OAAA,CAAQ,MAAM,CAAA,WAAA,EAAc,OAAO,OAAA,CAAQ,MAAM,EAAA,CAAA,GAG5F,OAAO,OAAA,CAAQ,MAAA,GAAS,KAAA,CAC1B,QAAQ,MAAM,0CAA0C,GAAA,CACtD,QAAQ,SAAS,OAAO,GAAA,IAAO,OAAO,OAAO,CAAA,GAE7C,OAAO,OAAA,CAAQ,MAAA,GAAS,GAAG;YACvB,MAAA,UAAA,aAAA,GAAA,IAAc,IAAY;YACrB,KAAA,MAAA,EAAC,IAAA,EAAA,IAAS,OAAO,OAAA,CAClB,QAAA,GAAA,CAAI,KAAK,OAAA,CAAQ,cAAc,GAAG,EAAE,OAAA,CAAQ,YAAY,IAAI,CAAC;YAEvE,QAAQ,MAAM,2CAA2C,CAAC;mBAAG,QAAQ,MAAA,CAAA,CAAQ;aAAC;QAAA;QAG5E,CAAA,cAAc,UAAA,KAChB,QAAQ,WAAW;IAAA;IAIhB,OAAA;AACT;AAEA,SAAS,qBAAqB,IAAA,EAA0C;IAC/D,OAAAC,SAAmB,qBAAqB,IAAI,CAAC;AACtD", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "debugId": null}}]}