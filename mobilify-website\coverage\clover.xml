<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1751693795738" clover="3.2.0">
  <project timestamp="1751693795738" name="All files">
    <metrics statements="1129" coveredstatements="69" conditionals="594" coveredconditionals="37" methods="274" coveredmethods="8" elements="1997" coveredelements="114" complexity="0" loc="1129" ncloc="1129" packages="17" files="65" classes="65"/>
    <package name="analytics">
      <metrics statements="82" coveredstatements="0" conditionals="57" coveredconditionals="0" methods="28" coveredmethods="0"/>
      <file name="CrispChat.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\analytics\CrispChat.tsx">
        <metrics statements="55" coveredstatements="0" conditionals="50" coveredconditionals="0" methods="17" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="36" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="48" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="61" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="70" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="79" count="0" type="stmt"/>
        <line num="89" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="90" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="105" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="106" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="135" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="136" count="0" type="stmt"/>
        <line num="142" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="143" count="0" type="stmt"/>
        <line num="149" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="150" count="0" type="stmt"/>
        <line num="156" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="157" count="0" type="stmt"/>
        <line num="163" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="164" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="165" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="166" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="167" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="173" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="174" count="0" type="stmt"/>
        <line num="180" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="181" count="0" type="stmt"/>
        <line num="187" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="188" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="195" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="196" count="0" type="stmt"/>
      </file>
      <file name="GoogleAnalytics.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\analytics\GoogleAnalytics.tsx">
        <metrics statements="6" coveredstatements="0" conditionals="3" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="19" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="20" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
      </file>
      <file name="WebVitals.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\analytics\WebVitals.tsx">
        <metrics statements="18" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="10" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="9" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="10" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\analytics\index.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="app">
      <metrics statements="35" coveredstatements="0" conditionals="8" coveredconditionals="0" methods="22" coveredmethods="0"/>
      <file name="layout.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\app\layout.tsx">
        <metrics statements="12" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
      </file>
      <file name="page.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\app\page.tsx">
        <metrics statements="14" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="18" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
      </file>
      <file name="robots.ts" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\app\robots.ts">
        <metrics statements="3" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="6" count="0" type="stmt"/>
      </file>
      <file name="sitemap.ts" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\app\sitemap.ts">
        <metrics statements="6" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="11" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
      </file>
    </package>
    <package name="app.about">
      <metrics statements="7" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="page.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\app\about\page.tsx">
        <metrics statements="7" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
      </file>
    </package>
    <package name="app.api.newsletter">
      <metrics statements="36" coveredstatements="0" conditionals="11" coveredconditionals="0" methods="4" coveredmethods="0"/>
      <file name="route.ts" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\app\api\newsletter\route.ts">
        <metrics statements="36" coveredstatements="0" conditionals="11" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="25" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="26" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="36" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="47" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="78" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="80" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="91" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="92" count="0" type="stmt"/>
        <line num="98" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="99" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
      </file>
    </package>
    <package name="app.blog">
      <metrics statements="21" coveredstatements="0" conditionals="18" coveredconditionals="0" methods="8" coveredmethods="0"/>
      <file name="page.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\app\blog\page.tsx">
        <metrics statements="21" coveredstatements="0" conditionals="18" coveredconditionals="0" methods="8" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="92" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="94" count="0" type="stmt"/>
        <line num="98" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="99" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
      </file>
    </package>
    <package name="app.blog.[slug]">
      <metrics statements="23" coveredstatements="0" conditionals="8" coveredconditionals="0" methods="5" coveredmethods="0"/>
      <file name="page.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\app\blog\[slug]\page.tsx">
        <metrics statements="23" coveredstatements="0" conditionals="8" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="74" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="75" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
      </file>
    </package>
    <package name="app.faq">
      <metrics statements="56" coveredstatements="0" conditionals="26" coveredconditionals="0" methods="23" coveredmethods="0"/>
      <file name="FAQClient.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\app\faq\FAQClient.tsx">
        <metrics statements="49" coveredstatements="0" conditionals="26" coveredconditionals="0" methods="22" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="16" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="33" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="35" count="0" type="stmt"/>
        <line num="39" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="41" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="51" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="61" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="78" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="79" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
      </file>
      <file name="page.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\app\faq\page.tsx">
        <metrics statements="7" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
      </file>
    </package>
    <package name="app.services">
      <metrics statements="6" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="page.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\app\services\page.tsx">
        <metrics statements="6" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
      </file>
    </package>
    <package name="components">
      <metrics statements="267" coveredstatements="35" conditionals="201" coveredconditionals="22" methods="54" coveredmethods="5"/>
      <file name="AnimationWrapper.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\components\AnimationWrapper.tsx">
        <metrics statements="8" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="11" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="12" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="18" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="25" count="0" type="stmt"/>
      </file>
      <file name="ChatTrigger.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\components\ChatTrigger.tsx">
        <metrics statements="22" coveredstatements="0" conditionals="18" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="37" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="38" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="64" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="72" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="90" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="116" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
      </file>
      <file name="ClientOnly.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\components\ClientOnly.tsx">
        <metrics statements="7" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="10" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="11" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="17" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="24" count="0" type="stmt"/>
      </file>
      <file name="CompanyValues.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\components\CompanyValues.tsx">
        <metrics statements="8" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
      </file>
      <file name="DarkModeToggle.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\components\DarkModeToggle.tsx">
        <metrics statements="19" coveredstatements="0" conditionals="33" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="26" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="37" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="45" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="131" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
      </file>
      <file name="Logo.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\components\Logo.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="1" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="3" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="11" count="0" type="stmt"/>
      </file>
      <file name="Mission.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\components\Mission.tsx">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
      </file>
      <file name="NewsletterSignup.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\components\NewsletterSignup.tsx">
        <metrics statements="36" coveredstatements="35" conditionals="25" coveredconditionals="22" methods="5" coveredmethods="5"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="16" count="156" type="stmt"/>
        <line num="17" count="156" type="stmt"/>
        <line num="18" count="156" type="stmt"/>
        <line num="20" count="156" type="stmt"/>
        <line num="21" count="7" type="stmt"/>
        <line num="22" count="7" type="cond" truecount="0" falsecount="1"/>
        <line num="24" count="7" type="stmt"/>
        <line num="25" count="7" type="stmt"/>
        <line num="27" count="7" type="stmt"/>
        <line num="29" count="7" type="stmt"/>
        <line num="30" count="7" type="stmt"/>
        <line num="32" count="7" type="cond" truecount="3" falsecount="0"/>
        <line num="33" count="4" type="stmt"/>
        <line num="35" count="4" type="stmt"/>
        <line num="36" count="4" type="stmt"/>
        <line num="37" count="4" type="stmt"/>
        <line num="40" count="4" type="cond" truecount="3" falsecount="0"/>
        <line num="41" count="4" type="stmt"/>
        <line num="46" count="4" type="stmt"/>
        <line num="51" count="3" type="stmt"/>
        <line num="62" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="63" count="2" type="stmt"/>
        <line num="64" count="2" type="stmt"/>
        <line num="67" count="2" type="cond" truecount="3" falsecount="0"/>
        <line num="68" count="2" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="77" count="1" type="stmt"/>
        <line num="78" count="1" type="stmt"/>
        <line num="80" count="7" type="stmt"/>
        <line num="109" count="16" type="stmt"/>
        <line num="161" count="109" type="stmt"/>
        <line num="204" count="18" type="stmt"/>
      </file>
      <file name="NoSSR.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\components\NoSSR.tsx">
        <metrics statements="7" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="10" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="11" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="17" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="24" count="0" type="stmt"/>
      </file>
      <file name="OrganizationSchema.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\components\OrganizationSchema.tsx">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
      </file>
      <file name="PortableText.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\components\PortableText.tsx">
        <metrics statements="66" coveredstatements="0" conditionals="61" coveredconditionals="0" methods="8" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="47" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="69" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="74" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="75" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="86" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="93" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="124" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="126" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="156" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="157" count="0" type="stmt"/>
        <line num="163" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="164" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="184" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="185" count="0" type="stmt"/>
        <line num="186" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="187" count="0" type="stmt"/>
        <line num="189" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="190" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="196" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="197" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="205" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="206" count="0" type="stmt"/>
        <line num="212" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="213" count="0" type="stmt"/>
        <line num="214" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="215" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
      </file>
      <file name="PricingTable.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\components\PricingTable.tsx">
        <metrics statements="16" coveredstatements="0" conditionals="15" coveredconditionals="0" methods="7" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="120" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="190" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="211" count="0" type="stmt"/>
        <line num="212" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="213" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
      </file>
      <file name="ServicesFAQ.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\components\ServicesFAQ.tsx">
        <metrics statements="12" coveredstatements="0" conditionals="5" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="70" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
      </file>
      <file name="SimpleDarkModeToggle.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\components\SimpleDarkModeToggle.tsx">
        <metrics statements="11" coveredstatements="0" conditionals="8" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="21" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
      </file>
      <file name="SimpleFloatingChat.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\components\SimpleFloatingChat.tsx">
        <metrics statements="10" coveredstatements="0" conditionals="7" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="9" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="10" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="17" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="18" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
      </file>
      <file name="SimpleHeaderChat.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\components\SimpleHeaderChat.tsx">
        <metrics statements="10" coveredstatements="0" conditionals="7" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="9" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="10" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="17" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="18" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
      </file>
      <file name="StructuredData.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\components\StructuredData.tsx">
        <metrics statements="17" coveredstatements="0" conditionals="13" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="14" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="89" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="100" count="0" type="stmt"/>
        <line num="103" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="112" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="143" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="144" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
      </file>
      <file name="TeamProfiles.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\components\TeamProfiles.tsx">
        <metrics statements="7" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
      </file>
    </package>
    <package name="components.layout">
      <metrics statements="98" coveredstatements="0" conditionals="26" coveredconditionals="0" methods="27" coveredmethods="0"/>
      <file name="Footer.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\components\layout\Footer.tsx">
        <metrics statements="9" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
      </file>
      <file name="FooterNav.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\components\layout\FooterNav.tsx">
        <metrics statements="13" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="6" coveredmethods="0"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="67" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
      </file>
      <file name="FooterNewsletter.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\components\layout\FooterNewsletter.tsx">
        <metrics statements="28" coveredstatements="0" conditionals="13" coveredconditionals="0" methods="12" coveredmethods="0"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="27" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="28" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="45" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
      </file>
      <file name="Header.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\components\layout\Header.tsx">
        <metrics statements="17" coveredstatements="0" conditionals="1" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
      </file>
      <file name="MobileMenu.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\components\layout\MobileMenu.tsx">
        <metrics statements="9" coveredstatements="0" conditionals="1" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
      </file>
      <file name="Navigation.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\components\layout\Navigation.tsx">
        <metrics statements="16" coveredstatements="0" conditionals="9" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="48" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="53" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="60" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\components\layout\index.ts">
        <metrics statements="6" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
      </file>
    </package>
    <package name="components.sections">
      <metrics statements="153" coveredstatements="0" conditionals="79" coveredconditionals="0" methods="33" coveredmethods="0"/>
      <file name="AboutSnippet.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\components\sections\AboutSnippet.tsx">
        <metrics statements="7" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
      </file>
      <file name="Contact.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\components\sections\Contact.tsx">
        <metrics statements="9" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
      </file>
      <file name="ContactForm.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\components\sections\ContactForm.tsx">
        <metrics statements="18" coveredstatements="0" conditionals="23" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="81" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="263" count="0" type="stmt"/>
      </file>
      <file name="DemoInput.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\components\sections\DemoInput.tsx">
        <metrics statements="25" coveredstatements="0" conditionals="32" coveredconditionals="0" methods="6" coveredmethods="0"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="61" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="65" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="67" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="78" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="92" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
      </file>
      <file name="DemoPreview.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\components\sections\DemoPreview.tsx">
        <metrics statements="16" coveredstatements="0" conditionals="7" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="60" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="158" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
      </file>
      <file name="DemoTabs.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\components\sections\DemoTabs.tsx">
        <metrics statements="8" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
      </file>
      <file name="Hero.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\components\sections\Hero.tsx">
        <metrics statements="15" coveredstatements="0" conditionals="1" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
      </file>
      <file name="InteractiveDemo.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\components\sections\InteractiveDemo.tsx">
        <metrics statements="25" coveredstatements="0" conditionals="5" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
      </file>
      <file name="Process.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\components\sections\Process.tsx">
        <metrics statements="8" coveredstatements="0" conditionals="1" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
      </file>
      <file name="ServicesOverview.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\components\sections\ServicesOverview.tsx">
        <metrics statements="15" coveredstatements="0" conditionals="6" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="31" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\components\sections\index.ts">
        <metrics statements="7" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
      </file>
    </package>
    <package name="components.ui">
      <metrics statements="36" coveredstatements="30" conditionals="16" coveredconditionals="15" methods="3" coveredmethods="2"/>
      <file name="Button.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\components\ui\Button.tsx">
        <metrics statements="9" coveredstatements="9" conditionals="7" coveredconditionals="7" methods="1" coveredmethods="1"/>
        <line num="38" count="2" type="stmt"/>
        <line num="39" count="2" type="stmt"/>
        <line num="52" count="2" type="stmt"/>
        <line num="54" count="186" type="stmt"/>
        <line num="56" count="186" type="stmt"/>
        <line num="62" count="186" type="stmt"/>
        <line num="68" count="186" type="stmt"/>
        <line num="114" count="2" type="stmt"/>
        <line num="116" count="186" type="stmt"/>
      </file>
      <file name="Card.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\components\ui\Card.tsx">
        <metrics statements="14" coveredstatements="8" conditionals="1" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="39" count="1" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="51" count="1" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="62" count="1" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="73" count="1" type="stmt"/>
        <line num="75" count="1" type="stmt"/>
      </file>
      <file name="Input.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\components\ui\Input.tsx">
        <metrics statements="10" coveredstatements="10" conditionals="8" coveredconditionals="8" methods="1" coveredmethods="1"/>
        <line num="45" count="2" type="stmt"/>
        <line num="46" count="2" type="stmt"/>
        <line num="59" count="2" type="stmt"/>
        <line num="61" count="190" type="stmt"/>
        <line num="63" count="190" type="stmt"/>
        <line num="69" count="190" type="stmt"/>
        <line num="75" count="190" type="cond" truecount="2" falsecount="0"/>
        <line num="76" count="190" type="cond" truecount="2" falsecount="0"/>
        <line num="105" count="2" type="stmt"/>
        <line num="107" count="190" type="stmt"/>
      </file>
      <file name="index.ts" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\components\ui\index.ts">
        <metrics statements="3" coveredstatements="3" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="156" type="stmt"/>
        <line num="4" count="156" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
      </file>
    </package>
    <package name="config">
      <metrics statements="10" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="site.ts" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\config\site.ts">
        <metrics statements="10" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="9" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
      </file>
    </package>
    <package name="contexts">
      <metrics statements="50" coveredstatements="0" conditionals="21" coveredconditionals="0" methods="15" coveredmethods="0"/>
      <file name="ThemeContext.tsx" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\contexts\ThemeContext.tsx">
        <metrics statements="50" coveredstatements="0" conditionals="21" coveredconditionals="0" methods="15" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="18" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="45" count="0" type="stmt"/>
        <line num="47" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="48" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="57" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="58" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="69" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="75" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
      </file>
    </package>
    <package name="hooks">
      <metrics statements="151" coveredstatements="0" conditionals="79" coveredconditionals="0" methods="32" coveredmethods="0"/>
      <file name="index.ts" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\hooks\index.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="8" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
      </file>
      <file name="useAnalytics.ts" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\hooks\useAnalytics.ts">
        <metrics statements="32" coveredstatements="0" conditionals="11" coveredconditionals="0" methods="9" coveredmethods="0"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="62" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="63" count="0" type="stmt"/>
        <line num="67" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="68" count="0" type="stmt"/>
        <line num="75" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
      </file>
      <file name="useContactForm.ts" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\hooks\useContactForm.ts">
        <metrics statements="76" coveredstatements="0" conditionals="24" coveredconditionals="0" methods="16" coveredmethods="0"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="62" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="64" count="0" type="stmt"/>
        <line num="67" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="70" count="0" type="stmt"/>
        <line num="74" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="75" count="0" type="stmt"/>
        <line num="78" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="79" count="0" type="stmt"/>
        <line num="82" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="83" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="84" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="85" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="99" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="156" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="157" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="165" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="166" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="202" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="203" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="212" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="215" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
      </file>
      <file name="useSiteSettings.ts" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\hooks\useSiteSettings.ts">
        <metrics statements="40" coveredstatements="0" conditionals="44" coveredconditionals="0" methods="7" coveredmethods="0"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="112" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="118" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="145" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="147" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
      </file>
    </package>
    <package name="lib">
      <metrics statements="95" coveredstatements="4" conditionals="42" coveredconditionals="0" methods="18" coveredmethods="1"/>
      <file name="metadata.ts" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\lib\metadata.ts">
        <metrics statements="30" coveredstatements="0" conditionals="23" coveredconditionals="0" methods="7" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="38" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="39" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="52" count="0" type="stmt"/>
        <line num="56" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="57" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="74" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="76" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="84" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
      </file>
      <file name="sanity.ts" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\lib\sanity.ts">
        <metrics statements="61" coveredstatements="0" conditionals="19" coveredconditionals="0" methods="10" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="11" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="20" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="232" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="233" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="240" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="241" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="248" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="249" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="256" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="257" count="0" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="260" count="0" type="stmt"/>
        <line num="263" count="0" type="stmt"/>
        <line num="264" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="265" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="268" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="272" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="273" count="0" type="stmt"/>
        <line num="274" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="279" count="0" type="stmt"/>
        <line num="280" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="281" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
        <line num="287" count="0" type="stmt"/>
        <line num="288" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="289" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="292" count="0" type="stmt"/>
        <line num="293" count="0" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
        <line num="296" count="0" type="stmt"/>
      </file>
      <file name="utils.ts" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\lib\utils.ts">
        <metrics statements="4" coveredstatements="4" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="1"/>
        <line num="1" count="3" type="stmt"/>
        <line num="2" count="3" type="stmt"/>
        <line num="4" count="376" type="stmt"/>
        <line num="5" count="376" type="stmt"/>
      </file>
    </package>
    <package name="types">
      <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="index.ts" path="C:\Users\<USER>\OneDrive\Desktop\My projects\Mobilify\website\gemini\mobilify-website\src\types\index.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
