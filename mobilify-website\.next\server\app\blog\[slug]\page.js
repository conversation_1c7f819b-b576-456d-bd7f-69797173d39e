(()=>{var e={};e.id=953,e.ids=[953],e.modules={163:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return i}});let i=r(71042).unstable_rethrow;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1322:(e,t)=>{"use strict";function r(e){let{widthInt:t,heightInt:r,blurWidth:i,blurHeight:s,blurDataURL:n,objectFit:l}=e,o=i?40*i:t,a=s?40*s:r,d=o&&a?"viewBox='0 0 "+o+" "+a+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+d+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(d?"none":"contain"===l?"xMidYMid":"cover"===l?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+n+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4536:(e,t,r)=>{let{createProxy:i}=r(39844);e.exports=i("C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\node_modules\\next\\dist\\client\\app-dir\\link.js")},9131:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return a}}),r(21122);let i=r(1322),s=r(27894),n=["-moz-initial","fill","none","scale-down",void 0];function l(e){return void 0!==e.default}function o(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function a(e,t){var r,a;let d,c,u,{src:p,sizes:m,unoptimized:f=!1,priority:h=!1,loading:b,className:g,quality:x,width:y,height:v,fill:_=!1,style:j,overrideSrc:w,onLoad:R,onLoadingComplete:O,placeholder:E="empty",blurDataURL:P,fetchPriority:N,decoding:k="async",layout:M,objectFit:C,objectPosition:D,lazyBoundary:A,lazyRoot:S,...T}=e,{imgConf:I,showAltText:q,blurComplete:z,defaultLoader:F}=t,L=I||s.imageConfigDefault;if("allSizes"in L)d=L;else{let e=[...L.deviceSizes,...L.imageSizes].sort((e,t)=>e-t),t=L.deviceSizes.sort((e,t)=>e-t),i=null==(r=L.qualities)?void 0:r.sort((e,t)=>e-t);d={...L,allSizes:e,deviceSizes:t,qualities:i}}if(void 0===F)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let U=T.loader||F;delete T.loader,delete T.srcSet;let B="__next_img_default"in U;if(B){if("custom"===d.loader)throw Object.defineProperty(Error('Image with src "'+p+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=U;U=t=>{let{config:r,...i}=t;return e(i)}}if(M){"fill"===M&&(_=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[M];e&&(j={...j,...e});let t={responsive:"100vw",fill:"100vw"}[M];t&&!m&&(m=t)}let $="",G=o(y),W=o(v);if((a=p)&&"object"==typeof a&&(l(a)||void 0!==a.src)){let e=l(p)?p.default:p;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(c=e.blurWidth,u=e.blurHeight,P=P||e.blurDataURL,$=e.src,!_)if(G||W){if(G&&!W){let t=G/e.width;W=Math.round(e.height*t)}else if(!G&&W){let t=W/e.height;G=Math.round(e.width*t)}}else G=e.width,W=e.height}let X=!h&&("lazy"===b||void 0===b);(!(p="string"==typeof p?p:$)||p.startsWith("data:")||p.startsWith("blob:"))&&(f=!0,X=!1),d.unoptimized&&(f=!0),B&&!d.dangerouslyAllowSVG&&p.split("?",1)[0].endsWith(".svg")&&(f=!0);let H=o(x),Z=Object.assign(_?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:C,objectPosition:D}:{},q?{}:{color:"transparent"},j),K=z||"empty"===E?null:"blur"===E?'url("data:image/svg+xml;charset=utf-8,'+(0,i.getImageBlurSvg)({widthInt:G,heightInt:W,blurWidth:c,blurHeight:u,blurDataURL:P||"",objectFit:Z.objectFit})+'")':'url("'+E+'")',V=n.includes(Z.objectFit)?"fill"===Z.objectFit?"100% 100%":"cover":Z.objectFit,J=K?{backgroundSize:V,backgroundPosition:Z.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:K}:{},Y=function(e){let{config:t,src:r,unoptimized:i,width:s,quality:n,sizes:l,loader:o}=e;if(i)return{src:r,srcSet:void 0,sizes:void 0};let{widths:a,kind:d}=function(e,t,r){let{deviceSizes:i,allSizes:s}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let i;i=e.exec(r);)t.push(parseInt(i[2]));if(t.length){let e=.01*Math.min(...t);return{widths:s.filter(t=>t>=i[0]*e),kind:"w"}}return{widths:s,kind:"w"}}return"number"!=typeof t?{widths:i,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>s.find(t=>t>=e)||s[s.length-1]))],kind:"x"}}(t,s,l),c=a.length-1;return{sizes:l||"w"!==d?l:"100vw",srcSet:a.map((e,i)=>o({config:t,src:r,quality:n,width:e})+" "+("w"===d?e:i+1)+d).join(", "),src:o({config:t,src:r,quality:n,width:a[c]})}}({config:d,src:p,unoptimized:f,width:G,quality:H,sizes:m,loader:U});return{props:{...T,loading:X?"lazy":b,fetchPriority:N,width:G,height:W,decoding:k,className:g,style:{...Z,...J},sizes:Y.sizes,srcSet:Y.srcSet,src:w||Y.src},meta:{unoptimized:f,priority:h,placeholder:E,fill:_}}}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21122:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},21820:e=>{"use strict";e.exports=require("os")},26373:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var i=r(61120);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),l=e=>{let t=n(e);return t.charAt(0).toUpperCase()+t.slice(1)},o=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),a=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,i.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:s,className:n="",children:l,iconNode:c,...u},p)=>(0,i.createElement)("svg",{ref:p,...d,width:t,height:t,stroke:e,strokeWidth:s?24*Number(r)/Number(t):r,className:o("lucide",n),...!l&&!a(u)&&{"aria-hidden":"true"},...u},[...c.map(([e,t])=>(0,i.createElement)(e,t)),...Array.isArray(l)?l:[l]])),u=(e,t)=>{let r=(0,i.forwardRef)(({className:r,...n},a)=>(0,i.createElement)(c,{ref:a,iconNode:t,className:o(`lucide-${s(l(e))}`,`lucide-${e}`,r),...n}));return r.displayName=l(e),r}},27894:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return i}});let r=["default","imgix","cloudinary","akamai","custom"],i={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32091:(e,t)=>{"use strict";function r(e){var t;let{config:r,src:i,width:s,quality:n}=e,l=n||(null==(t=r.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return r.path+"?url="+encodeURIComponent(i)+"&w="+s+"&q="+l+(i.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i}}),r.__next_img_default=!0;let i=r},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},36440:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(26373).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},40918:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(26373).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},44012:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var i=r(65239),s=r(48088),n=r(88170),l=r.n(n),o=r(30893),a={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>o[e]);r.d(t,a);let d={children:["",{children:["blog",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,75861)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\blog\\[slug]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,14974)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\blog\\[slug]\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new i.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/blog/[slug]/page",pathname:"/blog/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},48976:(e,t,r)=>{"use strict";function i(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"forbidden",{enumerable:!0,get:function(){return i}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},49603:(e,t,r)=>{let{createProxy:i}=r(39844);e.exports=i("C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\node_modules\\next\\dist\\client\\image-component.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58096:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,85814,23)),Promise.resolve().then(r.t.bind(r,46533,23)),Promise.resolve().then(r.bind(r,56857)),Promise.resolve().then(r.bind(r,99537))},62765:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"notFound",{enumerable:!0,get:function(){return s}});let i=""+r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function s(){let e=Object.defineProperty(Error(i),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw e.digest=i,e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68926:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});let i=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\My projects\\\\Mobilify\\\\website\\\\gemini\\\\mobilify-website\\\\src\\\\components\\\\layout\\\\Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\Header.tsx","default")},70099:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return a},getImageProps:function(){return o}});let i=r(72639),s=r(9131),n=r(49603),l=i._(r(32091));function o(e){let{props:t}=(0,s.getImgProps)(e,{defaultLoader:l.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!0,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let a=n.Image},70899:(e,t,r)=>{"use strict";function i(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unauthorized",{enumerable:!0,get:function(){return i}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},71042:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return function e(t){if((0,l.isNextRouterError)(t)||(0,n.isBailoutToCSRError)(t)||(0,a.isDynamicServerError)(t)||(0,o.isDynamicPostpone)(t)||(0,s.isPostpone)(t)||(0,i.isHangingPromiseRejectionError)(t))throw t;t instanceof Error&&"cause"in t&&e(t.cause)}}});let i=r(68388),s=r(52637),n=r(51846),l=r(31162),o=r(84971),a=r(98479);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},74075:e=>{"use strict";e.exports=require("zlib")},75861:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g,generateMetadata:()=>b});var i=r(37413);r(61120);var s=r(4536),n=r.n(s),l=r(70099),o=r.n(l),a=r(97576);let d=(0,r(26373).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);var c=r(88971),u=r(40918),p=r(36440),m=r(68926),f=r(84712);let h=({content:e,className:t=""})=>{let r=(e,t)=>{if("image"===e._type)return(0,i.jsxs)("div",{className:"my-8",children:[(0,i.jsx)("div",{className:"relative w-full h-64 md:h-96 rounded-lg overflow-hidden bg-gray-200 dark:bg-gray-700 flex items-center justify-center",children:(0,i.jsx)("span",{className:"text-gray-500 dark:text-gray-400",children:e.alt||"Blog image"})}),e.caption&&(0,i.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 text-center mt-2 italic",children:e.caption})]},e._key||t);if(!e.children)return null;let r=e.children.map((t,r)=>{let s=t.text;if(!t.marks||0===t.marks.length)return s;let l=(0,i.jsx)("span",{children:s},r);return t.marks.forEach(t=>{if("strong"===t)l=(0,i.jsx)("strong",{className:"font-semibold",children:l},`${r}-strong`);else if("em"===t)l=(0,i.jsx)("em",{className:"italic",children:l},`${r}-em`);else if("code"===t)l=(0,i.jsx)("code",{className:"bg-gray-100 px-1 py-0.5 rounded text-sm font-mono",children:l},`${r}-code`);else{let s=e.markDefs?.find(e=>e._key===t);s&&"link"===s._type&&s.href&&(l=s.href.startsWith("http")?(0,i.jsx)("a",{href:s.href,target:"_blank",rel:"noopener noreferrer",className:"text-electric-blue hover:underline",children:l},`${r}-link`):(0,i.jsx)(n(),{href:s.href,className:"text-electric-blue hover:underline",children:l},`${r}-link`))}}),l});switch(e.style){case"h1":return(0,i.jsx)("h1",{className:"text-3xl md:text-4xl font-bold text-dark-charcoal mb-6 mt-8",children:r},e._key||t);case"h2":return(0,i.jsx)("h2",{className:"text-2xl md:text-3xl font-bold text-dark-charcoal mb-4 mt-8",children:r},e._key||t);case"h3":return(0,i.jsx)("h3",{className:"text-xl md:text-2xl font-semibold text-dark-charcoal mb-3 mt-6",children:r},e._key||t);case"h4":return(0,i.jsx)("h4",{className:"text-lg md:text-xl font-semibold text-dark-charcoal mb-2 mt-4",children:r},e._key||t);case"blockquote":return(0,i.jsx)("blockquote",{className:"border-l-4 border-electric-blue pl-4 py-2 my-6 italic text-gray-700 bg-gray-50 rounded-r",children:r},e._key||t);default:if("bullet"===e.listItem||"number"===e.listItem)return(0,i.jsx)("li",{className:"mb-2",children:r},e._key||t);return(0,i.jsx)("p",{className:"mb-4 leading-relaxed text-gray-700",children:r},e._key||t)}},s=[],l=[],o=null;return e.forEach(e=>{if("block"===e._type&&e.listItem){let t=e.listItem;t===o?l.push(e):(l.length>0&&s.push([...l]),l=[e],o=t)}else l.length>0&&(s.push([...l]),l=[],o=null),s.push(e)}),l.length>0&&s.push([...l]),(0,i.jsx)("div",{className:`prose prose-lg max-w-none ${t}`,children:s.map((e,t)=>{if(Array.isArray(e)){let s=e[0].listItem;return(0,i.jsx)("bullet"===s?"ul":"ol",{className:"bullet"===s?"list-disc pl-6 mb-4":"list-decimal pl-6 mb-4",children:e.map((e,t)=>r(e,t))},t)}return r(e,t)})})};async function b({params:e}){let{slug:t}=await e;return{title:`${t.replace(/-/g," ")} | Mobilify Blog`,description:"Read our latest insights on mobile app development.",openGraph:{title:`${t.replace(/-/g," ")} | Mobilify Blog`,description:"Read our latest insights on mobile app development.",type:"article"}}}async function g({params:e}){let{slug:t}=await e,r={_id:"1",_type:"post",title:"Sample Blog Post",slug:{current:t},author:"Mobilify Team",mainImage:void 0,categories:[{_id:"mobile-dev",_type:"category",title:"Mobile Development",slug:{current:"mobile-development"},description:"Tips and tutorials for mobile app development"}],publishedAt:"2024-01-15T10:00:00Z",excerpt:"This is a sample blog post to demonstrate the blog functionality.",body:[{_type:"block",_key:"1",children:[{_type:"span",_key:"1",text:"This is a sample blog post. In a real implementation, this content would come from Sanity CMS.",marks:[]}]}],_createdAt:"2024-01-15T10:00:00Z",_updatedAt:"2024-01-15T10:00:00Z"};r||(0,a.notFound)();let s=[],l=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}),b={"@context":"https://schema.org","@type":"Article",headline:r.title,description:r.excerpt,image:{"@type":"ImageObject",url:"/placeholder-image.svg",width:1200,height:630},datePublished:r.publishedAt,dateModified:r._updatedAt,author:{"@type":"Person",name:r.author},publisher:{"@type":"Organization",name:"Mobilify",logo:{"@type":"ImageObject",url:"/logo.svg",width:200,height:200}},mainEntityOfPage:{"@type":"WebPage","@id":`https://mobilify.com/blog/${r.slug.current}`}};return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(b)}}),(0,i.jsxs)("div",{className:"min-h-screen w-full overflow-x-hidden",children:[(0,i.jsx)(m.default,{}),(0,i.jsxs)("main",{className:"pt-16",children:[(0,i.jsx)("section",{className:"py-6 bg-gray-50",children:(0,i.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,i.jsxs)(n(),{href:"/blog",className:"inline-flex items-center text-electric-blue hover:underline font-medium",children:[(0,i.jsx)(d,{className:"mr-2 w-4 h-4"}),"Back to Blog"]})})}),(0,i.jsx)("article",{className:"py-12",children:(0,i.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,i.jsx)("div",{className:"flex flex-wrap gap-2 mb-6",children:r.categories.map(e=>(0,i.jsx)(n(),{href:`/blog?category=${e.slug.current}`,className:"text-sm font-medium text-electric-blue bg-blue-50 px-3 py-1 rounded-full hover:bg-blue-100 transition-colors duration-200",children:e.title},e._id))}),(0,i.jsx)("h1",{className:"text-4xl md:text-5xl font-bold text-dark-charcoal mb-6 leading-tight",children:r.title}),r.excerpt&&(0,i.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:r.excerpt}),(0,i.jsxs)("div",{className:"flex items-center gap-6 text-gray-500 mb-8 pb-8 border-b",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(c.A,{className:"w-5 h-5"}),(0,i.jsx)("span",{className:"font-medium",children:r.author})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(u.A,{className:"w-5 h-5"}),(0,i.jsx)("span",{children:l(r.publishedAt)})]})]}),(0,i.jsx)("div",{className:"relative w-full h-64 md:h-96 rounded-xl overflow-hidden mb-12 bg-gray-200 dark:bg-gray-700 flex items-center justify-center",children:r.mainImage?(0,i.jsx)(o(),{src:r.mainImage,alt:r.title,width:800,height:400,className:"w-full h-full object-cover",priority:!0}):(0,i.jsx)("span",{className:"text-gray-500 dark:text-gray-400",children:"Sample Blog Post Image"})}),(0,i.jsx)("div",{className:"prose prose-lg max-w-none",children:(0,i.jsx)(h,{content:r.body})})]})}),s.length>0&&(0,i.jsx)("section",{className:"py-16 bg-gray-50",children:(0,i.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,i.jsx)("h2",{className:"text-3xl font-bold text-dark-charcoal mb-8 text-center",children:"Related Articles"}),(0,i.jsx)("div",{className:"grid md:grid-cols-3 gap-8",children:s.map(e=>(0,i.jsxs)("article",{className:"bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden",children:[(0,i.jsx)("div",{className:"relative h-48 w-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center",children:e.mainImage?(0,i.jsx)(o(),{src:"string"==typeof e.mainImage?e.mainImage:"/images/placeholders/blog-placeholder.jpg",alt:e.title,width:400,height:192,className:"w-full h-full object-cover"}):(0,i.jsx)("span",{className:"text-gray-500 dark:text-gray-400 text-sm",children:"Related Post Image"})}),(0,i.jsxs)("div",{className:"p-6",children:[(0,i.jsx)("h3",{className:"text-lg font-bold text-dark-charcoal mb-2 line-clamp-2",children:(0,i.jsx)(n(),{href:`/blog/${e.slug.current}`,className:"hover:text-electric-blue transition-colors duration-200",children:e.title})}),e.excerpt&&(0,i.jsx)("p",{className:"text-gray-600 mb-4 line-clamp-3 text-sm",children:e.excerpt}),(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("span",{className:"text-sm text-gray-500",children:l(e.publishedAt)}),(0,i.jsxs)(n(),{href:`/blog/${e.slug.current}`,className:"inline-flex items-center text-electric-blue font-medium hover:underline text-sm",children:["Read More",(0,i.jsx)(p.A,{className:"ml-1 w-3 h-3"})]})]})]})]},e._id))})]})})]}),(0,i.jsx)(f.default,{})]})]})}},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84712:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});let i=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\My projects\\\\Mobilify\\\\website\\\\gemini\\\\mobilify-website\\\\src\\\\components\\\\layout\\\\Footer.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\Footer.tsx","default")},86897:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getRedirectError:function(){return l},getRedirectStatusCodeFromError:function(){return u},getRedirectTypeFromError:function(){return c},getURLFromRedirectError:function(){return d},permanentRedirect:function(){return a},redirect:function(){return o}});let i=r(52836),s=r(49026),n=r(19121).actionAsyncStorage;function l(e,t,r){void 0===r&&(r=i.RedirectStatusCode.TemporaryRedirect);let n=Object.defineProperty(Error(s.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n.digest=s.REDIRECT_ERROR_CODE+";"+t+";"+e+";"+r+";",n}function o(e,t){var r;throw null!=t||(t=(null==n||null==(r=n.getStore())?void 0:r.isAction)?s.RedirectType.push:s.RedirectType.replace),l(e,t,i.RedirectStatusCode.TemporaryRedirect)}function a(e,t){throw void 0===t&&(t=s.RedirectType.replace),l(e,t,i.RedirectStatusCode.PermanentRedirect)}function d(e){return(0,s.isRedirectError)(e)?e.digest.split(";").slice(2,-2).join(";"):null}function c(e){if(!(0,s.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return e.digest.split(";",2)[1]}function u(e){if(!(0,s.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(e.digest.split(";").at(-2))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},88971:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(26373).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},97576:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return c},RedirectType:function(){return s.RedirectType},forbidden:function(){return l.forbidden},notFound:function(){return n.notFound},permanentRedirect:function(){return i.permanentRedirect},redirect:function(){return i.redirect},unauthorized:function(){return o.unauthorized},unstable_rethrow:function(){return a.unstable_rethrow}});let i=r(86897),s=r(49026),n=r(62765),l=r(48976),o=r(70899),a=r(163);class d extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class c extends URLSearchParams{append(){throw new d}delete(){throw new d}set(){throw new d}sort(){throw new d}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},99952:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4536,23)),Promise.resolve().then(r.t.bind(r,49603,23)),Promise.resolve().then(r.bind(r,84712)),Promise.resolve().then(r.bind(r,68926))}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[447,759,476,533,550,449],()=>r(44012));module.exports=i})();