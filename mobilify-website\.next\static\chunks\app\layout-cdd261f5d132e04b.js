(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{347:()=>{},1710:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c",variable:"__variable_e8ce0c"}},2180:(e,t,n)=>{"use strict";n.d(t,{default:()=>r});var i=n(5155);n(2115);let r=()=>(0,i.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify({"@context":"https://schema.org","@type":"Organization",name:"Mobilify",description:"Mobilify converts your existing website or new idea into a high-quality, native mobile app for iOS and Android. We specialize in custom mobile app development for startups and businesses.",url:"https://mobilify.vercel.app",logo:{"@type":"ImageObject",url:"https://mobilify.vercel.app/logo.svg",width:200,height:200},foundingDate:"2024",founder:{"@type":"Person",name:"Mobilify Team"},address:{"@type":"PostalAddress",addressCountry:"US"},contactPoint:{"@type":"ContactPoint",contactType:"customer service",availableLanguage:"English"},sameAs:["https://linkedin.com/company/mobilify","https://twitter.com/mobilify"],service:[{"@type":"Service",name:"Website to Mobile App Conversion",description:"Convert your existing website into a native mobile app for iOS and Android",provider:{"@type":"Organization",name:"Mobilify"}},{"@type":"Service",name:"Custom Mobile App Development",description:"Build custom mobile applications from scratch based on your ideas and requirements",provider:{"@type":"Organization",name:"Mobilify"}},{"@type":"Service",name:"Mobile App Consulting",description:"Expert consultation on mobile app strategy, design, and development",provider:{"@type":"Organization",name:"Mobilify"}}],keywords:["mobile app development","website to app conversion","custom app development","iOS app development","Android app development","startup app development","mobile app consulting"]},null,2)}})},2374:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{cancelIdleCallback:function(){return i},requestIdleCallback:function(){return n}});let n="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let t=Date.now();return self.setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})},1)},i="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2714:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"setAttributesFromProps",{enumerable:!0,get:function(){return a}});let n={acceptCharset:"accept-charset",className:"class",htmlFor:"for",httpEquiv:"http-equiv",noModule:"noModule"},i=["onLoad","onReady","dangerouslySetInnerHTML","children","onError","strategy","stylesheets"];function r(e){return["async","defer","noModule"].includes(e)}function a(e,t){for(let[a,s]of Object.entries(t)){if(!t.hasOwnProperty(a)||i.includes(a)||void 0===s)continue;let o=n[a]||a.toLowerCase();"SCRIPT"===e.tagName&&r(o)?e[o]=!!s:e.setAttribute(o,String(s)),(!1===s||"SCRIPT"===e.tagName&&r(o)&&(!s||"false"===s))&&(e.setAttribute(o,""),e.removeAttribute(o))}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3233:(e,t,n)=>{"use strict";n.d(t,{default:()=>a});var i=n(5155),r=n(3554);let a=()=>{let e="your_ga4_measurement_id_here";return e?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(r.default,{src:"https://www.googletagmanager.com/gtag/js?id=".concat(e),strategy:"afterInteractive"}),(0,i.jsx)(r.default,{id:"google-analytics",strategy:"afterInteractive",children:"\n          window.dataLayer = window.dataLayer || [];\n          function gtag(){dataLayer.push(arguments);}\n          gtag('js', new Date());\n          gtag('config', '".concat(e,"');\n        ")})]}):null}},3554:(e,t,n)=>{"use strict";n.d(t,{default:()=>r.a});var i=n(9243),r=n.n(i)},4843:(e,t,n)=>{"use strict";n.d(t,{default:()=>a});var i=n(5155),r=n(2115);let a=e=>{let{children:t,fallback:n=null}=e,[a,s]=(0,r.useState)(!1);return((0,r.useEffect)(()=>{s(!0)},[]),a)?(0,i.jsx)(i.Fragment,{children:t}):(0,i.jsx)(i.Fragment,{children:n})}},5613:(e,t,n)=>{"use strict";n.d(t,{default:()=>r});var i=n(2115);let r=()=>((0,i.useEffect)(()=>{window.gtag&&n.e(444).then(n.bind(n,7063)).then(e=>{let{onCLS:t,onFCP:n,onLCP:i,onTTFB:r,onINP:a}=e;t(e=>{window.gtag("event","web_vitals",{event_category:"Web Vitals",event_label:"CLS",value:Math.round(1e3*e.value),custom_map:{metric_id:e.id,metric_value:e.value,metric_delta:e.delta}})}),n(e=>{window.gtag("event","web_vitals",{event_category:"Web Vitals",event_label:"FCP",value:Math.round(e.value),custom_map:{metric_id:e.id,metric_value:e.value,metric_delta:e.delta}})}),i(e=>{window.gtag("event","web_vitals",{event_category:"Web Vitals",event_label:"LCP",value:Math.round(e.value),custom_map:{metric_id:e.id,metric_value:e.value,metric_delta:e.delta}})}),r(e=>{window.gtag("event","web_vitals",{event_category:"Web Vitals",event_label:"TTFB",value:Math.round(e.value),custom_map:{metric_id:e.id,metric_value:e.value,metric_delta:e.delta}})}),a(e=>{window.gtag("event","web_vitals",{event_category:"Web Vitals",event_label:"INP",value:Math.round(e.value),custom_map:{metric_id:e.id,metric_value:e.value,metric_delta:e.delta}})})}).catch(e=>{})},[]),null)},7740:(e,t,n)=>{"use strict";n.d(t,{DP:()=>s,ThemeProvider:()=>o});var i=n(5155),r=n(2115);let a=(0,r.createContext)(void 0),s=()=>{let e=(0,r.useContext)(a);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e},o=e=>{let{children:t}=e,[n,s]=(0,r.useState)("light"),[o,l]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{let e=localStorage.getItem("mobilify-theme"),t=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";s(e||t),l(!0)},[]),(0,r.useEffect)(()=>{if(!o)return;let e=document.documentElement;"dark"===n?e.classList.add("dark"):e.classList.remove("dark"),localStorage.setItem("mobilify-theme",n),window.gtag&&window.gtag("event","theme_changed",{event_category:"user_preference",event_label:n})},[n,o]),(0,r.useEffect)(()=>{if(!o)return;let e=window.matchMedia("(prefers-color-scheme: dark)"),t=e=>{localStorage.getItem("mobilify-theme")||s(e.matches?"dark":"light")};return e.addEventListener("change",t),()=>e.removeEventListener("change",t)},[o]),(0,i.jsx)(a.Provider,{value:{theme:n,toggleTheme:()=>{s(e=>"light"===e?"dark":"light")},setTheme:e=>{s(e)}},children:(0,i.jsx)("div",{suppressHydrationWarning:!0,children:t})})}},8152:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,9243,23)),Promise.resolve().then(n.t.bind(n,1710,23)),Promise.resolve().then(n.bind(n,9937)),Promise.resolve().then(n.bind(n,3233)),Promise.resolve().then(n.bind(n,5613)),Promise.resolve().then(n.t.bind(n,347,23)),Promise.resolve().then(n.bind(n,4843)),Promise.resolve().then(n.bind(n,2180)),Promise.resolve().then(n.bind(n,7740))},9243:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return w},handleClientScriptLoad:function(){return g},initScriptLoader:function(){return v}});let i=n(8229),r=n(6966),a=n(5155),s=i._(n(7650)),o=r._(n(2115)),l=n(2830),d=n(2714),c=n(2374),u=new Map,p=new Set,f=e=>{if(s.default.preinit)return void e.forEach(e=>{s.default.preinit(e,{as:"style"})});{let t=document.head;e.forEach(e=>{let n=document.createElement("link");n.type="text/css",n.rel="stylesheet",n.href=e,t.appendChild(n)})}},m=e=>{let{src:t,id:n,onLoad:i=()=>{},onReady:r=null,dangerouslySetInnerHTML:a,children:s="",strategy:o="afterInteractive",onError:l,stylesheets:c}=e,m=n||t;if(m&&p.has(m))return;if(u.has(t)){p.add(m),u.get(t).then(i,l);return}let g=()=>{r&&r(),p.add(m)},v=document.createElement("script"),h=new Promise((e,t)=>{v.addEventListener("load",function(t){e(),i&&i.call(this,t),g()}),v.addEventListener("error",function(e){t(e)})}).catch(function(e){l&&l(e)});a?(v.innerHTML=a.__html||"",g()):s?(v.textContent="string"==typeof s?s:Array.isArray(s)?s.join(""):"",g()):t&&(v.src=t,u.set(t,h)),(0,d.setAttributesFromProps)(v,e),"worker"===o&&v.setAttribute("type","text/partytown"),v.setAttribute("data-nscript",o),c&&f(c),document.body.appendChild(v)};function g(e){let{strategy:t="afterInteractive"}=e;"lazyOnload"===t?window.addEventListener("load",()=>{(0,c.requestIdleCallback)(()=>m(e))}):m(e)}function v(e){e.forEach(g),[...document.querySelectorAll('[data-nscript="beforeInteractive"]'),...document.querySelectorAll('[data-nscript="beforePageRender"]')].forEach(e=>{let t=e.id||e.getAttribute("src");p.add(t)})}function h(e){let{id:t,src:n="",onLoad:i=()=>{},onReady:r=null,strategy:d="afterInteractive",onError:u,stylesheets:f,...g}=e,{updateScripts:v,scripts:h,getIsSsr:w,appDir:_,nonce:b}=(0,o.useContext)(l.HeadManagerContext),y=(0,o.useRef)(!1);(0,o.useEffect)(()=>{let e=t||n;y.current||(r&&e&&p.has(e)&&r(),y.current=!0)},[r,t,n]);let I=(0,o.useRef)(!1);if((0,o.useEffect)(()=>{if(!I.current){if("afterInteractive"===d)m(e);else"lazyOnload"===d&&("complete"===document.readyState?(0,c.requestIdleCallback)(()=>m(e)):window.addEventListener("load",()=>{(0,c.requestIdleCallback)(()=>m(e))}));I.current=!0}},[e,d]),("beforeInteractive"===d||"worker"===d)&&(v?(h[d]=(h[d]||[]).concat([{id:t,src:n,onLoad:i,onReady:r,onError:u,...g}]),v(h)):w&&w()?p.add(t||n):w&&!w()&&m(e)),_){if(f&&f.forEach(e=>{s.default.preinit(e,{as:"style"})}),"beforeInteractive"===d)if(!n)return g.dangerouslySetInnerHTML&&(g.children=g.dangerouslySetInnerHTML.__html,delete g.dangerouslySetInnerHTML),(0,a.jsx)("script",{nonce:b,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([0,{...g,id:t}])+")"}});else return s.default.preload(n,g.integrity?{as:"script",integrity:g.integrity,nonce:b,crossOrigin:g.crossOrigin}:{as:"script",nonce:b,crossOrigin:g.crossOrigin}),(0,a.jsx)("script",{nonce:b,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([n,{...g,id:t}])+")"}});"afterInteractive"===d&&n&&s.default.preload(n,g.integrity?{as:"script",integrity:g.integrity,nonce:b,crossOrigin:g.crossOrigin}:{as:"script",nonce:b,crossOrigin:g.crossOrigin})}return null}Object.defineProperty(h,"__nextScript",{value:!0});let w=h;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9937:(e,t,n)=>{"use strict";n.d(t,{default:()=>s,l:()=>o});var i=n(5155),r=n(2115),a=n(3554);let s=e=>{let{websiteId:t}=e,n=t||"your_crisp_website_id_here";return((0,r.useEffect)(()=>{if(!n)return;let e=()=>{window.$crisp&&(window.$crisp.push(["safe",!0]),window.$crisp.push(["set","user:company","Mobilify Website Visitor"]),window.$crisp.push(["set","session:data",{source:"website",page:window.location.pathname,timestamp:new Date().toISOString()}]),window.$crisp.push(["on","chat:opened",()=>{window.gtag&&window.gtag("event","chat_opened",{event_category:"engagement",event_label:"crisp_chat"})}]),window.$crisp.push(["on","message:sent",()=>{window.gtag&&window.gtag("event","chat_message_sent",{event_category:"engagement",event_label:"crisp_chat"})}]),window.$crisp.push(["on","message:received",()=>{window.gtag&&window.gtag("event","chat_message_received",{event_category:"engagement",event_label:"crisp_chat"})}]))};if(window.$crisp)e();else{let t=setInterval(()=>{window.$crisp&&(e(),clearInterval(t))},100);setTimeout(()=>clearInterval(t),1e4)}},[n]),n)?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(a.default,{id:"crisp-chat-init",strategy:"afterInteractive",dangerouslySetInnerHTML:{__html:'\n            window.$crisp = [];\n            window.CRISP_WEBSITE_ID = "'.concat(n,'";\n          ')}}),(0,i.jsx)(a.default,{src:"https://client.crisp.chat/l.js",strategy:"afterInteractive"})]}):null},o={openChat:()=>{window.$crisp&&window.$crisp.push(["do","chat:open"])},closeChat:()=>{window.$crisp&&window.$crisp.push(["do","chat:close"])},showChat:()=>{window.$crisp&&window.$crisp.push(["do","chat:show"])},hideChat:()=>{window.$crisp&&window.$crisp.push(["do","chat:hide"])},setUser:e=>{window.$crisp&&(e.nickname&&window.$crisp.push(["set","user:nickname",e.nickname]),e.email&&window.$crisp.push(["set","user:email",e.email]),e.phone&&window.$crisp.push(["set","user:phone",e.phone]),e.avatar&&window.$crisp.push(["set","user:avatar",e.avatar]))},sendMessage:e=>{window.$crisp&&window.$crisp.push(["do","message:send",["text",e]])},setSessionData:e=>{window.$crisp&&window.$crisp.push(["set","session:data",e])},isChatAvailable:()=>!!window.$crisp&&window.$crisp.length>0,setSegments:e=>{window.$crisp&&window.$crisp.push(["set","session:segments",e])}}}},e=>{var t=t=>e(e.s=t);e.O(0,[314,441,684,358],()=>t(8152)),_N_E=e.O()}]);