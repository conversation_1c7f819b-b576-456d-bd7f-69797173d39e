module.exports = {

"[project]/src/components/sections/InteractiveDemo.tsx [app-rsc] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_components_sections_InteractiveDemo_tsx_6cdf9949._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/sections/InteractiveDemo.tsx [app-rsc] (ecmascript, next/dynamic entry)");
    });
});
}}),
"[project]/src/components/sections/ServicesOverview.tsx [app-rsc] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_components_sections_ServicesOverview_tsx_5834a7a4._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/sections/ServicesOverview.tsx [app-rsc] (ecmascript, next/dynamic entry)");
    });
});
}}),
"[project]/src/components/sections/Process.tsx [app-rsc] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_components_sections_Process_tsx_c4acd188._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/sections/Process.tsx [app-rsc] (ecmascript, next/dynamic entry)");
    });
});
}}),
"[project]/src/components/sections/AboutSnippet.tsx [app-rsc] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_components_sections_AboutSnippet_tsx_7654e51f._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/sections/AboutSnippet.tsx [app-rsc] (ecmascript, next/dynamic entry)");
    });
});
}}),
"[project]/src/components/NewsletterSignup.tsx [app-rsc] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_components_NewsletterSignup_tsx_3c225252._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/NewsletterSignup.tsx [app-rsc] (ecmascript, next/dynamic entry)");
    });
});
}}),
"[project]/src/components/sections/Contact.tsx [app-rsc] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_components_sections_Contact_tsx_8517f349._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/sections/Contact.tsx [app-rsc] (ecmascript, next/dynamic entry)");
    });
});
}}),
"[project]/src/components/layout/Footer.tsx [app-rsc] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_components_layout_Footer_tsx_30e7d946._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/layout/Footer.tsx [app-rsc] (ecmascript, next/dynamic entry)");
    });
});
}}),
"[project]/src/components/SimpleFloatingChat.tsx [app-rsc] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_components_SimpleFloatingChat_tsx_cbeb0ccd._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/SimpleFloatingChat.tsx [app-rsc] (ecmascript, next/dynamic entry)");
    });
});
}}),

};