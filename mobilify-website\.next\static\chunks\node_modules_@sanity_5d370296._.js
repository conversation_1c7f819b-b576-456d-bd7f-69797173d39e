(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@sanity/client/dist/_chunks-es/stegaEncodeSourceMap.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@sanity_client_dist__chunks-es_stegaEncodeSourceMap_b8f85c85.js",
  "static/chunks/node_modules_@sanity_client_dist__chunks-es_stegaEncodeSourceMap_6e7e2d6d.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@sanity/client/dist/_chunks-es/stegaEncodeSourceMap.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@sanity/eventsource/browser.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_8d3a178a._.js",
  "static/chunks/node_modules_@sanity_eventsource_browser_6e7e2d6d.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@sanity/eventsource/browser.js [app-client] (ecmascript)");
    });
});
}}),
}]);