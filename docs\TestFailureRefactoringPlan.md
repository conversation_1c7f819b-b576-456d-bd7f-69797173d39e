# Test Failure Refactoring Plan

This document outlines the necessary steps to fix the failing tests in the `Input` and `NewsletterSignup` components. The failures stem from a mix of outdated test assertions, accessibility gaps, and minor logic bugs.

**Testing Framework:** React Testing Library with Jest
**Priority:** Fix `Input` component first (foundational UI element), then `NewsletterSignup`

## I. `Input` Component (`src/components/ui/Input.tsx`)

The `Input` component tests are failing primarily due to accessibility issues and outdated class name assertions. **Current Status:** Component implementation is correct and accessible.

### 1.1. Fix Test Accessibility Queries

-   [ ] **Update `getByLabelText` test queries:**
    -   **Problem:** `getByLabelText` queries are failing in tests.
    -   **Current Implementation:** ✅ Component correctly uses `useId()` and applies `id` to input and `htmlFor` to label.
    -   **Solution:** Tests need to pass a `label` prop when testing `getByLabelText` functionality, as the component only renders a label when the `label` prop is provided.

-   [ ] **Update ARIA attribute test assertions:**
    -   **Problem:** Tests expecting `aria-invalid` are failing.
    -   **Current Implementation:** ✅ Component correctly implements:
        - `aria-invalid={hasError}` (boolean based on error prop)
        - `aria-describedby` linking to error message when error exists
        - Error message with `role="alert"`
    -   **Solution:** Update test assertions to check for correct boolean values and conditional `aria-describedby`.

### 1.2. Update Test Class Name Assertions

-   [ ] **Update class name checks to use current semantic tokens:**
    -   **Problem:** Tests are asserting outdated Tailwind classes (e.g., `border-gray-300`, `dark:placeholder-gray-400`).
    -   **Current Implementation:** Component uses semantic design tokens:
        - Base: `border-border-light dark:border-border-dark`
        - Background: `bg-surface-light dark:bg-surface-gray-dark`
        - Text: `text-text-primary dark:text-text-primary-dark`
        - Placeholder: `placeholder-text-muted dark:placeholder-text-muted-dark`
        - Focus: `focus:ring-electric-blue focus:border-electric-blue`
    -   **Solution:** Update all `toHaveClass` assertions in `Input.test.tsx` to match current semantic token classes.

## II. `NewsletterSignup` Component (`src/components/NewsletterSignup.tsx`)

The `NewsletterSignup` component has a wider range of issues, including accessibility, component logic, and fragile test selectors. **Current Status:** Component implementation is mostly correct but needs test updates.

### 2.1. Fix Test Accessibility Queries

-   [ ] **Update accessible name test queries:**
    -   **Problem:** The test `screen.getAllByRole('textbox', { name: /email/i })` fails.
    -   **Current Implementation:** ✅ Component correctly implements `aria-label`:
        - Footer variant: `aria-label="Email for newsletter"`
        - Inline variant: `aria-label="Email address for newsletter"`
    -   **Solution:** Update test queries to match the exact `aria-label` text or use more flexible regex patterns.

-   [ ] **Verify Framer Motion implementation:**
    -   **Problem:** Potential React warning about `whileInView` prop on standard `<div>`.
    -   **Current Implementation:** ✅ Component correctly uses `motion.div` for inline variant with proper Framer Motion props.
    -   **Solution:** If tests are still failing, verify that Framer Motion is properly mocked in test setup.

### 2.2. Update Test Logic and Assertions

-   [ ] **Update button disabled state test expectations:**
    -   **Problem:** Tests expect button to be enabled initially, but it's disabled.
    -   **Current Implementation:** ✅ Component correctly implements `disabled={isSubmitting || !email}`.
    -   **Solution:** Update tests to:
        1. Assert button is initially `toBeDisabled()` (empty email)
        2. Type into email input
        3. Assert button becomes `not.toBeDisabled()`

-   [ ] **Fix async form submission tests:**
    -   **Problem:** Tests for error/success states may have race conditions.
    -   **Current Implementation:** ✅ Component correctly handles:
        - Success: Sets `submitStatus` to 'success' and calls `gtag`
        - Error: Catches exceptions and sets `submitStatus` to 'error'
    -   **Solution:** Use proper async testing patterns:
        - Mock `/api/subscribe` endpoint with MSW
        - Use `await findByText()` for async state changes
        - Use `await waitFor()` for gtag assertions

### 2.3. Fix Test Selector Issues

-   [ ] **Update inline variant background color test:**
    -   **Problem:** Test `expect(section).toHaveClass('bg-electric-blue')` fails with unreliable selector.
    -   **Current Implementation:** ✅ Component correctly applies `bg-electric-blue` to the `motion.div` wrapper.
    -   **Solution:** Use more specific selector or test the component directly rather than traversing DOM.

## III. Test Infrastructure Setup

### 3.1. Install and Configure MSW (New Addition)

-   [ ] **Install MSW for API mocking:**
    -   **Current Status:** Not currently installed (TESTING_GUIDE.md mentions `mockFetch()` helper but not MSW).
    -   **Solution:**
        ```bash
        npm install --save-dev msw
        ```

-   [ ] **Configure MSW handlers for `/api/subscribe`:**
    -   **Endpoint Contract:**
        - POST `/api/subscribe`
        - Request body: `{ "email": "<EMAIL>" }`
        - Success response: `200 OK`
        - Error response: `500 Internal Server Error`
    -   **Solution:** Create MSW handlers in test setup to mock both success and failure scenarios.

### 3.2. Update Test Environment Configuration

-   [ ] **Configure test setup files:**
    -   **Current Files:**
        - `jest.config.js`: Main Jest configuration
        - `jest.setup.js`: Global test setup and mocks
        - `src/__tests__/test-utils.tsx`: Custom testing utilities
    -   **Solution:** Update `jest.setup.js` to include MSW server setup and console mocking.

-   [ ] **Silence expected console output:**
    -   **Problem:** Test output cluttered with expected console messages.
    -   **Solution:** Mock `console.error` and `console.warn` in test setup to suppress expected output during tests.

## IV. Implementation Order

1. **Phase 1: Input Component** (Foundational)
   - Update test class name assertions to use semantic tokens
   - Fix accessibility test queries
   - Verify ARIA attribute assertions

2. **Phase 2: Test Infrastructure**
   - Install and configure MSW
   - Update test setup files
   - Configure console mocking

3. **Phase 3: NewsletterSignup Component**
   - Update accessibility test queries
   - Fix async form submission tests with MSW
   - Update button state test expectations
   - Fix selector issues

## V. Questions for Clarification

1. **Test File Locations:** Where are the actual test files located? Need to see:
   - `src/components/ui/Input.test.tsx` (or similar)
   - `src/components/NewsletterSignup.test.tsx` (or similar)

2. **Current Test Failures:** Could you run the tests and share the specific error messages to confirm which assertions are failing?

3. **MSW vs mockFetch:** Should we replace the existing `mockFetch()` helper with MSW, or integrate both approaches?