# Google Analytics Configuration
# Get your Measurement ID from Google Analytics 4 (GA4)
# Format: G-XXXXXXXXXX
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=your_ga4_measurement_id_here

# Web3Forms Configuration
# Get your free access key from https://web3forms.com
NEXT_PUBLIC_WEB3FORMS_ACCESS_KEY=your_web3forms_access_key_here

# Live Chat Support
# Choose ONE of the following providers:

# Option 1: Crisp Chat
# Get your website ID from https://crisp.chat
NEXT_PUBLIC_CRISP_WEBSITE_ID=your_crisp_website_id_here

# Option 2: Tawk.to Chat
# Get your property ID from https://tawk.to
NEXT_PUBLIC_TAWK_TO_PROPERTY_ID=your_tawk_to_property_id_here

# Newsletter Service
# Choose ONE of the following providers:

# Option 1: Mailchimp
# Get your API key from Mailchimp dashboard
NEXT_PUBLIC_MAILCHIMP_API_KEY=your_mailchimp_api_key_here
MAILCHIMP_LIST_ID=your_mailchimp_list_id_here

# Option 2: ConvertKit
# Get your API key from ConvertKit settings
CONVERTKIT_API_KEY=your_convertkit_api_key_here
CONVERTKIT_FORM_ID=your_convertkit_form_id_here

# Sanity CMS Configuration
# Get these from your Sanity project dashboard at https://sanity.io
# 1. Create a new project at https://sanity.io
# 2. Note your Project ID from the project dashboard
# 3. Create an API token with Editor permissions in Settings > API
# 4. Deploy your Sanity Studio with: sanity deploy
NEXT_PUBLIC_SANITY_PROJECT_ID=your_sanity_project_id_here
NEXT_PUBLIC_SANITY_DATASET=production
SANITY_API_TOKEN=your_sanity_api_token_here
