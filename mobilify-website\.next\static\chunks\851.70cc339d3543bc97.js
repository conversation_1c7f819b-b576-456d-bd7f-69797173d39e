"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[851],{7851:(e,t,i)=>{let n;i.r(t),i.d(t,{CLSThresholds:()=>S,FCPThresholds:()=>L,INPThresholds:()=>R,LCPThresholds:()=>D,TTFBThresholds:()=>$,onCLS:()=>w,onFCP:()=>P,onINP:()=>q,onLCP:()=>O,onTTFB:()=>j});let r=-1,a=e=>{addEventListener("pageshow",t=>{t.persisted&&(r=t.timeStamp,e(t))},!0)},s=(e,t,i,n)=>{let r,a;return s=>{t.value>=0&&(s||n)&&((a=t.value-(r??0))||void 0===r)&&(r=t.value,t.delta=a,t.rating=((e,t)=>e>t[1]?"poor":e>t[0]?"needs-improvement":"good")(t.value,i),e(t))}},o=e=>{requestAnimationFrame(()=>requestAnimationFrame(()=>e()))},l=()=>{let e=performance.getEntriesByType("navigation")[0];if(e&&e.responseStart>0&&e.responseStart<performance.now())return e},d=()=>{let e=l();return e?.activationStart??0},h=(e,t=-1)=>{let i=l(),n="navigate";return r>=0?n="back-forward-cache":i&&(document.prerendering||d()>0?n="prerender":document.wasDiscarded?n="restore":i.type&&(n=i.type.replace(/_/g,"-"))),{name:e,value:t,rating:"good",delta:0,entries:[],id:`v5-${Date.now()}-${Math.floor(0x82f79cd8fff*Math.random())+1e12}`,navigationType:n}},u=new WeakMap;function c(e,t){return u.get(e)||u.set(e,new t),u.get(e)}class m{t;i=0;o=[];h(e){if(e.hadRecentInput)return;let t=this.o[0],i=this.o.at(-1);this.i&&t&&i&&e.startTime-i.startTime<1e3&&e.startTime-t.startTime<5e3?(this.i+=e.value,this.o.push(e)):(this.i=e.value,this.o=[e]),this.t?.(e)}}let p=(e,t,i={})=>{try{if(PerformanceObserver.supportedEntryTypes.includes(e)){let n=new PerformanceObserver(e=>{Promise.resolve().then(()=>{t(e.getEntries())})});return n.observe({type:e,buffered:!0,...i}),n}}catch{}},v=e=>{let t=!1;return()=>{t||(e(),t=!0)}},f=-1,g=()=>"hidden"!==document.visibilityState||document.prerendering?1/0:0,T=e=>{"hidden"===document.visibilityState&&f>-1&&(f="visibilitychange"===e.type?e.timeStamp:0,b())},y=()=>{addEventListener("visibilitychange",T,!0),addEventListener("prerenderingchange",T,!0)},b=()=>{removeEventListener("visibilitychange",T,!0),removeEventListener("prerenderingchange",T,!0)},C=()=>{if(f<0){let e=d();f=(document.prerendering?void 0:globalThis.performance.getEntriesByType("visibility-state").filter(t=>"hidden"===t.name&&t.startTime>e)[0]?.startTime)??g(),y(),a(()=>{setTimeout(()=>{f=g(),y()})})}return{get firstHiddenTime(){return f}}},E=e=>{document.prerendering?addEventListener("prerenderingchange",()=>e(),!0):e()},L=[1800,3e3],P=(e,t={})=>{E(()=>{let i=C(),n,r=h("FCP"),l=p("paint",e=>{for(let t of e)"first-contentful-paint"===t.name&&(l.disconnect(),t.startTime<i.firstHiddenTime&&(r.value=Math.max(t.startTime-d(),0),r.entries.push(t),n(!0)))});l&&(n=s(e,r,L,t.reportAllChanges),a(i=>{n=s(e,r=h("FCP"),L,t.reportAllChanges),o(()=>{r.value=performance.now()-i.timeStamp,n(!0)})}))})},S=[.1,.25],w=(e,t={})=>{P(v(()=>{let i,n=h("CLS",0),r=c(t,m),l=e=>{for(let t of e)r.h(t);r.i>n.value&&(n.value=r.i,n.entries=r.o,i())},d=p("layout-shift",l);d&&(i=s(e,n,S,t.reportAllChanges),document.addEventListener("visibilitychange",()=>{"hidden"===document.visibilityState&&(l(d.takeRecords()),i(!0))}),a(()=>{r.i=0,i=s(e,n=h("CLS",0),S,t.reportAllChanges),o(()=>i())}),setTimeout(i))}))},A=0,I=1/0,k=0,M=e=>{for(let t of e)t.interactionId&&(I=Math.min(I,t.interactionId),A=(k=Math.max(k,t.interactionId))?(k-I)/7+1:0)},F=()=>n?A:performance.interactionCount??0,B=()=>{"interactionCount"in performance||n||(n=p("event",M,{type:"event",buffered:!0,durationThreshold:0}))},N=0;class x{u=[];l=new Map;m;v;p(){N=F(),this.u.length=0,this.l.clear()}P(){let e=Math.min(this.u.length-1,Math.floor((F()-N)/50));return this.u[e]}h(e){if(this.m?.(e),!e.interactionId&&"first-input"!==e.entryType)return;let t=this.u.at(-1),i=this.l.get(e.interactionId);if(i||this.u.length<10||e.duration>t.T){if(i?e.duration>i.T?(i.entries=[e],i.T=e.duration):e.duration===i.T&&e.startTime===i.entries[0].startTime&&i.entries.push(e):(i={id:e.interactionId,entries:[e],T:e.duration},this.l.set(i.id,i),this.u.push(i)),this.u.sort((e,t)=>t.T-e.T),this.u.length>10)for(let e of this.u.splice(10))this.l.delete(e.id);this.v?.(i)}}}let _=e=>{let t=globalThis.requestIdleCallback||setTimeout;"hidden"===document.visibilityState?e():(e=v(e),document.addEventListener("visibilitychange",e,{once:!0}),t(()=>{e(),document.removeEventListener("visibilitychange",e)}))},R=[200,500],q=(e,t={})=>{globalThis.PerformanceEventTiming&&"interactionId"in PerformanceEventTiming.prototype&&E(()=>{B();let i,n=h("INP"),r=c(t,x),o=e=>{_(()=>{for(let t of e)r.h(t);let t=r.P();t&&t.T!==n.value&&(n.value=t.T,n.entries=t.entries,i())})},l=p("event",o,{durationThreshold:t.durationThreshold??40});i=s(e,n,R,t.reportAllChanges),l&&(l.observe({type:"first-input",buffered:!0}),document.addEventListener("visibilitychange",()=>{"hidden"===document.visibilityState&&(o(l.takeRecords()),i(!0))}),a(()=>{r.p(),i=s(e,n=h("INP"),R,t.reportAllChanges)}))})};class H{m;h(e){this.m?.(e)}}let D=[2500,4e3],O=(e,t={})=>{E(()=>{let i=C(),n,r=h("LCP"),l=c(t,H),u=e=>{for(let a of(t.reportAllChanges||(e=e.slice(-1)),e))l.h(a),a.startTime<i.firstHiddenTime&&(r.value=Math.max(a.startTime-d(),0),r.entries=[a],n())},m=p("largest-contentful-paint",u);if(m){n=s(e,r,D,t.reportAllChanges);let i=v(()=>{u(m.takeRecords()),m.disconnect(),n(!0)});for(let e of["keydown","click","visibilitychange"])addEventListener(e,()=>_(i),{capture:!0,once:!0});a(i=>{n=s(e,r=h("LCP"),D,t.reportAllChanges),o(()=>{r.value=performance.now()-i.timeStamp,n(!0)})})}})},$=[800,1800],W=e=>{document.prerendering?E(()=>W(e)):"complete"!==document.readyState?addEventListener("load",()=>W(e),!0):setTimeout(e)},j=(e,t={})=>{let i=h("TTFB"),n=s(e,i,$,t.reportAllChanges);W(()=>{let r=l();r&&(i.value=Math.max(r.responseStart-d(),0),i.entries=[r],n(!0),a(()=>{(n=s(e,i=h("TTFB",0),$,t.reportAllChanges))(!0)}))})}}}]);