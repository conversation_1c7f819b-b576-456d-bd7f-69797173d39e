exports.id=550,exports.ids=[550],exports.modules={1188:(e,t,i)=>{"use strict";i.d(t,{DP:()=>n,ThemeProvider:()=>a});var o=i(60687),r=i(43210);let s=(0,r.createContext)(void 0),n=()=>{let e=(0,r.useContext)(s);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e},a=({children:e})=>{let[t,i]=(0,r.useState)("light"),[n,a]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{let e=localStorage.getItem("mobilify-theme"),t=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";i(e||t),a(!0)},[]),(0,r.useEffect)(()=>{if(!n)return;let e=document.documentElement;"dark"===t?e.classList.add("dark"):e.classList.remove("dark"),localStorage.setItem("mobilify-theme",t)},[t,n]),(0,r.useEffect)(()=>{if(!n)return;let e=window.matchMedia("(prefers-color-scheme: dark)"),t=e=>{localStorage.getItem("mobilify-theme")||i(e.matches?"dark":"light")};return e.addEventListener("change",t),()=>e.removeEventListener("change",t)},[n]),(0,o.jsx)(s.Provider,{value:{theme:t,toggleTheme:()=>{i(e=>"light"===e?"dark":"light")},setTheme:e=>{i(e)}},children:(0,o.jsx)("div",{suppressHydrationWarning:!0,children:e})})}},12973:(e,t,i)=>{"use strict";i.d(t,{default:()=>s});var o=i(60687),r=i(72600);let s=()=>{let e="your_ga4_measurement_id_here";return e?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(r.default,{src:`https://www.googletagmanager.com/gtag/js?id=${e}`,strategy:"afterInteractive"}),(0,o.jsx)(r.default,{id:"google-analytics",strategy:"afterInteractive",children:`
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());
          gtag('config', '${e}');
        `})]}):null}},14974:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>f,metadata:()=>b});var o=i(37413),r=i(65047),s=i.n(r);i(61135);var n=i(88891),a=i(29935),l=i(65514),p=i(36162);let m=({type:e,data:t})=>{let i=process.env.NEXT_PUBLIC_SITE_URL||"https://mobilify.app",r=(()=>{switch(e){case"organization":return{"@context":"https://schema.org","@type":"Organization",name:"Mobilify",description:"Professional mobile app development company specializing in converting websites and ideas into custom iOS and Android applications.",url:i,logo:`${i}/logo.svg`,contactPoint:{"@type":"ContactPoint",contactType:"customer service",email:"<EMAIL>",availableLanguage:"English"},address:{"@type":"PostalAddress",addressCountry:"US"},sameAs:["https://twitter.com/mobilifyapp","https://linkedin.com/company/mobilify"],foundingDate:"2024",numberOfEmployees:"2-10",industry:"Software Development",serviceArea:{"@type":"Place",name:"Worldwide"}};case"service":return{"@context":"https://schema.org","@type":"Service",name:"Mobile App Development",description:"Professional mobile app development services including website conversion, custom app development, and enterprise solutions.",provider:{"@type":"Organization",name:"Mobilify",url:i},serviceType:"Mobile Application Development",areaServed:"Worldwide",hasOfferCatalog:{"@type":"OfferCatalog",name:"Mobile App Development Services",itemListElement:[{"@type":"Offer",itemOffered:{"@type":"Service",name:"Website to App Conversion",description:"Convert your existing website into a native mobile app"},price:"5000",priceCurrency:"USD"},{"@type":"Offer",itemOffered:{"@type":"Service",name:"Custom Mobile App Development",description:"Build a custom mobile app from your idea"},price:"15000",priceCurrency:"USD"}]}};case"faq":return{"@context":"https://schema.org","@type":"FAQPage",mainEntity:t?.faqs?.map(e=>({"@type":"Question",name:e.question,acceptedAnswer:{"@type":"Answer",text:e.answer}}))||[]};case"breadcrumb":return{"@context":"https://schema.org","@type":"BreadcrumbList",itemListElement:t?.breadcrumbs?.map((e,t)=>({"@type":"ListItem",position:t+1,name:e.name,item:`${i}${e.url}`}))||[]};case"website":return{"@context":"https://schema.org","@type":"WebSite",name:"Mobilify",description:"Turn your website or idea into a custom mobile app",url:i,potentialAction:{"@type":"SearchAction",target:{"@type":"EntryPoint",urlTemplate:`${i}/search?q={search_term_string}`},"query-input":"required name=search_term_string"},publisher:{"@type":"Organization",name:"Mobilify",logo:{"@type":"ImageObject",url:`${i}/logo.svg`}}};default:return null}})();return r?(0,o.jsx)(p.default,{id:`structured-data-${e}`,type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(r)}}):null};var c=i(74155),d=i(68462),u=i(37463);let b={title:{default:"Mobilify | Turn Your Website or Idea Into a Custom Mobile App",template:"%s | Mobilify - Custom Mobile App Development"},description:"Mobilify converts your existing website or new idea into a high-quality, native mobile app for iOS and Android. Get a beautiful, custom-designed app without the complexity. See our demo!",keywords:["mobile app development","website to app conversion","custom mobile apps","iOS app development","Android app development","mobile app builder","app development services","native mobile apps","mobile app design","app development company"],authors:[{name:"Mobilify Team"}],creator:"Mobilify",publisher:"Mobilify",formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL(process.env.NEXT_PUBLIC_SITE_URL||"https://mobilify.app"),alternates:{canonical:"/"},openGraph:{title:"Mobilify | Turn Your Website or Idea Into a Custom Mobile App",description:"Mobilify converts your existing website or new idea into a high-quality, native mobile app for iOS and Android. Get a beautiful, custom-designed app without the complexity.",url:"/",siteName:"Mobilify",images:[{url:"/og-image.png",width:1200,height:630,alt:"Mobilify - Custom Mobile App Development"}],locale:"en_US",type:"website"},twitter:{card:"summary_large_image",title:"Mobilify | Turn Your Website or Idea Into a Custom Mobile App",description:"Convert your website or idea into a beautiful mobile app. iOS & Android development made simple.",images:["/twitter-image.png"],creator:"@mobilifyapp"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},verification:{google:process.env.GOOGLE_SITE_VERIFICATION,yandex:process.env.YANDEX_VERIFICATION,yahoo:process.env.YAHOO_VERIFICATION}};function f({children:e}){return(0,o.jsxs)("html",{lang:"en",suppressHydrationWarning:!0,children:[(0,o.jsxs)("head",{children:[(0,o.jsx)(l.default,{}),(0,o.jsx)(m,{type:"organization"}),(0,o.jsx)(m,{type:"website"})]}),(0,o.jsx)("body",{className:`${s().variable} font-sans antialiased text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-900 transition-colors duration-300`,suppressHydrationWarning:!0,children:(0,o.jsxs)(d.ThemeProvider,{children:[e,(0,o.jsxs)(u.default,{children:[(0,o.jsx)(n.default,{}),(0,o.jsx)(a.default,{}),(0,o.jsx)(c.default,{})]})]})})]})}},17792:(e,t,i)=>{"use strict";i.d(t,{default:()=>r});var o=i(60687);i(43210);let r=()=>(0,o.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify({"@context":"https://schema.org","@type":"Organization",name:"Mobilify",description:"Mobilify converts your existing website or new idea into a high-quality, native mobile app for iOS and Android. We specialize in custom mobile app development for startups and businesses.",url:"https://mobilify.vercel.app",logo:{"@type":"ImageObject",url:"https://mobilify.vercel.app/logo.svg",width:200,height:200},foundingDate:"2024",founder:{"@type":"Person",name:"Mobilify Team"},address:{"@type":"PostalAddress",addressCountry:"US"},contactPoint:{"@type":"ContactPoint",contactType:"customer service",availableLanguage:"English"},sameAs:["https://linkedin.com/company/mobilify","https://twitter.com/mobilify"],service:[{"@type":"Service",name:"Website to Mobile App Conversion",description:"Convert your existing website into a native mobile app for iOS and Android",provider:{"@type":"Organization",name:"Mobilify"}},{"@type":"Service",name:"Custom Mobile App Development",description:"Build custom mobile applications from scratch based on your ideas and requirements",provider:{"@type":"Organization",name:"Mobilify"}},{"@type":"Service",name:"Mobile App Consulting",description:"Expert consultation on mobile app strategy, design, and development",provider:{"@type":"Organization",name:"Mobilify"}}],keywords:["mobile app development","website to app conversion","custom app development","iOS app development","Android app development","startup app development","mobile app consulting"]},null,2)}})},29935:(e,t,i)=>{"use strict";i.d(t,{default:()=>r});var o=i(12907);let r=(0,o.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\My projects\\\\Mobilify\\\\website\\\\gemini\\\\mobilify-website\\\\src\\\\analytics\\\\CrispChat.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\analytics\\CrispChat.tsx","default");(0,o.registerClientReference)(function(){throw Error("Attempted to call crispUtils() from the server but crispUtils is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\analytics\\CrispChat.tsx","crispUtils")},37463:(e,t,i)=>{"use strict";i.d(t,{default:()=>o});let o=(0,i(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\My projects\\\\Mobilify\\\\website\\\\gemini\\\\mobilify-website\\\\src\\\\components\\\\NoSSR.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\NoSSR.tsx","default")},46777:(e,t,i)=>{"use strict";i.d(t,{default:()=>r});var o=i(43210);let r=()=>((0,o.useEffect)(()=>{},[]),null)},47009:(e,t,i)=>{"use strict";i.d(t,{default:()=>s});var o=i(60687),r=i(43210);let s=({children:e,fallback:t=null})=>{let[i,s]=(0,r.useState)(!1);return((0,r.useEffect)(()=>{s(!0)},[]),i)?(0,o.jsx)(o.Fragment,{children:e}):(0,o.jsx)(o.Fragment,{children:t})}},47641:(e,t,i)=>{Promise.resolve().then(i.t.bind(i,16444,23)),Promise.resolve().then(i.t.bind(i,16042,23)),Promise.resolve().then(i.t.bind(i,88170,23)),Promise.resolve().then(i.t.bind(i,49477,23)),Promise.resolve().then(i.t.bind(i,29345,23)),Promise.resolve().then(i.t.bind(i,12089,23)),Promise.resolve().then(i.t.bind(i,46577,23)),Promise.resolve().then(i.t.bind(i,31307,23))},50336:(e,t,i)=>{Promise.resolve().then(i.t.bind(i,79167,23)),Promise.resolve().then(i.bind(i,91405)),Promise.resolve().then(i.bind(i,12973)),Promise.resolve().then(i.bind(i,46777)),Promise.resolve().then(i.bind(i,47009)),Promise.resolve().then(i.bind(i,17792)),Promise.resolve().then(i.bind(i,1188))},61135:()=>{},63488:(e,t,i)=>{Promise.resolve().then(i.t.bind(i,47429,23)),Promise.resolve().then(i.bind(i,29935)),Promise.resolve().then(i.bind(i,88891)),Promise.resolve().then(i.bind(i,74155)),Promise.resolve().then(i.bind(i,37463)),Promise.resolve().then(i.bind(i,65514)),Promise.resolve().then(i.bind(i,68462))},65514:(e,t,i)=>{"use strict";i.d(t,{default:()=>o});let o=(0,i(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\My projects\\\\Mobilify\\\\website\\\\gemini\\\\mobilify-website\\\\src\\\\components\\\\OrganizationSchema.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\OrganizationSchema.tsx","default")},68462:(e,t,i)=>{"use strict";i.d(t,{ThemeProvider:()=>r});var o=i(12907);(0,o.registerClientReference)(function(){throw Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\contexts\\ThemeContext.tsx","useTheme");let r=(0,o.registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\contexts\\ThemeContext.tsx","ThemeProvider");(0,o.registerClientReference)(function(){throw Error("Attempted to call useSystemTheme() from the server but useSystemTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\contexts\\ThemeContext.tsx","useSystemTheme"),(0,o.registerClientReference)(function(){throw Error("Attempted to call getThemeClasses() from the server but getThemeClasses is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\contexts\\ThemeContext.tsx","getThemeClasses")},74155:(e,t,i)=>{"use strict";i.d(t,{default:()=>o});let o=(0,i(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\My projects\\\\Mobilify\\\\website\\\\gemini\\\\mobilify-website\\\\src\\\\analytics\\\\WebVitals.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\analytics\\WebVitals.tsx","default")},84089:(e,t,i)=>{Promise.resolve().then(i.t.bind(i,86346,23)),Promise.resolve().then(i.t.bind(i,27924,23)),Promise.resolve().then(i.t.bind(i,35656,23)),Promise.resolve().then(i.t.bind(i,40099,23)),Promise.resolve().then(i.t.bind(i,38243,23)),Promise.resolve().then(i.t.bind(i,28827,23)),Promise.resolve().then(i.t.bind(i,62763,23)),Promise.resolve().then(i.t.bind(i,97173,23))},88891:(e,t,i)=>{"use strict";i.d(t,{default:()=>o});let o=(0,i(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\My projects\\\\Mobilify\\\\website\\\\gemini\\\\mobilify-website\\\\src\\\\analytics\\\\GoogleAnalytics.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\analytics\\GoogleAnalytics.tsx","default")},91405:(e,t,i)=>{"use strict";i.d(t,{default:()=>n,l:()=>a});var o=i(60687),r=i(43210),s=i(72600);let n=({websiteId:e})=>{let t=e||"your_crisp_website_id_here",i=e=>!!e&&!e.includes("your_crisp_website_id")&&!e.includes("placeholder")&&e.length>10;return((0,r.useEffect)(()=>{if(!t||!i(t))return;let e=()=>{};if(window.$crisp)e();else{let t=setInterval(()=>{window.$crisp&&(e(),clearInterval(t))},100);setTimeout(()=>clearInterval(t),1e4)}},[t]),t&&i(t))?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(s.default,{id:"crisp-chat-init",strategy:"afterInteractive",dangerouslySetInnerHTML:{__html:`
            window.$crisp = [];
            window.CRISP_WEBSITE_ID = "${t}";
          `}}),(0,o.jsx)(s.default,{src:"https://client.crisp.chat/l.js",strategy:"afterInteractive"})]}):null},a={openChat:()=>{},closeChat:()=>{},showChat:()=>{},hideChat:()=>{},setUser:e=>{},sendMessage:e=>{},setSessionData:e=>{},isChatAvailable:()=>!1,setSegments:e=>{}}}};